/**
 * Created by spatel on 11/9/18.
 */

import {User} from "./user.model";

export class AuthResponse {
    success: boolean;
    error: boolean;
    tokenInfo: any;
    user: User;
    message: string;
    email_pending?: boolean;
    otp_required?: boolean;
    reset_mfa?: boolean;
    invalid_credentials?: boolean;
}

export interface SSOProvider {
    key: string
    button: string
    url: string
}

export interface SSOCheckResponse {
    success: boolean
    sso_enabled: boolean
    exists?: boolean
    support_email?: string
    providers?: SSOProvider[]
}
