export class User {
    id?: number;
    name?: string;
    email?: string;
    password?: string;
    title?: string;
    other_title?: string;
    first_name?: string;
    middle_name?: string;
    last_name?: string;
    dob?: string;
    country_code?: string; // Two character ISO code of user's Country of Work selection
    country?: string;   // this is nationality field actually.
    gender?: string;
    marital_status?: string;
    sexual_orientation?: string;
    disability?: string;
    ethnicity?: string;
    subethnicity?: string;
    religion?: string;
    caring_responsibilities?: string;
    nin?: string;
    is_active?: number;
    // admin_access?: number; // @deprecated
    roles?: Array<string> = [];
    raw_uac?: Array<{
        role?: string,
        permission?: Array<string>,
        resource?: string,
        designation?: Array<string>
    }> = [];
    uac?: {
        permissions?: {},
        project_admins_v1?: Array<number>
    };
    user_onboard_status?: DefaultOnBoardStatus;
    selectedDob?: any;
    email_verified_on?:number;
    verify_email_token?: string;
    profile_pic_ref?: any;
    profile_pic_expiry?: number;
    timezone?: string;
    parent_company?: number|any;
    /*{
        id?: number;
        name?: string;
        has_company_portal?: boolean;
    };*/
}

export class DefaultOnBoardStatus {
    personal?: boolean = false;
    address?: boolean = false;
    health_assessment?: boolean = false;
    medical_assessments?: boolean = false;
    employment?: boolean = false;
    competencies?: boolean = true;
}
