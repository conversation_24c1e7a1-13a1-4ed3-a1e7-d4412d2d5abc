import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild} from "@angular/core";
import {ControlContainer, NgForm} from "@angular/forms";
import {concat, Observable, of, Subject} from "rxjs";
import {ToastService, UserService} from "@app/core";
import {debounceTime, distinctUntilChanged, filter, map, catchError, switchMap, take, tap} from "rxjs/operators";
import {NgSelectComponent} from "@ng-select/ng-select";
import {GenericConfirmationModalComponent, IModalComponent} from "@app/shared";

/*
    <form novalidate #addForm="ngForm" class="col-12">
        <div class="col-4">
            <company-selector-v2
                [required]="true"
                [country_code]="'GB'"
                [multiple]="true"
                classes="w-100"
                [selectId]="[96,101,5,120]"
            ></company-selector-v2>
        </div>
        <div class="col-4">
            <company-selector-v2
                [required]="true"
                [country_code]="'GB'"
                [name]="'approve'"
                [selectId]="86"
                [classes]="'bg-red'"
            ></company-selector-v2>
        </div>
    </form>
*/
@Component({
    viewProviders: [{provide: ControlContainer, useExisting: NgForm}],
    selector: 'company-selector-v2',
    templateUrl: './company-selector-v2.component.html',
})
export class CompanySelectorV2Component implements OnInit, OnChanges {
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;

    @Input()
    country_code: string;

    @Input()
    showHelper: boolean = false;

    @Input()
    fieldLabel: string = 'Company';

    @Input()
    required: boolean = false;

    @Input()
    multiple: boolean = false;

    // clear the search term from typeAhead once an item is selected. (useful for multi-select)
    @Input()
    clearSearchOnAdd: boolean = true;

    @Input()
    clearable: boolean = true;

    @Input()
    errorClasses: string = '';

    @Input()
    disabled: boolean = false;

    @Input()
    classes: string = '';

    @Input()
    requiredMessage: string = 'Company is required';

    @Input()
    placeholder: string = 'Search a Company';

    @Input()
    searchMessage: string = 'Type to search company';

    @Input()
    name: string = 'employer_ref';

    @Input()
    projectId?: number ;

    @Input()
    selectId: number | number[];

    _selectedCompany: string;
    @Input()
    set selectedCompany(value: string) {
        if (value) {
            this._selectedCompany = value;
            this.initializedWithSelection = false;
        } else {
            this.selectedCompanyId = null;
        }
    }

    @Output()
    selectionChanged: EventEmitter<{ selected: any;  record: any;}> = new EventEmitter<{ selected: any;  record: any;}>();

    initializedWithSelection: boolean = false;
    selectedCompanyId: number | number[];
    recordsLoading: boolean = false;
    records$: Observable<any[]>;
    userInput$ = new Subject<string>();

    modalInfo: {
        ready:boolean;
        saving:boolean;
        new_employer_name:string;
    } = {
        ready: false,
        saving: false,
        new_employer_name: null,
    };

    @ViewChild('employer_ref')
    private selectorRef: NgSelectComponent;

    constructor(
        private userService: UserService,
        private toastService: ToastService,
    ) {
    }

    ngOnInit(): void {
        this.bindInductionTypeAhead();
    }

    ngOnChanges(changes: SimpleChanges) {
        if (!this.initializedWithSelection && this.hasSelection(this.selectId?? this._selectedCompany) || (changes.selectId && changes.selectId.currentValue)) {
            console.log('ngOnChanges: pre-selected value of selection ID:', this.selectId, 'name:', this._selectedCompany);
            this.initializedWithSelection = true;
            this.getDefaultList(this.selectId, this._selectedCompany, true);
        }
        if(changes.selectId && (!changes.selectId.currentValue || !changes.selectId.currentValue.length)) {
            this.selectedCompanyId = null;
        }   
    } 

    private hasSelection(selection) {
        return (
            (this.multiple && selection && selection.length) ||
            (!this.multiple && selection)
        );
    }

    getDefaultList(preselect = undefined, selectedCompany = undefined, addTypeToSearch = false) {
        this.fetchList('', preselect, selectedCompany).pipe(take(1)).subscribe((data) => {
            let list = (data?.records || []);
            if ((this.selectId || this._selectedCompany) && (data?.selections || []).length) {
                let ids = data.selections.map(c => {
                    if (list.findIndex(l => l.id === c.id) === -1) {
                        list.push(c);
                    }
                    return c.id;
                });
                this.selectedCompanyId = this.multiple ? ids : ids.shift();
                list.sort((a, b) => a.name.localeCompare(b.name));
            }
            if(addTypeToSearch && data.records){
                data.records.push({name: 'Type to search more', disabled: true});
            }
            this.bindInductionTypeAhead(data?.records);
        });
    }

    private bindInductionTypeAhead(prefill = []) {
        this.records$ = concat(
            of(prefill), // default items
            this.userInput$.pipe(
                filter(Boolean),
                distinctUntilChanged(),
                debounceTime(400),
                tap(() => this.recordsLoading = true),
                switchMap(term => this.fetchList(term)),
                map(data => {
                    return (data?.records) || [{name: 'No records found', disabled: true}];
                })
            )
        );
    }

    private fetchList(term, id = undefined, selectedCompany = undefined) {
        let param: any = {
            country_code: this.country_code,
            q: term,
            sortKey: 'name',
            sortDir: 'asc',
            pageSize: 10,
        };

        if(id){
            param.selections = this.multiple ? (id || []).join(',') : id;
        }
        if(selectedCompany){
            param.selectedCompany = encodeURIComponent(selectedCompany);
        }
        if(this.projectId){
            param.projectId = this.projectId;
        }

        console.log('searching company', param)
        return this.userService.getCompaniesList(param).pipe(
            map((data: any) => {
                return data;//(data?.records) || [{name: 'No records found', disabled: true}];
            }),
            catchError(() => of({})), // empty list on error
            tap(() => this.recordsLoading = false)
        )
    }

    onClear() {
        if(!this.hasSelection(this.selectedCompanyId)){
            this.bindInductionTypeAhead([]);
        }
    }

    onOpen(){
        if(this.projectId && !this.hasSelection(this.selectedCompanyId)) {
            this.getDefaultList(undefined, undefined, true);
        }
    }

    onSelection($event, $elem) {
        // console.log('on selection', $event, $elem.selectedValues)
        if ($event) {
            this.initializedWithSelection = true;
        }
        let row = null;
        if ($event && !this.multiple) {
            row = (($elem.selectedValues && $elem.selectedValues.shift()) || null);
            // console.log('selected row', row)
        } else if (this.multiple) {
            row = (($elem.selectedValues) || []);
        }
        console.log('on selection, $event: ', {
            selected: $event,
            record: row,
        });
        this.selectionChanged.emit({
            selected: $event,
            record: row,
        });
    }

    trackByFn(item: any) {
        return item.id;
    }

    clearSelection() {
        return (this.selectorRef && this.selectorRef.handleClearClick());
    }

    @ViewChild('addModalHtml') private addModal: IModalComponent;
    openAddModal() {
        this.modalInfo.ready = true;
        this.addModal.open();
    }

    closeModalWithSelection(employer){
        this.modalInfo.new_employer_name = null;
        this.selectedCompanyId = employer.id;
        this.selectionChanged.emit({
            selected: employer.id,
            record: employer,
        });
        this.initializedWithSelection = false;
        this.getDefaultList(employer.id, undefined, true);
        this.modalInfo.ready = false;
        this.addModal.close();
    }

    saveInfo($event, $addForm) {
        let text = (this.modalInfo.new_employer_name || '').toString().trim();
        if (!$addForm.valid || !text) {
            console.log('Modal form is not valid');
            return;
        }
        this.modalInfo.saving = true;
        this.userService.createEmployer({name: text, country_code: (this.country_code || undefined)})
            .subscribe((result: any) => {
                    this.modalInfo.saving = false;
                    if (!result.employer) {
                        const message = result.message || 'Failed to create record.';
                        this.toastService.show(this.toastService.types.ERROR, message, { data: result });
                        return;
                    }
                    if (result.already_exists) {
                        this.confirmationModalRef.openConfirmationPopup({
                            headerTitle: 'Warning',
                            title: `${this.fieldLabel} has already been added.`,
                            confirmLabel: 'OK',
                            hasCancel: true,
                            onConfirm: () => {
                                this.closeModalWithSelection(result.employer);
                            }
                        });
                    } else {
                        this.closeModalWithSelection(result.employer);
                    }
                },
                error => {
                    this.modalInfo.saving = false;
                    const message = 'Failed to create record.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                });
    }

    checkIfEmployerExist($event){
        console.log('Lookup is', this.modalInfo.new_employer_name);
        let text = (this.modalInfo.new_employer_name || '').toString().trim();
        if(!text){
            return false;
        }
        return true;
        /*
        this.modalInfo.saving = true;
        this.userService.getCompaniesList({
            country_code: this.country_code,
            // q: text,
            pageSize: 2,
            selectedCompany: encodeURIComponent(text),
        }).pipe(take(1)).subscribe((result: any) => {
            this.modalInfo.saving = false;
            let alreadyRecord = (result.selections || []).find((r, i) => r.name.toLowerCase() === text.toLowerCase());
            if(alreadyRecord && alreadyRecord.id){
                this.confirmationModalRef.openConfirmationPopup({
                    headerTitle: 'Warning',
                    title: `${this.fieldLabel} has already been added.`,
                    confirmLabel: 'OK',
                    hasCancel: true,
                    onConfirm: () => {
                        this.closeModalWithSelection(alreadyRecord);
                    }
                });
            }
        });*/

    }
}
