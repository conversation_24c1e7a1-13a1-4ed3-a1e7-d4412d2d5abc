import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbDateStruct, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
    AuthService,
    Project,
    ProjectAssetVehicle,
    ProjectAssetService,
    ProjectService,
    User,
    UserService,
    WeeklyTimeSheet,
    ResourceService,
    Common, UACProjectDesignations,
    AssetActivityLog,
    AssetManagementConfModalType,
    ToastService,
    AssetVehiclesActionButtons,
    ActionButtonVal, CompanyAssetConfigService,
    AssetCustomConfig,
    AssetCustomField,
    AssetCustomConfigFieldTypes,
} from "@app/core";
import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import { DomSanitizer } from "@angular/platform-browser";
import { AppConstant } from "@env/environment";
import * as dayjs from 'dayjs';
import {SearchWithFiltersComponent} from '../search-with-filters/search-with-filters.component';
import { UsersSelectorModalComponent } from "../users-selector-modal/users-selector-modal.component";
import { filterData } from "@app/core";
import { FaultCloseoutComponent } from "../fault-closeout/fault-closeout.component";
import { AssetDeclineComponent } from "../asset-decline/asset-decline.component";
import { HttpParams } from '@angular/common/http';
import {AssetsUrl, GenericConfirmationModalComponent, IModalComponent} from "@app/shared";
import { forkJoin } from "rxjs";
import {DatatableComponent} from '@swimlane/ngx-datatable';

@Component({
    templateUrl: './asset-vehicles.component.html',
    selector: 'asset-vehicles',
})
export class AssetVehiclesComponent implements OnInit {

    @Input()
    projectId: number = 0;

    @Input()
    employerId: number = 0;

    @Input()
    project: any;

    @Input()
    isProjectPortal: boolean = false;

    @Output()
    isChildComponentLoaded: any = new EventEmitter<any>();

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('searchComponentRef') searchComponentRef: SearchWithFiltersComponent;

    authUser$: User;
    employer: any = {};
    dayjsFormat(x:number, fullDate: boolean = true){
        return (+x) ? dayjs(+x).format(fullDate ? AppConstant.fullDateTimeFormat : AppConstant.defaultDateFormat) : '';
    }
    allowedMime: Array<any> = ['image/jpeg', 'image/jpg', 'image/png'];
    addVehicleModal: any = null;
    assetVehicle: ProjectAssetVehicle = new ProjectAssetVehicle;
    TYPE_OF_VEHICLE: Array<any> = [];
    examinationCertPreviewURL: any;
    serviceCertPreviewURL: any;
    motCertPreviewURL: any;
    companiesListFilter: Array<any> = [];
    isVehicleImageUploaded: boolean = false;
    vehicleRecords: Array<any> = [];
    archivedVehicleRecords: Array<any> = [];
    filteredVehicleRecords: Array<any> = [];
    vehiclesLoading: boolean = false;
    certificateType: string = '';
    certificateUrl: string = '';
    certificatePreviewURL: any;
    isPdfDocument: boolean = false;
    showPdfImg = false;
    minDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs().add(1, 'day'));
    globalSearch: any = '';
    ownerFilter: any[] = [];
    vehicleTypeFilter: any[] = [];
    viewVehicleOptionModalRef: any = null;
    dailyInspectionOptionsModalRef: any = null;

    displayDateFormat: string = AppConstant.defaultDateFormat;
    downloadingWeeklyInspection: boolean = false;
    hasInspections: boolean = false;
    loadingVehicleRecords: boolean = false;
    loadingArchivedVehicleRecords: boolean = false;
    projectAdminIds: Array<any> = [];
    project_admins: Array<any> = [];
    vehicleManagerIds: Array<any> = [];
    vehicle_managers: Array<any> = [{}];
    projectRequest: Project = new Project;
    declined_comment: string = '';
    onSiteVehicleCount: number = 0;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    vehiclePhotos: Array<any> = [];
    closeFormModal: boolean = true;
    years: number[] = [];
    dropdownIndex: number = null;
    fileModalTitle: string = '';
    certificateImg: any;
    activeNav: number = 1;
    week_Days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    archivedVehicleListModalRef: any = null;
    vehicleFaults: Array<any> = [];
    faultCloseOutReq: any = {};
    blockLoader: boolean = false;
    hasOnlySiteManagement: boolean = false;
    userEmployer: any = {};
    projectResolverResponse: any = {};
    assetActivityLogs: Array<AssetActivityLog> = [];
    isListView: boolean = false;
    total_asset: number = 0;
    STATUS_CODES_LIST: Array<any> = new Common().ASSET_STATUS_CODES;
    selectedStatus: any[] = [];
    filterData:filterData[] = this.renderFilterData();
    archiveFilterData:filterData[] = this.renderArchivedFilterData();
    actionButtonMetaData = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    showModal: boolean = false;
    showVehicleModal: boolean = false;
    showPhotoCollage: boolean = false;
    indexNumber: number = 0;
    metaConfig;
    assetConfig: AssetCustomConfig = new AssetCustomConfig;
    assetTypeSelected: boolean = true;
    assetCustomConfigFieldTypes = AssetCustomConfigFieldTypes;
 
    // Pagination and filters
    paginationData = new Common();
    archivePaginationData = new Common();
    page = this.paginationData.page;
    archivedPage = this.archivePaginationData.page;
    @ViewChild(DatatableComponent) table: DatatableComponent;
    @ViewChild('faultCloseOutRef') faultCloseOutRef: FaultCloseoutComponent;
    @ViewChild('assetDeclineRef') assetDeclineRef: AssetDeclineComponent;
    isInitAssetVehicles: boolean = false;
    loadingInlineAssetVehicles: boolean = false;
    loadingInlineArchivedAssetVehicles: boolean = false;
    taggedOwners: Array<any> = [];
    archivedTaggedOwners: Array<any> = [];
    sortValues: any = null;
    loadingMetaConfig: boolean = false;
    public maxlength: number = 30;

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private modalService: NgbModal,
        private toastService: ToastService,
        private authService: AuthService,
        private projectService: ProjectService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private domSanitizer: DomSanitizer,
        private projectAssetService: ProjectAssetService,
        private resourceService: ResourceService,
        private companyAssetConfigService: CompanyAssetConfigService,
    ) {
        // this.resourceService.getInnDexSettingByName('type_of_asset_vehicles_en_gb').subscribe((data: any) => {
        //     if (data.success && data.record && data.record.value) {
        //         this.TYPE_OF_VEHICLE = data.record.value;
        //         this.filterData= this.renderFilterData();
        //     } else {
        //         alert('Something went wrong while getting type of vehicles.');
        //     }
        // });

        // this.resourceService.getInnDexSettingByName('type_of_asset_vehicles_en_gb').subscribe((data: any) => {
        //     if (data.success && data.record && data.record.value) {
        //         this.TYPE_OF_VEHICLE = data.record.value;
        //         this.filterData= this.renderFilterData();
        //         this.archiveFilterData = this.renderArchivedFilterData();
        //     } else {
        //         alert('Something went wrong while getting type of vehicles.');
        //     }
        // });


        this.years = new Common().getYears(2000);
    }

    ngOnInit() {
        if (!this.isProjectPortal) {
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        }
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });

        this.userService.getProjectAdmins(this.projectId, false, false, (!this.isProjectPortal ? this.employer.id : undefined)).subscribe((data:any) =>{
            if(data && data.admins) {
                this.projectAdminIds = (data.admins || []).reduce((acc, item) => {
                    if (item.user_ref) {
                      acc.push(item.user_ref.id || item.user_ref);
                    }
                    return acc;
                  }, []);
            } else {
                const message = data.message || 'Failed to get list of admins.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
        this.getViewType();
        this.getTaggedOwnersList();
        this.hasOnlySiteManagement = (this.project._my_designations.length === 1 && this.project._my_designations.includes(UACProjectDesignations.DELIVERY_MANAGER));
        this.setVehicleManagerIds(this.project);

        this.loadingVehicleRecords = true;
        this.getCompanyAssetConfig();
        if(this.isListView){
            this.loadingVehicleRecords = true;
            this.initVehicles(false, 0);
        }
        this.loadingArchivedVehicleRecords = true;
        this.initVehicles(true, 0);
        this.actionButtonMetaData.actionList = [
            {
                code: AssetVehiclesActionButtons.VEHICLE_MANAGERS,
                name: `Vehicle Managers`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'person',
                enabled: true,
            },
            {
                code: AssetVehiclesActionButtons.ARCHIVED_LIST,
                name: `Archived List`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'inventory_2',
                enabled: true,
            },
            {
                code: AssetVehiclesActionButtons.DOWNLOAD_REGISTER,
                name: `Download Register`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
        ];
    }

    getCompanyAssetConfig() {
        this.loadingMetaConfig = true;
        const params = new HttpParams().set('assetType',  'asset-vehicle');
        this.companyAssetConfigService.getProjectAssetConfig(this.project.id, params).subscribe((data: any)=> {
            this.loadingMetaConfig = false;
            if(data.error) {
                alert("Invalid request! Failed to get company meta asset configuration!")
                return;
            }
            this.metaConfig = data.records;
            this.mapAlternatePhrase();
        });
    }

    mapAlternatePhrase() {
        this.TYPE_OF_VEHICLE = this.metaConfig.map(a=> {
            return {
                key: a.key,
                value: a.name,
                alternate_phrase: a.alternate_phrase
            }
        });
        this.filterData= this.renderFilterData();
        this.archiveFilterData = this.renderArchivedFilterData();
    }

    toggler(activeNav: number) {
        this.activeNav = activeNav;
    }

    openCloseDropdown(index) {
        this.dropdownIndex = index !== this.dropdownIndex ? index : null;
    }

    setVehicleManagerIds(project) {
        this.vehicleManagerIds = (project.custom_field.vehicle_managers || []).reduce((arr, item) => {
            if (item && typeof item === 'number') {
                arr.push(item);
            }
            return arr;
        }, []);
    }

    sortCallback({sorts}, is_archived: boolean = false) {
        let [firstSort] = sorts || [];
        let {dir, prop} = firstSort;
        this.sortValues = firstSort;
        this.initVehicles( is_archived, 0, true, {sortKey: prop, sortDir: dir});
    }
    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, is_archived: boolean = false, isPageChange: boolean) {
        if (!this.isInitAssetVehicles) {
            this.isInitAssetVehicles = true;
            return;
        }
        console.log("here we are in pageCallback", this.page.pageNumber, pageInfo.offset, pageInfo);
        let pageNumber = 0;
        pageNumber = pageInfo.offset;
        this.initVehicles(is_archived, pageNumber, isPageChange, this.sortValues != null ? {sortKey: this.sortValues.prop, sortDir: this.sortValues.dir} : {sortKey: 'id'});
    }
    paginationCallback(event:any){
        this.page.pageNumber = event-1;
        this.initVehicles(false, this.page.pageNumber);
    }

    getTaggedOwnersList(is_archived = 'false') {
        this.projectAssetService.getAssetTaggedOwners(this.projectId, is_archived, 'asset-vehicle').subscribe((res:any) => {
            if(res.success) {
                if (is_archived === 'false') {
                    this.taggedOwners = res.tagged_owners;
                    this.filterData = this.renderFilterData();
                } else {
                    this.archivedTaggedOwners = res.tagged_owners;
                    this.archiveFilterData = this.renderArchivedFilterData();
                }
            }
        })
    }

    initVehicles(is_archived:boolean = false, pageNumber:number = 0, isPageChange:boolean = false, sortInfo: any = {sortKey: 'id'}) {
        if (!isPageChange) {
            !is_archived ? (this.loadingVehicleRecords = true) : (this.loadingArchivedVehicleRecords = true);
        }
        if(isPageChange){
            !is_archived ? (this.loadingInlineAssetVehicles = true) : (this.loadingInlineArchivedAssetVehicles = true);
        }
        let pageSize = this.page.size;
        if(is_archived){
            pageSize = this.archivedPage.size;
        }
        let params = new HttpParams()
            .set('pageNumber', `${pageNumber}`)
            .set('pageSize', `${pageSize}`)
            .set('sortKey', `${sortInfo.sortKey}`);
        if(this.globalSearch !== null){
            params = params.append('q',`${encodeURIComponent(this.globalSearch)}`);
        }
        if(this.vehicleTypeFilter.length != 0){
            params  = params.append('type', `${this.vehicleTypeFilter.join(",")}`);
        }
        if(this.ownerFilter.length != 0){
            params = params.append('owner', `${this.ownerFilter.join(",")}`);
        }
        if (this.selectedStatus.length) {
            params = params.append('status', `${this.selectedStatus.join(",")}`);
        }
        if(sortInfo.sortDir){
            params = params.append('sortDir', `${sortInfo.sortDir}`);
        }
        params = params.append('expand_inspections', false.toString());
        params = params.append('is_archived', is_archived.toString());

        this.projectAssetService.getProjectAssetVehicles(this.projectId, params).subscribe((data: any) => {
            this.isChildComponentLoaded.emit(false);
            this.loadingVehicleRecords = false;
            this.vehiclesLoading = false;
            if (data.success && data.records) {
                this.loadingInlineAssetVehicles = false;
                if(!is_archived){
                    this.loadingVehicleRecords = false;
                    this.loadingInlineAssetVehicles = false;
                    this.total_asset = data.total_asset || data.records.length;
                    this.page.totalElements = data.totalCount;
                    let vehicles_list = data.records;
                    this.onSiteVehicleCount = data.totalOnsiteAssets;
                    this.vehicleRecords = vehicles_list;
                    this.filteredVehicleRecords = vehicles_list;
                    this.filteredVehicleRecords.forEach(record => {
                        record.expiry_dates = [];
                        record.expiry_dates_count = 0;
                        record.type_of_vehicle_name = this.getVehicleType(record.type_of_vehicle);
                        if (record?.examination_cert_expiry_date) {
                            const isExpired = this.isExpired(record.examination_cert_expiry_date);
                            record.expiry_dates.push({ name: 'Thorough Examination', expiry_date: record.examination_cert_expiry_date, isExpired,});
                            if (isExpired === 0) {
                                record.expiry_dates_count += 1;
                            }
                        }

                        if (record?.mot_expiry_date) {
                            const isExpired = this.isExpired(record.mot_expiry_date);
                            record.expiry_dates.push({ name: 'MOT', expiry_date: record.mot_expiry_date, isExpired,});
                            if (isExpired === 0) {
                                record.expiry_dates_count += 1;
                            }
                        }

                        if (record?.service_expiry_date) {
                            const isExpired = this.isExpired(record.service_expiry_date);
                            record.expiry_dates.push({ name: 'Service', expiry_date: record.service_expiry_date, isExpired,});
                            if (isExpired === 0) {
                                record.expiry_dates_count += 1;
                            }
                        }
                        if (record?.latest_inspection?.createdAt) {
                            record.lastDailyInspectionAt = record.latest_inspection;
                        }
                        for(let k=0; k<record.custom_fields.length; k++) {
                            if(record.custom_fields[k].type === this.assetCustomConfigFieldTypes.Certification) {
                                const isExpired = this.isExpired(record.custom_fields[k].expiry_date);
                                record.expiry_dates.push({ 'name': record.custom_fields[k].title, 'expiry_date': record.custom_fields[k].expiry_date, 'isExpired': isExpired });
                                if (isExpired === 0) {
                                    record.expiry_dates_count += 1;
                                }
                            }
                        }

                        if (record?.expiry_dates?.length) {
                            record.expiry_dates.sort((a, b) => a.isExpired - b.isExpired);
                        }
                        record.last_inspected = record?.lastDailyInspectionAt?.updatedAt ? this.dayjsFormat(record.lastDailyInspectionAt.updatedAt, false) : 'N/A';
                        record.expired_certs = `${record.expiry_dates_count}/${record.expiry_dates.length}`;
                    });
                    this.userEmployer = data.userEmployer;
                } else {
                    this.loadingArchivedVehicleRecords = false;
                    this.loadingInlineArchivedAssetVehicles = false;
                    this.archivedPage.totalElements = data.totalCount;
                    this.archivedVehicleRecords = data.records;
                }
            }
        });
    }
    
    processVehicleData(data) {
        if (data.success && data.project_asset_vehicles) {
            this.total_asset = data.total_asset || data.project_asset_vehicles.length;
            this.isChildComponentLoaded.emit(false);
            this.loadingVehicleRecords = false;
            let vehicles_list = data.unarchived_asset_vehicle;
            this.onSiteVehicleCount = (vehicles_list || []).reduce((accumulator, item) => {
                if (item.approval_pending === 2) {
                    accumulator += 1;
                }
                return accumulator;
            }, 0);
            this.vehicleRecords = vehicles_list;
            this.filteredVehicleRecords = vehicles_list;
            this.filteredVehicleRecords.forEach(record => {
                record.expiry_dates = [];
                record.expiry_dates_count = 0;
                record.type_of_vehicle_name = this.getVehicleType(record.type_of_vehicle);
                if (record?.examination_cert_expiry_date) {
                    const isExpired = this.isExpired(record.examination_cert_expiry_date);
                    record.expiry_dates.push({ name: 'Thorough Examination', expiry_date: record.examination_cert_expiry_date, isExpired,});
                    if (isExpired === 0) {
                        record.expiry_dates_count += 1;
                    }
                }

                if (record?.mot_expiry_date) {
                    const isExpired = this.isExpired(record.mot_expiry_date);
                    record.expiry_dates.push({ name: 'MOT', expiry_date: record.mot_expiry_date, isExpired,});
                    if (isExpired === 0) {
                        record.expiry_dates_count += 1;
                    }
                }

                if (record?.service_expiry_date) {
                    const isExpired = this.isExpired(record.service_expiry_date);
                    record.expiry_dates.push({ name: 'Service', expiry_date: record.service_expiry_date, isExpired,});
                    if (isExpired === 0) {
                        record.expiry_dates_count += 1;
                    }
                }
                if (record?.latest_inspection?.createdAt) {
                    record.lastDailyInspectionAt = record.latest_inspection;
                }
                for(let k=0; k<record.custom_fields.length; k++) {
                    if(record.custom_fields[k].type === this.assetCustomConfigFieldTypes.Certification) {
                        const isExpired = this.isExpired(record.custom_fields[k].expiry_date);
                        record.expiry_dates.push({ 'name': record.custom_fields[k].title, 'expiry_date': record.custom_fields[k].expiry_date, 'isExpired': isExpired });
                        if (isExpired === 0) {
                            record.expiry_dates_count += 1;
                        }
                    }
                }

                if (record?.expiry_dates?.length) {
                    record.expiry_dates.sort((a, b) => a.isExpired - b.isExpired);
                }
                record.last_inspected = record?.lastDailyInspectionAt?.updatedAt ? this.dayjsFormat(record.lastDailyInspectionAt.updatedAt, false) : 'N/A';
                record.expired_certs = `${record.expiry_dates_count}/${record.expiry_dates.length}`;
            });
            this.archivedVehicleRecords = data.archived_asset_vehicles;
            this.userEmployer = data.userEmployer;
            this.getCompanies();
        }
    }

    private isExpired(expiry_date: number) {
        const today = dayjs().unix() * 1000;
        let priorDateInTimestamp = dayjs().add(30, 'day').unix() * 1000;
        if (expiry_date < today) {
            return 0;
        } else if (expiry_date > today && expiry_date <= priorDateInTimestamp) {
            return 1;
        } else if (expiry_date > priorDateInTimestamp) {
            return 2;
        }
    }

    dayjsTz(n: number) {
        let tz = this.project?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    associateAvailableMondays(vehicles_list) {
        vehicles_list.forEach(vl => {
            let vehicle_inspections = vl.vehicle_inspections;
            let availableMondays = (vehicle_inspections || []).reduce((arr, vi) => {
                let dateOnMonday = dayjs(+vi.createdAt).startOf('isoWeek').format(this.displayDateFormat);
                let existingWeekIndex = arr.findIndex(v => v.date === dateOnMonday);
                let existingWeek = arr.find(v => v.date === dateOnMonday);
                let weekDay = this.dayjsTz(+vi.createdAt).format('ddd');
                let createdAt = this.dayjsTz(+vi.createdAt).format(this.displayDateFormat+'  HH:mm:ss');
                let user_name = vi.user_ref ? (vi.user_ref.first_name + ' ' + vi.user_ref.last_name) : '';

                if (existingWeekIndex == -1) {
                    let newWeek: any = {};
                    newWeek.date = dateOnMonday;
                    newWeek.weekdays = [{
                        day: weekDay,
                        user_name,
                        createdAt
                    }];
                    arr.push(newWeek)
                } else {
                    let weekDayIndex = (existingWeek.weekdays || []).findIndex(v => v.day === weekDay);
                    if (weekDayIndex == -1) {
                        existingWeek.weekdays.push({
                            day: weekDay,
                            user_name,
                            createdAt
                        });
                    }
                    arr[existingWeekIndex] = existingWeek;
                }

                let inspectionFaults = (vi.fault_details || []).reduce((arr, fault) => {
                    fault.vi_ref = vi.id;
                    fault.vi_createdAt = vi.createdAt;
                    fault.status = (!fault.status || !fault.closedout_at) ? 'open' : fault.status;
                    arr.push(fault);
                    return arr;
                }, []);

                this.vehicleFaults.push(...inspectionFaults);

                return arr;
            }, []);
            vl.availableMondays = availableMondays.sort((a, b) => (dayjs(b.date, this.displayDateFormat).diff(dayjs(a.date, this.displayDateFormat))));
        });
        return vehicles_list;
    }

    dayIndex(weekdays, day): number {
        return weekdays.findIndex(d => d.day === day);
    }

    @ViewChild('viewFileRef') private viewFileRef: IModalComponent;
    viewFileModal(fileUrl, title) {
        this.certificateUrl = fileUrl;
        this.fileModalTitle = title;
        if (fileUrl) {
            this.showPdfImg = true;
            this.isPdfDocument = this.isPDF(this.certificateUrl, this.fileModalTitle);
            this.viewFileRef.open();
        }
    }

    closePdfImgModal(event?) {
        this.showPdfImg = false;
        if(event) {
            event.closeFn();
        }
    }

    getVehicle(vehicleId: number, isEditable: boolean, viewOptionModal: boolean) {
        this.projectAssetService.getVehicleAsset(this.projectId, vehicleId, 'true').subscribe((data: any) => {
            this.blockLoader = false;
            this.assetTypeSelected = true;
            if (viewOptionModal) {
                this.viewVehicleOptionsRef.open();
            }
            if (data.success && data.asset_vehicle) {
                let todayMs = dayjs().valueOf();
                data.asset_vehicle.examination_certificates.push({});
                data.asset_vehicle.service_certificates.push({});
                data.asset_vehicle.mot_certificates.push({});
                if (data.asset_vehicle.examination_cert_expiry_date && (+data.asset_vehicle.examination_cert_expiry_date - todayMs) >= 0) {
                    data.asset_vehicle._examinationCertExpiryDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.asset_vehicle.examination_cert_expiry_date));
                }

                if (data.asset_vehicle.service_expiry_date && (+data.asset_vehicle.service_expiry_date - todayMs) >= 0) {
                    data.asset_vehicle._serviceExpiryDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.asset_vehicle.service_expiry_date));
                }

                if (data.asset_vehicle.mot_expiry_date && (+data.asset_vehicle.mot_expiry_date - todayMs) >= 0) {
                    data.asset_vehicle._motExpiryDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.asset_vehicle.mot_expiry_date));
                }
                data.asset_vehicle._arrivedAt = (data.asset_vehicle.arrived_at) ? this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.asset_vehicle.arrived_at)) : null;

                this.assetVehicle = data.asset_vehicle;
                this.vehicleFaults = [];
                let vehicleWithInspections = this.associateAvailableMondays([data.asset_vehicle]);
                this.assetVehicle = vehicleWithInspections[0];
                this.hasInspections = (this.assetVehicle['availableMondays'] && this.assetVehicle['availableMondays'].length) ? true : false;
                this.manageVehicleFaults();
                if (isEditable) {
                    this.assetTypeChanged();
                    this.transformDatesToNgb();
                    if (this.viewVehicleOptionsRef) {
                        this.viewVehicleOptionsRef.close();
                        this.assetVehicle.tagged_owner = (this.assetVehicle.tagged_owner || []).map(owner => owner.id);
                        this.showVehicleModal = true;
                        this.checkPhotoCollage();
                        this.addVehicleModalRef.open();
                    }
                }
                this.isVehicleImageUploaded = false;
                if (this.assetVehicle.vehicle_photos.length && this.assetVehicle.vehicle_photos[0].file_url) {
                    this.isVehicleImageUploaded = true;
                }
            }
        });
    }

    manageVehicleFaults() {
        let openFaults = [];
        let closedFaults = [];
        this.vehicleFaults = (this.vehicleFaults).sort((a, b) => (a.fault_id < b.fault_id) ? 1 : ((b.fault_id < a.fault_id) ? -1 : 0))
        this.vehicleFaults.map(fault => {
            if ((!fault.status && !fault.closedout_at) || fault.status == 'open') {
                openFaults.push(fault);
            } else {
                closedFaults.push(fault);
            }
        });
        closedFaults = (closedFaults).sort((a, b) => (a.closedout_at < b.closedout_at) ? 1 : ((b.closedout_at < a.closedout_at) ? -1 : 0))

        this.vehicleFaults = [...openFaults, ...closedFaults];
    }

    getCompanies() {
        let companyIds = [];
        (this.vehicleRecords || []).map(vehicle => {
            if (vehicle.tagged_owner && vehicle.tagged_owner.length) {
                (vehicle.tagged_owner || []).map(owner => {
                   if (owner.id && !companyIds.includes(owner.id)) {
                       companyIds.push(owner.id);
                       this.companiesListFilter.push({ 'id': owner.id, 'name': owner.name });
                   }
                });
            }
        });
        this.companiesListFilter.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
        this.vehiclesLoading = false;
    }

    @ViewChild('addVehicleModalRef')
    private addVehicleModalRef: IModalComponent;
    addVehiclePopup() {
        this.isVehicleImageUploaded = false;
        this.assetVehicle = new ProjectAssetVehicle();
        this.assetVehicle.tagged_owner = (this.hasOnlySiteManagement && this.userEmployer.id) ? [this.userEmployer.id] : [];
        this.assetVehicle.vehicle_photos = [{}];
        this.assetVehicle.vehicle_id = `${this.total_asset + 1}`;
        this.showVehicleModal = true;
        this.checkPhotoCollage();
        this.assetTypeSelected = true;
        this.addVehicleModalRef.open();
    }

    openModal(content, size) {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size
        });
    }

    examinationCertificateUploadDone($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetVehicle.examination_certificates.length) ? this.assetVehicle.examination_certificates.length - 1 : 0;
            this.assetVehicle.examination_certificates[index] = $event.userFile;
            this.assetVehicle.examination_certificates[this.assetVehicle.examination_certificates.length] = {};
        }
    }

    deleteExaminationCertificateRecord($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            this.assetVehicle.examination_certificates = this.assetVehicle.examination_certificates.filter(r => (r.id !== $event.userFile.id));
        }
    }

    serviceCertificateUploadDone($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetVehicle.service_certificates.length) ? this.assetVehicle.service_certificates.length - 1 : 0;
            this.assetVehicle.service_certificates[index] = $event.userFile;
            this.assetVehicle.service_certificates[this.assetVehicle.service_certificates.length] = {};
        }
    }

    deleteServiceCertificateRecord($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            this.assetVehicle.service_certificates = this.assetVehicle.service_certificates.filter(r => (r.id !== $event.userFile.id));
        }
    }

    motCertificateUploadDone($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetVehicle.mot_certificates.length) ? this.assetVehicle.mot_certificates.length - 1 : 0;
            this.assetVehicle.mot_certificates[index] = $event.userFile;
            this.assetVehicle.mot_certificates[this.assetVehicle.mot_certificates.length] = {};
        }
    }

    deleteMOTCertificateRecord($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            this.assetVehicle.mot_certificates = this.assetVehicle.mot_certificates.filter(r => (r.id !== $event.userFile.id));
        }
    }

    isPDF(url, type) {
        if (url && url.split('.').pop() && url.split('.').pop().toLowerCase() === 'pdf') {
            if (type === 'Examination Certificate') {
                this.examinationCertPreviewURL = this.domSanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');
            } else if (type === 'Service Certificate') {
                this.serviceCertPreviewURL = this.domSanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');
            } else {
                this.motCertPreviewURL = this.domSanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');
            }
            this.certificatePreviewURL = this.domSanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');
            return true;
        }
        return false;
    }

    logoImgRetries: number = 5;

    onLogoError($img: any, targetSrc: any, isLogo = true) {
        if (isLogo) {
            $img.src = AssetsUrl.siteAdmin.logoPlaceholder; // AppConstant.apiServerUrl + '/images/logo-placeholder.png';
        } else {
            $img.src = AssetsUrl.siteAdmin.projectPlaceholder; // AppConstant.apiServerUrl + '/images/project-placeholder.png';
        }

        if (targetSrc && targetSrc.length && this.logoImgRetries) {
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img) {
        this.logoImgRetries = 5;
    }

    saveVehicle() {
        this.vehiclesLoading = true;
        this.assetVehicle.examination_cert_expiry_date = this.assetVehicle._examinationCertExpiryDate ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.assetVehicle._examinationCertExpiryDate).valueOf() : null;
        this.assetVehicle.service_expiry_date = this.assetVehicle._serviceExpiryDate ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.assetVehicle._serviceExpiryDate).valueOf() : null;
        this.assetVehicle.mot_expiry_date = this.assetVehicle._motExpiryDate ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.assetVehicle._motExpiryDate).valueOf() : null;
        this.assetVehicle.arrived_at = this.assetVehicle._arrivedAt ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.assetVehicle._arrivedAt).valueOf() : null;
        this.transformDatesToDayJS();
        if (!this.assetVehicle.id) {
            this.setActivityLogRequest("Added");
            this.assetVehicle.approval_pending = this.hasOnlySiteManagement ? 1 : 2;
            this.projectAssetService.addVehicleAsset(this.assetVehicle, this.projectId).subscribe(this.responseHandler.bind(this, false, false));
        } else {
            this.setActivityLogRequest("Modified");
            let request:any = Object.assign({}, this.assetVehicle);
            delete request.availableMondays;
            delete request.vehicle_inspections;
            this.projectAssetService.updateVehicleAsset(request, this.projectId, this.assetVehicle.id).subscribe(this.responseHandler.bind(this, false, false));
        }
        this.closeVehicleModal();
        this.addVehicleModalRef.close();
    }

    responseHandler(loadVehicle = false, isArchived:boolean = false, out: any, data?: any) {
        this.blockLoader = false;
        if (out.success) {
            (this.addVehicleModal) ? this.addVehicleModal.close() : '';
            if (data) {
                data.closeFn();
            }
            this.initVehicles(isArchived, 0, true);

            if (loadVehicle) {
                this.getVehicle(this.assetVehicle?.id, false, false);
            }
        } else {
            const message = out.message || 'Failed to store data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        }
    }

    hasExaminationNumber() {
        if (!this.assetVehicle.examination_cert_number) {
            this.assetVehicle.examination_certificates = [{}];
            this.assetVehicle.examination_cert_expiry_date = null;
            return false;
        }
        return true;
    }

    hasExaminationCertExpDate() {
        if (!this.assetVehicle._examinationCertExpiryDate) {
            this.assetVehicle.examination_certificates = [{}];
            return false;
        }
        return true;
    }

    hasServiceExpDate() {
        if (!this.assetVehicle._serviceExpiryDate) {
            this.assetVehicle.service_certificates = [{}];
            return false;
        }
        return true;
    }

    hasMOTExpDate() {
        if (!this.assetVehicle._motExpiryDate) {
            this.assetVehicle.mot_certificates = [{}];
            return false;
        }
        return true;
    }

    getTaggedOwners(tagged_owner) {
        let ownersInfo = [];
        (tagged_owner || []).map(company => {
            if (company && company.name) {
                ownersInfo.push(company.name);
            }
            return company;
        });
        return ownersInfo.join(', ');
    }


    filterRecords(is_archived:boolean = false) {
        this.initVehicles(is_archived, 0, true);
    }

    resetFilter(filter_field) {
        if (filter_field === 'vehicle_type_filter') {
            this.vehicleTypeFilter = null;
        } else if (filter_field === 'owner_filter') {
            this.ownerFilter = null;
        } else {
            this.globalSearch = '';
        }
        this.filterRecords();
    }

    getVehicleType(vehicleTypeKey) {
        let typeInfo = this.TYPE_OF_VEHICLE.find(v => v.key === vehicleTypeKey);
        return typeInfo ? typeInfo.alternate_phrase : '';
    }

    @ViewChild('viewVehicleOptionsRef') private viewVehicleOptionsRef: IModalComponent;
    viewVehicleOptionModal(vehicle, tab= 1) {
        this.blockLoader = true;
        this.assetVehicle = vehicle;
        this.getVehicle(this.assetVehicle?.id, false, true);
        this.activeNav = tab;
        this.assetActivityLogs = [...this.assetVehicle.activity_logs].reverse();
        this.modalService.dismissAll();
    }

    @ViewChild('dailyInspectionOptionsHtml', {static: true}) private dailyInspectionOptionsHtmlRef = TemplateRef;
    dailyInspectionOptionsModal(vehicle: any = {}) {
        this.assetVehicle = (vehicle && vehicle.id) ? vehicle : this.assetVehicle;
        if (this.assetVehicle['availableMondays'] && !this.assetVehicle['availableMondays'].length) {
            const message = 'No inspection available.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }

        if (this.viewVehicleOptionModalRef) {
            this.viewVehicleOptionModalRef.close();
        }
        this.dailyInspectionOptionsModalRef = this.openModal(this.dailyInspectionOptionsHtmlRef, 'sm');
    }

    downloadWeeklyInspection(fromDate, format, vehicleId) {
        this.downloadingWeeklyInspection = true;
        let fileName = 'Vehicle-Weekly-Inspections-Report-' + dayjs().format(AppConstant.apiRequestDateFormat);

        let request = {
            from_day: dayjs(fromDate, this.displayDateFormat).format(AppConstant.apiRequestDateFormat),
            format,
            file_name: fileName
        };

        if (format === 'pdf') {
            this.projectAssetService.downloadWeeklyInspection(this.projectId, 'vehicle', vehicleId, request, () => {
                this.downloadingWeeklyInspection = false;
            });
        } else {
            this.projectAssetService.downloadWeeklyInspection(this.projectId, 'vehicle', vehicleId, request).subscribe((data: any) => {
                this.downloadingWeeklyInspection = false;
                let newWindow = window.open('', '_blank', 'location1=no,height1=570,width1=520,scrollbars=yes,status=yes,toolbar=no');
                newWindow.document.body.innerHTML = data;
            });
        }
    }

    archiveVehicleAsset(id) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Archive Vehicle',
            title: `Are you sure you want to archive this vehicle?`,
            confirmLabel: 'Archive',
            onConfirm: () => {
                this.vehiclesLoading = true;
                this.setActivityLogRequest("Archived");
                let request = {
                    is_archived: true,
                    activity_logs: this.assetVehicle.activity_logs
                };
                this.projectAssetService.archiveUnarchiveVehicleAsset(request, this.projectId, id).subscribe(this.responseHandler.bind(this, false, false));
                this.viewVehicleOptionsRef.close();
            }
        });
    }

    unArchiveVehicleAsset(id, isArchived, isViewModal) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Unarchive Vehicle',
            title: `Are you sure you want to unarchive this vehicle?`,
            confirmLabel: 'Unarchive',
            onConfirm: () => {
                this.vehiclesLoading = true;
                this.setActivityLogRequest("Unarchived");
                let request = {
                    is_archived: false,
                    activity_logs: this.assetVehicle.activity_logs
                };
                this.projectAssetService.archiveUnarchiveVehicleAsset(request, this.projectId, id).subscribe(this.responseHandler.bind(this, false, isArchived));
                if(isViewModal){
                    this.viewVehicleOptionsRef.close();
                }
                this.initVehicles(false, 0, true);
            }
        });
    }

    @ViewChild('archivedVehicleListRef') private archivedVehicleListRef: IModalComponent
    showArchivedVehicles() {
        this.getTaggedOwnersList('true');
        this.initVehicles(true, 0, true);
        this.showModal = true;
        this.archivedVehicleListRef.open();
    }

    @ViewChild('usersSelectorModal') usersSelectorModalRef: UsersSelectorModalComponent
    vehicleManagersPopup() {
        this.userService.getUsersById(Array.from(new Set([...this.vehicleManagerIds, ...this.projectAdminIds])), ['email', 'first_name', 'last_name'], this.projectId).subscribe((data: any) => {
            if (data.success && data.users) {
                this.vehicle_managers = (data.users || []).filter(user => this.vehicleManagerIds.includes(user.id)).map(user => ({ id: user.id })) || [{}];
                this.project_admins = (data.users || []).filter(user => this.projectAdminIds.includes(user.id));
                this.usersSelectorModalRef.openUsersSelectorModalModal();
            } else {
                const message = data.message || 'Failed to get data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    saveVehicleManagers($event) {
        this.vehiclesLoading = true;
        this.vehicleManagerIds = ($event || []).reduce((arr, item) => {
            if (item.id) {
                arr.push(item.id);
            }
            return arr;
        }, []);

        this.projectRequest = {
            "id": this.projectId,
            "custom_field": {
                "vehicle_managers": this.vehicleManagerIds
            }
        };

        this.projectService.updateProjectPartially(this.projectRequest).subscribe((data: any) => {
            this.vehiclesLoading = false;
            if (data.success) {
                this.project.custom_field = data.project.custom_field;
                this.setVehicleManagerIds(data.project);
                console.log("Vehicle managers added successfully.");
            }
        });
    }

    approveVehicle(item) {
        this.dropdownIndex = null;
        this.assetVehicle = item;
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Approve Vechicle',
            title: `Are you sure you want to approve this vehicle?`,
            confirmLabel: 'Approve',
            onConfirm: () => {
                this.vehiclesLoading = true;
                this.setActivityLogRequest("Approved");
                let request = {
                    "approval_pending": 2,
                    "activity_logs": this.assetVehicle.activity_logs
                };
                this.projectAssetService.updateVehicleAsset(request, this.projectId, this.assetVehicle.id).subscribe(this.responseHandler.bind(this, false, false));

            }
        });
    }

    declineVehicleConfirmation(item) {
        this.dropdownIndex = null;
        this.assetVehicle = item;
        this.declined_comment = '';
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Decline Vehicle',
            title: `Are you sure you want to decline this vehicle?`,
            confirmLabel: 'Decline',
            onConfirm: () => {
                this.declined_comment = '';
                this.assetDeclineRef.openAssetDeclineModal();
            }
        });
    }

    declineVehicle(data) {
        this.vehiclesLoading = true;
        let request = {
            "approval_pending": 3,
            "declined_comment": data.comment
        };
        this.projectAssetService.updateVehicleAsset(request, this.projectId, this.assetVehicle.id).subscribe(res => this.responseHandler(false, false, res, data));
    }

    vehiclePhotoUploadDone($event: any, uploadMore = false) {
        if ($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetVehicle.vehicle_photos.length && Object.keys(this.assetVehicle.vehicle_photos[0]).length) ? this.assetVehicle.vehicle_photos.length : 0;
            index = (uploadMore) ? index - 1 : index;
            this.assetVehicle.vehicle_photos[index] = $event.userFile;
            if (uploadMore) {
                this.assetVehicle.vehicle_photos[this.assetVehicle.vehicle_photos.length] = {};
            }
            this.checkPhotoCollage();
        }
    }

    vehiclePhotoCollageUploadDone($event: any, uploadMore = false) {
        if ($event && $event.userFile && $event.userFile.id) {
            if ((this.assetVehicle.vehicle_photos.length && Object.keys(this.assetVehicle.vehicle_photos[0]).length)) {
                this.indexNumber = this.assetVehicle.vehicle_photos.length;
            } else {
                this.indexNumber = 0;
                uploadMore = false;
            }
            this.indexNumber = (uploadMore) ? this.indexNumber - 1 : this.indexNumber;
            uploadMore = true;
            this.assetVehicle.vehicle_photos[this.indexNumber] = $event.userFile;
            if (uploadMore) {
                this.assetVehicle.vehicle_photos[this.assetVehicle.vehicle_photos.length] = {};
            }
            this.checkPhotoCollage();
        }
    }

    checkPhotoCollage() {
        if((this.assetVehicle.vehicle_photos?.length && this.assetVehicle.vehicle_photos[0].file_url)) {
            this.showPhotoCollage = true;
        }
    }

    fileDeleteDone($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            this.assetVehicle.vehicle_photos = this.assetVehicle.vehicle_photos.filter(r => (r.id !== $event.userFile.id));
            this.checkPhotoCollage();
        }
    }

    downloadRegister() {
        let projectId = this.projectId;
        let vehicleIds = ([...this.vehicleRecords, ...this.archivedVehicleRecords] || []).reduce((arr, item) => {
            if (item.approval_pending == 2) {
                arr.push(item.id);
            }
            return arr;
        }, []);
        let request = {
            asset_ids: vehicleIds
        };
        this.vehiclesLoading = true;
        this.projectAssetService.downloadRegisterXLSX(request, projectId, 'vehicle', `asset-register-vehicle-${this.project.project_number}-${this.project.name}.xlsx`, () => {
            this.vehiclesLoading = false;
        });
    }

    faultCloseoutModal(faultDetail) {
        this.faultCloseOutReq = faultDetail;
        this.faultCloseOutReq.closedout_images = [{}];
        this.faultCloseOutReq.closedout_at = null;
        this.faultCloseOutReq.closedout_by = null;
        this.faultCloseOutReq.closedout_details = '';

        this.faultCloseOutRef.openFaultCloseOutModal();
    }

    uploadDoneFault($event) {
        if($event && $event.userFile) {
            this.faultCloseOutReq.closedout_images.splice(1, 0, ...$event.userFile);
            this.faultCloseOutReq.closedout_images[0] = {};
        }
    }

    fileDeleteDoneFault($event: any) {
        if ($event && $event.userFile && $event.userFile.id) {
            this.faultCloseOutReq.closedout_images = this.faultCloseOutReq.closedout_images.filter(r => (r.id !== $event.userFile.id));
        }
    }

    requestCloseOut(data) {
        this.faultCloseOutRequest(data);
    }

    faultCloseOutRequest(data) {
        this.faultCloseOutReq = data.faultCloseOutReqObj;
        this.faultCloseOutReq.closedout_images = this.faultCloseOutReq.closedout_images.reduce((arr, image) => {
            if (image.id) {
                arr.push(image.id);
            }
            return arr;
        }, []);

        this.faultCloseOutReq.images = this.faultCloseOutReq.images.reduce((arr, image) => {
            if (image.id) {
                arr.push(image.id);
            }
            return arr;
        }, []);

        this.faultCloseOutReq.reported_by = (this.faultCloseOutReq.reported_by) ? this.faultCloseOutReq.reported_by : "N/A";

        let inspectionId = this.faultCloseOutReq.vi_ref;
        //Remove extra properties
        delete this.faultCloseOutReq.vi_ref;
        delete this.faultCloseOutReq.vi_createdAt;

        this.faultCloseOutReq.status = 'closed';
        this.faultCloseOutReq.closedout_at = dayjs().valueOf();
        this.faultCloseOutReq.closedout_by = this.authUser$.first_name+' '+this.authUser$.last_name;
        delete this.faultCloseOutReq.reported_to;

        this.blockLoader = true;
        this.projectAssetService.updateAssetVehicleInspection({'fault': this.faultCloseOutReq}, this.projectId, this.assetVehicle.id, inspectionId).subscribe(res => this.responseHandler(true, false, res, data));
    }

    getRowSpan(fault) {
        let rowSpan = 1;
        if (fault.images && fault.images.length) {
            rowSpan += 1;
        }

        if (fault.closedout_at) {
            rowSpan += 1;
        }

        if (fault.closedout_at && fault.closedout_images && fault.closedout_images.length) {
            rowSpan += 1;
        }

        return rowSpan;
    }

    setActivityLogRequest(type: string) {
        let activityLog = new AssetActivityLog();
        activityLog.type = type;
        activityLog.name = this.authUser$.first_name+' '+this.authUser$.last_name;
        activityLog.user_id = this.authUser$.id;
        activityLog.timestamp = dayjs().valueOf();
        this.assetVehicle.activity_logs.push(activityLog);
    }

    getViewType() {
        this.isListView = JSON.parse(localStorage.getItem("isListView"));
    }

    setViewType() {
        this.isListView = !this.isListView;
        localStorage.setItem("isListView", JSON.stringify(this.isListView));
    }
    onFilterSelection(data, is_archived:boolean = false) {
        this.vehicleTypeFilter = data.type.map(a=>a.key);
        this.ownerFilter = data.owner.map(a=>a.id);
        if(!is_archived){
            this.selectedStatus = data.status.map(a => +a.code);
        }
        if(this.isListView) {
            this.table.offset = 0;
        }
        this.filterRecords(is_archived);
    }
    searchFunction(data, is_archived:boolean = false){
        this.globalSearch = data.search;
        if(this.isListView) {
            this.table.offset = 0;
        }
        this.filterRecords(is_archived);
    }

    getQRCodeString(asset) {
        let str = this.projectId + '-AMV-'+asset.id;
        return str;
    }

    getFileName(asset) {
        let fileName = 'Asset-Vehicle-' + asset.id + '-qr-code';
        return fileName;
    }

    closeArchiveModal() {
        if(this.archivedVehicleRecords){
            this.searchComponentRef.clearFilters();
        }
        this.showModal = false;
    }

    closeVehicleModal() {
        this.showVehicleModal = false;
        this.showPhotoCollage = false;
        this.assetConfig = {};
    }

    renderFilterData(){
        return [
            {
                name:'type',
                list:this.TYPE_OF_VEHICLE,
                enabled:true,
                state:false

            },
            {
                name:'owner',
                list:this.taggedOwners,
                enabled:true,
                state:false

            },
            {
                name:'status',
                list:this.STATUS_CODES_LIST,
                enabled:!this.hasOnlySiteManagement,
                state:false

            },
        ];
    }

    renderArchivedFilterData() {
        return [
            {
                name:'type',
                list:this.TYPE_OF_VEHICLE,
                enabled:true,
                state:false
            },
            {
                name:'owner',
                list:this.archivedTaggedOwners,
                enabled:true,
                state:false
            }
        ];
    }

    public onActionSelection(actionVal: ActionButtonVal) {
        const code = actionVal.code;
        if(code === AssetVehiclesActionButtons.VEHICLE_MANAGERS) {
            this.vehicleManagersPopup();
        } else if(code === AssetVehiclesActionButtons.ARCHIVED_LIST) {
            this.showArchivedVehicles();
        } else if(code === AssetVehiclesActionButtons.DOWNLOAD_REGISTER) {
            this.downloadRegister();
        }
    }

    syncCustomFields() {
        let updatedCustomFields = [];
        this.assetConfig.custom_fields.forEach((configField, index) => {
            const matchingIndex = this.assetVehicle.custom_fields.findIndex(
                tempField => tempField.key === configField.key
            );
            if (matchingIndex !== -1) {
                if(this.assetVehicle.custom_fields[matchingIndex].type === AssetCustomConfigFieldTypes.Certification) {
                    this.assetVehicle.custom_fields[matchingIndex].certificates = [...this.assetVehicle.custom_fields[matchingIndex].certificates, {}];
                }
                updatedCustomFields[index] = {
                    ...this.assetVehicle.custom_fields[matchingIndex],
                };
            } else {
                //add newly added field
                if(configField.type === AssetCustomConfigFieldTypes.Certification) {
                    configField.certificates = [{}];
                } else if(configField.type === AssetCustomConfigFieldTypes.Date) {
                    configField[configField.key] = null;
                }
                updatedCustomFields[index] = {
                    ...configField,
                };
            }
        });
        this.assetVehicle.custom_fields = updatedCustomFields;
    }

    assetTypeChanged() {
        this.assetTypeSelected = this.assetVehicle.type_of_vehicle ? false: true;
        this.blockLoader = true;
        let params = new HttpParams()
            .set('assetType', 'asset-vehicle')
            .set('assetKey', this.assetVehicle.type_of_vehicle)
            .set('extra', 'fields');
        this.companyAssetConfigService.getProjectAssetConfig(this.project.id, params).subscribe((data:any) => {
            if(data.success && data.records) {
                this.assetConfig = data.records.find(m=> m.key === this.assetVehicle.type_of_vehicle);
                this.assetConfig = this.assetConfig ? this.assetConfig: {} as Partial<AssetCustomConfig>;
                this.setAssetConfig();
            }
            this.blockLoader = false;
        });

    }   

    setAssetConfig() {
        if(this.assetConfig && this.assetVehicle.type_of_vehicle) {
            this.assetConfig.defaultFields = this.assetConfig.default_fields.reduce((a, v) => ({ ...a, [v.key]: v}), {});
            if(this.assetVehicle.id) {
                this.syncCustomFields();
            } else {
                this.assetVehicle.custom_fields = [];
                this.assetVehicle.custom_fields = this.assetConfig.custom_fields.map(d=> {
                    let a: any = {
                        'key': d.key,
                        'title': d.title,
                        'type': d.type,
                        'value': null
                    };
                    if(d.type === AssetCustomConfigFieldTypes.Certification) {
                       a.certificates = [{}];
                    }
                    return a;
                });
            }
        }
    }


    transformDatesToDayJS() {
        this.assetVehicle.custom_fields = this.assetVehicle.custom_fields.reduce<AssetCustomField[]>((result,a)=> {
            if(a.type === AssetCustomConfigFieldTypes.Certification) {
                a.expiry_date = a._examinationCertExpiryDate ? this.ngbMomentjsAdapter.ngbDateToDayJs(a._examinationCertExpiryDate).valueOf() : null;
                a.certificates = a.certificates.filter(c=> JSON.stringify(c) !== '{}');
                delete a._examinationCertExpiryDate;
                delete a.value;
            } else if(a.type === AssetCustomConfigFieldTypes.Date) {
                a.value = a.value ? this.ngbMomentjsAdapter.ngbDateToDayJs(a.value).valueOf() : null;
            }
            if((a.type === AssetCustomConfigFieldTypes.Certification && a.expiry_date) || (a.value)) {
                result.push(a);
            }
            return result;
        }, []);
    }

    transformDatesToNgb() {
        this.assetVehicle.custom_fields = this.assetVehicle.custom_fields.map(a=> {
            if(a.type === AssetCustomConfigFieldTypes.Certification) {
                a._examinationCertExpiryDate = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+a.expiry_date));
            } else if(a.type === AssetCustomConfigFieldTypes.Date) {
                a.value = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+a.value));
            }
            return a;
        });
    }

    trackByKey(index: number, field: any): string {
        return field.key;
    }

    certificateUploadDone($event:any, i){
        if($event && $event.userFile && $event.userFile.id) {
            let index = (this.assetVehicle.custom_fields[i].certificates.length) ? this.assetVehicle.custom_fields[i].certificates.length-1 : 0;
            this.assetVehicle.custom_fields[i].certificates[index] = $event.userFile;
            this.assetVehicle.custom_fields[i].certificates[this.assetVehicle.custom_fields[i].certificates.length] = {};
        }
    }

    deleteCertificateRecord($event:any, i) {
        if($event && $event.userFile && $event.userFile.id) {
            this.assetVehicle.custom_fields[i].certificates = this.assetVehicle.custom_fields[i].certificates.filter(r => (r.id !== $event.userFile.id));
        }
    }
}
