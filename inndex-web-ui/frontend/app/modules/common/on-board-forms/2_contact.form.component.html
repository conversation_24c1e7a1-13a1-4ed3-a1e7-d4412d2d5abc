<div class="w-100 mb-3 d-flex flex-column justify-content-center align-items-center">
    <p class="h6 fw-500 mb-0 text-quick-silver text-center"> {{ title }}</p>
    <p class="h4 fw-500 mb-0 text-center"> CONTACT DETAILS </p>
</div>

<ngx-skeleton-loader *ngIf="loading" count="8" [theme]="{'border-radius': '0', height: '50px'}"></ngx-skeleton-loader>
<form *ngIf="!loading" role="form" #addressForm="ngForm" class="editForm" novalidate>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">Country<small class="required-asterisk ">*</small></label>
        <div class="col-md-8">
            <!-- <select [(ngModel)]="contact_detail.country" #country="ngModel"
                    name="country" class="form-control" required
                    (change)="onCountrySelection()"
                    ng-value="contact_detail.country">
                <option *ngFor="let c of countries" [ngValue]="c.name">
                    {{ c.name }}
                </option>
            </select> -->
            <ng-select
                [items]="countries"
                bindLabel="name"
                [bindValue]="'name'"
                placeholder="Select Country"
                name="country" #country="ngModel"
                ng-value="contact_detail.country"
                [(ngModel)]="contact_detail.country" required
                (change)="onCountrySelection($event)">
            </ng-select>
        </div>
    </div>

    <ng-container *ngIf="contact_detail.country && !isAlternateFlow">
        <div class="form-group row">
            <label class="col-md-4 col-form-label form-control-label"><span i18n="@@pc">Post Code</span><small class="required-asterisk ">*</small></label>
            <div class="col-md-8">
                <ng-select [items]="postcodes$ | async"
                           name="postCode"
                           bindLabel="postcode"
                           [loading]="postCodeLoading"
                           [typeahead]="postCodeSearchInput$"
                           #projectRef required
                           #postCode="ngModel"
                           placeholder="Search post code..."
                           (change)="setSelctedAddress($event)"
                           (keypress)="blockSpecialChars($event)"
                           [(ngModel)]="contact_detail.post_code">
                    <ng-template ng-option-tmp let-item="item">
                        <span title="{{getAddressLine(item)}}">{{getAddressLine(item)}}</span>
                    </ng-template>
                </ng-select>
                <div class="alert alert-danger" [hidden]="(postCode.valid)">Post code is required</div>
            </div>
        </div>
        <!-- <div class="form-group row">
            <label class="col-md-3 col-form-label form-control-label">House Name/No</label>
            <div class="col-md-3">
                <input class="form-control input-md" #houseNo="ngModel" required name="houseNo" type="text" placeholder="House Name/No" [(ngModel)]="contact_detail.house_no" ng-value="contact_detail.house_no">
                <div class="alert alert-danger" [hidden]="(houseNo.valid)">House Name/No is required</div>
            </div>
        </div> -->
        <div class="form-group row">
            <label class="col-md-4 col-form-label form-control-label">Address<small class="required-asterisk ">*</small></label>
            <div class="col-md-8">
                <input class="form-control input-md" #street="ngModel" required name="street" type="text" placeholder="Address" [(ngModel)]="contact_detail.street" ng-value="contact_detail.street" (change)="onInputChanged($event)" pattern="^[^\s]+(\s.*)?$">
                <div class="alert alert-danger" [hidden]="(street.valid)">Address detail is required</div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-4 col-form-label form-control-label">City<small class="required-asterisk ">*</small></label>
            <div class="col-md-8">
                <input class="form-control input-md" #city="ngModel" required name="city" type="text" placeholder="City" [(ngModel)]="contact_detail.city" ng-value="contact_detail.city" (change)="onInputChanged($event)" (keypress)="blockSpecialChars($event)" pattern="^[^\s]+(\s.*)?$">
                <div class="alert alert-danger" [hidden]="(city.valid)">City is required</div>
            </div>
        </div>
    </ng-container>
    <ng-container *ngIf="contact_detail.country && isAlternateFlow">
        <div class="form-group row">
            <label class="col-md-4 col-form-label form-control-label">Search address</label>
            <div class="col-md-8">
                <input #search autocorrect="off" name="search_address" [value]="contact_detail.street" autocapitalize="off" spellcheck="off" type="text" class="form-control">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-4 col-form-label form-control-label">Address Line<small class="required-asterisk ">*</small></label>
            <div class="col-md-8">
                <input class="form-control input-md" #street="ngModel" required
                       name="street" type="text" placeholder="Enter your address"
                       [(ngModel)]="contact_detail.street" ng-value="contact_detail.street" pattern="^[^\s]+(\s.*)?$">
                <div class="alert alert-danger" [hidden]="(street.valid)">Address detail is required</div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-4 col-form-label form-control-label">City<small class="required-asterisk ">*</small></label>
            <div class="col-md-8">
                <input class="form-control input-md" #city="ngModel" required name="city" type="text" placeholder="City" [(ngModel)]="contact_detail.city" ng-value="contact_detail.city" (change)="onInputChanged($event)" pattern="^[^\s]+(\s.*)?$">
                <div class="alert alert-danger" [hidden]="(city.valid)">City is required</div>
            </div>
        </div>
        <div class="form-group row" *ngIf="postcodeInput?.type === 'postcode-lookup'">
            <label class="col-md-4 col-form-label form-control-label"><span i18n="@@pc">Post Code</span><small class="required-asterisk ">*</small></label>
            <div class="col-md-8">
                <input class="form-control input-md" #postCode="ngModel" required name="post_code" type="text" placeholder="Post Code" i18n-placeholder="@@pc"
                       [(ngModel)]="contact_detail.post_code" ng-value="contact_detail.post_code" (change)="onInputChanged($event)" pattern="^[^\s]+(\s.*)?$">
                <div class="alert alert-danger" [hidden]="(postCode.valid)"><span i18n="@@pc">Post Code</span> is required</div>
            </div>
        </div>
    </ng-container>

    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">Home Phone No.</label>
        <div class="col-md-8">
            <app-country-code-contact-input [countries]="countries" [contactData]="numberPickerInitial"
                [name]="'home_number'"
                [errorMessageTitle]="'Home No.'" (phoneModelChange)="onNumberInput($event, 'home_number')" [defaultCountryCode]="selectedCountryCode">
            </app-country-code-contact-input>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">Mobile No.<small
                class="required-asterisk ">*</small></label>
        <div class="col-md-8">
            <app-country-code-contact-input [countries]="countries" [contactData]="numberPickerInitial"
                            [name]="'mobile_number'" [isRequired]="true"
                            [errorMessageTitle]="'Mobile No.'" (phoneModelChange)="onNumberInput($event, 'mobile_number')" [defaultCountryCode]="selectedCountryCode">
            </app-country-code-contact-input>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">Emergency Contact Name<small class="required-asterisk"
                *ngIf="emergencyContactNameIsRequired">*</small></label>
        <div class="col-md-8">
            <input class="form-control input-md" #emergencyContact="ngModel" [required]="emergencyContactNameIsRequired"
                name="emergencyContact" type="text" placeholder="Emergency Contact name"
                [(ngModel)]="contact_detail.emergency_contact" ng-value="contact_detail.emergency_contact"
                (change)="onInputChanged($event)" (keypress)="blockSpecialChars($event)" pattern="^[^\s]+(\s.*)?$">
            <div class="alert alert-danger" [hidden]="(emergencyContact.valid)">Emergency Contact is required</div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-4 col-form-label form-control-label">Emergency Contact No.<small class="required-asterisk"
                *ngIf="emergencyContactNoIsRequired">*</small></label>
        <div class="col-md-8">
            <app-country-code-contact-input [countries]="countries" [contactData]="numberPickerInitial"
                            [name]="'emergency_contact_number'" [isRequired]="emergencyContactNoIsRequired"
                            [errorMessageTitle]="'Emergency Contact No.'" (phoneModelChange)="onNumberInput($event, 'emergency_contact_number')" [defaultCountryCode]="selectedCountryCode">
            </app-country-code-contact-input>
        </div>
    </div>

    <!-- <div class="form-group row">
        <label class="col-lg-3 col-form-label form-control-label"></label>
        <div class="col-lg-3 position-relative">
            <input type="reset" class="btn btn-secondary mr-1" value="Clear">
            <input type="submit" class="btn btn-primary" value="Save Changes" [disabled]="!addressForm.valid" (click)="save(addressForm)" />
            <block-loader [show]="(isProcessing)"></block-loader>
        </div>
    </div> -->
    <div class="d-flex my-1">
        <button class="btn btn-outline-brandeis-blue mr-2 w-50" (click)="goBack()"> Back </button>
        <button type="submit" class="btn btn-brandeis-blue ml-2 w-50"
            (click)="save(addressForm)" value="Save Changes" [disabled]="!addressForm.valid || disableNextBtn"> Next </button>
    </div>
    <block-loader [alwaysInCenter]="true" [show]="(isProcessing)" [showBlockBackdrop]="true"></block-loader>
</form>
<!-- Confirmation Modal on route change with save form data  -->
<generic-confirmation-modal #changeConfirmationModalRef (confirmEvent)="confirmationVal($event)"></generic-confirmation-modal>
