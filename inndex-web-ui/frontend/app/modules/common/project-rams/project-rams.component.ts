import {Component, OnInit, TemplateRef, ViewChild} from "@angular/core";
import { HttpParams } from '@angular/common/http';
import {ActivatedRoute, Router} from "@angular/router";
import {
    AuthService,
    ProjectService,
    User,
    UserService,
    CreateEmployer,
    ProjectRams,
    ToastService,
    Project,
    isInheritedProjectOfCompany,
    Common,
    ProjectRamsService,
    UACProjectDesignations,
    ResourceService,
    EmploymentDetail,
    HttpService,
    ProjectRamsConfModalType,
    RAMSActionButtons,
    ActionButtonVal, ToolboxTalksService,
} from "@app/core";
import {forkJoin} from "rxjs";
import { DomSanitizer } from "@angular/platform-browser";
import {NgbDateStruct, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import * as dayjs from 'dayjs';
import {AppConstant} from "@env/environment";
import { AssetsUrl } from "@app/shared/assets-urls/assets-urls";
import { SearchWithFiltersComponent } from "../search-with-filters/search-with-filters.component";
import { filterData } from "@app/core";
import { GenericConfirmationModalComponent, IModalComponent } from "@app/shared";
import {SLIDE_UP_DOWN_ON_SHOW_HIDE} from "@app/core/animations";

@Component({
    animations: [SLIDE_UP_DOWN_ON_SHOW_HIDE],
    templateUrl: './project-rams.component.html',
    styleUrls: ['./project-rams.component.scss'],
    styles: [`
        :host ::ng-deep .datatable-body-cell {
            overflow-x: visible !important;
        }

        .tableDownloadItem { left: -22px !important; }
    `]
})

export class ProjectRamsComponent implements OnInit {
    @ViewChild('confirmationModalReference') private confirmationModalReference: GenericConfirmationModalComponent;
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;

    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    employerId: number = 0;
    records: Array<ProjectRams> = [];
    selectedRams: Array<ProjectRams> = [];
    projectUsers: Array<any> = [];
    filteredUsers: Array<any> = [];
    authUser$: User;
    briefing_row: ProjectRams = new ProjectRams;
    briefings:  Array<any> = [];
    induction_briefings:  Array<any> = [];
    allowedMime : Array<any> = ['application/pdf', 'application/x-pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    rams_briefing_file = null;
    briefing_title: string = '';
    briefing_file_ref: any = null;
    reference_number: string = null;
    revision_number: string = null;
    briefingFile: any = {};
    img_link: string = `/images/project-placeholder.png`;

    processingRams = false;
    addingRamsInProgress = false;
    employer: CreateEmployer = {};
    processingPackagePlans: boolean = false;
    jobRoles: any = [];
    selectedRole: string = null;
    processingRamsDownload: boolean = false;
    is_inherited_project: boolean = false;
    loadingPackagePlans: boolean = false;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    ramsPhrase: string = ``;
    ramsSglrPhrase: string = ``;
    inductionSglrPhrase: string = 'induction';
    paginationData = new Common();
    page = this.paginationData.page;
    blockLoader : boolean = false;
    hasOnlySiteManagement: boolean = false;
    change_request: any = {
        row: null,
        note: null,
        code: null,
    };
    userEmployer: any = {};
    activeRams: Array<any> = [];
    tagged_owner: number;
    is_auto_approve: boolean = false;
    selectedOwner: number[]=[];
    // temp_records: Array<any> = [];
    ownerList: Array<any> = [];
    search:any = null;
    reject_reason: string = '';
    modalRef = null;
    titleEditDisable: boolean = true;
    companyEditDisable: boolean = true;
    briefingFiles: any = [{}];
    filePreviewUrl: any;
    isPdfMerged: boolean = false;
    allFilesUploadSize: object = {};
    allFilesUploadSizeLimit: number = 144 * 1024 * 1024; // ~151 MB
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    toolKey: string = 'rams';
    projectId: number;
    isProjectPortal: boolean = false;
    ramsAssessmentFormFieldsDefault: Array<any> = [];
    ramsAssessmentFormFields: Array<any> = [];
    rams_assessment_file: any;
    ramsActionType: string = '';
    ramsModalRef: any = null;
    activeNav: number = 1;
    employment_detail: EmploymentDetail = new EmploymentDetail();
    STATUS_CODES: any = {
        DECLINED: 0,
        PENDING: 1,
        APPROVED: 2,
        IN_REVIEW: 3
    };
    showPdfViewer: boolean = false;
    sorts: {
        dir:string, prop: string
    } = {
        dir:'desc', prop: 'id'
    };
    archivedRamsModelRef = null;

    STATUS_CODES_LIST: Array<any> = [
        {label:'Rejected', color: 'var(--danger)', badge_class: 'badge-danger', code: 0},
        {label:'Pending', color: '#807c7c', badge_class: 'badge-im-helmet', code: 1},
        {label:'Accepted', color: 'var(--success)', badge_class: 'badge-success', code: 2},
        {label:'In Review', color: '#807c7c', badge_class: 'badge-im-helmet', code: 3}
    ];

    STATUS_COLORS: any = {0 : "danger", 1 : "warning-yellow", 2 : "success", 4: "none text-black-50 disabled", 5: "none text-black-50 disabled"};
    selectedEmployer: string = null;
    availableEmployers: Array<any> = [];
    inviteModalRef = null;
    availableRecords: Array<any> = [];
    processingRamsInvites: boolean = false;
    inviteApiError = null;
    inviteApiSuccess = null;
    isMobileDevice: boolean = false;
    include_during_induction: boolean = false;
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    searchWithFilter:any={search:""};
    archivedRamsRecords: Array<any> = [];
    archivedRamsListModalRef: any = null;
    confirmationMsg: string = '';
    confirmationModalRef: any = null;
    filterData:filterData[] = this.renderFilterData();
    reloadFilterData:boolean = false;
    hasSelectedUsers:boolean = false;
    isFilteredUsersReset:boolean = false;
    actionButtonMetaData2 = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    addingNewRams: boolean = false;
    selectedRamsRevision: ProjectRams = new ProjectRams;
    ramsList: Array<any> = [];
    modalTitle: string = 'New RAMS';
    actionButtonMetaData = {
        actionList: [],
        class: 'material-symbols-outlined',
    };
    showModal: boolean = false;
    showReviewModal: boolean = false;
    selectedArchivedRamsOwner: number[] = [];
    searchArchived:string = null;
    archivedRamsFilter:filterData[] = this.renderArchivedRamsFilter();
    archivedRamsOwnerList: Array<any> = [];
    archivedPage = {...this.paginationData.page};
    showArchivedRamsTable: boolean = false;
    convert_doc_to_pdf: boolean = false;
    ramsRevisions: Array<ProjectRams> = [];
    ramsDownload?: {
        selected_revision: number;
        download_type: string;
    } = {
        selected_revision: null,
        download_type: null
    }
    isValidRevisionNumber: boolean = true;
    revisionType: number = 1;
    hasAddRevisionAbility: boolean = false;
    isInitRams = false;
    loadingInlineRams: boolean = false;

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private projectRamsService: ProjectRamsService,
        private toastService: ToastService,
        private toolboxTalksService: ToolboxTalksService,
        private authService: AuthService,
        private projectService: ProjectService,
        private modalService: NgbModal,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private domSanitizer: DomSanitizer,
        private resourceService: ResourceService,
        private httpService: HttpService,
    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project) {
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    AppConstant = AppConstant;
    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    ngOnInit() {
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
                this.getUserEmploymentDetail();
            }
        });

        if (!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
            this.img_link = this.employer.logo_file_url;
        } else {
            this.initializeTable();
        }
    }

    initiateDownload(resp) {
        this.blockLoader = true;
        this.from_date = resp.fromDate;
        this.to_date = resp.toDate;
        this.downloadRamsReport();
    }

    @ViewChild('searchFilters') searchFilters: SearchWithFiltersComponent;
    @ViewChild('archivedRamsFilters') archivedRamsFiltersRef: SearchWithFiltersComponent;
    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitRams) {
          this.isInitRams = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.initializeTable(isPageChange);
    }

    private initializeTable(isPageChange?: boolean) {
        if (isPageChange) {
          this.loadingInlineRams = true;
        } else {
          this.loadingPackagePlans = true;
        }
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('is_inherited_project', `${this.is_inherited_project}`)
            .set('sortKey', 'status')
            .set('sortKey', this.sorts.prop)
            .set('sortDir', this.sorts.dir)
            .set('search',`${this.search ? encodeURIComponent(this.search): ''}`);
            
        if(this.selectedOwner.length){
            params = params.append('tagged_owner', this.selectedOwner.join(','));

        }

        this.ramsPhrase = this.projectInfo ? this.projectInfo.custom_field.rams_phrase : '';
        this.ramsSglrPhrase = this.projectInfo ? this.projectInfo.custom_field.rams_phrase_singlr : '';
        this.inductionSglrPhrase = this.projectInfo ? this.projectInfo.custom_field.induction_phrase_singlr : '';
        this.actionButtonMetaData.actionList = [
            {
                code: RAMSActionButtons.INVITE_TO_RAMS,
                name: `Invite to Brief`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'send',
                enabled: (this.isProjectPortal || this.isCompanyRoute()),
            },
            {
                code: RAMSActionButtons.ARCHIVED_LIST,
                name: `Archived List`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'inventory_2',
                enabled: true,
            },
            {
                code: RAMSActionButtons.DOWNLOAD_REGISTER,
                name: `Download Register`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
        ];

        this.actionButtonMetaData2.actionList = [
            {
                code: RAMSActionButtons.NEW_RAMS,
                name: `New ${this.ramsPhrase}`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'add',
                enabled: (this.isProjectPortal || this.isCompanyRoute()),
            }
        ];

        this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.projectInfo.createdAt));
        this.hasOnlySiteManagement = (this.projectInfo._my_designations.length === 1 && this.projectInfo._my_designations.includes(UACProjectDesignations.DELIVERY_MANAGER));
        let extra = ['briefing-count', 'active-rams'];
        if(!this.page.pageNumber && !this.search && !this.selectedOwner.length){
            extra.push('tagged-owners');
        }
        params = params.append('extra', extra.join(','));
        this.toolboxTalksService.getProjectBriefingToolRecordsList(this.projectId, this.toolKey, params).subscribe((data: any) => {
            this.loadingPackagePlans = false;
            this.loadingInlineRams = false;
            if (data && data.tool_records) {
                this.records = data.tool_records;
                this.userEmployer = (data.userEmployer || {});
                this.addRevisionAbilityCheck();
                this.actionButtonMetaData2.actionList.push({
                    code: RAMSActionButtons.ADD_REVISION,
                    name: `New Revision`,
                    iconClass: this.actionButtonMetaData.class,
                    iconClassLabel: 'add',
                    enabled: this.hasAddRevisionAbility,
                });
                this.page.totalElements = data.totalCount;
                this.activeRams = data.active_rams;
                if(!this.reloadFilterData){
                    this.loadRamsAssessmentForm();
                    this.ownerList = data.taggedOwnersList;
                    this.archivedRamsOwnerList = data.taggedOwnersList;
                    this.filterData = this.renderFilterData()
                    this.reloadFilterData = true;
                }
            }else{
                const message = `Failed to fetch rams, projectId: ${this.projectId}.`;
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    addRevisionAbilityCheck(): void {
        if (this.page.pageNumber === 0) {
            this.hasAddRevisionAbility = ((this.isProjectPortal || this.isCompanyRoute()) && !!this.records.length);
        }
    }

    cancelModal(form) {
        this.selectedRamsRevision = new ProjectRams();
        this.addingNewRams = false;
        form.reset();
    }

    @ViewChild('addRamsRef')
    private addRamsRef: IModalComponent;
    showAddRamsPopup(addingNewRams, populate='') {
        this.isValidRevisionNumber = true;
        this.addingNewRams = addingNewRams;
        this.selectedRamsRevision = new ProjectRams();
        if (populate === 'ramsList') {
            this.blockLoader = true;
            this.projectRamsService.getProjectRamsList(this.projectId).subscribe((data: any) => {
                this.blockLoader = false;
                if (data && data.project_rams) {
                    this.ramsList = data.project_rams;
                } else {
                    const message = `Failed to fetch rams, projectId: ${this.projectId}.`;
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
            });
        }

        this.modalTitle = (this.addingNewRams === true) ? `New ${this.ramsPhrase}` : 'New Revision';
        this.briefing_title = '';
        this.reference_number = '';
        this.revision_number = '';
        this.briefing_file_ref = null;
        this.tagged_owner = (this.hasOnlySiteManagement && this.userEmployer?.id) ? this.userEmployer.id : null;
        this.is_auto_approve = false;
        this.include_during_induction = false;
        this.briefingFiles = [{}];
        this.filePreviewUrl = '';
        this.isPdfMerged = false;
        this.addRamsRef.open();
    }

    availableJobRoles() {
        return (this.projectUsers || []).reduce((acc, user) => {
          if (!acc.some((role) => role.name === user.job_role)) {
            acc.push({ name: user.job_role });
          }
          return acc;
        }, []);
    }

    getAvailableEmployers() {
        let result = this.projectUsers.map(u => {
            return u.employer;
        });
        this.availableEmployers = [...new Set(result)];
    }

    checkHasSelectedUsers(noOfSelectedUsers: number) {
        this.hasSelectedUsers = (noOfSelectedUsers > 0) ? true : false;
    }

    @ViewChild('ramsDetailsHtml')
    private ramsDetailsHtmlRef: TemplateRef<any>;
    ramsDetailModal(row, modelAlreadyOpen=false) {
        if (!modelAlreadyOpen) {
            this.modalRef = null;
        }
        let params = new HttpParams()
            .set('briefing_count', 'false');
        let apiCalls = [this.toolboxTalksService.getProjectBriefingToolRecord(this.projectId, this.toolKey, row.id, {
                allBriefings: true,
                expand_attendees: true
            }), this.projectRamsService.ramsRevisionList(this.projectId, row.id, params)];

        this.blockLoader = true;
        forkJoin(apiCalls).subscribe((responseList:any) => {
            this.blockLoader = false;
            let errorKey = Object.keys(responseList).find(key => !responseList[key].success);
            if (responseList[errorKey]) {
                const message = responseList[errorKey].message || 'Failed to get data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: responseList[errorKey] });
                return;
            }

            this.briefing_row = responseList[0].briefing;
            this.titleEditDisable = true;
            this.companyEditDisable = true;
            this.tagged_owner = (this.briefing_row.tagged_owner && this.briefing_row.tagged_owner.id) ? this.briefing_row.tagged_owner.id : this.briefing_row.tagged_owner;
            this.briefing_title = this.briefing_row.briefing_title;
            this.reference_number = this.briefing_row.reference_number;
            this.revision_number = this.briefing_row.revision_number;
            this.briefings = (this.briefing_row.register || []).filter(r => !r.is_induction_briefing);
            this.induction_briefings = (this.briefing_row.register || []).filter(r => r.is_induction_briefing);
            this.include_during_induction = this.briefing_row.include_during_induction;
            this.ramsRevisions = (responseList[1] && responseList[1].ramsRecords) ? responseList[1].ramsRecords : this.ramsRevisions;

            if (!this.modalRef) {
                this.modalRef = this.openModal(this.ramsDetailsHtmlRef, 'lg', 'l-modal modalHeightAuto modal_v2', );
            }
        });
    }

    openModal(content, size, windowClass='') {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            windowClass
        });
    }

    addRamsRequest(form, event) {
        if (!form.valid) {
            console.log('form is not valid, please fill all required fields.');
            return;
        }
        let addBriefingReq = form.value;
        //console.log("addBriefingReq: ", addBriefingReq);
        addBriefingReq.project_ref = this.projectId;
        addBriefingReq.user_ref = this.authUser$.id;
        addBriefingReq.tagged_owner = this.tagged_owner;
        addBriefingReq.is_available = true;
        addBriefingReq.is_auto_approve = this.is_auto_approve;
        addBriefingReq.include_during_induction = this.include_during_induction;
        addBriefingReq.is_rams_revision_request = !this.addingNewRams;
        addBriefingReq.reference_number = this.reference_number;
        if (!this.addingNewRams && this.selectedRamsRevision.id) {
            addBriefingReq.ref_id = this.selectedRamsRevision.id;
        }

        if (this.briefingFiles.length > 2 && !this.isPdfMerged) {
            addBriefingReq.pdf_files = (this.briefingFiles || []).reduce((arr, item) => {
                if (item && item.file_url) {
                    arr.push(item.file_url);
                }
                return arr;
            }, []);
            addBriefingReq.merged_file_name = this.replaceAll((this.briefing_title).toLowerCase() + '-' + dayjs().valueOf() + '.pdf', ' ', '-');
            addBriefingReq.file_category = 'rams-upload';

            /*addBriefingReq.pdf_files = [
                'https://inductme-uploads.s3.eu-west-2.amazonaws.com/staging/4f6a27ad-2893-4e81-8497-c83596b19c9a-1668669247281.pdf',
                'https://inductme-uploads.s3.eu-west-2.amazonaws.com/staging/21f33aa5-53c6-47a4-97d8-033c2688815a-1668669302982.pdf'
            ];*/
        }
        this.addingRamsInProgress = true;
        this.projectRamsService.createRams(addBriefingReq, this.projectId).subscribe(out => {
            this.addingRamsInProgress = false;
            if(!out.success){
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
                return;
            }
            form.reset();
            event.closeFn();
            this.showModal = false;
            this.initializeTable(true);
        });
    }

    uploadDone($event) {
        if($event && $event.uploaded_items && $event.userFile) {
            let currentFilesSize = ($event.uploaded_items) ? ($event.uploaded_items || []).reduce((sum, item) => {
                sum += item.file.size;
                return sum;
            }, 0) : 0;

            let previousFilesSize = 0;
            if (Object.keys(this.allFilesUploadSize).length) {
                for (let index in this.allFilesUploadSize) {
                    previousFilesSize += this.allFilesUploadSize[index];
                }
            }

            if ((previousFilesSize + currentFilesSize) > this.allFilesUploadSizeLimit) {
                const message = 'Size of total uploads should be less than 150Mb.';
                this.toastService.show(this.toastService.types.INFO, message);
                return;
            }
        }

        if($event && $event.userFile) {
            for (let index in $event.uploaded_items) {
                let item = $event.uploaded_items[index];
                this.allFilesUploadSize[$event.userFile[index].id] = item.file.size;
            }

            //this.briefingFiles.splice(1, 0, ...$event.userFile);
            this.briefingFiles.push(...$event.userFile);
            this.briefingFiles[0] = {};
            this.onPDFPositionChanges(this.briefingFiles);
        }
    }

    onPDFPositionChanges(pdfArr) {
        let topMostFile = (pdfArr || []).reduce((obj, pdf) => {
            if (pdf.id && !obj.id) {
                obj = pdf
            }
            return obj;
        }, {});
        this.resetFileVars(topMostFile);
    }

    deleteFile(file) {
        if(file.id){
            this.confirmationModalReference.openConfirmationPopup({
                headerTitle: 'Delete',
                title: `Are you sure you want to delete this?`,
                confirmLabel: 'Delete',
                onConfirm: () => {
                    this.userService.deleteUserFile(file.id).subscribe(data => {
                        if (data.success) {
                            delete this.allFilesUploadSize[file.id];
                            this.briefingFiles = (this.briefingFiles || []).filter(pdfFile => pdfFile.id != file.id);
                            this.onPDFPositionChanges(this.briefingFiles);
                        } else {
                            const message = data.message || 'Failed to delete the file.';
                            this.toastService.show(this.toastService.types.ERROR, message);
                        }
                    });
                }
            });
        }
    }

    @ViewChild('viewFileRef')
    private viewFileRef: IModalComponent;
    mergeUploadedPDFs() {
        if (this.briefingFiles.length < 3) {
            return false;
        }
        this.blockLoader = true;
        let request = {
            pdf_files: (this.briefingFiles || []).reduce((arr, item) => {
                if (item && item.file_url) {
                    arr.push(item.file_url);
                }
                return arr;
            }, []),
            merged_file_name: this.replaceAll((this.briefing_title)+'-'+dayjs().valueOf()+'.pdf', ' ', '-'),
            file_category: 'rams-upload'
        };

        /*request.pdf_files = [
            'https://inductme-uploads.s3.eu-west-2.amazonaws.com/staging/4f6a27ad-2893-4e81-8497-c83596b19c9a-1668669247281.pdf',
            'https://inductme-uploads.s3.eu-west-2.amazonaws.com/staging/21f33aa5-53c6-47a4-97d8-033c2688815a-1668669302982.pdf'
        ];*/
        this.userService.mergePDFs(request).subscribe((data:any) => {
            this.blockLoader = false;
            if (data.success) {
                this.showModal = true;
                this.resetFileVars(data.user_file, true);
                this.filePreviewUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(data.user_file.file_url + '?embed=frame');
                this.viewFileRef.open();
            } else {
                const message = data.message || 'Failed to merge the files.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    resetFileVars(user_file, isMerged=false) {
        this.isPdfMerged = isMerged;
        this.rams_briefing_file = user_file.file_url || null;
        this.briefing_file_ref = user_file.id || null;
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.resetFileVars({});
        }
    }

    makeBriefingAvailable($event, row) {
        let makeAvailable = $event.target.checked;
        let req = {
            is_available: makeAvailable,
            user_ref: row.user_ref.id
        };

        this.processingRams = true;
        this.projectRamsService.updateRams(req, this.projectId, row.id).subscribe(out => {
            this.processingRams = false;
        });
    }

    checkAvailability(record) {
        return (record.is_available && record.status === 2);
    }

    isCompanyRoute() {
        let routeName = this.activatedRoute.snapshot.url[0].path;
        return routeName === 'company-admin';
    }

    downloadRamsReport() {
        let fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date);
        let toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date);
        if(!fromDate || !toDate) {
            const message = 'Please enter from date and to date.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }

        if(toDate.isBefore(fromDate)) {
            const message = 'Incorrect {from,to} date.';
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }
        let formattedFromDate = fromDate.format(AppConstant.apiRequestDateFormat);
        let formattedToDate = toDate.format(AppConstant.apiRequestDateFormat);
        let companyId = (this.employerId ? this.employerId : null);
        let request = {
            projectId: this.projectId,
            fromDate: fromDate.startOf('day').valueOf(),
            toDate: toDate.endOf('day').valueOf(),
            companyId: companyId
        };
        this.addingRamsInProgress = true;
        this.projectRamsService.downloadRamsReport(request, this.projectId, `${this.ramsPhrase} Report-${this.projectId}-[${fromDate.format(AppConstant.defaultDateFormat)}-${toDate.format(AppConstant.defaultDateFormat)}].xlsx`, () => {
            this.addingRamsInProgress = false;
            this.blockLoader = false;
        });
    }

    openPopup(content, row, statusCode) {
        //reset modal
        this.change_request.note = null;

        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
        });
        this.change_request.row = row;
        this.change_request.note = null;
        this.change_request.code = statusCode;
    }

    @ViewChild('ramsActionRef')
    private ramsActionRef: IModalComponent;
    async ramsAction(actionType, item) {
        this.showReviewModal = false;
        this.ramsActionType = actionType;
        this.briefing_row = item;
        if (this.rams_assessment_file && this.rams_assessment_file.id) {
            this.showPdfViewer = true;
            this.reject_reason = null;
            if (
                actionType == 'review_again' &&
                this.briefing_row.custom_field.assessment_form_fields &&
                this.briefing_row.custom_field.assessment_form_fields.length
            ) {
                this.blockLoader = true;
                this.projectRamsService.getProjectRamsById(this.projectId, this.briefing_row.id).subscribe((data:any) => {
                    this.blockLoader = false;
                    if (data && data.rams) {
                        if (this.briefing_row.status !== data.rams.status) {
                            this.toastService.show(this.toastService.types.ERROR, 'This record’s status was updated by someone else. Please refresh the page to view the latest update.');
                            return;
                        }
                        this.selectedRamsRevision = data.rams;
                        this.ramsAssessmentFormFields = data.rams.custom_field.assessment_form_fields;
                        this.ramsActionRef.open();
                        return;
                    }
                    const message = `Failed to fetch selected ${this.ramsPhrase} detail.`;
                    this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                });
            } else {
                this.blockLoader = true;
                this.projectRamsService.ramsRecentRevision(this.projectId, this.briefing_row.id).subscribe((data:any) => {
                    this.blockLoader = false;
                    this.ramsActionRef.open();
                    if (data.success && data.rams_recent_revision && data.rams_recent_revision.id) {
                        this.confirmationModalReference.openConfirmationPopup({
                            headerTitle: 'Review',
                            title: `This review is for a new ${this.ramsSglrPhrase} revision; would you like to pre-fill the assessment details from the previous revision assessment or begin with a blank assessment form?`,
                            confirmLabel: 'Blank',
                            cancelLabel: 'Pre-fill',
                            onClose: () => {
                                let prefillFields = (data.rams_recent_revision.custom_field.assessment_form_fields || []).map(field => {
                                    if (['signature', 'esignature'].includes(field.field_type)) {
                                        field.answer = '';
                                    }
                                   return field;
                                });
                                this.ramsAssessmentFormFields = prefillFields;
                            },
                            onConfirm: () => {
                            }
                        });
                    }
                    this.ramsAssessmentFormFields = JSON.parse(JSON.stringify(this.ramsAssessmentFormFieldsDefault));
                });
            }
        } else {
            if (actionType == 'approve') {
                this.approveRamsConfirmation();
            } else if (actionType == 'decline') {
                this.declineRamsConfirmation();
            }
        }
    }

    @ViewChild('declineFormHtml')
    private declineFormHtmlRef: TemplateRef<any>;
    declineRamsConfirmation() {
        this.reject_reason = '';
        this.confirmationModalReference.openConfirmationPopup({
            headerTitle: 'Reject',
            title: `Are you sure you want to reject this submission?`,
            confirmLabel: 'Reject',
            onConfirm: () => {
                this.openModal(this.declineFormHtmlRef, 'md');
            }
        });
    }

    declineRams(cb) {
        this.blockLoader = true;
        let request = {
            "reject_reason": (this.reject_reason || '').replace(/\r?\n/g,'<br>'),
            "is_declining": true
        };
        cb();
        this.projectRamsService.updateRams(request, this.projectId, this.briefing_row.id).subscribe(this.responseHandler.bind(this, true));
    }

     approveRamsConfirmation() {
        this.reject_reason = '';
        this.confirmationModalReference.openConfirmationPopup({
            headerTitle: 'Accept',
            title: `Are you sure you want to accept this submission?`,
            confirmLabel: 'Accept',
            onConfirm: () => {
                this.blockLoader = true;
                let request = {
                    "is_approving": true
                };
                this.projectRamsService.updateRams(request, this.projectId, this.briefing_row.id).subscribe(this.responseHandler.bind(this, true));
            }
        });
    }

    responseHandler(forceClose, out: any) {
        if (this.modalRef && forceClose) {
            this.modalRef.close();
        }

        if (this.confirmationModalRef) {
            this.confirmationModalRef.close();
        }

        if (this.archivedRamsListModalRef) {
            this.archivedRamsListModalRef.close();
        }

        if(out.success) {
            this.initializeTable(true);
        } else {
            const message = out.message || 'Failed to store data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        }
        this.blockLoader = false;
        this.ramsRevisions = [];
    }

    replaceAll(str='', find, replace) {
        const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
        return str.replace(new RegExp(escapedFind, 'g'), replace);
    }

    updateRAMS() {
        if (!this.briefing_title && !this.tagged_owner) return false;
        this.blockLoader = true;
        let request: any = {}
        if (this.briefing_title) {
            request.briefing_title = this.briefing_title;
        }

        if (this.tagged_owner) {
            request.tagged_owner = this.tagged_owner;
        }

        this.projectRamsService.updateRams(request, this.projectId, this.briefing_row.id).subscribe(this.responseHandler.bind(this, false));
    }

    downloadRegister() {
        this.blockLoader = true;
        this.projectRamsService.downloadRamsRegister(this.projectId, () => {
            this.blockLoader = false;
        });
    }

    enableEdit(type) {
        if (type == 'title') {
            this.titleEditDisable = !(this.titleEditDisable);
        } else if(type == 'company') {
            this.companyEditDisable = !(this.companyEditDisable);
        }
    }

    hasFullAccessOrRestrictedOf() {
        return (this.projectInfo._my_designations
            && (this.projectInfo._my_designations.includes(UACProjectDesignations.RESTRICTED)
                || this.projectInfo._my_designations.includes(UACProjectDesignations.FULL_ACCESS
                )
            ));
    }

    loadRamsAssessmentForm() {
        if(this.projectId) {
            this.resourceService.getProjectSettingsByName(['rams_assessment_form'], this.projectId).subscribe((data: any) => {
                if (data.success && data.project_settings && data.project_settings.rams_assessment_form) {
                    this.ramsAssessmentFormFieldsDefault = [...(data.project_settings.rams_assessment_form.assessment_form_fields || [])];
                    this.rams_assessment_file = data.project_settings.rams_assessment_form.rams_assessment_ref;
                }
            });
        }
    }

    tabChange($event) {
    }

    getUserEmploymentDetail() {
        this.userService.getMyEmploymentDetail().subscribe(data => {
            if (data && data.employment_detail) {
                this.employment_detail = data.employment_detail;
            }
        });
    }

    @ViewChild('ramsInviteModalRef')
    private ramsInviteModalRef: IModalComponent;
    inviteToRamsPopup() {
        this.processingRamsDownload = true;
        this.getAvailableRamsRecords();
        if(this.isProjectPortal) {
            this.userService.getProjectAdmins(this.projectId).subscribe((data:any) =>{
                if(data && data.admins) {
                    this.setDataInviteToRamsPopup(data.admins);
                }
            });
        }
        if(this.isCompanyRoute()) {
            let companyAdminsRequest = this.userService.getCompanyAdmins(this.employerId);
            let companyProjectAdminsRequest = this.userService.getCompanyProjectAdmins(this.employerId, this.projectId);
            forkJoin([
                companyProjectAdminsRequest,
                companyAdminsRequest
            ]).subscribe(responseList => {
                let res: any = responseList;
                let error = res.filter(r => !r.success);
                if(error.length == 0) {
                    this.setDataInviteToRamsPopup([...res[0].admins, ...res[1].admins]);
                }
            });
        }
    }

    setDataInviteToRamsPopup(projectUsers) {
        this.projectUsers = (projectUsers || []).reduce((acc, item) => {
          if (item.inductionStatus !== null) {
            acc.push({ ...item, _isSelected: false });
          }
          return acc;
        }, []);
        this.jobRoles = this.availableJobRoles();
        this.filteredUsers = [...this.projectUsers];
        this.processingRamsDownload = false;
        this.getAvailableEmployers();
        this.ramsInviteModalRef.open();
    }

    getAvailableRamsRecords() {
        this.toolboxTalksService.getBriefingToolRecordForInvite(this.toolKey, this.projectId).subscribe((data:any) => {
            if (data.success && data.availableBriefingTools) {
                this.availableRecords = data.availableBriefingTools;
                return;
            }
            const message = `Failed to fetch ${this.toolKey}, projectId: ${this.projectId}.`;
            this.toastService.show(this.toastService.types.ERROR, message);
        });
    }

    inviteUsers(form) {
        if (!form.valid) {
            return;
        }
        this.processingRamsInvites = true;
        let projectRams = form.value.selectedTools.map(r=> r.id);
        let users = form.value.selectedUsers.map(({ _isSelected, ...rest }) => rest);
        let req = {
            project_rams: projectRams,
            usersToInvite: users
        }
        this.projectRamsService.inviteToRams(req, this.projectId).subscribe(res => {
            this.processingRamsInvites = false;
            if(res.success) {
                this.inviteApiSuccess = res.status;
                this.filteredUsers = [ ...this.projectUsers ];
                this.isFilteredUsersReset = true;
                form.reset();
            }
            else {
                this.inviteApiError = "Failed to send Invites";
            }
        })

    }

    closeInviteModal(form) {
        form.reset();
        this.inviteApiError = null;
        this.inviteApiSuccess = null;
        this.selectedRole = null;
        this.filteredUsers = [];
        this.selectedRams = null;
        this.selectedEmployer = null;
        this.ramsInviteModalRef.close();
    }

    includeDuringInduction($event, action='') {
        if ($event.target.checked == false) {
            this.include_during_induction = false;
            $event.target.checked = false;
            this.includeDuringInductionConfirm(action);
            return;
        }
        let records = this.activeRams.filter(rams => rams.tagged_owner == this.tagged_owner);
        if(records.length) {
            this.confirmationModalReference.openConfirmationPopup({
                headerTitle: 'Confirm',
                title: `${this.ramsSglrPhrase} has already been added and included in the ${this.inductionSglrPhrase} process for this company. Would you like to continue and add multiple ${this.ramsSglrPhrase} for the same company?`,
                confirmLabel: 'Yes',
                onConfirm: () => {
                    this.include_during_induction = true;
                    this.includeDuringInductionConfirm(action);
                },
                onClose: () => {
                    this.include_during_induction = false;
                    $event.target.checked = false;
                }
            });
        } else {
            this.include_during_induction = true;
            this.includeDuringInductionConfirm(action);
        }
    }

    includeDuringInductionConfirm(action) {
        if (action === 'save') {
            this.blockLoader = true;
            let request: any = {
                include_during_induction: this.include_during_induction
            }
            this.projectRamsService.updateRams(request, this.projectId, this.briefing_row.id).subscribe(this.responseHandler.bind(this, false));
        }
        return;
    }

    onFilterSelection(data){
        this.selectedOwner = data.owner.map(a=>a.id);
        this.pageCallback({offset: 0 }, true);
    }
    trackByRowIndex(index: number, obj: any) {
        return index;
    }

    onSort($event){
        let {sorts} = $event;
        let{ dir, prop } = sorts[0];
        this.sorts = { dir, prop };
        this.pageCallback({ offset: 0 }, true);
    }

    closeModal(event) {
        this.showArchivedRamsTable = false;
        this.archivedRamsModelRef = null;
        this.searchArchived = '';
        this.selectedArchivedRamsOwner = [];
        this.archivedPage.pageNumber = 0;
        this.archivedRamsFiltersRef.clearFilters();
        if (event.closeFn) {
            event.closeFn();
        }
    }

    searchFunction(data){
        this.search = data.search;
        let stopReload = data?.stopReload;
        if(!stopReload){
            this.initializeTable(true);
        }
        this.page.pageNumber = 0;
    }
    renderFilterData(){
        return [
            {
                name:'owner',
                list:this.ownerList,
                enabled:!this.hasOnlySiteManagement,
                state:false,
            }
        ];
    }

    reviewRamsClose() {
        this.showPdfViewer = false;
        let filledField = (this.ramsAssessmentFormFields).find(field => field.answer);
        if(filledField) {
            this.ramsActionRef.close();
        }
    }

    public onActionSelection(actionVal: ActionButtonVal) {
        const code = actionVal.code;
        if(code === RAMSActionButtons.INVITE_TO_RAMS) {
            this.inviteToRamsPopup();
        } else if(code === RAMSActionButtons.ARCHIVED_LIST) {
            this.initializeArchivedRamsTable(true);
        } else if(code === RAMSActionButtons.DOWNLOAD_REGISTER) {
            this.downloadRegister();
        } else if(code === RAMSActionButtons.NEW_RAMS) {
            this.showAddRamsPopup(true);
        } else if(code === RAMSActionButtons.ADD_REVISION) {
            this.showAddRamsPopup(false, 'ramsList');
        }
    }

    archiveRams(id) {
        this.blockLoader = true;
        let request = {
            is_archived: true
        };
        this.projectRamsService.archiveUnarchiveRams(request, this.projectId, id).subscribe(this.responseHandler.bind(this, true));
        this.archivedRamsModelRef = null;
    }

    unArchiveRams(id) {
        this.blockLoader = true;
        let request = {
            is_archived: false
        };
        this.projectRamsService.archiveUnarchiveRams(request, this.projectId, id).subscribe(this.responseHandler.bind(this, true));
        this.archivedRamsListRef.close();
        this.archivedRamsModelRef = null;
    }

    openConfirmationModal(briefing_row) {
        let actionString = (briefing_row.is_archived) ? 'Unarchive' : 'Archive';
        this.confirmationModalReference.openConfirmationPopup({
            headerTitle: actionString,
            title: `Are you sure you wish to ${(briefing_row.is_archived) ? 'unarchive' : 'archive' } the following <strong>${this.ramsSglrPhrase}: ${briefing_row.briefing_title}</strong> ?`,
            confirmLabel: actionString,
            onConfirm: () => {
                if (!briefing_row.is_archived) {
                  this.archiveRams(briefing_row.id);
                } else {
                  this.unArchiveRams(briefing_row.id);
                }
            }
        });
    }

    @ViewChild('archivedRamsListRef')
    private archivedRamsListRef : IModalComponent;
    initializeArchivedRamsTable(initModal: boolean = false) {
        this.blockLoader = true;
        let params = new HttpParams()
            .set('pageNumber', `${this.archivedPage.pageNumber}`)
            .set('pageSize', `${this.archivedPage.size}`);

        if(this.searchArchived){
            params = params.append('search',`${encodeURIComponent(this.searchArchived)}`);
            this.archivedPage.pageNumber = 0;
        }
        if(this.selectedArchivedRamsOwner.length){
            params = params.append('tagged_owner', this.selectedArchivedRamsOwner.join(','));
            this.archivedPage.pageNumber = 0;
        }

        this.projectRamsService.getArchivedRams(this.projectId, this.employerId, params).subscribe((data:any) => {
            this.blockLoader = false;
            if (data && data.archived_rams) {
                this.archivedRamsRecords = data.archived_rams;
                this.archivedPage.totalElements = data.total_record_count;

                if(!this.archivedRamsModelRef && initModal) {
                    this.showArchivedRamsTable = true;
                    this.archivedRamsModelRef = this.archivedRamsListRef.open();
                    this.archivedRamsFilter = this.renderArchivedRamsFilter();
                }
                return;
            }
            const message = 'Failed to fetch archived RAMS.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
        });
    }

    renderArchivedRamsFilter(){
        return [
            {
                name:'owner',
                list:this.ownerList,
                enabled:!this.hasOnlySiteManagement,
                state:false,
            }
        ];
    }

    searchArchivedRams(data){
        this.searchArchived = data.search;
        let stopReload = data?.stopReload;
        if(!stopReload){
            this.archivedPage.pageNumber = 0;
            this.initializeArchivedRamsTable();
        }
    }

    onFilterArchivedRams(data){
        this.selectedArchivedRamsOwner = data.owner.map(a=>a.id);
        this.archivedRamsPageCallback({offset: 0 });
    }

    archivedRamsPageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }) {
        this.archivedPage.pageNumber = pageInfo.offset;
        this.initializeArchivedRamsTable();
    }

    showRamsDetail($event, revisionType=1) {
        this.isValidRevisionNumber = true;
        this.revisionType = revisionType;
        if (this.revisionType === 2) {
            this.addingNewRams = false;
        }
        if (!$event || !$event.id) {
            this.selectedRamsRevision = new ProjectRams();
            this.tagged_owner = null;
            this.briefing_title = null;
            this.reference_number = null;
            this.revision_number = null;
            this.briefing_file_ref = null;
            this.briefingFiles = [{}];
            this.include_during_induction = false;
            return;
        }

        this.projectRamsService.getProjectRamsById(this.projectId, $event.id).subscribe((data:any) => {
            this.blockLoader = false;
            if (data && data.rams) {
                this.selectedRamsRevision = data.rams;
                this.tagged_owner = (data.rams.tagged_owner && data.rams.tagged_owner.id) ? data.rams.tagged_owner.id : data.rams.tagged_owner;
                this.briefing_title = data.rams.briefing_title;
                this.reference_number = data.rams.reference_number;
                this.revision_number = null;
                this.briefing_file_ref = null;
                this.briefingFiles = [{}];
                this.include_during_induction = data.rams.include_during_induction;
                if (this.modalRef) {
                    this.modalRef.close();
                }
                if (this.revisionType === 2) {
                    this.addRamsRef.open();
                }
                return;
            }
            const message = `Failed to fetch selected ${this.ramsPhrase} detail.`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
        });
    }

    @ViewChild('ramsDownloadModel')
    private ramsDownloadModel: IModalComponent;
    proceedToDownloadReport(id) {
        this.blockLoader = true;
        this.ramsDownload = {
            selected_revision: null,
            download_type: null
        };
        this.projectRamsService.ramsRevisionList(this.projectId, id).subscribe((data:any) => {
            this.blockLoader = false;
            if (data && data.ramsRecords) {
                this.ramsRevisions = (data.ramsRecords.length) ? data.ramsRecords : [];
                this.ramsDownload.selected_revision = data.ramsRecords[0].id;
                this.briefing_title = data.rams_title;
                this.ramsDownloadModel.open();
                return;
            }
            const message = 'Something went while downloading the record, please try again later.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
        });
    }

    closeDownloadRamsModal() {
        this.ramsDownload = {
            selected_revision: null,
            download_type: null
        };
        this.briefing_title = null;
        this.ramsRevisions = [];
    }

    downloadRams(event) {
        let request = {
            projectId: this.projectId,
            timezone: this.authUser$.timezone
        }

        this.processingRamsDownload = true;
        if (this.ramsDownload.download_type === 'xlsx') {
            this.projectRamsService.downloadRamsXLSX(request, this.ramsDownload.selected_revision, this.projectId, `${this.ramsPhrase} Report.xlsx`, () => {
                this.processingRamsDownload = false;
                event.closeFn();
                this.closeDownloadRamsModal();
            });
        } else {
            let queryParams = (this.employerId ? '?companyId=' + this.employerId : '');
            this.projectRamsService.downloadRams(this.projectId, this.ramsDownload.selected_revision, 'pdf', queryParams, (data) => {
                this.processingRamsDownload = false;
                event.closeFn();
                this.closeDownloadRamsModal();
            });
        }
    }

    searchRamsByRevisionNumber(e, control) {
        if (this.selectedRamsRevision && this.selectedRamsRevision.group_id) {
            this.blockLoader = true;
            this.projectRamsService.searchRamsByRevision(this.projectId, this.selectedRamsRevision.group_id, encodeURIComponent(e.target.value))
                .subscribe((data:any) => {
                    if (data.success) {
                        this.isValidRevisionNumber = !(data.rams_count);
                        if (!this.isValidRevisionNumber) {
                            control.control.setErrors({ forcedError: true }); // Set custom error
                            control.control.markAsTouched(); // Show validation messages
                        }
                    }
                    this.blockLoader = false;
                });
        }
    }
}
