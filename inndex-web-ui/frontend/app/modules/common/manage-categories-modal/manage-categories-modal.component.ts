import {Component, OnInit, TemplateRef, Input, ViewChild, Output, EventEmitter} from '@angular/core';
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {Project} from "@app/core";
import { GenericConfirmationModalComponent, IModalComponent } from '@app/shared';

@Component({
    selector: 'manage-categories-modal',
    templateUrl: './manage-categories-modal.component.html'
})
export class ManageCategoriesModalComponent implements OnInit {

    @Input()
    inputRecords: Array<{
        "name": string,
        "is_active": boolean
    }> = [];

    records: Array<{
        "name": string,
        "is_active": boolean
    }> = [];

    @Input()
    feature_name: string = '';

    @Output()
    updatedRecords: any = new EventEmitter<{
        records: Array<{
            "name": string,
            "is_active": boolean
        }>
    }>();

    changeDetected: boolean = false;

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;

    constructor(
        private modalService: NgbModal
    ) {

    }

    ngOnInit() {
    }

    ngOnChanges() {
        this.initialComponent();
    }

    ngDoCheck() {
        if (this.changeDetected) {
            this.initialComponent();
            this.changeDetected = false;
        }
    }

    initialComponent() {
        if (this.inputRecords.length) {
            this.records = [
                {
                    "name": '',
                    "is_active": true,
                },
                ...(this.inputRecords)
            ];
        } else {
            this.records = [{
                    "name": '',
                    "is_active": true
            }];
        }
    }

    openModal(content, size='') {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size
        });
    }

    @ViewChild('categoryModal')
    private categoryModalRef: IModalComponent;
    openCategoryModal() {
        this.changeDetected = true;
        this.categoryModalRef.open();
    }

    saveRecords(event) {
        this.updatedRecords.emit({
            records: this.records
        });
        event.closeFn();
    }

    trackByRowIndex(index: number, obj: any) {
        return index;
    }

    getCategoryName(index: number) {
        return this.records[index].name ?? '';
    }

    removeCategoryRow(i: number):void {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Remove Category',
            title: `Are you sure you want to remove ${(this.getCategoryName(i)) ? '<span class="fw-500">'+this.getCategoryName(i)+'</span>' : 'this'}?`,
            confirmLabel: 'Remove',
            onConfirm: () => {
                this.records.splice(i, 1);
            }
        });
    }

    addCategory(i) {
        if(this.records[i] && this.records[i].name) {
            this.records = [
                {
                    "name": '',
                    "is_active": true
                },
                ...(this.records)
            ];
        }
    }
}
