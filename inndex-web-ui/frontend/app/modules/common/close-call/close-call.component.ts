import { ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from "@angular/router";
import { HttpParams } from '@angular/common/http';
import { Observable } from "rxjs";
import * as dayjs from 'dayjs';
let advancedFormat = require('dayjs/plugin/advancedFormat');
dayjs.extend(advancedFormat);
import { NgbModalConfig, NgbModal, NgbDateStruct, NgbDate } from '@ng-bootstrap/ng-bootstrap';
import { CloseCallService, AuthService, UserService, User, ExcelUtility, Project, isInheritedProjectOfCompany, Common, ResourceService, CloseCall, HttpService, CloseCallActionButtons, ToastService } from "@app/core";

import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import { AppConstant } from "@env/environment";
import { AssetsUrl } from "@app/shared/assets-urls/assets-urls";
import { GenericModalComponent } from '../generic-modal/generic-modal.component';
import { GenericModalConfig } from '../generic-modal/generic-modal.config';
import { filterData } from '@app/core';
import { GenericConfirmationModalComponent, IModalComponent } from '@app/shared';
import { ReportDownloaderComponent } from '../report-downloader/report-downloader.component';

@Component({
    templateUrl: './close-call.component.html',
    // add NgbModalConfig and NgbModal to the component providers
    providers: [NgbModalConfig, NgbModal]
})
export class CloseCallComponent implements OnInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('reportDownloader') private reportDownloader: ReportDownloaderComponent;
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    project_close_calls: any;
    closeCallObj: CloseCall;
    newPhotos: Array<any> = [];
    regUserCompanyId: number;
    showMapWithMultiPin = false;
    employerId: number = 0;
    project: Observable<{}>;
    records: Array<any> = [];
    temp_records: Array<any> = [];
    exportInProgress: boolean = false;
    isAssignToMandatory: boolean = false;
    hazardousCategoryList: Array<any> = [];
    lightingConditionList: Array<any> = [];
    closeCallDefaultFields: Array<any> = [];
    closeCallCustomFields: Array<any> = [];
    initialCoseCallCustomFields: Array<any> = [];
    close_call_row: Array<any> = [];
    close_out_images: Array<any> = [{
        identifier: (new Date()).getTime()
    }];
    allowedMime: Array<any> = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    img_link: string = `/images/project-placeholder.png`;
    userFile: any;
    corrective_images: Array<any> = [];
    corrective_images_file: Array<any> = [];
    authUser$: User;
    openRecords: Array<any> = [];
    totalCloseCalls: Array<any> = [];
    project_category: string = `default`;
    ccPhrase: string = `Close Calls`;
    ccSglrPhrase: string = `Close Call`;
    dateFormat: string = AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS;
    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    employer: any = {};
    is_inherited_project: boolean = false;
    selectedHazardType: any[] = [];
    selectedAssignTo: any[] = [];
    selectedRaisedBy: any[] = [];
    ownerFilter: number[] = []; 
    closeCallsDwStatus: any = { open: true, closed: true };
    employersList = [];
    downloadHazardType: string = null;
    filterEmployer: string = null;
    tagged_owners: any = [];
    assignedToUsers: any = [];
    raisedByUsers: any = [];
    filterByOwner: number;
    inductedUsersEmployer: any = [];
    updateRequestLoader: boolean = false;
    tagOwner: number;
    tagUser: number;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    loadingCloseCalls: boolean = false;
    tableOffset: number = 0;
    dashboardStartDate: NgbDate;
    dashboardLoader: boolean = false;
    dashboardHtmlContent: any;
    dashboardReportFrom: any;
    dashboardReportTo: any;
    paginationData = new Common();
    page = this.paginationData.page
    search: any = null;
    downloadRecords: any = null;
    // inductedUsers: Array<any>;
    selectedStatus: number = 0;
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    statusFilter: any[] = [];
    all_status: any = [{ status: 1, status_message: 'Open' }, { status: 2, status_message: 'Closed Out' }];
    decimalConfig = AppConstant.decimelConfig;
    isMobileDevice: boolean = false;
    hazardCategoryEditDisable: boolean = true;
    closeCallDetailsModalRef: any;
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    defaultFieldsTemp = [
        {
            "field": "hazard_category",
            "label": "Hazard Category",
            "is_active": true,
            "is_default": true
        },
        {
            "field": "lighting_conditions",
            "label": "Lighting Conditions",
            "is_active": true,
            "is_default": true
        },
        {
            "field": "location",
            "label": "Location Tag(Lat, Long)",
            "is_active": true,
            "is_default": true
        },
        {
            "field": "location_and_description",
            "label": "Location",
            "is_active": true,
            "is_default": true
        },
        {
            "field": "additional_detail",
            "label": "Details",
            "is_active": true,
            "is_default": true
        },
        {
            "field": "cc_detail",
            "label": "What could have happened?",
            "is_active": true,
            "is_default": true
        },
        {
            "field": "is_anonymous",
            "label": "Submit Anonymously",
            "is_active": true,
            "is_default": true
        }
    ];
    showCloseOutForm: boolean = false;
    filterData:filterData[] = this.renderFilterData();
    showProgressPhotosHtmlMap: boolean = false;
    actionButtonMetaData = {
        isTraining: false,
        actionList: [],
        class: 'material-symbols-outlined',
    };
    loadPowerBiComponent: boolean = false;
    biToolName: string = 'close_call';
    biToolLabel: string = `Close Calls`;
    biModalTitle: string = 'Dashboard';
    isInitCloseCall: boolean = false;
    loadingInlineCloseCall: boolean = false;
    imagesArray:  Array<string> = [];
    closeOutImageArray:  Array<string> = [];

    constructor(
        private activatedRoute: ActivatedRoute,
        private closeCallService: CloseCallService,
        private excelUtility: ExcelUtility,
        private modalService: NgbModal,
        private authService: AuthService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private httpService: HttpService,
        private resourceService: ResourceService,
        private cdRef: ChangeDetectorRef,
        private toastService: ToastService,
    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }
    dayjs(n: number) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).tz(tz);
    };

    dayjsDisplayDateOrTime(n: number, onlyDate = true) {
        return dayjs(n).format(onlyDate ? AppConstant.defaultDateFormat : AppConstant.defaultTimeFormat);
    };

    displaySelectedDay(n: number) {
        return dayjs(n).format(AppConstant.dateFormat_Do_MMM_YYYY);
    }

    isCloseoutAllowed() {
        if (this.isProjectPortal && this.project_category === "default") {
            return true;
        } else if (!this.isProjectPortal && this.project_category === "company-project") {
            return true;
        } else {
            return false;
        }
    }

    ngOnInit() {
        if (!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                this.projectId = params['projectId'];
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;

        } else {
            this.initializeTable();
            this.getProjectSettingsDataByName();
            this.getProjectUtils();
        }
        this.actionButtonMetaData.actionList = [
            {
                code: CloseCallActionButtons.MAP,
                name: `Location Map`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'location_on',
                enabled: true,
            },
            {
                code: CloseCallActionButtons.DASHBOARD,
                name: `View Dashboard`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'dashboard',
                enabled: true,
            },
            {
                code: CloseCallActionButtons.DOWNLOAD_REPORT,
                name: `Download Report`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'download',
                enabled: true,
            },
        ];
        this.getInductionsUsersEmployer();
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
                this.regUserCompanyId = data.parent_company.id;
            }
        });
        this.loadingCloseCalls = true;

        this.biToolName = 'close_call';
        this.biToolLabel = this.ccPhrase;
    }

    /**
     * get emlployer list
     */
    public getInductionsUsersEmployer(): void {
        this.userService.getProjectInductionsUsersEmployer(this.projectId, 2).subscribe((data: any) => {
            if (data.success && data.users_employer) {
                this.inductedUsersEmployer = (data.users_employer || []).sort((a, b) => a.name.localeCompare(b.name));
            } else {
                const message = data.message || 'Failed to get companies of the users who have had an approved induction on the project.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    /**
     * function to show only field which is active
     * @param field field-name
     * @returns  true/false
     */
    public checkIsActive(field: string): boolean {
        const closeCallobj = this.closeCallDefaultFields.find(data => data.field == field);
        if (closeCallobj) {
            return closeCallobj.is_active;
        }
        return false;
    }

    /**
     * To sort custom dropdown fields data
     * @param data list of data
     * @returns
     */
    sortCustomFieldOptions(data) {
        let datas = (data || []).sort((a, b) => a.label.localeCompare(b.label));;
        return datas;
    }

    @ViewChild('addCloseCallModalRef') private addCloseCallModalRefGenericModal: GenericModalComponent
    public openAddNewCloseCallModal() {
        this.resetCloseCallModal();
        if (this.closeCallDefaultFields.length === 0 || this.closeCallCustomFields.length === 0) {
            this.dashboardLoader = true;
            this.resourceService.getProjectSettingsByName(['close_call_custom_fields', 'close_call_setting'], this.projectId).subscribe(data => {
                if (data && data.success) {
                    if (data.project_settings && Object.keys(data.project_settings).length) {
                        this.closeCallDefaultFields = (data.project_settings.close_call_custom_fields || []).filter(data => data.is_default);
                        this.closeCallCustomFields = (data.project_settings.close_call_custom_fields || []).filter(data => !data.is_default);
                        this.initialCoseCallCustomFields = JSON.parse(JSON.stringify(this.closeCallCustomFields)); // for deep clone
                        this.isAssignToMandatory = data.project_settings.close_call_setting?.assign_to_mandatory;
                        this.closeCallObj.custom_fields = this.closeCallCustomFields;
                        this.cdRef.detectChanges()
                    } else {
                        this.closeCallDefaultFields = [...this.defaultFieldsTemp];
                    }
                } else {
                    const message = data.message || 'Failed to get close call settings.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                    return;
                }
            }).add(() => {
                this.dashboardLoader = false;
            });
        }
        this.addImageBlock();
        this.addCloseCallForm.form.statusChanges.subscribe(status => {
            this.addCloseCallModalRefConfig.primaryBtnDisabled = status !== 'VALID'
        });
        this.addCloseCallModalRefConfig.modalTitle = `Add ${this.ccSglrPhrase}`
        return this.addCloseCallModalRefGenericModal.open();
    }

    @ViewChild('addCloseCallForm') addCloseCallForm;
    public addCloseCallModalRefConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: 'Submit',
        primaryTrailingBtnAction: () => {
            this.saveCloseCall();
            return true;
        },
        primaryBtnDisabled: true,
        modalOptions: {
            size: 'md',
        }
    }

    /**
     * get list of hazardous-category and lightning-condition from inndex-setting
     */
    private getProjectSettingsDataByName(): void {
        this.dashboardLoader = true;
        this.resourceService.getInnDexSettingsByName(['close_call_category_en_gb', 'meta_lighting_conditions_en_gb']).subscribe(data => {
            if (data && data.success) {
                if (data.settings) {
                    this.hazardousCategoryList = (data.settings.close_call_category_en_gb || []);
                    this.lightingConditionList = (data.settings?.meta_lighting_conditions_en_gb || []).sort((a, b) => a.name.localeCompare(b.name));
                    this.filterData = this.renderFilterData();
                }
            } else {
                const message = data.message || 'Failed to get category and lighting conditions.';
                this.toastService.show(this.toastService.types.ERROR, message);
                return;
            }
        }).add(() => {
            this.dashboardLoader = false;
        });
    }

    /**
     * for toggle radio of submit-anonymous field
     */
    public changeIsAnnonymous(): void {
        this.closeCallObj.is_anonymous = !this.closeCallObj.is_anonymous;
        this.cdRef.detectChanges();
    }

    /**
     * clear or initialize/reset all inputs for new close-call
     */
    private resetCloseCallModal(): CloseCall {
        this.newPhotos = [];
        this.closeCallObj = new CloseCall();
        this.closeCallObj.project_ref = this.projectId;
        this.closeCallObj.company_ref = this.regUserCompanyId;
        this.closeCallCustomFields = JSON.parse(JSON.stringify(this.initialCoseCallCustomFields));
        this.addCloseCallForm.reset()
        return this.closeCallObj;
    }

    onAssigneeSelection({ selectedUser }) {
        this.closeCallObj.assigned_to = selectedUser;
    }
    /**
     * for add new close call
     */
    public saveCloseCall(): void {

        if(!this.addCloseCallForm.valid){
            const message = 'Invalid form!';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }

        this.closeCallObj.cc_images = (this.newPhotos || []).filter(m => m?.id).map(m => m.id);
        this.dashboardLoader = true;
        this.closeCallService.addCloseCall(this.closeCallObj).subscribe(res => {
            if (res.success) {
                const message = 'Close-call created successfully.';
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.closeModalAndTableInit();
                this.addCloseCallModalRefGenericModal.close();
            } else {
                const message = res.message || 'Failed to save information.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        }).add(() => this.dashboardLoader = false);
    }

    private closeModalAndTableInit(): void {
        this.modalService.dismissAll();
        this.initializeTable(true);
    }

    private addImageBlock(): void {
        if (!this.newPhotos) {
            this.newPhotos = [];
        }
        this.newPhotos = this.newPhotos.filter(p => Object.keys(p).length);
        this.newPhotos.unshift({});
    }

    public mediaUploadDone($event): void {
        this.newPhotos.splice(1, 0, ...$event.userFile);
        this.newPhotos[0] = {};
    }

    public imgDeleteDone(event): void {
        const userFileId = event?.userFile?.id;
        if (userFileId) {
            this.newPhotos = this.newPhotos.filter(r => (r?.id !== userFileId));
        }
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project) {
        this.projectInfo = project;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitCloseCall) {
          this.isInitCloseCall = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.initializeTable(isPageChange);
    }

    private initializeTable(isPageChange?: boolean) {
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('is_inherited_project', `${this.is_inherited_project}`);
        if (this.search && this.search.length !== 0) {
            params = params.append('search', encodeURIComponent(this.search))
        }
        if (this.selectedHazardType && this.selectedHazardType.length != 0) {
            params = params.append('category', encodeURIComponent(this.selectedHazardType.join(",")))
        }
        if (this.statusFilter.length != 0) {
            params = params.append('status', `${this.statusFilter}`);
        }
        if (this.selectedAssignTo && this.selectedAssignTo.length != 0) {
            params = params.append('assigned_to', `${this.selectedAssignTo}`);
        }
        if (this.selectedRaisedBy && this.selectedRaisedBy.length != 0) {
            params = params.append('raised_by', `${this.selectedRaisedBy}`);
        }
        if(this.ownerFilter && this.ownerFilter.length !== 0) {
            params = params.append('tagged_owner', `${this.ownerFilter}`);
        }
        this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.projectInfo.createdAt));
        this.project_category = this.projectInfo.project_category;
        this.ccPhrase = this.projectInfo.custom_field.cc_phrase;
        this.ccSglrPhrase = this.projectInfo.custom_field.cc_phrase_singlr;
        let companyId = (this.employerId ? this.employerId : null);
        if (isPageChange) {
          this.loadingInlineCloseCall = true;
        }
        this.closeCallService.getProjectCloseCalls(this.projectId, companyId, params).subscribe((data: any) => {
            this.openRecords = [];
            this.loadingCloseCalls = false;
            this.loadingInlineCloseCall = false;
            if (data && data.project_close_calls) {
                this.records = data.project_close_calls;
                this.temp_records = data.project_close_calls;
                this.page.totalElements = data.total_record_count;
               
                this.openRecords = data.openCalls;
                this.tagged_owners = data.taggedOwners;
                this.project_close_calls = data.project_close_calls;
                return data.project_close_calls;
            }
            const message = `Failed to fetch close calls, id: ${this.projectId}`;
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            return [];
        });
    }

    getProjectUtils(){
        this.closeCallService.getProjectUtils(this.projectId).subscribe((data: any) => {
            this.employersList = [...new Set(data.employer)];
            this.tagged_owners = data.tagged_owners;
            this.assignedToUsers = data.assigned_to;
            this.raisedByUsers = data.raised_by;
            this.filterData = this.renderFilterData();
        });
    }

    initEmployers() {
        let emp = ['Anonymous'];
        this.temp_records.filter(t => {
            if (!t.is_anonymous && t.user_employer && t.user_employer.employer) {
                emp.push(t.user_employer.employer);
            }
        });
        this.employersList = [...new Set(emp)];
    }


    onClearHazardFilter() {
        this.records = this.temp_records;
        this.tableOffset = 0;
    }

    filterRecords() {
        this.page.pageNumber = 0;
        this.initializeTable()
    }
    @ViewChild('closeCallDetailsHtml') private closeCallDetailsModalRefGenericModal: GenericModalComponent
    @ViewChild('addForm') addForm;
    closeCallDetailModal(row) {
        this.dashboardLoader = true;
        this.closeCallService.getCloseCallData(this.projectInfo?.id, row.id).subscribe(
            (response) => {
            if(response.success) {
                const data = response.close_call;
                this.imagesArray = (data?.cc_images || []).reduce((acc, file) => {
                    if (file.img_translation?.length) {
                        acc.push(...file.img_translation);
                    } else if (file.file_url && file.file_type != 7) {
                        acc.push(file.file_url);
                    }
                    return acc;
                }, []).map(img => ({ file_url: img }));
    
                this.closeOutImageArray = (data?.corrective_images || []).reduce((acc, file) => {
                    if (file.img_translation?.length) {
                        acc.push(...file.img_translation);
                    } else if (file.file_url && file.file_type != 7) {
                        acc.push(file.file_url);
                    }
                    return acc;
                }, []).map(img => ({ file_url: img }));
    
                this.close_call_row = { ...data };
                this.selectedStatus = data['status'];
                this.tagUser = undefined;
                this.tagOwner = undefined;
                this.hazardCategoryEditDisable = true;
                if (data.location) {
                    data.location.lat = Number(data['location']?.lat);
                    data.location.long = Number(data['location']?.long);
                }
                if(data.status_message == "Closed Out"){
                    this.closeCallDetailsModalRefConfig.primaryTrailingBtnLabel = 'OK';
                    this.closeCallDetailsModalRefConfig.showCancelTrailingBtn = false;
                } else{
                    this.closeCallDetailsModalRefConfig.primaryTrailingBtnLabel = 'Close Out';
                    this.closeCallDetailsModalRefConfig.showCancelTrailingBtn = true;
                }
                this.closeCallDetailsModalRefConfig.modalTitle = `${this.projectInfo?.custom_field?.cc_phrase_singlr} #${data.cc_number} Details`
                if (this.addForm) this.addForm.reset();
                this.closeCallDetailsModalRefConfig.primaryTrailingBtnLabel = data['status'] == 1 ? 'Close out' : 'OK';
                this.closeCallDetailsModalRefConfig.showCancelTrailingBtn = data['status'] == 1;
                this.closeCallDetailsModalRefGenericModal.open();
            } else {
                alert(response.message ? response.message : 'Failed to fetch close calls');
            }
    }).add(() => this.dashboardLoader = false);
    }


    public closeCallDetailsModalRefConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: '',
        primaryTrailingBtnAction: () => {
            if(this.close_call_row['status'] == 1){
                this.closeOutModal(this.close_call_row, '')
            }
            this.closeCallDetailsModalRefGenericModal.close()
            return true;
        },
        modalOptions: {
            size: 'lg',
        }
    }

    @ViewChild('closeOutHtml') private closeOutHtmlModalRefGenericModal: GenericModalComponent
    @ViewChild('closeOutForm') closeOutForm;
    closeOutModal(row, c) {
        this.showCloseOutForm = true
        if (c) {
            c('Clock Click');
        }
        this.close_call_row = row;
        this.corrective_images = [];
        this.close_out_images = [{
            identifier: (new Date()).getTime()
        }];

        if (this.closeOutForm) {
            this.closeOutForm.form.controls?.corrective_detail?.setValue('')
            this.closeOutForm?.form.markAsUntouched();
            this.closeOutForm.form.statusChanges.subscribe(status => {
                this.closeOutModalRefConfig.primaryBtnDisabled = status !== 'VALID';
            });
        }
        this.closeOutModalRefConfig.modalTitle = `${this.projectInfo?.custom_field?.cc_phrase_singlr} Close Out`
        return this.closeOutHtmlModalRefGenericModal.open();
    }

    public closeOutModalRefConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: 'Close Out',
        primaryTrailingBtnAction: () => {
            this.closeOutRequest(this.closeOutForm)
            return true;
        },
        cancelTrailingBtnAction: () => {
            this.showCloseOutForm = false
            return true;
        },
        closeTrailingBtnAction: () => {
            this.showCloseOutForm = false
            return true;
        },
        primaryBtnDisabled: true,
        modalOptions: {
            size: 'md',
        }
    }

    replaceAll(str, find, replace) {
        const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
        return str.replace(new RegExp(escapedFind, 'g'), replace);
    }


    getDescription(str) {
        // '\n', '<br>'
        return str.replace(new RegExp('<br>', 'g'), '\n');
    }

    openModal(content, size, windowClass = '') {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            windowClass: windowClass
        });
    }

    public openMapWithMultiPin() {
        this.showMapWithMultiPin = true;
        this.progressPhotosModalShowMap();
    }

    public openMapWithPin() {
        if (this.close_call_row['location'].lat && this.close_call_row['location'].long) {
            this.showMapWithMultiPin = false;
            this.progressPhotosModalShowMap();
        }
    }

    openProgressPhotosMapModal(content, size) {
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
        });
        return false;
    }

    public progressPhotoMapConfig: GenericModalConfig = {
        modalTitle: '',
        hideFooter: true,
        modalOptions: {
            size: 'lg',
        }
    }

    @ViewChild('progressPhotosHtmlMap') private progressPhotosHtmlMapModal: GenericModalComponent;
    progressPhotosModalShowMap() {
        this.progressPhotoMapConfig.modalTitle = `${this.ccSglrPhrase} location map`;
        this.showProgressPhotosHtmlMap = true;
        this.progressPhotosHtmlMapModal.open();
    }

    @ViewChild('cc')
    public ccRef: TemplateRef<any>;

    addMorePhotoRow() {
        this.close_out_images.push({
            identifier: (new Date()).getTime()
        })
    }

    logoImgRetries: number = 5;

    onLogoError($img: any, targetSrc: any) {
        $img.src = '/images/project-placeholder.png';
        if (targetSrc && targetSrc.length && this.logoImgRetries) {
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img) {
        this.logoImgRetries = 5;
    }

    uploadDone($event) {
        if ($event && $event.userFile) {
            // this.userFile = $event.userFile;
            // this.img_link = $event.userFile.file_url;
            this.corrective_images.push(...$event.userFile.map(f => f.id));
            this.corrective_images_file.push(...$event.userFile.map(f => f.file_url));

            this.close_out_images.splice(1, 0, ...$event.userFile);
            this.close_out_images[0] = {};
        }
    }

    closeOutRequest(form) {
        if (!form.valid) {
            const message = 'form is not valid';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }

        let closeOutReq = {
            id: this.close_call_row['id'],
            corrective_detail: form.value.corrective_detail,
            corrective_images: form.value.corrective_images,
            closed_out_by: this.authUser$.name,
            status: 2
        };

        let additionalInfo = {
            projectData: this.close_call_row['project_ref'],
            userData: this.close_call_row['user_ref'],
            correctiveImages: this.corrective_images_file
        };
        this.updateRequestLoader = true;

        let request = {
            close_out_request: closeOutReq,
            additional_data: additionalInfo,
        };
        this.closeCallService.updateCloseCall(request, this.projectId, closeOutReq.id).subscribe(out => {
            this.updateRequestLoader = false;
            if (out.success) {
                this.closeOutHtmlModalRefGenericModal.close();
                this.showCloseOutForm = false
                const message = 'Close call successfully closed.';
                this.toastService.show(this.toastService.types.SUCCESS, message);
            } else {
                const message = out.message || 'Failed to update data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            }
            this.initializeTable(true);
        });
    }

    onMouseOver(infoWindow, _event: MouseEvent) {
        infoWindow.open();
    }

    onMouseOut(infoWindow, _event: MouseEvent) {
        infoWindow.close();
    }

    async downloadCloseCallReport($event) {
        this.from_date = $event.fromDate;
        this.to_date = $event.toDate;

        this.dashboardLoader = true;
        let request: any = {};
        request.projecId = this.projectId;
        request.open_status = (this.closeCallsDwStatus.open) ? 1 : 0;
        request.closed_status = (this.closeCallsDwStatus.closed) ? 2 : 0;
        request.hazard_category = this.downloadHazardType;
        request.tagged_owner = this.filterByOwner;
        request.employer = this.filterEmployer;
        request.fromDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.from_date).startOf('day').valueOf();
        request.toDate = this.ngbMomentjsAdapter.ngbDateToDayJs(this.to_date).endOf('day').valueOf();
        request.is_site_admin = (this.isProjectPortal) ? true : false;

        this.closeCallService.downloadCloseCallReport(this.projectId, request, () => {
            this.dashboardLoader = false;
        });
    }

    isStatusMatched(status) {
        return (status === 1 && this.closeCallsDwStatus.open) || (status === 2 && this.closeCallsDwStatus.closed);
    }

    addImagesToWorkbook = this.excelUtility.addImagesToWorkbook;

    fileDeleteDone($event) {
        if ($event && $event.userFile && $event.userFile.id) {
            let index = this.corrective_images.indexOf($event.userFile.id);
            let urlIndex = this.corrective_images_file.indexOf($event.userFile.file_url);
            if (index > -1) {
                this.corrective_images.splice(index, 1);
                this.close_out_images = this.close_out_images.filter(r => (r.id !== $event.userFile.id));
            }
            if (urlIndex > -1) {
                this.corrective_images_file.splice(urlIndex, 1);
            }
        }
    }

    getCloseOutDate(date) {
        return (date && !isNaN(date)) ? this.dayjs(+date).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) : '';
    }

    prepareOwnerFilter() {
        this.tagged_owners = this.records.reduce((arr, record) => {
            if (record.tagged_owner &&
                record.tagged_owner.id &&
                !arr.find(item => record.tagged_owner.id == item.company_id)) {
                arr.push({
                    "company_id": record.tagged_owner.id,
                    "company_name": record.tagged_owner.name
                })
            }
            return arr;
        }, []);
        console.log(this.tagged_owners)
    }

    getSelectedTagCompanyName(id: number) {
        const company = this.inductedUsersEmployer.find(data => data.id === id);
        return (company) ? company.name : '';
    }

    public tagOwnerRequest(companySelectorElm$): void {
        if (this.tagOwner) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Tag Company',
                title: `Are you sure you want to tag <span class="fw-500">${this.getSelectedTagCompanyName(this.tagOwner)}</span>?`,
                confirmLabel: 'Tag',
                onConfirm: () => {
                    let request: any = {
                        update_request: {
                            id: this.close_call_row['id'],
                            tagged_owner: this.tagOwner
                        }
                    };

                    this.updateRequestLoader = true;
                    this.closeCallService.updateCloseCall(request, this.projectId, request.update_request.id).subscribe(out => {
                        this.updateRequestLoader = false;
                        if (out.success) {
                            this.close_call_row['tagged_owner'] = (this.inductedUsersEmployer || []).find(emp => emp.id === this.tagOwner) || null;
                        } else {
                            const message = out.message || 'Failed to update data.';
                            this.toastService.show(this.toastService.types.ERROR, message);
                        }
                        this.initializeTable(true);
                    });
                },
                onClose: () => {
                    companySelectorElm$.handleClearClick();
                },
            });
        }
    }

    onUserSelection({ selectedUser, selectedRecord }, userSelectorElm$) {
        if (selectedUser){
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Assign Action',
                title: `Are you sure you want to assign an action to <span class="fw-500">${selectedRecord.name}</span>?`,
                confirmLabel: 'Assign',
                onConfirm: () => {
                    let request = {
                        update_request: {
                            id: this.close_call_row['id'],
                            assigned_to: selectedUser
                        }
                    };

                    this.updateRequestLoader = true;
                    this.closeCallService.updateCloseCall(request, this.projectId, request.update_request.id).subscribe(out => {
                        this.updateRequestLoader = false;
                        if (out.success) {
                            this.close_call_row['assigned_to'] = selectedRecord || null;
                        } else {
                            const message = out.message || 'Failed to update data.';
                            this.toastService.show(this.toastService.types.ERROR, message);
                        }
                        this.initializeTable(true);
                    });
                },
                onClose: () => {
                    userSelectorElm$.clearSelection();
                },
            });
        }
    }

    openDashboardModal() {
        this.loadPowerBiComponent = true;
        this.biModalTitle = `${this.ccPhrase} Dashboard`;
    }

    powerBiDashboardClose() {
        this.loadPowerBiComponent = false;
    }

    downloadCloseCall(row) {
        this.dashboardLoader = true;
        let fileName = `${this.ccSglrPhrase}-Report-${dayjs().format(AppConstant.dateFormat_MM_DD_YYYY)}`;

        let body = {
            createdAt: row.createdAt,
            companyId: this.employerId,
            type: 'pdf'
        };

        this.closeCallService.downloadCloseCall(body, row.id, () => {
            this.dashboardLoader = false;
        });
    }

    public enableEdit(): void {
        this.hazardCategoryEditDisable = !this.hazardCategoryEditDisable;
    }

    /**
     * to update hazard-category
     * @param hazardCategory selected hazard-category
     */
    public updateHazardCategory(hazardCategory): void {
        let request = {
            update_request: {
                id: this.close_call_row['id'],
                hazard_category: hazardCategory
            }
        };
        this.updateRequestLoader = true;
        this.closeCallService.updateCloseCall(request, this.projectId, request.update_request.id).subscribe(res => {
            if (res.success) {
                this.closeCallDetailsModalRefGenericModal.close()
                this.initializeTable(true);
            } else {
                const message = res.message || 'Failed to update data.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        }).add(() => {
            this.updateRequestLoader = false;
        });
    }

    onFilterSelection(data) {
        this.selectedHazardType = data.category.map(a => a.name);
        this.statusFilter = data.status.map(a => a.status);
        this.selectedAssignTo = data['assigned to'].map(a => a.id);
        this.selectedRaisedBy = data['raised by'].map(a => a.id);
        this.ownerFilter = data['companies'].map(a => +a.company_id);
        this.page.pageNumber = 0;
        this.initializeTable(true);
    }
    searchFunction(data){
        this.page.pageNumber = 0;
        this.search = data.search;
        let stopReload = data?.stopReload;
        if(!stopReload){
            this.initializeTable(true);
        };
    }
    renderFilterData(){
        return [
            {
                name:'category',
                list:this.hazardousCategoryList,
                enabled:true,
                state:false
            },
            {
                name:'status',
                list:this.all_status,
                enabled:true,
                state:false
            },
            {
                name: 'assigned to',
                list: this.assignedToUsers,
                enabled: true,
                state: false
            },
            {
                name: 'raised by',
                list: this.raisedByUsers,
                enabled: true,
                state: false
            },  
            {
                name: 'companies',
                list: this.tagged_owners,
                enabled: true,
                state: false,
                labelKey: 'company_name'
            },
        ];
    }

    public onActionSelection(actionVal: any) {
        const code = actionVal.code;
        this.actionButtonMetaData.isTraining = false;
        if(code === CloseCallActionButtons.MAP) {
            this.openMapWithMultiPin();
        } else if(code === CloseCallActionButtons.DASHBOARD) {
            this.openDashboardModal();
        } else if(code === CloseCallActionButtons.DOWNLOAD_REPORT) {
            this.openCloseCallReportModal();
        }
    }

    public openCloseCallReportModal() {
        this.reportDownloader.openModal();
    }

    async closeCallReportDownload(event) {
        this.downloadHazardType = event.selection.category;
        this.filterEmployer = event.selection.employer;
        this.closeCallsDwStatus = event.selection.status;
        await this.downloadCloseCallReport(event.selection);
        this.closeCallsDwStatus = { open: true, closed: true };
        event.closeFn();
    }
}


