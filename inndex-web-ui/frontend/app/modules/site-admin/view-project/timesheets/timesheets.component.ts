import {Component, Input, OnInit, ViewChild} from "@angular/core";
import {
    filterData, HttpService,
    Project,
    ProjectService, TIME_HOUR_STATES_VALUE, TIME_INPUT_STATE, TimeInput,
    TIMESHEET_STATUS,
    TimesheetInductionsPage,TimesheetInductionsRow,
    TimesheetWeekDay, TimeUtility,
    ToastService,
} from "@app/core";
import {ActivatedRoute, Router} from "@angular/router";
import {AssetsUrl, GenericConfirmationModalComponent, IModalComponent} from "@app/shared";
import {innDexConstant} from "@env/constants";
import * as dayjs from "dayjs";
import {DatatableComponent, SortType} from "@swimlane/ngx-datatable";
import {AppConstant} from "@env/environment";
import {ViewTimesheetComponent} from "./view-timesheet/view-timesheet.component";
import {forkJoin} from "rxjs";
import {NgbCalendar, NgbDate} from "@ng-bootstrap/ng-bootstrap";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {AmendTimesheetComponent} from './amend-timesheet/amend-timesheet.component';

enum TimesheetActionButtons {
    BULK_APPROVE = 1,
    DOWNLOAD_RECORDS = 2,
}

@Component({
    selector: 'project-timesheets',
    templateUrl: 'timesheets.component.html',
    styleUrls: ['./timesheets.component.scss'],
})
export class TimesheetsComponent implements OnInit {
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    apiRequestDateFormat = AppConstant.apiRequestDateFormat;
    tableSortType = SortType.single;

    @Input()
    projectId: number;

    @Input()
    project: Project;

    weekEndsOn: number;

    isMobile: boolean;

    visibleInductionStatusCodes: string = '2';

    viewInfo: {
        iRLoading: boolean;
        selectedDay?: NgbDate;
        inductionsPage: TimesheetInductionsPage;
        daysLoading?: boolean;
        totalChanges?: number;
        minDate: dayjs.Dayjs;
        maxDate: dayjs.Dayjs;
        weekDays: Array<{ id: number; label: string; sub_label?: string; day_of_yr: string; }>;
        start?: dayjs.Dayjs;
        end?: dayjs.Dayjs;
    } = {
        iRLoading: true,
        inductionsPage: new TimesheetInductionsPage(),
        // daysLoading: false,
        totalChanges: 0,
        minDate: dayjs().subtract(1, 'month'),
        maxDate: dayjs(),
        weekDays: innDexConstant.weekDays,
    };
    bulkAction: {
        active: boolean;
        approve?: Array<number>;
    } = {
        active: false,
        approve: []
    };
    actionsList: Array<any> = [];
    savingChanges: boolean = false;
    timesheetChanges = [];
    invalidRowUsers = [];
    isInitTimesheet: boolean = false;
    loadingInlineTimesheet: boolean = false;

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;

    constructor(
        private projectService: ProjectService,
        private timeUtility: TimeUtility,
        private route: ActivatedRoute,
        private router: Router,
        private httpService: HttpService,
        public ngbMomentjsAdapter: NgbMomentjsAdapter,
        private calendar: NgbCalendar,
        private toastService: ToastService,
    ) {
        this.isMobile = this.httpService.isMobileDevice();
    }

    ngOnInit() {
        this.viewInfo.selectedDay = this.getInitialDate();
        this.viewInfo.inductionsPage.pageSize = 15;
        this.weekEndsOn = (this.project.custom_field?.timesheet_week_end || 7);
        this.viewInfo.minDate = dayjs(+this.project.start_date);
        if(this.viewInfo.minDate.isAfter(dayjs(), 'day')){
            this.viewInfo.maxDate = dayjs(+this.project.start_date);
        }
        this.getEmployersList();
        this.actionsList = [
            {
                code: TimesheetActionButtons.BULK_APPROVE,
                name: `Bulk Approve`,
                iconClass: 'material-symbols-outlined',
                iconClassLabel: 'group',
                enabled: true,
            },
            {
                code: TimesheetActionButtons.DOWNLOAD_RECORDS,
                name: `Download Records`,
                iconClass: 'material-symbols-outlined',
                iconClassLabel: 'vertical_align_bottom',
                enabled: true,
            }
        ];
    }

    private getInitialDate(): NgbDate {
        let defaultDay = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(), false) as NgbDate;
        let pastSelection = this.route.snapshot.queryParamMap.get('date');
        if (pastSelection) {
            let d = dayjs(pastSelection, AppConstant.apiRequestDateFormat);
            if (d.isValid()) {
                defaultDay = this.ngbMomentjsAdapter.dayJsToNgbDate(d, false) as NgbDate;
            }
        }
        return defaultDay;
    }

    onRangeSelection({start, end}) {
        // console.log(start, ' ==> ', end);
        this.viewInfo.start = start;
        this.viewInfo.end = end;
        this.generateWeekDays(start, end);
        this.loadPage(0, {sortKey: 'name'}, true);
        this.router.navigate(
            [],
            {
                queryParams: {
                    date: end.format(AppConstant.apiRequestDateFormat),
                },
                relativeTo: this.route,
                // By using the `replaceUrl` option, we don't increase the Browser's history depth.
                // This way, this page remains a single item in the Browser's history, which allows the easier back
                replaceUrl: true
            }
        );
    }

    private generateWeekDays(start: dayjs.Dayjs, end: dayjs.Dayjs) {
        let weeks = [];
        let startClone = start.clone();
        for (let i = 0; i < 7; i++) {
            weeks.push({
                id: startClone.isoWeekday(),
                label: startClone.format('ddd'),
                sub_label: startClone.format('Do MMM'),
                day_of_yr: startClone.format(AppConstant.apiRequestDateFormat),
            });
            startClone = startClone.add(1, 'day');
        }
        // console.log('weeks', weeks);
        this.viewInfo.weekDays = weeks;
        return weeks;
    }

    filterData: filterData[] = this.renderFilterData();
    inductedEmployers: Array<any> = [];
    inductedJobRoles: Array<any> = [];
    inductedUsers: Array<any> = [];
    typeOfEmployments: Array<any> = [];
    filter: {
        searchText?: string;
        employer?: string[];
        jobRole?: string[];
        // statusCodes?: number[];
    } = {
        // statusCodes: [],
        employer: [],
        jobRole: [],
    };

    renderFilterData() {
        return [
            // {
            //     name: 'status',
            //     list: this.STATUS_CODES_LIST,
            //     enabled: true,
            //     state: false,
            // },
            {
                name: 'company',
                list: this.inductedEmployers,
                enabled: !!(this.inductedEmployers || []).length,
                state: false
            },
            {
                name: 'job role',
                list: this.inductedJobRoles,
                enabled: !!(this.inductedJobRoles || []).length,
                state: false
            }
        ];
    }

    private getEmployersList() {
        forkJoin({
            employers: this.projectService.getSAInductionEmployers(this.projectId, {statusCodes: this.visibleInductionStatusCodes}),
            job_roles: this.projectService.getSAInductionJobRoles(this.projectId, {statusCodes: this.visibleInductionStatusCodes})
        }).subscribe(({employers, job_roles}: any) => {
            if (!employers || !employers.inducted_employers || !job_roles || !job_roles.list) {
                const message = employers.message ||'Failed to fetch filter options.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: employers });
                return false;
            }
            this.inductedEmployers = (employers.inducted_employers || []).map(row => row.name).filter(name => name);
            this.inductedJobRoles = (job_roles.list || []).filter(name => name);
            this.filterData = this.renderFilterData();

        })
    }

    @ViewChild('pTimesheetTable') pTimesheetTable: DatatableComponent;

    loadPage(pageNumber: number = 0, sortInfo: any = {sortKey: 'name'}, isPageChange?: boolean) {
        let q = this.filter.searchText ? encodeURIComponent(this.filter.searchText) : '';
        let employer = this.filter.employer ? this.filter.employer.join(",") : '';
        let jobRole = this.filter.jobRole ? this.filter.jobRole.join(",") : '';
        let statusCodes = this.visibleInductionStatusCodes;
        if (isPageChange) {
          this.loadingInlineTimesheet = true;
        } else {
          this.viewInfo.iRLoading = true;
        }
        this.projectService.getActiveUsersTimesheetList(this.projectId, {
            extra: 'name,employment',
            ...sortInfo,
            pageNumber,
            pageSize: this.viewInfo.inductionsPage.pageSize,
            q,
            employer,
            jobRole,
            statusCodes,
            from_date: this.viewInfo.start.format(AppConstant.apiRequestDateFormat),
            to_date: this.viewInfo.end.format(AppConstant.apiRequestDateFormat),
        }).subscribe((data: TimesheetInductionsPage) => {
            if (data.success) {
                this.viewInfo.inductionsPage = data;
                this.preCalculateRenderingFields(data.daily_logs, data.timesheets);
                this.timesheetChanges = [];
                this.refreshValidationStatus();
                this.viewInfo.iRLoading = false;
                this.loadingInlineTimesheet = false;
                if (data.sortKey) {
                    setTimeout(() => {
                        if (this.pTimesheetTable) {
                            this.pTimesheetTable.sorts = [{prop: data.sortKey, dir: data.sortDir}];
                        }
                    }, 0);
                }
                // this.getTimesheets();
            } else {
                const message = data.message || 'Failed to fetch inductions.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        }).add(() => {
            // this.viewInfo.iRLoading = false;
        });
    }

    pageCallback($event, isPageChange: boolean) {
        if (!this.isInitTimesheet) {
            this.isInitTimesheet = true;
            return;
        }
        if (this.viewHasChanges() || !this.viewInfo.start) {
            return false;
        }
        let pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number } = $event;
        this.loadPage(pageInfo.offset, {
            sortKey: this.viewInfo.inductionsPage.sortKey,
            sortDir: this.viewInfo.inductionsPage.sortDir
        }, isPageChange);
    }

    sortCallback($event, isPageChange: boolean) {
        if (this.viewHasChanges()) {
            return false;
        }
        let {sorts} = $event;
        let [firstSort] = sorts || [];
        let {dir, prop} = firstSort;
        this.loadPage(0, {sortKey: prop, sortDir: dir}, isPageChange);
    }

    scrollTable($event, right) {
        let tb = document.querySelector('.datatable-body') as HTMLElement;
        if (tb) {

            const scrollBy = 140;
            let previous = tb.scrollLeft || 0;
            right ? (tb.scrollTo({
                left: (previous - scrollBy),
                behavior: 'smooth'
            })) : (tb.scrollTo({left: (previous + scrollBy), behavior: 'smooth'}));
        }
    }

    showScroller(end = false) {
        let tb = document.querySelector('.datatable-body') as HTMLElement;
        if (end) {
            return !(tb ? (tb.scrollLeft + tb.offsetWidth === tb.scrollWidth) : true);
        }
        return !!(tb ? tb.scrollLeft : 0);
    }

    onFilterSelection(data, isPageChange: boolean) {
        // this.filter.statusCodes = data.status.map(a => a.code);
        this.filter.employer = data.company;
        this.filter.jobRole = data['job role'];
        this.loadPage(0, {sortKey: 'name'}, isPageChange);
    }

    onSearch($event, isPageChange: boolean) {
        this.filter.searchText = $event.search;
        let stopReload = $event?.stopReload;
        if (!stopReload) {
            this.loadPage(0, {sortKey: 'name'}, isPageChange);
        }
    }

    getRowClassFn(row) {
        return (row._all_ts_approved) ? 'disabled-row' : '';
    }

    private preCalculateRenderingFields(daily_logs, timesheet_records) {
        /**
         * Within this function, we are preparing JSON objects to initialize state of timesheet table view,
         * This help us in rendering with static data, instead of re-calculating via template driven functions calls.
         */
        let records = this.viewInfo.inductionsPage.records;
        for (let i = 0; i < records.length; i++) {
            let user_ref = records[i].user_ref || 0;
            let approved_ts = [];
            let ts_info = [];
            records[i]._weeks = this.viewInfo.weekDays.reduce((weeks_group, w) => {
                let timesheet_row = (timesheet_records || []).find(row => (user_ref === row.user_ref) && (w.day_of_yr === row.day_of_yr)) || {};
                let log = (daily_logs || []).find(log => (user_ref === log.user_id) && (w.day_of_yr === log.day_of_yr));
                let initial_value = null;
                if (TIME_HOUR_STATES_VALUE.includes(timesheet_row.hours_state)) {
                    if (timesheet_row.hours_state === TIME_INPUT_STATE.DAY_HOURS.v) {
                        initial_value = timesheet_row.day_seconds;
                    } else if (timesheet_row.hours_state === TIME_INPUT_STATE.NIGHT_HOURS.v) {
                        initial_value = timesheet_row.night_seconds;
                    } else if (timesheet_row.hours_state === TIME_INPUT_STATE.SPLIT_SHIFT.v) {
                        initial_value = (timesheet_row.day_seconds || 0) + (timesheet_row.night_seconds || 0);
                    }
                } else if (timesheet_row.hours_state && !TIME_HOUR_STATES_VALUE.includes(timesheet_row.hours_state)) {
                    initial_value = 0;
                } else {
                    initial_value = (log && +log.effective_time) || initial_value;
                }
                let _input_disable = (timesheet_row.status === TIMESHEET_STATUS.APPROVED);
                let timesheet_log = {
                    timesheet: timesheet_row,
                    log: log,

                    // This is being passed to each time input into table.
                    _input: {
                        state: timesheet_row.hours_state ? timesheet_row.hours_state : TIME_INPUT_STATE.DAY_HOURS.v,
                        val: initial_value,
                    },
                    placeholder: _input_disable ? 'N/A' : '--.--',
                    _input_disable: _input_disable,
                    // _tooltip: this.getTimeInputToolTip(log, timesheet_row.day_seconds),
                }
                if (timesheet_row.id && timesheet_row.status === TIMESHEET_STATUS.APPROVED) {
                    approved_ts.push(timesheet_row.id);
                }

                if (timesheet_row.id) {
                    ts_info.push({
                        day_of_yr: w.day_of_yr,
                        id: timesheet_row.id,
                        user_ref: user_ref,
                        status: timesheet_row.status
                    });
                } else if (!timesheet_row.day_of_yr && initial_value) {
                    ts_info.push({
                        day_of_yr: w.day_of_yr,
                        user_ref: user_ref,
                        day_seconds: initial_value,
                        hours_state: TIME_INPUT_STATE.DAY_HOURS.v,
                    });
                }
                weeks_group[w.day_of_yr] = timesheet_log;
                return weeks_group;
            }, {});
            records[i]._ts_info = ts_info;
            records[i]._approved_ts = approved_ts;
            records[i]._all_ts_approved = (approved_ts.length === 7);

        }
        this.viewInfo.inductionsPage.records = records;//.filter(i => i._has_daily_log);
        console.log(this.viewInfo.inductionsPage.records);
    }

    // unix(n: number) {
    //     let tz = this.project?.custom_field?.timezone;
    //     return dayjs.unix(n).tz(tz);
    // }
    // getTimeInputToolTip(log, day_seconds) {
    //     if (log && log.effective_time) {
    //         return `Site Hours: \n ${this.unix(+log.clock_in).format('HH:mm:ss')} - ${this.unix(+log.clock_out).format('HH:mm:ss')}`;
    //     }
    //     if (log && !log.effective_time && log.clock_in) {
    //         return `Site Hours: \n ${this.unix(+log.clock_in).format('HH:mm:ss')} - N/A`;
    //     }
    //     if (log && !log.effective_time && log.clock_out) {
    //         return `Site Hours: \n N/A - ${this.unix(+log.clock_out).format('HH:mm:ss')}`;
    //     }
    //     return ``;
    //     // return day_seconds ? `Manually added` : '';
    // }

    timeInputChanged($event, user_ref, day_of_yr, week_row: TimesheetWeekDay, splitData: { day_seconds?: number; night_seconds?: number; } = {}) {
        console.log(user_ref, day_of_yr, $event);
        let input = {
            day_seconds: 0,
            night_seconds: 0,
            changed: $event.changed,
            valid: $event.valid,
            hours_state: $event.state,
        };
        if ($event.state === TIME_INPUT_STATE.SPLIT_SHIFT.v && splitData.day_seconds !== undefined) {
            input.day_seconds = splitData.day_seconds;
            input.night_seconds = splitData.night_seconds;

        } else if ($event.state === TIME_INPUT_STATE.SPLIT_SHIFT.v) {
            input.night_seconds = $event.value || 0;
        } else if ($event.state === TIME_INPUT_STATE.DAY_HOURS.v) {
            input.day_seconds = $event.value;
        } else if ($event.state === TIME_INPUT_STATE.NIGHT_HOURS.v) {
            input.night_seconds = $event.value;
        }
        let index = this.timesheetChanges.findIndex(change => (
            change.user_ref === user_ref &&
            change.day_of_yr === day_of_yr
        ));
        const value = {
            user_ref,
            day_of_yr,
            // week_row,
            ...input,
        };
        if (index > -1) {
            this.timesheetChanges[index] = value;
        } else {
            this.timesheetChanges.push(value);
        }
        console.log(this.timesheetChanges);
        // console.log(this.viewInfo.totalChanges);
        this.refreshValidationStatus();
    }

    private refreshValidationStatus() {
        this.viewInfo.totalChanges = this.timesheetChanges.filter(c => c.changed).length;
        let invalid_users = this.timesheetChanges.filter(change => !change.valid).map(change => change.user_ref);
        this.invalidRowUsers = [...new Set(invalid_users)];
        console.log(`invalidRowUsers: ${this.invalidRowUsers}`);
    }

    viewHasChanges(): boolean {
        return this.viewInfo.totalChanges > 0 || (this.bulkAction.active);
    }

    userRowHasChanges(user_ref?: number): boolean {
        return this.invalidRowUsers.includes(user_ref) || this.timesheetChanges.findIndex(c => c.user_ref === user_ref && c.changed) > -1;
    }

    rowApproveNotAllowed(row) {
        // return row._ts_info.length !== 7 ||
        return this.userRowHasChanges(row.user_ref) ||
            (row._all_ts_approved);
    }

    beforeBulkApprove() {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Confirm',
            title: `You're about to approve ${this.bulkAction.approve.length > 1 ? `${this.bulkAction.approve.length} timesheets` : `1 timesheet`}. Are you sure you would like to continue?`,
            confirmLabel: 'Continue',
            onConfirm: this.bulkApprove.bind(this),
        });
    }

    private bulkApprove() {
        let rows_to_approve = this.bulkAction.approve.reduce((list, user_ref) => {
            let row = this.viewInfo.inductionsPage.records.find(r => r.user_ref === user_ref) || {};
            list.push(...this.getRowsToApprove(row));
            return list;
        }, []);
        console.log('bulk rows to approve', rows_to_approve);
        this.approveTimesheetSets(rows_to_approve);
    }

    toggleApproval(ev, user_ref) {
        if (ev.target.checked) {
            this.bulkAction.approve.push(user_ref);
        } else {
            let i = this.bulkAction.approve.indexOf(user_ref);
            this.bulkAction.approve.splice(i, 1);
        }
        return this.bulkAction.approve;
    }

    selectAllCheckbox(ev) {
        if (ev.target.checked) {
            this.bulkAction.approve = [...new Set(this.viewInfo.inductionsPage.records.filter(ir => !this.rowApproveNotAllowed(ir)).map(ir => ir.user_ref))];
        } else {
            this.bulkAction.approve = [];
        }
    }

    resetBulkApproveMode() {
        this.bulkAction = {
            active: false,
            approve: []
        }
    }

    clearViewChanges() {
        if (this.bulkAction.active && !this.bulkAction.approve.length) {
            this.resetBulkApproveMode();
            return true;
        }
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Cancel',
            title: `Please confirm you would like to clear your selection`,
            confirmLabel: 'Clear',
            onConfirm: () => {
                this.timesheetChanges = [];
                this.refreshValidationStatus();
                this.resetBulkApproveMode();
                this.preCalculateRenderingFields(this.viewInfo.inductionsPage.daily_logs, this.viewInfo.inductionsPage.timesheets);
                // this.viewInfo.inductionsPage.records = [...this.viewInfo.inductionsPage.records];
                this.viewInfo.iRLoading = true;
                setTimeout(() => this.viewInfo.iRLoading = false, 0);
            }
        });
    }

    saveTimesheetChanges() {
        console.log(this.viewInfo.totalChanges);
        console.log(this.timesheetChanges);
        let seconds = this.timesheetChanges.reduce((sum, change) => {
            sum = sum + ((change.day_seconds || 0) + (change.night_seconds || 0));
            return sum;
        }, 0);
        const hr = this.timeUtility.showDurationAsHours(seconds || 0, true);
        console.log('seconds', seconds, 'hrs', hr);
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Confirm',
            title: `You're about to save ${hr} hours. These hours are assumed to be less any breaks. Would you like to save these changes?`,
            confirmLabel: 'Save',
            onConfirm: () => {
                this.saveTsChanges();
            }
        });
    }

    private saveTsChanges() {
        let payload = {
            from_date: this.viewInfo.start.format(AppConstant.apiRequestDateFormat),
            to_date: this.viewInfo.end.format(AppConstant.apiRequestDateFormat),
            timesheets: this.timesheetChanges.map(c => {
                let extra = {};
                if(!TIME_HOUR_STATES_VALUE.includes(c.hours_state)){
                    extra = {
                        travel_seconds: 0,
                        overtime_seconds: 0,
                        training_seconds: 0,
                        manager_auth_seconds: 0,
                        price_work_amount: 0,
                    }
                }
                return {
                    day_of_yr: c.day_of_yr,
                    user_ref: c.user_ref,
                    day_seconds: c.day_seconds,
                    night_seconds: c.night_seconds,
                    hours_state: c.hours_state,
                    ...extra,
                }
            })
        };
        console.log(payload);
        this.savingChanges = true;
        this.projectService.bulkSaveTimesheetInfo(this.projectId, payload).subscribe((data: any) => {
            this.savingChanges = false;
            // console.log(data);
            if (data.success) {
                // @todo: need to see if we can avoid this, otherwise someday, we may get affected due to DB replication lag
                this.loadPage(this.viewInfo.inductionsPage.pageNumber, {
                    sortKey: this.viewInfo.inductionsPage.sortKey,
                    sortDir: this.viewInfo.inductionsPage.sortDir
                }, true);
            } else {
                const message = data.message || 'Failed to save timesheet info.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    private getRowsToApprove(row: TimesheetInductionsRow) {
        let ts_info = (row._ts_info || []);
        console.log(ts_info);
        let weekdays = Object.keys(row._weeks);
        let rows_to_approve = weekdays.map(day => {
            let ts_log_row = ts_info.find(r => r.day_of_yr === day);
            if (ts_log_row) {
                return ts_log_row;
            }
            return {
                day_of_yr: day,
                user_ref: row.user_ref,
                day_seconds: 0,
                night_seconds: 0,
                hours_state: TIME_INPUT_STATE.DAY_HOURS.v
            }
        });
        console.log(`rows_to_approve for user:${row.user_ref}`, rows_to_approve);
        return rows_to_approve;
    }

    approveTimesheet($event: any, row: TimesheetInductionsRow) {
        if (this.userRowHasChanges(row.user_ref)) {
            return false;
        }
        const rows_to_approve = this.getRowsToApprove(row);
        if (!rows_to_approve.length) {
            return false;
        }
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Timesheet Approval',
            title: `You are about to approve the hours for this worker. If the hours are not accurate, please ensure that they are adjusted before approval is provided`,
            confirmLabel: 'Approve',
            onConfirm: () => {
                this.approveTimesheetSets(rows_to_approve);
            }
        });
    }

    private approveTimesheetSets(rows_to_approve) {
        let payload = {
            from_date: this.viewInfo.start.format(AppConstant.apiRequestDateFormat),
            to_date: this.viewInfo.end.format(AppConstant.apiRequestDateFormat),
            timesheets: rows_to_approve.reduce((list, ts) => {
                if (!ts.id) {
                    ts.status = TIMESHEET_STATUS.APPROVED;
                    list.push(ts);
                } else if (ts.status === TIMESHEET_STATUS.PENDING) {
                    list.push({
                        id: ts.id,
                        user_ref: ts.user_ref,
                        status: TIMESHEET_STATUS.APPROVED
                    });
                }
                return list;
            }, [])
        };
        console.log(payload);
        this.savingChanges = true;
        this.projectService.bulkSaveTimesheetInfo(this.projectId, payload).subscribe(this.afterSaving.bind(this));
    }

    unApproveTimesheet($event, row) {
        console.log(`_approved_ts: ${row._approved_ts}`);
        if (!row._approved_ts?.length) {
            return false;
        }
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Confirm',
            title: `You're about to unapprove a timesheet. Are you sure you would like to continue?`,
            confirmLabel: 'Continue',
            onConfirm: () => {
                this.savingChanges = true;
                this.projectService.approveUserTimesheets(this.projectId, {
                    timesheetIds: row._approved_ts,
                    status: TIMESHEET_STATUS.PENDING
                }).subscribe(this.afterSaving.bind(this));
            }
        });

    }

    private afterSaving(data) {
        this.savingChanges = false;
        // console.log(data);
        if (data.success) {
            // @todo: need to see if we can avoid this, otherwise someday, we may get affected due to DB replication lag
            this.resetBulkApproveMode();
            this.loadPage(this.viewInfo.inductionsPage.pageNumber, {
                sortKey: this.viewInfo.inductionsPage.sortKey,
                sortDir: this.viewInfo.inductionsPage.sortDir
            }, true);
        } else {
            const message = data.message || 'Failed to save timesheet info.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
        }
    }


    @ViewChild('viewTimesheetComponent')
    private viewTimesheetComponentRef: ViewTimesheetComponent;

    view($event, row) {
        $event && $event.target && $event.target.closest('datatable-body-cell').blur();
        this.viewTimesheetComponentRef.openTimeSheetModalWithConfig(row);
    }

    dayjs(n?: any) {
        return dayjs(n);
    }

    downloadFilters: {
        userIds?: Array<number>,
        jobRoles?: Array<string>,
        typeOfEmployments?: Array<string>,
        employers?: Array<string>,
        fromDate?: NgbDate;
        toDate?: NgbDate;
        hoveredDate?: NgbDate;
    } = {};
    inductedUsersSubSet: Array<any> = [];


    isInside(date: NgbDate) {
        return date.after(this.downloadFilters.fromDate) && date.before(this.downloadFilters.toDate);
    }

    isRange(date: NgbDate) {
        return date.equals(this.downloadFilters.fromDate) || date.equals(this.downloadFilters.toDate) || this.isInside(date);// || this.isHovered(date);
    }

    isWeekend = (date: NgbDate) => {
        // console.log(date, this.calendar.getWeekday(date));
        return (this.calendar.getWeekday(date) == (this.weekEndsOn));
    }

    isAboveMaxRange(date: NgbDate) {
        let max = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        return (!date.before(max) && !date.equals(max));
    }

    data = (date: NgbDate, current?: { year: number, month: number }) => {
        return this.calendar.getWeekday(date) === (this.weekEndsOn) ? 'font-weight-bold yash' : null;
    }

    onDateSelection(date: NgbDate) {
        const selection = this.ngbMomentjsAdapter.ngbDateToDayJs(date);
        // let selectedDay = selection.day();                              //  0 (Sunday) to 6 (Saturday).
        // the difference between the selected day and it's weekend day
        const daysUntilWeekend = (this.weekEndsOn - selection.day() + 7) % 7;
        // console.log('daysUntilWeekend', daysUntilWeekend)

        // subtract remaining days of week, to get starting point
        const startDate = selection.subtract((7 - daysUntilWeekend), 'day').add(1, 'day');
        // console.log('startOfWeek', startDate);

        const endOfWeek = startDate.clone().add(6, 'day');
        this.downloadFilters.fromDate = this.ngbMomentjsAdapter.dayJsToNgbDate((startDate), false) as NgbDate;
        this.downloadFilters.toDate = this.ngbMomentjsAdapter.dayJsToNgbDate((endOfWeek), false) as NgbDate;
    }

    onMetaFilterChange($event) {
        this.inductedUsersSubSet = this.inductedUsers.filter((ir: any) => (
            // ir.employer &&
            (this.downloadFilters.employers.length === 0 || this.downloadFilters.employers.includes(ir.employer)) &&
            (this.downloadFilters.typeOfEmployments.length === 0 || this.downloadFilters.typeOfEmployments.includes(ir.type_of_employment)) &&
            (this.downloadFilters.jobRoles.length === 0 || this.downloadFilters.jobRoles.includes(ir.job_role))
        ));
        this.downloadFilters.userIds = [];
    }

    private getAllUsersList(cb = () => {}) {
        if (this.inductedUsers.length) {
            return cb();
        }

        this.savingChanges = true;

        this.projectService.getProjectInductedUsersNames(this.projectId, {
            extra: `employment,employment-type`,
            statusCodes: this.visibleInductionStatusCodes
        }).subscribe((data: any) => {
            this.savingChanges = false;
            if (data.success) {
                this.inductedUsers = data.records || [];
                this.inductedUsersSubSet = data.records || [];
                this.typeOfEmployments = [...new Set(this.inductedUsersSubSet.map(ir => ir.type_of_employment).filter(s => s))];
                cb();
            } else {
                const message = data.message || 'Failed to fetch users list.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    @ViewChild('downloadTimesheetFilter') private downloadTimesheetFilter: IModalComponent;

    timesheetFilterModal() {
        this.downloadFilters = {
            employers: [],
            jobRoles: [],
            typeOfEmployments: [],
            userIds: [],
            fromDate: this.ngbMomentjsAdapter.dayJsToNgbDate(this.viewInfo.start) as NgbDate,
            toDate: this.ngbMomentjsAdapter.dayJsToNgbDate(this.viewInfo.end) as NgbDate
        }
        this.getAllUsersList(() => {
            return this.downloadTimesheetFilter.open();
        });
    }

    get disableTimesheetDownload(): boolean {
        return (this.inductedUsersSubSet.length === 0) &&
            !(
            this.downloadFilters.employers?.length === 0 &&
            this.downloadFilters.jobRoles?.length === 0 &&
            this.downloadFilters.typeOfEmployments?.length === 0 &&
            this.inductedUsersSubSet.length > 0
        );
    }

    private hasValidDownloadFilter(){
        return (
            this.downloadFilters.employers.length ||
            this.downloadFilters.jobRoles.length ||
            this.downloadFilters.typeOfEmployments.length
        ) && this.inductedUsersSubSet.length;
    }

    downloadTimesheets({closeFn}) {
        this.savingChanges = true;
        let userIds = [...this.downloadFilters.userIds];
        if(!userIds.length && this.hasValidDownloadFilter()){
            userIds = this.inductedUsersSubSet.map(ir => ir.user_ref);
        }
        this.projectService.downloadTimesheets(this.projectId, {
            from_date: this.ngbMomentjsAdapter.ngbDateToDayJs(this.downloadFilters.fromDate).format(AppConstant.apiRequestDateFormat),
            to_date: this.ngbMomentjsAdapter.ngbDateToDayJs(this.downloadFilters.toDate).format(AppConstant.apiRequestDateFormat),
            userIds: userIds,
        }, () => {
            this.savingChanges = false;
            closeFn && closeFn()
        });
    }


    @ViewChild('amendTsModal') private amendTsModalRef: AmendTimesheetComponent;

    openSplitShiftInput({$timeInput}, row, day_of_yr, week_day_row: TimesheetWeekDay) {
        // $event && $event.target && $event.target.closest('datatable-body-cell').blur();
        let selectedDays = [day_of_yr];
        let selection = {
            hours_state: TIME_INPUT_STATE.SPLIT_SHIFT.v,
            day_seconds: 0,
            night_seconds: 0,
        };
        let index = this.timesheetChanges.findIndex(change => (
            change.user_ref === row.user_ref &&
            change.day_of_yr === day_of_yr
        ));
        if (index > -1) {
            selection.day_seconds = this.timesheetChanges[index].day_seconds;
            selection.night_seconds = this.timesheetChanges[index].night_seconds;
        } else if (week_day_row && week_day_row.timesheet) {
            selection.day_seconds = week_day_row.timesheet.day_seconds;
            selection.night_seconds = week_day_row.timesheet.night_seconds;
        }
        this.amendTsModalRef.open({selectedDays, name: row.name, user_ref: row.user_ref, selection});
    }

    amendUserTs({data, closeFn}) {
        console.log({data});
        if (!data) {
            return false;
        }
        for (let i = 0; i < (data.days || []).length; i++) {
            let inputEvent: TimeInput = new TimeInput();
            inputEvent.changed = true;
            inputEvent.valid = true;
            inputEvent.label = data.label;
            inputEvent.state = data.hours_state;
            inputEvent.value = data.day_seconds + data.night_seconds;
            this.timeInputChanged(inputEvent, data.user_ref, data.days[i], null, data);

            this.viewInfo.inductionsPage.records = this.viewInfo.inductionsPage.records.map(row => {
                if (row.user_ref === data.user_ref) {
                    row._weeks[data.days[i]]._input = {
                        val: inputEvent.value,
                        state: inputEvent.state,
                        changed: inputEvent.changed
                    };
                }
                return row;
            });
        }

        closeFn && closeFn();
    }

    bulkAmend(row) {
        let days = this.viewInfo.weekDays.map(w => w.day_of_yr);
        this.amendTsModalRef.open({
            selectedDays: days, name: row.name, user_ref: row.user_ref, selection: {
                hours_state: TIME_INPUT_STATE.SPLIT_SHIFT.v,
            }
        });
    }


    onActionSelection($event){
        if($event.code === TimesheetActionButtons.BULK_APPROVE){
            this.bulkAction.active = !this.bulkAction.active;
        }
        else if($event.code === TimesheetActionButtons.DOWNLOAD_RECORDS){
            this.timesheetFilterModal();
        }
    }
}
