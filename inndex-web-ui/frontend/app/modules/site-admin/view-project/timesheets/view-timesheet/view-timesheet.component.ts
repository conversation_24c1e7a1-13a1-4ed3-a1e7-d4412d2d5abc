import {Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild} from "@angular/core";
import { AppConstant } from "@env/environment";
import * as dayjs from "dayjs";
import {getCurrencySymbol} from "@angular/common";
import {
    DailyLogForTimesheet,
    ProjectService,
    TIME_INPUT_STATE,
    TIMESHEET_STATUS,
    TimesheetInductionsRow,
    TimeUtility,
    ToastService,
    TimesheetLog,
    Comment, AuthService, User, TIME_HOUR_STATES_VALUE,
} from "@app/core";
import {GenericConfirmationModalComponent, IModalComponent} from "@app/shared";

class TsLog extends TimesheetLog {
    disabled?: boolean;
    changed?: boolean;
    valid?: {
        [key:string]: boolean;
    };
    updated?: {
        [key:string]: boolean;
    };
}

class DefaultTotal {
    effective_time: number = 0;
    day_seconds: number = 0;
    night_seconds: number = 0;
    travel_seconds: number = 0;
    overtime_seconds: number = 0;
    training_seconds: number = 0;
    manager_auth_seconds: number = 0;
    price_work_amount: number = 0;
}

@Component({
    selector: "view-timesheet",
    templateUrl: "./view-timesheet.component.html",
    styleUrls: ["./view-timesheet.component.scss"],
})
export class ViewTimesheetComponent implements OnInit {
    @Input()
    projectId: number;
    @Input()
    currencyCode: string;
    @Input()
    isMobile: boolean;
    @Input()
    tz: string;
    @Input()
    from: string;
    @Input()
    to: string;

    @Output()
    changed: EventEmitter<any> = new EventEmitter<any>();

    modalInfo: {
        ready: boolean;
        groups?: {
            valid: boolean;
            actual: {
                [day_of_yr: string]: DailyLogForTimesheet
            };
            ts: {
                [day_of_yr: string]: TsLog;
            };
            activities: Array<any>;
            comments: Array<any>;
            approved_ts: Array<number>;
            total: DefaultTotal;
        };
    } = {
        ready: false,
    };
    authUser$: User;
    commentNote: string;

    hoursState: number = TIME_INPUT_STATE.DAY_HOURS.v;
    TIME_INPUT_STATE = TIME_INPUT_STATE;
    status = TIMESHEET_STATUS;
    loading: boolean = false;
    processing: boolean = false;
    currencySymbol: string;

    @Input()
    weekDays: Array<{ id: number; label: string; sub_label?: string; day_of_yr?: string; }> = [];
    record: TimesheetInductionsRow;

    dateFormat: string = AppConstant.displayDateFormat;
    dateDisplayFormat = AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS;
    activityLogData = [
    ];
    comments: Array<Comment> = [];

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;

    constructor(
        private authService: AuthService,
        private toastService: ToastService,
        private projectService: ProjectService,
        public timeUtility: TimeUtility,
    ) {}

    ngOnInit(): void {
        // console.log('modal init');
        this.currencySymbol = getCurrencySymbol(this.currencyCode, 'narrow');
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
            }
        });
    }

    viewWeek(day_of_yr: string){
        return dayjs(day_of_yr, AppConstant.apiRequestDateFormat).format(this.dateFormat);
    }

    @ViewChild('messageDetailsHtml') private messageDetailsHtmlGenericModal: IModalComponent;

    openTimeSheetModalWithConfig(param: TimesheetInductionsRow) {
        this.loading = true;
        this.projectService.getUserTimesheetDetails(this.projectId, param.user_ref, {
            from_date: this.from,
            to_date: this.to,
        }).subscribe((data: any) => {
            this.loading = false;
            if (data.success) {
                console.log('data', data);
                this.record = param;
                this.groupInfoForRendering(data.daily_logs, data.timesheets);
                this.deriveComments(data.timesheets);
                this.modalInfo.ready = true;
                this.commentNote = null;
                this.messageDetailsHtmlGenericModal.open();
            } else {
                const message = data.message || 'Failed to get timesheet info.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        })
    }

    private groupInfoForRendering(daily_logs: Array<DailyLogForTimesheet>, timesheets: Array<TimesheetLog>) {
        let groups = this.weekDays.reduce((out, week) => {
            let log = daily_logs.find(l => l.day_of_yr === week.day_of_yr);
            out.actual[week.day_of_yr] = log || {};
            let ts: TsLog = timesheets.find(t => t.day_of_yr === week.day_of_yr);
            if (!ts) {
                ts = {
                    day_of_yr: week.day_of_yr,
                    user_ref: this.record.user_ref,
                    hours_state: TIME_INPUT_STATE.DAY_HOURS.v,
                    day_seconds: 0,
                    night_seconds: 0,
                    travel_seconds: 0,
                    overtime_seconds: 0,
                    training_seconds: 0,
                    manager_auth_seconds: 0,
                    price_work_amount: 0,
                }
            }
            ts.disabled = (ts.status === TIMESHEET_STATUS.APPROVED);
            ts.valid = {};
            ts.updated = {};
            ts.changed = false;
            ts.actual_seconds = (log && log.effective_time) || 0;
            out.ts[week.day_of_yr] = ts;

            if(ts.comments && ts.comments.length){
                out.comments.push(...ts.comments);
            }
            if(ts.change_logs && ts.change_logs.length){
                out.activities.push(...ts.change_logs);
            }
            if(ts.id && ts.status === this.status.APPROVED){
                out.approved_ts.push(ts.id);
            }
            return out;
        }, {
            valid: false,
            actual: {},
            ts: {},
            activities: [],
            comments: [],
            approved_ts: [],
            total: new DefaultTotal(),
        });

        groups.total = this.deriveTotals(groups);
        console.log('group', groups);
        this.modalInfo.groups = groups;
    }

    private deriveTotals(groups): DefaultTotal {
        let total = this.weekDays.reduce((out, week) => {
            let log = groups.actual[week.day_of_yr];
            let ts = groups.ts[week.day_of_yr];
            if (log && log.effective_time) {
                out.effective_time += +log.effective_time;
            }

            if (TIME_HOUR_STATES_VALUE.includes(ts.hours_state) && ts.day_seconds) {
                out.day_seconds += +ts.day_seconds;
            }

            if (TIME_HOUR_STATES_VALUE.includes(ts.hours_state) && ts.night_seconds) {
                out.night_seconds += +ts.night_seconds;
            }
            out.travel_seconds += (+ts.travel_seconds || 0);
            out.overtime_seconds += (+ts.overtime_seconds || 0);
            out.training_seconds += (+ts.training_seconds || 0);
            out.manager_auth_seconds += (+ts.manager_auth_seconds || 0);
            out.price_work_amount += (+ts.price_work_amount || 0);

            return out;
        }, new DefaultTotal());

        // console.log(`totals are`, total);
        return total;
    }

    private deriveComments(list: Array<TimesheetLog> = []){
        const comments = list.reduce((all, ts: TimesheetLog) => {
            if(ts && ts.comments){
                all.push(...ts.comments)
            }
            return all;
        }, []);
        this.comments = comments;
    }

    disableInput(day_of_yr) {
        return (this.modalInfo.groups.ts[day_of_yr]?.hours_state && ![TIME_INPUT_STATE.DAY_HOURS.v, TIME_INPUT_STATE.NIGHT_HOURS.v, TIME_INPUT_STATE.SPLIT_SHIFT.v].includes(this.modalInfo.groups.ts[day_of_yr].hours_state)) //||
            // (!this.modalInfo.groups.ts[day_of_yr]?.hours_state && this.modalInfo.groups.ts[day_of_yr].id && ![TIME_INPUT_STATE.DAY_HOURS.v, TIME_INPUT_STATE.NIGHT_HOURS.v, TIME_INPUT_STATE.SPLIT_SHIFT.v].includes(this.modalInfo.groups.ts[day_of_yr].hours_state));
    }

    timeInputChanged($event, day_of_yr, key){
        const effective_time = this.modalInfo.groups.actual[day_of_yr].effective_time || 0;
        let previous = this.modalInfo.groups.ts[day_of_yr];
        console.log(`input changed`, day_of_yr, key, $event);

        previous = {
            ...previous,
            [key]: $event.value||0,
            changed: true,
            updated: {
                ...(previous.updated || {}),
                [key]: $event.changed,

                // both of below are needed to keep them in parity,
                // when state is switched to Holiday/Sick/Hour in either of input
                day_seconds: true,
                night_seconds: true,
            },
            valid: {
                ...(previous.valid || {}),
                [key]: $event.valid,
            }
        }
        if(key === 'day_seconds' || key === 'night_seconds'){
            previous.hours_state = $event.state;
            if(!TIME_HOUR_STATES_VALUE.includes(previous.hours_state)){
                previous.travel_seconds = 0;
                previous.overtime_seconds = 0;
                previous.training_seconds = 0;
                previous.manager_auth_seconds = 0;
                previous.price_work_amount = 0;
            }

            if([TIME_INPUT_STATE.DAY_HOURS.v, TIME_INPUT_STATE.NIGHT_HOURS.v].includes(previous.hours_state)){
                if(previous.day_seconds && previous.night_seconds){
                    previous.hours_state = TIME_INPUT_STATE.SPLIT_SHIFT.v;
                }
                else if(previous.day_seconds){
                    previous.hours_state = TIME_INPUT_STATE.DAY_HOURS.v;
                }
                else if(previous.night_seconds){
                    previous.hours_state = TIME_INPUT_STATE.NIGHT_HOURS.v;
                }
            }else{
                // empty out Adjusted Hours, when Holiday/Sick is selected
                previous.day_seconds = 0;
                previous.night_seconds = 0;
            }
        }
        console.log(previous);
        this.modalInfo.groups.ts[day_of_yr] = previous;

        this.modalInfo.groups.valid = Object.values(this.modalInfo.groups.ts).findIndex((column:any) => {
            return Object.values(column.valid).findIndex(v => !v) > -1
        }) === -1;
        console.log(this.modalInfo.groups);
        this.modalInfo.groups.total = this.deriveTotals(this.modalInfo.groups);
    }

    onSaveFn({closeFn}){
        let payload = {
            from_date: this.from,
            to_date: this.to,
            timesheets: Object.values(this.modalInfo.groups.ts || {}).reduce((list, c: any) => {
                if(!c.changed){
                    return list;
                }
                let {day_seconds, actual_seconds, night_seconds, hours_state, travel_seconds, overtime_seconds, training_seconds, manager_auth_seconds, price_work_amount} = c;

                list.push({
                    day_of_yr: c.day_of_yr,
                    user_ref: this.record.user_ref,
                    hours_state,
                    actual_seconds,
                    day_seconds, night_seconds,

                    travel_seconds,
                    overtime_seconds,
                    training_seconds,
                    manager_auth_seconds,
                    price_work_amount,
                });
                return list;
            }, []),
        };
        console.log(payload);
        this.processing = true;
        this.projectService.bulkSaveTimesheetInfo(this.projectId, payload).subscribe(this.afterSaving.bind(this, closeFn));
    }

    onUnApproveFn({closeFn}){
        console.log(this.modalInfo.groups?.approved_ts);
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Confirm',
            title: `You're about to unapprove this timesheet. Are you sure you would like to continue?`,
            confirmLabel: 'Continue',
            onConfirm: () => {
                this.processing = true;
                this.projectService.approveUserTimesheets(this.projectId, {timesheetIds: this.modalInfo.groups?.approved_ts, status: TIMESHEET_STATUS.PENDING}).subscribe(this.afterSaving.bind(this, closeFn));
            }
        });
    }

    private afterSaving(closeFn, data){
        this.processing = false;
        console.log(data);
        if (data.success) {
            this.changed.emit({});
            closeFn && closeFn();
            this.modalInfo.ready = false;
        } else {
            const message = data.message || 'Failed to save timesheet info.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
        }
    }

    myCancelFn($event){
        console.log($event)
        this.modalInfo.ready = false;
    }

    dayjsDisplayDateOrTime(n: number, onlyDate = true) {
        return dayjs(n).tz(this.tz).format(onlyDate ? AppConstant.defaultDateFormat : AppConstant.defaultTimeFormat);
    }
    displayUnixTime(n: number| string){
        return n ? dayjs((+n)* 1000).tz(this.tz).format(AppConstant.timFormatWithoutSecs) : '';
    }

    saveTimesheet(){
        let comment = new Comment();
        comment.timestamp = dayjs().valueOf();
        comment.note = this.commentNote;
        comment.user_id = this.authUser$.id;
        comment.name = this.authUser$.name;
        comment.origin = 'admin';
        console.log(this.from, this.to, comment);
        this.processing = true;
        this.projectService.saveTimesheetComment(this.projectId, {
            day_of_yr: this.to,
            user_ref: this.record.user_ref,
            comment: comment,
        }).subscribe((data: any) => {
            this.processing = false;
            if (data.success) {
                this.commentNote = null;
                this.comments = [comment].concat(...(this.comments || []));
                // closeFn && closeFn();
                // this.modalInfo.ready = false;
            } else {
                const message = data.message || 'Failed to save timesheet info.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }
}
