/**
 * Created by spatel on 15/09/19.
 */
import {
    Component,
    ElementRef,
    Inject,
    Input,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { AuthService, Project, ProjectService, ScriptLoaderService, TranslationService } from '@app/core';

import { AppConstant } from '@env/environment';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
declare var mapboxgl: any;

@Component({
    selector: 'heat-map',
    templateUrl: './heat-map.component.html',
    styleUrls: ['./heat-map.component.scss'],
})
export class HeatMapComponent implements OnInit,OnDestroy {
    @Input()
    projectId: Number;

    @Input()
    project: Project;

    @Input()
    dateFilter?: string | boolean = false;

    @Input()
    fullScreenMode?: boolean = false;
    
    @Input()
    isLive?: boolean = false;

    @Input()
    onSiteLiveUsers?:number[] = [];

    @Input()
    style?: any = { minHeight: 400, height: 375 };
    distanceCounts: any = null;
    statsOpened: boolean = true;
    coordinates: any[] = [];
    total: any = 0;
    isProjectPortal:boolean = false;
    employerId:number;
    toolTipInfo = "This heatmap visualises the cumulative percentage distribution of the distances between worker's home addresses and the project site, offering a visual aggregation of how far workers travel to reach the site.";
    subs = new Subscription();

    constructor(
        @Inject(DOCUMENT) private document,
        private projectService: ProjectService,
        private activatedRoute:ActivatedRoute,
        private authService:AuthService,
        private scriptLoaderService: ScriptLoaderService,
        private translationService: TranslationService,
    ) {}
    ngOnDestroy(): void{
        this.subs.unsubscribe()
    }

    ngOnInit(): void {
        let loadScript = this.scriptLoaderService.loadScript('mapbox');
        let loadStyle = this.scriptLoaderService.loadStyle('mapbox-style');
        Promise.all([loadScript,loadStyle]).then(a=>{
            this.initMapBox();
        }).catch(e=>console.log(e));
    }
    keys() {
        return this.distanceCounts && Object.keys(this.distanceCounts);
    }

    getMilesToKm(distanceInMiles) {
        let wind_speed_unit = $localize`:@@windSpeedUnit:wind_speed_unit`;
        if (wind_speed_unit === 'mile/h') {
            return distanceInMiles;
        }

        return this.translationService.milesToKm(distanceInMiles);
    }

    initMapBox() {
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        let {
            custom_field: { location },
        } = this.project || {};
        if(this.isProjectPortal){

             this.subs = this.projectService
                .getHeatmapDataSA(this.project.id,{liveUserIds:this.onSiteLiveUsers,isLive:this.isLive})
                .subscribe((data) => this.saveHeatmapData(data, location));
        }else if(this.authService.isLiveTvLogin()){
            this.subs = this.projectService
                .getHeatmapDataLiveTv(this.project.id,{liveUserIds:this.onSiteLiveUsers,isLive:this.isLive})
                .subscribe((data) => this.saveHeatmapData(data, location));
        }else{
            this.activatedRoute.params.subscribe(params => {
                this.employerId = params['employerId'];
            });
            this.subs = this.projectService
                .getHeatmapDataCA(this.employerId,this.project.id,{liveUserIds:this.onSiteLiveUsers,isLive:this.isLive})
                .subscribe((data) => this.saveHeatmapData(data, location));
        }
    }

    calculateBounds(coordinatesArray,centerLocation): [[number, number], [number, number]] {
        const coordinates= [...coordinatesArray.map(obj => obj.geometry.coordinates), [centerLocation.long, centerLocation.lat]];
    
        // Calculate the bounds
        const bounds = coordinates.reduce((bounds, coord) => {
            return bounds.extend(coord);
        }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));
        return bounds.toArray();
    }

    saveHeatmapData(data,location?){
        if (data && data.coordinates) {
            this.coordinates = data.coordinates;
            this.distanceCounts = data.proximityList;
            this.total = data.total;
            if(document.getElementById('heat-map')){
                this.renderMapbox(location);
            }
        }
    }
    renderMapbox(location){
        const el = document.createElement('div');
        el.className = 'marker';
        el.style.backgroundImage = `url(/assets/images/location_pin.svg)`;
        el.style.width = `40px`;
        el.style.height = `40px`;
        el.style.backgroundRepeat = 'no-repeat';
        el.style.backgroundPosition = "center bottom"
        el.style.transform = "translate(0px --50%) !important";
        if (location && Object.keys(location).length) {
            mapboxgl.accessToken = AppConstant.mapboxglKey;
            let bounds = this.calculateBounds(this.coordinates, location);
            let map = new mapboxgl.Map({
                container: 'heat-map',
                style: 'mapbox://styles/joevousden/clpsfgi4v018001pafdjo4s7c',
                zoom: 7,
                maxZoom: 9,
                minZoom: 4,
                center: [location.long, location.lat],
                bounds:bounds,
            });
            map.zoomOut()
            const popup = new mapboxgl.Popup().setHTML(
                `<b style="padding: 0 15px;">${this.project.name}</b>`,
            );

            const marker = new mapboxgl.Marker(el,{offset:[0,-21]})
                .setLngLat([location.long, location.lat])
                .setPopup(popup)
                .addTo(map);

            map.addControl(new mapboxgl.FullscreenControl());
            
            map.on('load', () => {
                map.resize();

                map.addSource('heatmap', {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: this.coordinates,
                    },
                });
                if(!this.coordinates.length){
                    map.zoomTo(5)
                }
                const metersToPixelsAtMaxZoom = (meters, latitude) =>
                    meters / 0.075 / Math.cos((latitude * Math.PI) / 180);
                let distance = metersToPixelsAtMaxZoom(5 * 1069, location.lat);

                map.addSource('markers', {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features:[1,2,3,4,5,6].map((featureId=>{
                            return {
                                type:'Feature',
                                geometry:{
                                    type:"Point",
                                    coordinates:[location.long, location.lat]
                                },
                                properties:{
                                    modelId: featureId,
                                    circleWidth: featureId > 2 ? (featureId/2) : 1,
                                }
                            }
                        }))
                        
                    },
                });
               
                map.addSource('textLayer', {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: [5,10,20,30,50,100].map((feature,index)=>{
                            return{
                                type: 'Feature',
                                geometry: {
                                    type: 'Point',
                                    coordinates: [
                                        location.long,
                                        location.lat - (feature/2)/ 69,
                                    ],
                                },
                                properties: {
                                    modelId: index+1,
                                },
                            }
                        })
                      
                    },
                });
                this.distanceCounts &&
                    this.keys().map((textLayer, index) => {
                        map.addLayer({
                            id: `text-layer${index}`,
                            type: 'symbol',
                            layout: {
                                'text-field': `${this.getMilesToKm(textLayer)} ${$localize`:@@kms:kms`}`,
                                'text-font': ['Open Sans Regular'],
                                'text-size': [
                                    'interpolate',
                                    ['linear'],
                                    ['zoom'],
                                    2, // Starting zoom level
                                    6, // Initial text size
                                    12, // Maximum zoom level for increasing size
                                    index > 2 ? 22 : 20, // Maximum text size
                                ], // Maximum text size
                                'text-anchor': 'center',
                            },
                            paint: {
                                'text-color': '#000',
                            },
                            source: 'textLayer',
                            filter: ['==', 'modelId', index + 1],
                    });
                });

                [1, 2, 4, 6, 10, 20].map((multiplier, index) => {
                    map.addLayer({
                        id: `circles${index}`,
                        source: 'markers',
                        type: 'circle',
                        paint: {
                            'circle-radius': {
                                stops: [
                                    [0, 0],
                                    [20, distance * multiplier],
                                ],
                                base: 2,
                            },
                            'circle-opacity': 0.23,
                            'circle-color': '#008053',
                            'circle-stroke-width': ['get', 'circleWidth'],
                            'circle-stroke-color': '#008053',
                            'circle-stroke-opacity': 0.4,
                        },
                        filter: ['==', 'modelId', index + 1],
                    });
                });
                map.addLayer(
                    {
                        id: 'earthquakes-heat',
                        type: 'heatmap',
                        source: 'heatmap',
                        maxzoom: 9,
                        paint: {
                            // Increase the heatmap weight based on frequency and property magnitude
                            'heatmap-weight': [
                                'interpolate',
                                ['linear'],
                                ['get', 'mag'],
                                0,
                                0,
                                6,
                                1,
                            ],
                            // Increase the heatmap color weight weight by zoom level
                            // heatmap-intensity is a multiplier on top of heatmap-weight
                            'heatmap-intensity': [
                                'interpolate',
                                ['linear'],
                                ['zoom'],
                                0,
                                1,
                                6,
                                3,
                            ],
                            // Color ramp for heatmap.  Domain is 0 (low) to 1 (high).
                            // Begin color ramp at 0-stop with a 0-transparancy color
                            // to create a blur-like effect.
                            'heatmap-color': [
                                'interpolate',
                                ['linear'],
                                ['heatmap-density'],
                                0,
                                'rgba(33,102,172,0)',
                                0.1,
                                'rgba(0,194,225,0.2)',
                                0.2,
                                'rgba(0,194,225,0.4)',
                                0.5,
                                'rgba(103,164,255,0.5)',
                                0.8,
                                'rgba(103,164,255,0.6)',
                                0.9,
                                'rgba(103,164,255,0.7)',
                                0.99,
                                'rgba(103,164,255,0.8)',
                                1,
                                'rgba(0,26,255,1)', 
                            ],
                            // Adjust the heatmap radius by zoom level
                            'heatmap-radius': [
                                'interpolate',
                                ['linear'],
                                ['zoom'],
                                4,
                                8,
                                9,
                                34,
                            ],
                            // Transition from heatmap to circle layer by zoom level
                            'heatmap-opacity': [
                                'interpolate',
                                ['linear'],
                                ['zoom'],
                                7,
                                1,
                                9,
                                0,
                            ],
                        },
                    },
                    'waterway-label',
                );
                let indexes = [0, 1, 2, 3, 4, 5];
                let zoomlevels = {
                    0: 8.5,
                    1: 8,
                    2: 6,
                    3: 5.5,
                    4: 5.2,
                    5: 4.5,
                };

                map.addLayer(
                    {
                        id: 'earthquakes-point',
                        type: 'circle',
                        source: 'heatmap',
                        minzoom: 7,
                        paint: {
                            // Size circle radius by earthquake magnitude and zoom level
                            'circle-radius': [
                                'interpolate',
                                ['linear'],
                                ['zoom'],
                                7,
                                [
                                    'interpolate',
                                    ['linear'],
                                    ['get', 'mag'],
                                    1,
                                    1,
                                    6,
                                    4,
                                ],
                                16,
                                [
                                    'interpolate',
                                    ['linear'],
                                    ['get', 'mag'],
                                    1,
                                    5,
                                    6,
                                    50,
                                ],
                            ],
                            // Color circle by earthquake magnitude
                            'circle-color': [
                                'interpolate',
                                ['linear'],
                                ['get', 'mag'],
                                1,
                                'rgba(33,102,172,0)',
                                2,
                                'rgb(103,169,207)',
                                3,
                                'rgb(209,229,240)',
                                4,
                                'rgb(253,219,199)',
                                5,
                                'rgb(239,138,98)',
                                6,
                                'rgba(0,26,255,1)',
                            ],
                            'circle-stroke-color': 'white',
                            // Transition from heatmap to circle layer by zoom level
                            'circle-opacity': [
                                'interpolate',
                                ['linear'],
                                ['zoom'],
                                7.5,
                                0,
                                9,
                                1,
                            ],
                        },
                    },
                    'waterway-label',
                );

                map.on('zoom', (a) => {
                    let currentZoom = a.target.getZoom();
                    indexes.map((text) => {
                        if (
                            currentZoom < zoomlevels[text] &&
                            map.getLayer('text-layer' + text)
                        ) {
                            map.setLayoutProperty(
                                'text-layer' + text,
                                'text-size',
                                0,
                            );
                        } else {
                            map.getLayer('text-layer' + text) && map.setLayoutProperty(
                                'text-layer' + text,
                                'text-size',
                                [
                                    'interpolate',
                                    ['linear'],
                                    ['zoom'],
                                    2, // Starting zoom level
                                    6, // Initial text size
                                    12, // Maximum zoom level for increasing size
                                    text > 2 ? 22 : 20, // Maximum text size
                                ],
                            );
                        }
                    });
                });
            });
            map.on('idle', (data) => {
                map.setFog(null);
            })
        }
    }
}
