<!--<div #heatMap [style.min-height.px]="(fullScreenMode ? 390 : style.minHeight)" [style.height.px]="(fullScreenMode ? 390 : style.height)"></div>-->

<div id="heat-map" [style.min-height.px]="(fullScreenMode ? 390 : style.minHeight)" [style.height.px]="(fullScreenMode ? 390 : style.height)">
    <div *ngIf="distanceCounts && total" [ngClass]="{'heatmap-stats':(statsOpened), 'fs-mode': fullScreenMode,'heatmap-stats-collapsed':!statsOpened}">
        <div  [ngStyle]="{'padding':(statsOpened)?'0px':'5px 7px','display':'flex','align-items':'center','justify-content':'space-between','border-bottom':'1px solid lightgray'}">
            <span class="d-flex align-items-center">
                <span class="material-symbols-outlined hamburger" [ngStyle]="{'padding':statsOpened?'5px 0px':'0px'}" (click)="statsOpened = !statsOpened" >menu</span>
                <span *ngIf="statsOpened" style="font-weight: 600;" class="ml-2">Distance from site</span>
            </span>
            <span style="font-size: 20px;" class="icon-help text-right" *ngIf="statsOpened" tooltipClass="custom-tooltip" open [ngbTooltip]="tipContent" placement="bottom"></span>
            <ng-template #tipContent style="font-size: 6px;">{{toolTipInfo}}</ng-template>
        </div>
        <div *ngFor="let item of keys()">
            <div class="horizontal-center" style="gap:5px" *ngIf="statsOpened"> <ngb-progressbar style="width: calc(100% - 32px);" [value]="(distanceCounts[item] / total * 100)">
                <span class="progress-title">within {{getMilesToKm(item)}} <span i18n="@@kms">kms</span></span>
            </ngb-progressbar> <span style="width: 32px; text-align: right;">{{((distanceCounts[item]/total)*100) | number: '1.0-0'}}%</span>
        </div>
    </div> 
</div>
