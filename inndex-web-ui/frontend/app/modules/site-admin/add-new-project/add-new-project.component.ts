/**
 * Created by spatel on 22/9/18.
 */
import {Component, ElementRef, OnInit, AfterViewInit, ViewChild, TemplateRef, HostListener} from '@angular/core';
import {
    Project,
    ProjectService,
    UserService,
    ResourceService,
    AuthService,
    User,
    ProjectGateBooking,
    ProjectGate,
    ToastService,
    ProjectGateBookingService,
    InductionQuestionsService,
    InductionQuestions,
    UserRolePermission,
    TimeUtility,
    ScriptLoaderService,
    ProjectPermit,
    ProjectLocation,
    PermitService, ProjectFeaturePermission, FeatureExclusionUtility,
} from "@app/core";
import { DragulaService } from 'ng2-dragula';
import {ActivatedRoute, Router} from "@angular/router";
import * as dayjs from 'dayjs';
import {Observable, concat, of, Subject, zip} from "rxjs";
import {map, catchError, debounceTime, distinctUntilChanged, switchMap, tap, filter} from "rxjs/operators";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {DomSanitizer} from "@angular/platform-browser";
import {NgbModal, NgbModalConfig} from "@ng-bootstrap/ng-bootstrap";
import {CompanySiteConfigComponent} from "../../company/company-site-configuration/company-site-config.component";
import {DailyActivitiesImportComponent} from "@app/modules/common/daily-activities/daily-activities-import.component";
import {innDexConstant} from "@env/constants";
import {AppConstant} from "@env/environment";
import {CowOwenerTaggingComponent} from "../../company/cow-owener-tagging/cow-owener-tagging.component";
import {CowSiteDrawingsComponent} from "../../company/cow-site-drawings/cow-site-drawings.component";
import {TagOwnerModalComponent} from "../../common/tag-owner-modal/tag-owner-modal.component";
import {ManageCategoriesModalComponent} from "../../common/manage-categories-modal/manage-categories-modal.component";
import {CustomFieldsManagerComponent} from "../../common/custom-fields-manager/custom-fields-manager.component";
import { FormControl, FormGroup, NgForm } from '@angular/forms';
import {PDFDocument, PDFButton, PDFTextField, PDFSignature} from "pdf-lib";
import {AssetsUrl, GenericConfirmationModalComponent, IModalComponent} from '@app/shared';
import {forkJoin} from "rxjs";
import {SupplyChainSelectorComponent} from "../../common/supply-chain-selector/supply-chain-selector.component";

@Component({
    selector: 'add-new-project',
    templateUrl: './add-new-project.component.html',
    providers: [NgbModalConfig, NgbModal],
    styleUrls: ['./add-new-project.component.scss']
})
export class AddNewProjectComponent implements OnInit, AfterViewInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    @ViewChild('supplyChainSelectorComponentRef') private supplyChainSelectorComponentRef: SupplyChainSelectorComponent;
    locales: Array<string> = innDexConstant.locales;
    weekDays: Array<{ id: number; label: string; }> = innDexConstant.weekDays;
    isDeliveryManMenuCollapsed: boolean = true;
    isFatigueManMenuCollapsed: boolean = true;
    isGateBookingMenuCollapsed: boolean[] = [];
    public knownTypeOfWorks: Array<any>;
    public availableTimeZones: Array<any>;

    employers_list: Array<any> = [];
    isSelectedSCValid: boolean = true;
    authUser$: any;
    hasInductionPin: boolean = false;
    projectId: number;
    projectContractor: any = {};
    publishedOnSave: boolean = false;

    project: Project = new Project;
    loadingInProgress: boolean = false;
    formTemplates: Observable<{}>;

    PROJECT_USER_TYPE: Array<any> = [{
        key: 'inductor',
        value: 'Inductor'
    },{
        key: 'nominated',
        value: 'Nominated Manager'
    },{
        key: 'other',
        value: 'Other'
    },];

    PROJECT_VALUE: Array<any> = [];
    PROJECT_TYPES: Array<any> = [];
    isPostcodeRequired: boolean = true;

    // user_fields: Array<any> = [];

    cscsCompetenciesNames = [];
    // ['CSCS', 'CPCS', 'CISRS', 'ECS', 'EUSR', 'NPORS Operator', 'PTS', 'PTS (AC)', 'PTS (DCCR)', 'CCDO', 'SIA - Security Industry Authority', 'FISA - Forest Industry Safety Accord', 'NPTC - National Proficiency Test Council', 'LANTRA - Traffic Management', 'NEBOSH', 'IOSH', 'SSSTS', 'SMSTS', 'SafePass'];
    nonCscsCompetencies: Array<any> = [];
    is_other_doc_required: boolean = false;
    has_competency_exception_list: boolean = false;
    userSearch$: Observable<{} | any[]>;
    emailSearchLoading : boolean = false;
    emailSearchInput$ = new Subject<string>();
    previewURL: any;
    selectedGateDetail: ProjectGate = new ProjectGate;
    timeSlots: Array<string> = [];
    daysInWeek: Array<string> = [
        'Mon',
        'Tue',
        'Wed',
        'Thu',
        'Fri',
        'Sat',
        'Sun'
    ];

    formStages: Array<string> = [
        'Information',
        // 'Select Template',
        'Induction Settings',
        'Access',
        'Site Policies',
        'Declarations',
        'Project Setup',
    ];

    siteMainRisks: Array<string> = [
        "Temporary Works",
        "Confined Spaces",
        "Safe Digging Practices",
        "Traffic & Pedestrian Interface",
        "Isolation & Guarding",
        "Subcontractor Control",
        "Working at Height",
        "Lifting Operations",
        "Occupational Health",
        "Occupational Road Risk"
    ];

    activeStage: number = 0;

    public quillModule = {
        toolbar: [
            [{ 'header': [1, 2, 3, false] }],
            // [{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
            ['blockquote', 'code-block', 'link'],
            // [{ 'header': 1 }, { 'header': 2 }],               // custom button values
            [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'indent': '-1'}, { 'indent': '+1' }],
            // [{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
            [{ 'align': [] }, { 'color': [] }, { 'background': [] }],          // outdent/indent
            // [{ 'direction': 'rtl' }],                         // text direction
            // [],          // dropdown with defaults from theme
            // [{ 'font': [] }],
            // [{ 'align': [] }],
            ['clean'],                                         // remove formatting button
            ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
            // ['link', 'image', 'video']                         // link and image, video
        ]
    };
    /*public editorConfig: Object = {
        toolbarGroups: [
        //{ name: 'document',    groups: [ 'mode', 'document', 'doctools' ] },
        { name: 'clipboard',   groups: [ 'clipboard', 'undo' ] },
        { name: 'editing',     groups: [ 'find', 'selection', 'spellchecker' ] },
        //{ name: 'forms' },
        '/',
        { name: 'basicstyles', groups: [ 'basicstyles', 'cleanup' ] },
        { name: 'tools' },
        { name: 'paragraph',   groups: [ 'list', 'indent', 'blocks', 'align', 'bidi' ] },
        { name: 'links' },
        '/',
        { name: 'insert' },
        { name: 'styles' },
        { name: 'colors' },

        { name: 'others' },
        //{ name: 'about' },
        ],
        removePlugins: `source,sourcearea,pagebreak,about,forms,language,pastefromword,save,newpage,docprops,preview,print,templates,document,a11yhelp,bidi,
        specialchar,scayt,stylescombo,smiley,htmlwriter,iframe,image,filebrowser,flash`,
        //uiColor: '#162734',
    };*/

    DEFAULT_DECLARATION: string = `I confirm that I have reviewed all information provided in my profile and on this form and agree it is correct and up to date.`;

    site_hours_daily_status: boolean = false;
    total_hours_daily_status: boolean = false;
    site_hours_weekly_status: boolean = false;
    total_hours_shifts_status: boolean = false;
    total_duty_periods_biweekly_status: boolean = false;
    delivery_managers: Array<UserRolePermission> = [];
    fatigue_managers: Array<any> = [];

/*    has_c_lens_policy: boolean = false;
    c_lens_policy: any = {
        order: 0,
        key:'c_lens_policy',
        policy_name: "Contact Lens Policy",
        policy: '', //`In the Railway environment and for personal track safety (PTS), it is essential that any personnel who wear contact lenses must abide by the following directive.\\n\\nA pair of prescription spectacles of equal strength and clarity must be carried at all times. This enables the operative to carry on working safely if a contact lens is lost or damaged.\\nAll contact lens wearers must make themselves known to the Controller of Site Safety / Safe Work Leader (COSS/SWL) at the pre-work briefing. Failure to bring along the requisite pair of spectacles will result in the operative not being allowed on site.`,
        policy_ref: [],
        is_default: true,
        is_text: true
    };*/

    has_d_and_a_policy: boolean = false;
    d_and_a_policy: any = {
        order: 1,
        key:'d_and_a_policy',
        policy_name: "Drug and Alcohol Policy",
        policy: ``,
        policy_ref: [],
        is_default: true,
        is_text: true
    };

    has_working_hr_agreement: boolean = false;
    working_hr_agreement: any = {
        order: 2,
        key:'working_hr_agreement',
        policy_name: "Working hours agreement",
        policy: '', //`I agree I have watched and understood all information displayed in the video / slide show provided`,
        policy_ref: [],
        is_default: true,
        is_text: true
    };
    selectedPolicy: any = {};
    policyIndex: any = null;
    quizSets: Array<InductionQuestions> = [];
    additionalQSets: Array<InductionQuestions> = [];
    cowDefaultPhrase: string = "Clerk of Works";
    tooltipPlacement: string = 'left-top';
    has_cow_phrase: boolean = false;
    qclDefaultPhrase: string = "ITPs";
    users_employer: Array<any> = [];
    fatigueManagerTooltip: string = 'Assign fatigue managers who will be notified of project fatigue breaches.';
    enableDivisions: boolean = false;
    divisionsList: any = [];
    contractorName: string = 'company';
    featureSettingToolTipForLockOn:string = 'This tool is currently mandated for all company projects. To disable it, please reach out to your teams innDex champion';
    featureSettingToolTipForLockOff:string = 'This tool is currently unavailable for all company projects. To enable it, please reach out to your teams innDex champion';
    alternativeUserListTooltip: string = 'As a default, to take a register of participants the list of users comprises of anyone clocked in on the project. As an alternative, it can be set so that the list of users comprises of all users who have been inducted on the project rather than clocked in.';
    deliveryManagerTooltip: string = 'The project delivery manager/s will be notified when a booking is made, amended or deleted and will have the ability to approve bookings. The delivery manager also has the ability to amend or delete bookings.';
    focusElementRef: string;
    dayjs = dayjs;
    ramsAssessmentFormFields: Array<any> = [];
    ramsAssessmentSelectField: any = {};
    ramsAssessmentFormFieldTypes: Array<object> = [{"label":"Autofill Field","key":"data_field"},{"label":"Date Selector","key":"date"},{"label":"Date/Time Selector","key":"datetime_selector"},{"label":"Dropdown","key":"selectbox"},{"label":"Multi-Select List","key":"multi_select"},{"label":"Multiline Textbox","key":"textarea"},{"label":"Number","key":"number"},{"label":"Radio Y/N","key":"radio_yn"},{"label":"Radio Y/N/NA","key":"radio_ynna"},{"label":"Textbox","key":"textbox"},{"label":"Time Selector","key":"time_selector"},{"label":"Signature","key":"signature"},{"label":"eSignature","key":"esignature"}];
    predefinedDataFields: Array<object> = [{"label":"Date Submitted","key":"rams_submitted_at"},{"label":"Date/Time Submitted","key":"datetime_of_submit"},{"label":"Project Name","key":"project_name"},{"label":"Project/Contract Number","key":"project_number"},{"label":"RAMS Title","key":"rams_title"},{"label":"Reference Number","key":"reference_number"},{"label":"Revision","key":"rams_revision"},{"label":"Site Postcode","key":"project_postcode"},{"label":"Status","key":"rams_status"},{"label":"Submitted By","key":"rams_submitted_by"}];
    rams_assessment_file: any;
    rams_assessment_ref = null;
    rams_assessment_url = null;
    fontSizeOptions: Array<number> = [];
    rams_assessment_font_size: number = 10;
    briefing_signature_labels: Array<any> = [
        {id: 0, title: 'Signature not required'},
        {id: 1, title: 'Signature optional'},
        {id: 2, title: 'Signature mandatory'}
    ]
    cscs_require_msg: string;
    // disable_company_sc_edit:boolean = false;
    company_sc_setting: {
        supply_chain: Array<number>;
        pre_select: boolean;
        active_for_all_projects: boolean;
        // restricted_to_sc_only: boolean;
        allowed_to_edit: boolean;
    };
    countries: any[] = [];
    uk_districts = [];
    meta_districts:any = [];
    projectPermitTemplates: Array<any> = [];
    showPermitConfig: boolean = false;
    projectPermits: Array<ProjectPermit> = [];
    selectPermitIndex: number;
    processingRequest: boolean = false;
    masterPermitManagers: Array<number> = [];
    selectedPermitTemplate: any = {};
    contractorInfo: any = {};
    activePermits: Array<number> = [];
    prevDefBookingSlot: number = null;
    isProjectAdminV1: boolean = false;
    showRouteMapModal: boolean = false;
    isPdfDocument: boolean = false;
    observationAdditionalCategories: Array<{
        "name": string,
        "is_active": boolean
    }> = [];
    projectFeaturePermission = new ProjectFeaturePermission();

    @ViewChild('infoForm', { static: true }) infoForm: NgForm;
    @ViewChild('mediaForm', { static: false }) mediaForm: NgForm;
    @ViewChild('accessForm', { static: false }) accessForm: NgForm;
    @ViewChild('policiesForm', { static: true }) policiesForm: NgForm;
    @ViewChild('declarationForm', { static: true }) declarationForm: NgForm;
    @ViewChild('addOnsForm', { static: true }) addOnsForm: NgForm;
    signatureTypes: Array<string> = ['signature', 'esignature'];
    optionFieldIndex = -1;
    permitHasCloseout: boolean = false;
    postcodeInput: any = {};

    constructor(
        private elementRef:ElementRef,
        private router: Router,
        private route: ActivatedRoute,
        private projectService: ProjectService,
        private userService: UserService,
        private toastService: ToastService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private resourceService: ResourceService,
        private authService: AuthService,
        private sanitizer: DomSanitizer,
        private modalService: NgbModal,
        private projectGateBookingService: ProjectGateBookingService,
        private inductionQuestionsService: InductionQuestionsService,
        private dragulaService: DragulaService,
        private timeUtility: TimeUtility,
        private scriptLoaderService: ScriptLoaderService,
        private permitService: PermitService,
        private featureExclusionUtility: FeatureExclusionUtility,
    ) {
        for (let i = 8; i <= 20; i++) {
            this.fontSizeOptions.push(i);
        }
        this.PROJECT_VALUE = this.projectService.PROJECT_VALUE;
        this.PROJECT_TYPES = this.projectService.PROJECT_TYPES;
        dragulaService.destroy('additional');
        dragulaService.createGroup('additional', {
            moves: (el) => {
                //Disabling drag for default element
                if(el.children && el.children.length && el.children[0].id == 'rowId_0') {
                    console.log("Cancel Dragging.");
                    return false;
                }
                return true;
            },
            accepts: (el, target, source, sibling) => {
                //Disabling drop for default element
                if(sibling.children && sibling.children.length && sibling.children[0].id == 'rowId_0') {
                    return false;
                }
                return true;
            }
        });

        this.resourceService.getMetaDistrict().subscribe((data: any) => {
            if (data.success && data.meta_districts) {
                this.meta_districts = data.meta_districts;
            } else {
                const message = 'Something went wrong while fetching meta uk districts.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    addQuizRow(data_type, existingCount = 0) {
        let row = {
            induction_questions: [],
            induction_answers: [],
            status: true,
            data_type: data_type,
            ...(data_type === 'question' ? {section_title: 'Induction Questions'} : {}),
            ...(existingCount === 0 ? {lang: innDexConstant.defaultLocale, is_default : true} : {}),
        };
        if (data_type === 'question') {
            row.induction_questions.push({
                "question": '',
                "q_id": 1,
                "ans_field_type": '',
                "is_mandatory": true,
            });
        } else {
            row.induction_questions.push({
                id: Date.now(),
                question: '',
                multichoice: false,
                options: []
            });
        }
        data_type === 'question' ? this.additionalQSets.push(row) : this.quizSets.push(row)
    }

    getAlreadySelectedQLang(skipIndex){
        return this.quizSets.filter((q, i) => i !== skipIndex && q.lang).map(q => q.lang);
    }

    getAlreadySelectedIQLang(skipIndex){
        return this.additionalQSets.filter((q, i) => i !== skipIndex && q.lang).map(q => q.lang);
    }

    removeElement(index, data_type){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you want to delete this question set?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                if(data_type === 'question'){
                    this.additionalQSets.splice(index, 1);
                }else{
                    this.quizSets.splice(index, 1);
                }
            }
        });
    }

    ngAfterViewInit() {
        let loadScript = this.scriptLoaderService.loadScript('quill');
        let loadStyle = this.scriptLoaderService.loadStyle('quill-style');
        Promise.all([loadScript,loadStyle]).then(a=>{
            // let q = new Quill('#d_and_a_policy-editor');
            // console.log(q);
        }).catch(e=>console.log(e));
        // let s = document.createElement("script");
        // s.type = "text/javascript";
        // // s.src = "https://cdn.ckeditor.com/4.14.1/full/ckeditor.js";
        // this.elementRef.nativeElement.appendChild(s);

        // this.calculateVisibleItems();
        // window.addEventListener('resize', () => {
        //     this.calculateVisibleItems();
        // });
    }

    @HostListener('window:resize', ['$event'])
    onResize() {
        this.tooltipPlacement = window.innerWidth < 1230 ? 'top-left' : 'left-top';
    }

    ngOnInit(): void {
        this.onResize();
        this.project = {...this.project, main_contact_number_obj: {code: null, number: ''}}
        this.projectId = +this.route.parent?.snapshot.params['projectId'] || undefined;
        if(!this.project.custom_field.location){
            this.project.custom_field.location = {
                lat:null,
                long:null,
                region:'',
                country:'',
                admin_district:'',
            }
        }
        if(this.projectId){
            this.initialize();
        }else{
            this.project.custom_field.country_code = AppConstant.defaultCountryCode;
            this.getNonCscsCompetenciesList();
            this.getEmployersList();
            this.postcodeInput = this.featureExclusionUtility.showProjectPostalCode();
            console.log('this.postcodeInput', this.postcodeInput);
        }
        this.route.queryParams
            .subscribe(params => {
                this.publishedOnSave = params['publish'] === 'true' || undefined;
            });
        this.formTemplates = this.projectService.getFormTemplates().pipe(map(data => {
            return data.form_templates || [];
        }));

        if(!this.knownTypeOfWorks){
            this.initializeData();
        }
        if(!this.availableTimeZones){
            this.initializeTimezoneData();
        }
        if(!this.project.further_policies || !this.project.further_policies.length) {
            // no need to auto enabled these policies
            this.project.further_policies = [/*this.c_lens_policy, this.d_and_a_policy, this.working_hr_agreement*/];
        }
        if(!this.project.admins){
            this.project.admins = [];
        }

        if(!this.project.project_gates){
            this.project.project_gates = [];
        }

        if(this.project.project_gates.length === 0){
            this.project.project_gates.unshift({
                gate_name: '',
                time_slot: {}
            });
        }

        if(!this.project.project_section_access || !(Object.keys(this.project.project_section_access).length)) {
            this.project.project_section_access = {
                close_calls:false,
                take_5s:false,
                toolbox_talks:false,
                progress_photos:false,
                delivery_notes:false,
                good_calls:false,
                site_messaging:false,
                clerk_of_works:false,
                task_briefings:false,
                inspection_tour: false,
                powra: false,
                asset_vehicles: false,
                work_package_plan: false,
                rams: false,
                asset_equipment: false,
                quality_checklist: false,
                ib_checklist: false,
            }
        }

        this.authService.authUser.subscribe( (user:User) => {
            if(user && user.id){
                this.authUser$ = user;
                if(this.project.admins.length === 0 && !this.projectId){
                    this.project.admins.unshift({
                        _email: this.authUser$.email,
                        _name: this.authUser$.name,
                        user_ref:this.authUser$.id,
                        designation: [],
                        permission: [],
                        _isValid: true,
                        _is_admin: true
                    });
                }
            }
        });



        //delivery managers
        this.getDeliveryManagers();
        this.getFatigueManagers();
        console.log("induction questions get");
        this.getInductionQuestions();
        this.loadRamsAssessmentForm();
        if(!this.project.custom_field.maximum_booking_status) {
            this.project.custom_field.maximum_booking_time = '00:30 minutes';
        }
        this.prevDefBookingSlot = this.project.custom_field.default_booking_slot;
        this.timeSlots = this.timeUtility.generateTimeSlots(this.project.custom_field.default_booking_slot);
        this.initializeTypeAhead();
    }

    get isEndDateValid() {
        if (this.project.start_date && this.project.end_date) {
            const endDate = this.ngbMomentjsAdapter.toModel(this.project.end_date);
            const startDate = this.ngbMomentjsAdapter.toModel(this.project.start_date);
            return endDate ? endDate.isAfter(startDate) : false;
        }
        return true;
    }

    trackByUsersListFn(item: any) {
        return item.id;
    }

    private initializeTypeAhead() {
        this.userSearch$ = concat(
            of([]), // default items
            this.emailSearchInput$.pipe(
                filter(Boolean),
                distinctUntilChanged(),
                debounceTime(200),
                tap(() => this.emailSearchLoading = true),
                switchMap(term => this.projectService.getUserInfo(<string>term, true).pipe(
                    map((data: any) => {
                        return data.userInfo?.id ? [data.userInfo] : [{name: 'No records found', disabled: true}];
                    }),
                    catchError(() => of([])), // empty list on error
                    tap(() => this.emailSearchLoading = false)
                ))
            )
        );
    }

    initialize() {
        this.loadingInProgress = true;
        forkJoin([
            this.projectService.siteAdmin_getProject(this.projectId, 'true', true, true, false, false, true),
            this.resourceService.getProjectSettingsByName(['uk_districts', 'observation_setting'], this.projectId)
        ]).subscribe((responseList:any) => {
            let errorKey = Object.keys(responseList).find(key => !responseList[key].success);
            if(responseList[errorKey]){
                const message = responseList[errorKey].message || 'Failed to get data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: responseList[errorKey] });
                return ;
            }

            let data = responseList[0];
            this.contractorInfo = data.contractor || {};
            if(data.project && data.project.id) {
                this.uk_districts = (responseList[1].project_settings && responseList[1].project_settings.uk_districts) || {};
                let observationCategoryOutput = (responseList[1].project_settings && responseList[1].project_settings.observation_setting) || {};
                this.observationAdditionalCategories = (observationCategoryOutput.categories || []);
                if(!this.project.custom_field){
                    this.project.custom_field = {};
                }
                if(!this.project.custom_field.country_code){
                    // Default for existing live projects
                    this.project.custom_field.country_code = AppConstant.defaultCountryCode;
                    this.postcodeInput = this.featureExclusionUtility.showProjectPostalCode();
                }
                this.postcodeInput = this.featureExclusionUtility.showProjectPostalCode(data.project.custom_field.country_code);
                data.project.has_default_time = !!data.project.default_in_duration;
                if(data.project.start_date) {
                    data.project.start_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.project.start_date));
                }

                if(data.project.end_date) {
                    data.project.end_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(+data.project.end_date));
                }

                if(data.project.other_doc_required && data.project.other_doc_required.length){
                    this.is_other_doc_required = true;
                }
                if(data.project.competency_exception_list && data.project.competency_exception_list.length){
                    this.has_competency_exception_list = true;
                }

                if(!data.project.custom_field.qcl_phrase) { data.project.custom_field.qcl_phrase = this.qclDefaultPhrase;}
                this.hasInductionPin = data.project.induction_pin == null? false :true;

                this.project = data.project;
                this.loadingInProgress = false
                this.isProjectAdminV1 = this.authService.isProjectAdminV1(this.authUser$, this.project);

                this.project._has_quiz_questions = !!(this.quizSets || []).find(quiz => quiz.status);
                this.project._has_additional_questions = !!(this.additionalQSets || []).find(q => q.status);
                if(this.project.has_media_content){
                    // Adding empty media list when not already there.
                    this.toggleMediaTab({target: {checked: true}});
                }
                if(data.project.logo_file_id && data.project.logo_file_id.id){

                    this.project.logo_file_url = data.project.logo_file_id.file_url;
                    this.project.logo_file_id = data.project.logo_file_id.id;
                }
                if(data.contractor.divisions && data.contractor.divisions.length) {
                    this.divisionsList = data.contractor.divisions;
                    this.enableDivisions = true;
                }

                this.userService.getProjectAdmins(this.projectId, false, true).subscribe((data:any) =>{
                    if(data && data.admins) {
                        this.project.admins = (data.admins || [])
                            .sort((a, b) => {
                                let a_is_default = Boolean(a.flags && a.flags.is_default);
                                let b_is_default = Boolean(b.flags && b.flags.is_default);
                                return a_is_default.toString().toLowerCase().localeCompare(b_is_default.toString().toLowerCase());
                            })
                            .sort((a, b) => a.id - b.id);

                        let userIndex = this.project.admins.findIndex(user => ((user.user_ref && user.user_ref.id) || user.user_ref) === this.authUser$.id);
                        if(userIndex > 0) {

                            let tempVar = this.project.admins[userIndex];
                            this.project.admins.splice(userIndex, 1);
                            //place admin user at first place
                            tempVar._is_admin = true;
                            this.project.admins.unshift(tempVar);
                        }
                    } else {
                        const message = data.message || 'Failed to get list of admins.';
                        this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                    }
                });

                if(this.project.project_gates.length === 0){
                    this.project.project_gates.unshift({
                        gate_name: '',
                        time_slot: {}
                    });
                }

                if(!this.project.project_section_access || !(Object.keys(this.project.project_section_access).length)) {
                    this.project.project_section_access = {
                        close_calls:false,
                        take_5s:false,
                        toolbox_talks:false,
                        progress_photos:false,
                        delivery_notes:false,
                        good_calls:false,
                        site_messaging:false,
                        clerk_of_works:false,
                        task_briefings:false,
                        inspection_tour: false,
                        powra: false,
                        asset_vehicles: false,
                        work_package_plan: false,
                        rams: false,
                        asset_equipment: false,
                        quality_checklist: false,
                        ib_checklist: false,
                    }
                }

                let fatigueStatus = this.project.fatigue_management_status;

                this.site_hours_daily_status = this.project.site_hours_daily && fatigueStatus? true : false;
                this.total_hours_daily_status = this.project.total_hours_daily && fatigueStatus? true : false;
                this.site_hours_weekly_status = this.project.site_hours_weekly && fatigueStatus? true : false;
                this.total_hours_shifts_status = this.project.total_hours_shifts && fatigueStatus? true : false;
                this.total_duty_periods_biweekly_status = this.project.total_duty_periods_biweekly && fatigueStatus? true : false;

                this.getEmployersList();
                //get delivery managers from project users list
                this.getDeliveryManagers();
                this.getFatigueManagers();

                //filter default policies
                this.getDefaultPolicies();

                this.getNonCscsCompetenciesList();
                //get companies of users who have had approved induction on the project
                this.getInductionsUsersEmployer();
                if(!this.project.custom_field.maximum_booking_status) {
                    this.project.custom_field.maximum_booking_time = '00:30 minutes';
                }
                this.prevDefBookingSlot = this.project.custom_field.default_booking_slot;
                this.timeSlots = this.timeUtility.generateTimeSlots(this.project.custom_field.default_booking_slot);
                if(!this.project.custom_field.briefing_signatures) {
                    this.project.custom_field.briefing_signatures = {
                        toolbox_talks: 1,
                        task_briefings: 1,
                        rams: 2,
                        work_package_plans: 1,
                        take_5s: 1
                    }
                }
            }
        })
    }

    private getNonCscsCompetenciesList(){
        this.resourceService.getCompetencies({country_code: (this.project?.custom_field?.country_code || undefined)}).subscribe((data: any) => {
            if(data && data.success) {
                this.nonCscsCompetencies = (data.competencieslist || []).filter(item => (!item.is_master));
                let mandatory_docs_list = (data.competencieslist || []).filter(item => (item.tags && item.tags.includes('mandatory-competency')));

                // this variable is not in use
                this.cscsCompetenciesNames = mandatory_docs_list.map(d => d.name);

                let mandatory_doc_groups = mandatory_docs_list.reduce((groups, item) => {
                    if(!groups[item.category]){
                        groups[item.category] = [];
                    }
                    groups[item.category].push(item);
                    return groups;
                }, {});
                let names = Object.keys(mandatory_doc_groups).reduce((list, category) => {
                    let group_master = (mandatory_doc_groups[category] || []).find(d => d.is_master);
                    if(group_master){
                        list.push(group_master.name);
                    }else{
                        list.push(...(mandatory_doc_groups[category] || []).map(d => d.name));
                    }
                    return list;
                }, []);

                names = [...new Set(names)].sort((a, b) => a.localeCompare(b));

                this.cscs_require_msg = `${names.join(', ')}`;
            } else {
                const message = data.message || 'Failed to fetch competencies meta.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    toggleHasCompetencyExclusion($event){
        this.has_competency_exception_list = $event.target.checked;
        this.project.competency_exception_list = [];
    }

    toggleOtherCompetenciesRequired($event){
        // console.log('this.is_other_doc_required', this.is_other_doc_required, $event.target.checked);
        // console.log($event.target.checked);
        if ($event.target.checked) {
            this.is_other_doc_required = true;
            if(!this.project.other_doc_required || !this.project.other_doc_required.length){
                this.project.other_doc_required = [];
            }
        } else {
            this.is_other_doc_required = false;
            this.project.other_doc_required = [];
        }
    }

    getInductionQuestions() {
        this.quizSets = [];
        this.additionalQSets = [];
        if(this.projectId) {
            this.inductionQuestionsService.getAllQuestionsOfProject(this.projectId).subscribe((res1: any) => {
                if(res1.success){
                    this.quizSets = res1.quiz || [];
                    this.additionalQSets = res1.question || [];
                    this.project._has_quiz_questions = !!this.quizSets.find(quiz => quiz.status);
                    this.project._has_additional_questions = !!this.additionalQSets.find(q => q.status);
                }
            });
        }
    }


    getDeliveryManagers() {
        let delivery_managers = [];
        this.project.admins.filter(pu => {
            if (pu.designation && pu.designation.includes('delivery_management') && pu.flags && pu.flags.is_delivery_manager) {
                pu._isValid = true;
                delivery_managers.push(pu);
            }
        });
        this.delivery_managers = [...delivery_managers];
    }

    getFatigueManagers() {
        let f_managersIds = this.project.custom_field['fatigue_managers'] || [];
        let f_managers = [];
        if(f_managersIds && f_managersIds.length){
            this.userService.getUsersById([...f_managersIds], ['email', 'id', 'first_name', 'last_name']).subscribe((data: any) => {
                if (data.success && data.users) {
                    this.fatigue_managers = data.users || [];
                    this.fatigue_managers = this.fatigue_managers.map(pu => {
                        pu._isValid = true;
                        return pu;
                    });
                }
            });
        }
        this.fatigue_managers = [...f_managers];
    }

    toggleDeliveryManMenu(event) {
        event.stopPropagation();
        this.isDeliveryManMenuCollapsed = !this.isDeliveryManMenuCollapsed;
        if(this.isDeliveryManMenuCollapsed) {
            this.delivery_managers = (this.delivery_managers || []).filter(obj => obj.hasOwnProperty('_isValid'));
        }
    }

    toggleFatigueManMenu(event) {
        event.stopPropagation();
        this.isFatigueManMenuCollapsed = !this.isFatigueManMenuCollapsed;
        if(this.isFatigueManMenuCollapsed) {
            this.fatigue_managers = (this.fatigue_managers || []).filter(obj => Object.keys(obj).length > 0);
        }
    }

    handleSCChange(updatedSC) {
        this.project.custom_field.supply_chain_companies = updatedSC;
    }

    getEmployersList(showLoading = false){
        let country_code = this.project.custom_field.country_code;
        if(showLoading){
            this.loadingInProgress = true;
        }
        this.userService.getEmployer(country_code, false, true).subscribe((data: any) => {
            if(showLoading){
                this.loadingInProgress = false;
            }
            if(data.employerlist){
                this.employers_list = data.employerlist;
                this.projectContractor = (this.employers_list || []).find(company => company.name === this.project.contractor);
                (this.projectContractor && this.projectContractor.id) && this.getCompanySupplyChainConf();
                (this.projectContractor && this.projectContractor.id) && this.initToolsSetting();
            }else{
                const message = data.message || 'Failed to get employer data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    getInductionsUsersEmployer() {
        this.userService.getProjectInductionsUsersEmployer(this.projectId, 2).subscribe((data: any) => {
            if(data.users_employer){
                this.users_employer = data.users_employer;
            } else {
                const message = data.message || 'Failed to get companies of the users who have had an approved induction on the project.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    isNotThisStage(label){
        let index = this.formStages.findIndex(v => v === label);
        return this.activeStage !== index
    }

    movePrev(){
        if (this.activeStage === 0) { // i would become 0
            this.activeStage = this.formStages.length; // so put it at the other end of the array
        }
        this.activeStage = this.activeStage - 1; // decrease by one
        this.formStages[this.activeStage]; // give us back the item of where we are now
        window.scroll({ top: 0, left: 0 }); // Scroll to top on every Route previous btn click
    }

    moveNext(){
        this.activeStage = this.activeStage + 1; // increase i by one
        this.activeStage = this.activeStage % this.formStages.length; // if we've gone too high, start from `0` again
        this.formStages[this.activeStage]; // give us back the item of where we are now
        window.scroll({ top: 0, left: 0 }); // Scroll to top on every Route Next btn click
    }

    public goToStep(item: string, step: number): void {
        if(this.disableNextStep(item, step)) return;
        this.activeStage = step;
        window.scroll({ top: 0, left: 0 });
    }

    public disableNextStep(item: string, index: number): boolean {
        if(!this.isNotThisStage(item)){
            return false;
        }
        // maintain the form array order to disable next header steps
        const forms = [this.infoForm, this.mediaForm, this.accessForm, this.policiesForm, this.declarationForm];
        return index === 0 ? false : forms.slice(0, index).some(form => !!form?.invalid);
    }

    getKeys(obj) {
        return Object.keys(obj);
    }

    changeModel(ev, list, val) {
        if (ev.target.checked) {
            list.push(val);
        } else {
            let i = list.indexOf(val);
            list.splice(i, 1);
        }
        return list;
    }

    checkIfFatigueMgrAlreadyExist(source, index, emailText) {
        console.log("in fatigue duplicate check", emailText, this.fatigue_managers);
        let list = this.fatigue_managers;
        let alreadyRecord = (list || []).find((r, i) => i !== index && ((r.id ? (r.id && r.email) : r.email) || '').toLowerCase() === emailText.toLowerCase());
        if (alreadyRecord) {
            const message = `Looks like you are making duplicate entry for ${emailText}, please check.`;
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }
        return true;
    }

    checkIfAlreadyExist(source, index, emailText) {
        console.log('emailText', emailText, 'index', index);
        let list = source === 'admins' ? this.project.admins : this.delivery_managers;
        if(source === 'admins'){
            list = this.project.admins;
        }else if(source === 'delivery_managers'){
            list = this.delivery_managers;
        } else if(source === 'fatigue_managers') {
            list = this.fatigue_managers;
        }
        let alreadyRecord = (list || []).find((r, i) => i !== index && ((r.id ? (r.user_ref && r.user_ref.email) : r._email) || '').toLowerCase() === emailText.toLowerCase());
        if (alreadyRecord) {
            const message = `Looks like you are making duplicate entry for ${emailText}, please check.`;
            this.toastService.show(this.toastService.types.INFO, message);
            return false;
        }
        return true;
    }

    validityChanged($event, source, index) {
        if(source === 'admins'){
            this.project.admins[index]._isValid = $event;
        }else if(source === 'delivery_managers'){
            this.delivery_managers[index]._isValid = $event;
        } else if(source === 'fatigue_managers') {
            this.fatigue_managers[index]._isValid = $event;
        }
    }


    onProjectAdminsChanged({isValid, admins, removed}){
        console.log('$event', {isValid, admins, removed});
        this.project.admins = admins;
        if(removed){
            this.getDeliveryManagers();
        }
    }

    addFurtherPolicyRow(){
        if(!this.project.further_policies){
            this.project.further_policies = [];
        }

        this.project.further_policies.push({
            order: 4,
            key: null,
            policy_name: "",
            policy: null,
            policy_ref: [],
            is_default: false,
            is_text: true
        });
    }

    removeFurtherPolicyRow(e: any, i: number){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Do you really want to delete this?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.project.further_policies.splice(i, 1);
            }
        });
    }

    trackByRowIndex(index: number, obj: any){
        return index;
    }

    addDeclarationRow(){
        if(!this.project.declarations){
            this.project.declarations = [];
        }
        this.project.declarations.push({
            content: ""
        });
    }

    removeDeclarationRow(i: number){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Do you really want to delete this?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.project.declarations.splice(i, 1);
            }
        });
    }

    addClockInDeclarationRow(){
        if(!this.project.custom_field || !this.project.custom_field.clock_in_declarations){
            this.project.custom_field = {
                ...(this.project.custom_field || {}),
                clock_in_declarations: []
            };
        }
        this.project.custom_field.clock_in_declarations.push({
            key: (new Date()).getTime(),
            message: "",
            answer_type: 'y-n'
        });
    }

    removeClockInDeclarationRow(i: number){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Do you really want to delete this?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.project.custom_field.clock_in_declarations.splice(i, 1);
            }
        });
    }

    toggleMediaTab($event){
        if ($event.target.checked) {
            if(!this.project.media_resources || !this.project.media_resources.length){
                this.addMediaResource();
            }
        } else {
            this.project.has_html_media = false;
            this.project.html_media_url = null;
            this.project.media_resources = [];

        }
    }

    isAlreadySelectedMLang(skipIndex, lang, type) : boolean{
        return (this.project.media_resources || []).findIndex((q, i) => i !== skipIndex && q.type === type && q.lang === lang) !== -1;
    }

    allMediaNotDefined(type){
        let typeBasedMedia = (this.project.media_resources || []).filter(m => m.type === type);
        return (this.locales.length > typeBasedMedia.length);
    }

    addMediaResource(){
        if(!this.project.media_resources){
            this.project.media_resources = [];
        }
        let type: any = this.allMediaNotDefined('file') ? 'file' : 'url';
        let is_first = this.project.media_resources.filter(m => m.type === type).length === 0;
        this.project.media_resources.push({
            key: (new Date()).getTime(),
            is_default: is_first,
            type: type
        });
    }
    mediaResourceUploadDone(data:any, index:number){
        console.log(index, ` == `,data);
        if(data && data.userFile){
            this.project.media_resources[index].file_ref = data.userFile;
        }
    }
    mediaResourceRemoved($event, index) {
        if($event && $event.userFile && $event.userFile.id) {
            this.project.media_resources[index].file_ref = null;
        }
    }
    hasValidMediaResources(){
        return ((this.project.media_resources || []).filter(m => m && !m.file_ref && !m.content)).length === 0;
    }
    hasDefaultPerMedia(){
        let file_media = ((this.project.media_resources || []).filter(m => m && m.type === 'file'));
        if(file_media.length && file_media.findIndex(m => m.is_default) === -1){
            return false;
        }
        let url_media = ((this.project.media_resources || []).filter(m => m && m.type === 'url'));
        if(url_media.length && url_media.findIndex(m => m.is_default) === -1){
            return false;
        }
        return true;
    }
    toggleMediaDefaultState($event, type, index){
        this.project.media_resources = this.project.media_resources.map((m, i) => {
            if(m.type === type){
                m.is_default = (i === index);
            }
            return m;
        })
    }
    toggleMediaUrlMode($event, index){
        this.project.media_resources[index].type = ($event.target.checked ? 'url' : 'file');
        let current_media = this.project.media_resources[index];
        let already_exists = this.project.media_resources.find((m, i) => i !== index && m.lang === current_media.lang && m.type === current_media.type);

        if(already_exists){
            const message = `Media ${current_media.type} with "${current_media.lang}" already exists.`;
            this.toastService.show(this.toastService.types.INFO, message);
            this.project.media_resources[index].type = (current_media.type === 'file' ? 'url' : 'file');
        }
        return !already_exists;
    }
    removeMediaResource(index){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you want to remove this media?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.project.media_resources.splice(index, 1);
            }
        });
    }
    // toggleHtmlMediaState($event){
    //     if (!$event.target.checked) {
    //         this.project.html_media_url = null;
    //     }
    // }

    toggleQStatus($event, setsName, label, data_type) {
        const status = $event.target.checked;
        this[setsName] = this[setsName].map(q => ({ ...q, status }));
        if(!status && this[setsName].length){
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: `Remove Question Set`,
                title: `Are you sure you want to remove this question set from the ${(setsName == 'quizSets') ? `induction ${label.toLowerCase()}?` : `${label.toLowerCase()} section of the induction?`}`,
                confirmLabel: 'Remove',
                onConfirm: () => {
                    this[setsName] = [];
                },
                onClose: () => {
                    if(setsName == 'quizSets') {
                        this.project._has_quiz_questions = true;
                    } else {
                        this.project._has_additional_questions = true;
                    }
                }
            });
        } else if(status &&  !this[setsName].length){
            this.addQuizRow(data_type);
        }
    }

    toggleQDefault({index}, setsName){
        this[setsName] = this[setsName].map((q, i) => {
            q.is_default = (i === index);
            return q;
        })
    }

    mediaUploadDone(data:any, index:number){
        console.log(index, ` == `,data);
        if(data && data.userFile){
            this.project.media_file_ids[index] = data.userFile;
        }
    }

    hasValidMediaContent(){
        let has_media = ((this.project.media_file_ids || []).filter(m => m && m.id)).length > 0;
        if(this.project.has_html_media){
            return (this.project.html_media_url && (this.project.html_media_url).toString().trim().length) || has_media;
        }
        return has_media;
    }

    get logoInitParams(){
        return this.project;
    }

    logoImgRetries:number = 5;

    onLogoError($img:any, targetSrc:any, isLogo = true){
        if (isLogo) {
            $img.src = AssetsUrl.siteAdmin.logoPlaceholder; // AppConstant.apiServerUrl + '/images/logo-placeholder.png';
        } else {
            $img.src = AssetsUrl.siteAdmin.projectPlaceholder; // AppConstant.apiServerUrl + '/images/project-placeholder.png';
        }

        if(targetSrc && targetSrc.length && this.logoImgRetries){
            setTimeout(() => {
                this.logoImgRetries--;
                $img.src = targetSrc;
            }, 2000);
        }
    }

    onLogoLoad($img){
        this.logoImgRetries = 5;
    }

    uploadDone(data:any){
        if(data && data.userFile && data.userFile.id){
            this.project.logo_file_id = data.userFile.id;
            this.project.logo_file_url = data.userFile.file_url;
        }
    }

    deleteLogoRecord($logoUploader:any) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Do you really want to delete this?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                if (this.project && this.project.logo_file_id) {
                    this.userService.deleteUserFile(this.project.logo_file_id).subscribe(data => {
                        if (data.success) {
                            delete this.project.logo_file_id;
                            delete this.project.logo_file_url;
                        }else{
                            const message = data.message || 'Failed to store data.';
                            this.toastService.show(this.toastService.types.ERROR, message);
                        }
                        $logoUploader.completed = false;
                    });
                }
            }
        });
    }

    saveProject(form: any) {
        if (!form.valid) {
            const message = 'Something went wrong, form data is not valid.';
            this.toastService.show(this.toastService.types.INFO, message);
            console.log(form.errors);
            return false;
        }

        this.loadingInProgress = true;
        console.log("Pre Project ", this.project);
        let fatigueStatus = this.project.fatigue_management_status;

        this.project.site_hours_daily = this.site_hours_daily_status && fatigueStatus? this.project.site_hours_daily : null;
        this.project.total_hours_daily = this.total_hours_daily_status && fatigueStatus? this.project.total_hours_daily : null;
        this.project.site_hours_weekly = this.site_hours_weekly_status && fatigueStatus? this.project.site_hours_weekly : null;
        this.project.total_hours_shifts = this.total_hours_shifts_status && fatigueStatus? this.project.total_hours_shifts : null;
        this.project.total_duty_periods_biweekly = this.total_duty_periods_biweekly_status && fatigueStatus? this.project.total_duty_periods_biweekly : null;

        if(this.project.admins && this.project.admins.length){
            this.project.admins[0].flags = {...this.project.admins[0].flags, is_default: true};
        }
        this.project.induction_pin = this.hasInductionPin? this.project.induction_pin : null;
        this.project.start_date = this.project.start_date ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.project.start_date)?.valueOf() : null;
        this.project.end_date = this.project.end_date ? this.ngbMomentjsAdapter.ngbDateToDayJs(this.project.end_date)?.valueOf() : null;
        this.project['custom_field']['obsr_tagged_companies'] = (this.project['custom_field']['obsr_tagged_companies'] || []).filter(company => company.company_id);
        if(this.project.custom_field?.has_supply_chain_companies === true) { // Contractor should be only added in case supply chain is enabled.
            this.project['custom_field']['supply_chain_companies'] = this.getUniqueIds([
                ...this.project['custom_field']['supply_chain_companies'],
                this.projectContractor.id
            ]);
        }

        const {main_contact_number, ...updatedProjectData} = this.project

        if(this.project.id){
            // update project
            this.projectService.updateProject(updatedProjectData).subscribe(this.responseHandler.bind(this, false));

        }else{
            // create project
            this.project.is_active = 0;
            this.project.disabled_on = null;
            this.projectService.createProject(updatedProjectData).subscribe(this.responseHandler.bind(this, true));
        }
    }

    responseHandler(creatingProject: boolean, out: any) {
        if(out.success) {
            let rams = of<any>({success: true});
            let questions = of<any>({success: true});
            let uk_districts = of<any>({success: true});
            if(this.rams_assessment_ref && Object.keys(this.ramsAssessmentFormFields).length > 0) {
                console.log("Saving rams form setting.");
                // this.additional_induction_question.data_type = 'question';
                this.ramsAssessmentFormFields = (this.ramsAssessmentFormFields || []).filter(field => field.field_name);

                let request = {
                    'setting_name': 'rams_assessment_form',
                    'setting_value': {
                        rams_assessment_ref: this.rams_assessment_ref,
                        font_size: this.rams_assessment_font_size || 10,
                        assessment_form_fields: this.ramsAssessmentFormFields
                    }
                };
                rams = this.projectService.saveProjectSetting(out.project.id, request);
            }

            if(this.uk_districts && this.uk_districts.length) {
                this.uk_districts = (out.project.custom_field.country_code === 'GB') ? this.uk_districts : [];
                console.log("Saving districts in setting:", this.uk_districts);
                let request = {
                    'setting_name': 'uk_districts',
                    'setting_value': this.uk_districts
                };

                uk_districts = this.projectService.saveProjectSetting(out.project.id, request);
            }

            if (creatingProject) {
                this.saveObservationCategories();
            }

            let iq_list = [...this.quizSets, ...this.additionalQSets];
            questions = this.inductionQuestionsService.saveInductionQuestions(out.project.id, iq_list);
            zip(rams, questions, uk_districts).subscribe(([rams_data, iq_data, uk_districts_data] : any) => {
                this.loadingInProgress = false;
                console.log(rams_data, iq_data);
                if(!rams_data.success) {
                    const message = rams_data.message || 'Failed to save RAMS assessment form.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: rams_data });
                    return;
                }
                if (!iq_data.success) {
                    const message = iq_data.message || 'Failed to store induction questions data.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                    return;
                }
                if(!uk_districts_data.success) {
                    const message = uk_districts_data.message || 'Failed to save uk district.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: uk_districts_data });
                    return;
                }
                this.router.navigate(['/site-admin']);
            });
        } else {
            this.loadingInProgress = false;
            const message = out.message || 'Failed to store data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        }
    }

    /**
     * Get typeOfWorklist
     */
    initializeData() {
        this.resourceService.getTypeOfWorks().subscribe((data: any) => {
            if(data && data.success) {
                this.knownTypeOfWorks = data.typeOfWorklist;
            } else {
                const message = data.message || 'Failed to fetch type of works.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
        return;
    }

    /**
     * Get availableTimeZones
     */
    initializeTimezoneData() {
        this.resourceService.getTimezones().subscribe((data: any) => {
            if(data && data.success) {
                this.availableTimeZones = data.timezones;
            } else {
                const message = data.message || 'Failed to fetch timezones.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
        return;
    }

    onValidDeliveryAdminEmailInput($event, index) {
        if (!$event.email) {
            return;
        }
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Warning',
            title: `By making <span class="fw-500">${$event.name}</span> a delivery manager, you are about to give them <span class="fw-500">'Site Management'</span> access level. Would you like to continue?`,
            confirmLabel: 'Continue',
            onConfirm: () => {
                let delivery_manager = {
                    _isValid: true,
                    _email: $event.email,
                    _name: $event.name,
                    user_ref: $event.id,
                    designation: ['delivery_management'],
                    flags: {
                        is_delivery_manager: true
                    }
                };
                this.delivery_managers[index] = {...this.delivery_managers[index], ...delivery_manager};
                let filteredProjectUser = this.project.admins.find(pu => ((pu.user_ref && pu.user_ref.id) || pu.user_ref) == delivery_manager.user_ref) || {};
                this.project.admins = this.project.admins.map(pu => {
                    if(!pu.flags){
                        pu.flags = {};
                    }
                    if (filteredProjectUser.user_ref && ((pu.user_ref && pu.user_ref.id) || pu.user_ref) == delivery_manager.user_ref) {
                        if (!pu.designation.includes('delivery_management')) {
                            pu.designation.push('delivery_management');
                        }
                        pu.flags.is_delivery_manager = true;
                    }
                    return pu;
                });
                if (!filteredProjectUser.user_ref) {
                    this.project.admins.push(delivery_manager);
                }
            },
            onClose: () => {
                this.delivery_managers.splice(index, 1);
            }
        });
    }

    onValidFatigueAdminEmailInput($event, index) {
        if (!$event.email) {
            return;
        }
        let f_manager = {
            _isValid: true,
            email: $event.email,
            name: $event.name,
            id: $event.id,
        };
        this.fatigue_managers[index] = f_manager;
        let fatigueMgrIds = this.fatigue_managers.map(f => f.id);
        this.project.custom_field['fatigue_managers'] = fatigueMgrIds;
    }

    addGateRow() {
        if(!this.project.project_gates){
            this.project.project_gates = [];
        }

        this.project.project_gates.unshift({
            gate_name: '',
            time_slot: {}
        });
    }

    removeGateRow(i: number) {
        const gateName = this.project.project_gates[i].gate_name;
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Remove Gate',
            title: `Are you sure you want to remove ${(gateName) ? '<span class="fw-500">'+ gateName +'</span>' : 'this gate' }?`,
            confirmLabel: 'Remove',
            onConfirm: () => {
                this.project.project_gates.splice(i, 1);
            }
        });
    }

    slotClicked(i, day, timeframe) {
        if (typeof this.project.project_gates[i].time_slot[day] !== 'undefined') {
            if(!this.project.project_gates[i].time_slot[day].includes(timeframe)) {
                //select
                this.project.project_gates[i].time_slot[day].push(timeframe);
            }
        } else {
            //select
            this.project.project_gates[i].time_slot[day] = [timeframe];
        }
    }

    unSelectSlot(i, day, timeframe) {
        if (typeof this.project.project_gates[i].time_slot[day] !== 'undefined') {
            if(this.project.project_gates[i].time_slot[day].includes(timeframe)) {
                //unselect
                var index = this.project.project_gates[i].time_slot[day].indexOf(timeframe);
                if (index > -1) {
                    let timeSlots = this.project.project_gates[i].time_slot[day];
                    timeSlots.splice(index, 1);
                    this.project.project_gates[i].time_slot[day] = timeSlots;
                }
            }
        }
    }

    slotIsSelected(i, day, timeframe) {
        if (typeof this.project.project_gates[i].time_slot[day] !== 'undefined') {
            if (this.project.project_gates[i].time_slot[day].includes(timeframe)) {
                return true;
            }
        }
        return false;
    }

    selectAllSlots(i, day) {
        if (typeof this.project.project_gates[i].time_slot[day] !== 'undefined') {
            //unselect
            delete this.project.project_gates[i].time_slot[day];
        } else {
            //select
            this.project.project_gates[i].time_slot[day] = [...this.timeSlots];
        }
    }

    dayIsSelected(i, day) {
        if (typeof this.project.project_gates[i].time_slot[day] !== 'undefined' && this.project.project_gates[i].time_slot[day].length) {
            return true;
        }
        return false
    }

    resetDefaultDuration(clickedOn){
        if (clickedOn){
            this.project.default_in_duration = 8;
        }else{
            this.project.default_in_duration = null;
        }
    }

    useDefaultCowPhrase() {
        if (this.has_cow_phrase) {
            this.has_cow_phrase = false;
            this.project.cow_setting.cow_phrase = this.cowDefaultPhrase;
        } else {
            this.has_cow_phrase = true;
        }
    }

    fileDeleteDone($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.project.media_file_ids = [{}];
        }
    }

    isPDF(url) {
        if (url && url.split('.').pop() && url.split('.').pop().toLowerCase() === 'pdf') {
            this.previewURL = this.sanitizer.bypassSecurityTrustResourceUrl(url + '?embed=frame');
            return true;
        }
        return false;
    }

    @ViewChild('routeMapHtml') private routeMapRef: IModalComponent;
    gateRouteMap(gateDetail, index) {
        this.selectedGateDetail = gateDetail;
        if (this.selectedGateDetail.route_map_file_id && (typeof this.selectedGateDetail.route_map_file_id === 'object')) {
            this.selectedGateDetail.route_map_file_url = this.selectedGateDetail.route_map_file_id.file_url;
        }
        this.showRouteMapModal = true;
        this.isPdfDocument = this.isPDF(this.selectedGateDetail.route_map_file_url);
        this.routeMapRef.open();
    }

    closeRouteMapModal() {
        this.showRouteMapModal = false;
        this.isPdfDocument = false;
    }

    routeMapUploadDone(data:any) {
        if(data && data.userFile && data.userFile.id){
            this.selectedGateDetail.route_map_file_id = data.userFile.id;
            this.selectedGateDetail.route_map_file_url = data.userFile.file_url;
            this.isPdfDocument = this.isPDF(this.selectedGateDetail.route_map_file_url);
            //save
            if (this.selectedGateDetail.id) {
                this.updateProjectGate();
            }
        }
    }

    deleteRouteMapRecord(event, routeMapUploader:any) {
        if(event && event.userFile) {
            routeMapUploader.completed = false;
            this.selectedGateDetail.route_map_file_id = null;
            delete this.selectedGateDetail.route_map_file_url;
            //save
            if (this.selectedGateDetail.id) {
                this.updateProjectGate();
            }
        }
    }

    updateProjectGate() {
        this.projectGateBookingService.updateProjectGate(this.selectedGateDetail, this.projectId, this.selectedGateDetail.id).subscribe(data => {
            if(!data.success) {
                const message = 'Failed to update gate.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }

    addDeliveryManagerRow(){
        this.delivery_managers.push({
            designation: ['delivery_management'],
            flags: {is_delivery_manager: true}
        });
    }

    addFatigueManagerRow(){
        this.fatigue_managers.push({});
    }

    removeDeliveryManagerRow(e: any, i: number){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you want to remove this record?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.project.admins = [...this.project.admins.filter(pu => !(pu.designation.includes('delivery_management') && pu.designation.length == 1 && pu.flags && pu.flags.is_delivery_manager && ((pu.user_ref && pu.user_ref.id) || pu.user_ref) === ((this.delivery_managers[i].user_ref && this.delivery_managers[i].user_ref.id) || this.delivery_managers[i].user_ref)))];
                let designationIndex = -1;
                this.project.admins = this.project.admins.map(pu => {
                    if (((pu.user_ref && pu.user_ref.id) || pu.user_ref) == ((this.delivery_managers[i].user_ref && this.delivery_managers[i].user_ref.id) || this.delivery_managers[i].user_ref)) {
                        designationIndex = pu.designation.indexOf('delivery_management');
                        if (designationIndex > -1) {
                            pu.designation.splice(designationIndex, 1);
                        }
                        if (pu.flags.is_delivery_manager) {
                            pu.flags.is_delivery_manager = false;
                        }
                    }
                    return pu;
                });
                this.delivery_managers.splice(i, 1);
            }
        });
    }

    removeFatigueManagerRow(e: any, i: number){
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you want to remove this record?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.fatigue_managers.splice(i, 1);
                let fatigueMgrIds = this.fatigue_managers.map(f => f.id);
                this.project.custom_field['fatigue_managers'] = fatigueMgrIds;
            }
        });
    }

    @ViewChild('projectSiteConfigPopup', { static: true }) projectSiteConfigPopupRef: CompanySiteConfigComponent;
    projectSiteConfigModal() {
        if(this.projectSiteConfigPopupRef && this.projectSiteConfigPopupRef.projectSiteConfig){
            this.projectSiteConfigPopupRef.projectSiteConfig();
        }
    }

    @ViewChild('cowOwenerTaggingPopup') cowOwenerTaggingPopupRef: CowOwenerTaggingComponent;
    tagOwnersModal() {
        if(this.cowOwenerTaggingPopupRef && this.cowOwenerTaggingPopupRef.openOwenerTagging){
            this.cowOwenerTaggingPopupRef.openOwenerTagging();
        }
    }

    @ViewChild('cowSiteDrawingsPopup') cowSiteDrawingsPopupRef: CowSiteDrawingsComponent;
    cowSiteDrawingsModal() {
        if(this.cowSiteDrawingsPopupRef && this.cowSiteDrawingsPopupRef.openSiteDrawingsPopup){
            this.cowSiteDrawingsPopupRef.openSiteDrawingsPopup();
        }
    }

    getDefaultPolicies() {
        // this.has_c_lens_policy = false;
        this.has_d_and_a_policy = false;
        this.has_working_hr_agreement = false;
        (this.project.further_policies || []).map(fp => {
            // if (fp.key && fp.key === 'c_lens_policy') {
            //     this.c_lens_policy = fp;
            //     this.has_c_lens_policy = true;
            // }

            if(fp.key && fp.key === 'd_and_a_policy') {
                this.d_and_a_policy = fp;
                this.has_d_and_a_policy = true;
            }

            if(fp.key && fp.key === 'working_hr_agreement') {
                this.working_hr_agreement = fp;
                this.has_working_hr_agreement = true;
            }
        });
    }

    togglePolicy($event, item) {
        if ($event.target.checked) {
            this.project.further_policies.push(item);
        } else {
            let index = this.project.further_policies.findIndex(further_policy => further_policy.key === item.key);
            this.project.further_policies.splice(index, 1);
        }

        this.getDefaultPolicies();
    }

    @ViewChild('policyFileUploaderRef')
    private policyFileUploaderModalRef: IModalComponent;
    openFileUploader(policyObj, policyType, isDefault=false, objIndex=null) {
        if (isDefault) {
            let index = this.project.further_policies.findIndex(further_policy => further_policy.key === policyObj.key);
            this.project.further_policies[index].is_text = policyType;
            this.policyIndex = index;
        } else {
            this.policyIndex = objIndex;
            this.project.further_policies[objIndex].is_text = policyType;
        }

        //if Add PDF selected
        if (!policyType) {
            policyObj.policy_ref = policyObj.policy_ref.length ? policyObj.policy_ref : [{}];
            this.selectedPolicy = policyObj;
            this.policyFileUploaderModalRef.open();
        }
        return true;
    }

    closePolicyFileUploader(event) {
        event.closeFn();
    }

    deletePolicyFile($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.project.further_policies[this.policyIndex].policy_ref = [{}];
        }
    }

    policyFileUploadDone(data:any, index:number) {
        if(data && data.userFile){
            let policy_index = this.project.further_policies.findIndex(further_policy => further_policy.policy_name === this.selectedPolicy.policy_name);
            this.project.further_policies[policy_index].policy_ref[index] = data.userFile;
        }
    }

    isFileSelected(policy_ref) {
        let fileObj = policy_ref.slice(0,1).shift();
        if(fileObj && Object.keys(fileObj).length) {
            return 'uploaded';
        }
        return null;
    }

    onContentDom($event, pFurtherPolicyEditor) {
        pFurtherPolicyEditor.config.readOnly = false;
    }

    @ViewChild('dailyActivitiesImportModalHtml', { static: true }) dailyActivitiesImportModalHtmlRef: DailyActivitiesImportComponent;
    dailyActivitiesImportModal() {
        if(this.dailyActivitiesImportModalHtmlRef && this.dailyActivitiesImportModalHtmlRef.openModal) {
            this.dailyActivitiesImportModalHtmlRef.openModal();
        }
    }

    @ViewChild('closeCallTagOwnerModal') closeCallTagOwnerModalRef: TagOwnerModalComponent;
    ccAssociateEmailWithOwnerModal() {
        this.closeCallTagOwnerModalRef.openOwnerTaggingModal();
    }

    @ViewChild('goodCallTagOwnerModal') goodCallTagOwnerModalRef: TagOwnerModalComponent;
    gcAssociateEmailWithOwnerModal() {
        this.goodCallTagOwnerModalRef.openOwnerTaggingModal();
    }

    @ViewChild('observationsTagOwnerModal') observationsTagOwnerModalRef: TagOwnerModalComponent;
    obrsAssociateEmailWithOwnerModal() {
        this.observationsTagOwnerModalRef.openOwnerTaggingModal();
    }

    @ViewChild('observationsCategoriesModal') observationsCategoriesModalRef: ManageCategoriesModalComponent;
    observationCategoriesModal() {
        this.observationsCategoriesModalRef.openCategoryModal();
    }

    onCompanySelect($event) {
        this.project.parent_company = $event ? $event.id : $event;
        this.projectContractor = $event || {};
        this.getCompanySupplyChainConf();
        this.initToolsSetting();

        if($event && $event.divisions.length) {
            this.enableDivisions = true;
            this.divisionsList = $event.divisions;
            return;
        }
        this.enableDivisions = false;
    }

    @ViewChild('handlinEquipmentHtml')
    private handlinEquipmentHtmlRef: IModalComponent;
    openEquipmentModal() {
        if(!this.project.custom_field.handling_equipment || this.project.custom_field.handling_equipment.length <1) {
            this.project.custom_field.handling_equipment = [''];
        }
        this.handlinEquipmentHtmlRef.open();
    }

    removeEquipment(i: number):void {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Do you really want to delete this?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.project.custom_field.handling_equipment.splice(i, 1);
            }
        });
    }

    addEquipment() {
        this.project.custom_field.handling_equipment.sort((a, b) => a.localeCompare(b));
        this.project.custom_field.handling_equipment = ['', ...this.project.custom_field.handling_equipment];
    }

    addHandlingEquipment(event) {
        this.project.custom_field.handling_equipment.splice(0, 1);
        if(this.project.custom_field.handling_equipment.length > 0) {
            this.project.custom_field.handling_equipment_status = true;
        }
        event.closeFn();
    }

    decreaseDuration() {
        let duration = this.project.custom_field.maximum_booking_time;
        if (duration != '00:30 minutes') {
            if (duration == '01:30 hours') {
                duration = '01:00 hour';
            } else if (duration == '01:00 hour') {
                duration = '00:30 minutes';
            } else {
                let [hour] = duration.split(" ");
                duration = dayjs(hour, 'hh:mm').subtract(30, 'minute').format('hh:mm') + ' hours';
            }
            this.project.custom_field.maximum_booking_time = duration;
        }
    }

    increaseDuration() {
        let duration = this.project.custom_field.maximum_booking_time;
        if (duration == '00:30 minutes') {
            this.project.custom_field.maximum_booking_time = '01:00 hour';
        } else {
            let [hour] = duration.split(" ");
            this.project.custom_field.maximum_booking_time = dayjs(hour, 'hh:mm').add(30, 'minute').format('hh:mm') + ' hours';
        }
    }

    changeMinTimeSlot(durationInMinutes) {
        if(this.prevDefBookingSlot != durationInMinutes) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Change Default Booking Slot',
                title: `This change will erase all bookings created for today and future dates. Do you want to proceed?`,
                confirmLabel: 'Confirm',
                onConfirm: () => {
                    this.project.custom_field.default_booking_slot = durationInMinutes;
                    this.timeSlots = this.timeUtility.generateTimeSlots(durationInMinutes);
                    for(let i=0; i<this.project.project_gates.length; i++) {
                        this.project.project_gates[i].time_slot = {};
                    }
                },
                onClose: () => {
                    this.project.custom_field.default_booking_slot = this.prevDefBookingSlot;
                }
            });
        } else {
            this.timeSlots = this.timeUtility.generateTimeSlots(durationInMinutes);
        }
    }

    openModal(content, size, windowClass='') {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            windowClass: windowClass
        });
    }

    toggleSupplyChainCompanies(ev) {
        if(!ev.target.checked) {
            this.project.custom_field.supply_chain_companies = [];
        } else {
            this.project.custom_field.supply_chain_companies = [this.projectContractor.id];
            (this.supplyChainSelectorComponentRef) && this.supplyChainSelectorComponentRef.checkApplicableSupplyChain(this.company_sc_setting);
        }
    }

    isRequired(index) {
        return (index == 0) ? false : true;
    }

    isAnyFieldEmpty(array) {
        if(array.find(item => !item.question)) {
            return true;
        }
        return false;
    }

    @ViewChild('uploadRamsDocModal')
    private uploadRamsDocModalRef: IModalComponent;
    openRamsDocModal() {
        this.predefinedDataFields = (this.predefinedDataFields).map((field:any) => {
            if (field.key == 'rams_title') {
                field.label = `${this.project.custom_field.rams_phrase} Title`
            }
            return field;
        });
        this.uploadRamsDocModalRef.open();
    }

    async uploadRamsAssessmentDone($event:any) {
        if($event && $event.userFile && $event.userFile.id){
            this.rams_assessment_file = $event.userFile;
            this.rams_assessment_ref = $event.userFile.id;
            this.rams_assessment_url = $event.userFile.file_url;

            // Fetch the PDF with form fields
            const formUrl = $event.userFile.file_url; //'https://pdf-lib.js.org/assets/dod_character.pdf'
            const formBytes= await fetch(formUrl).then((res) => res.arrayBuffer());
            // Load the PDF with form fields
            const pdfDoc = await PDFDocument.load(formBytes);
            // Get two text fields from the form
            const form = pdfDoc.getForm();
            const fields = form.getFields();
            fields.forEach(field => {
                console.log(field);
                let type = field.constructor.name
                let name = field.getName();

                //for signature
                if (field instanceof PDFButton || field instanceof PDFSignature) {
                    this.ramsAssessmentFormFields.push({
                        "field_id": (this.ramsAssessmentFormFields.length + 1),
                        "field_label": name,
                        "field_name": name,
                        "field_type": (field instanceof PDFButton) ? 'signature' : 'esignature',
                        "is_mandatory": false,
                    });
                }

                if (field instanceof PDFTextField) {
                    let textField = form.getTextField(name);
                    if (textField.isMultiline()) {
                        type = 'textarea';
                    } else {
                        type = 'textbox';
                    }

                    this.ramsAssessmentFormFields.push({
                        "field_id": (this.ramsAssessmentFormFields.length + 1),
                        "field_label": name,
                        "field_name": name,
                        "field_type": type,
                        "is_mandatory": false,
                    });
                }
            });
        }
    }

    removeAssessmentFormField(i: number):void {
        this.ramsAssessmentFormFields.splice(i, 1);
    }

    addAssessmentFormField() {
        this.ramsAssessmentFormFields.push({
            "field_id": (this.ramsAssessmentFormFields.length + 1),
            "field_label": '',
            "field_name": '',
            "field_type": '',
            "is_mandatory": true,
        });
    }

    deleteRamsAssessment($event) {
        if($event && $event.userFile && $event.userFile.id) {
            this.rams_assessment_file = undefined;
            this.rams_assessment_ref = null;
            this.rams_assessment_url = null;
            this.ramsAssessmentFormFields = [];
            this.rams_assessment_font_size = 10;
        }
    }

    validateRamsAssessmentForm(form, event) {
        console.log(this.ramsAssessmentFormFields);
        if (!form.valid) {
            const message = 'Something went wrong, form data is not valid.';
            this.toastService.show(this.toastService.types.INFO, message, { data: form.errors });
            return false;
        }

        event.closeFn();
    }

    loadRamsAssessmentForm() {
        if(this.projectId) {
            this.resourceService.getProjectSettingsByName(['rams_assessment_form'], this.projectId).subscribe((data: any) => {
                if (data.success && data.project_settings && data.project_settings.rams_assessment_form
                    && data.project_settings.rams_assessment_form.rams_assessment_ref
                    && data.project_settings.rams_assessment_form.rams_assessment_ref.id) {
                    this.rams_assessment_font_size = (data.project_settings.rams_assessment_form.font_size || this.rams_assessment_font_size);
                    this.rams_assessment_file = data.project_settings.rams_assessment_form.rams_assessment_ref;
                    this.rams_assessment_ref = data.project_settings.rams_assessment_form.rams_assessment_ref.id;
                    this.rams_assessment_url = data.project_settings.rams_assessment_form.rams_assessment_ref.file_url;
                    this.ramsAssessmentFormFields = data.project_settings.rams_assessment_form.assessment_form_fields;
                }
            });
        }
    }

    deleteRamsAssessmentForm() {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you want to remove this?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                this.resourceService.deleteProjectSetting(this.projectId, 'rams_assessment_form').subscribe((data: any) => {
                    if (data.success && data.deleted_project_setting) {
                        this.rams_assessment_file = undefined;
                        this.rams_assessment_ref = null;
                        this.rams_assessment_url = null;
                        this.ramsAssessmentFormFields = [];
                        this.rams_assessment_font_size = 10;
                    }
                });
            }
        });
    }

    @ViewChild('customSelectOptionsModal')
    private customSelectOptionsModalRef: IModalComponent;
    manageCustomSelectOption(fieldIndex) {
        this.optionFieldIndex = fieldIndex;
        this.ramsAssessmentSelectField = this.ramsAssessmentFormFields[fieldIndex];
        this.ramsAssessmentSelectField.options =  [
            {
                'label': '',
            },
            ...((this.ramsAssessmentSelectField.options || []).filter(option => option.label))
        ];
        this.customSelectOptionsModalRef.open();
    }

    saveOptions(event)  {
        this.ramsAssessmentSelectField.options = (this.ramsAssessmentSelectField.options || []).filter(option => option.label);
        this.ramsAssessmentFormFields[this.optionFieldIndex] = this.ramsAssessmentSelectField;
        event.closeFn();
    }

    closeOptionFieldModal(event){
        this.optionFieldIndex = -1;
        event.closeFn();
    }

    addCustomSelectOption(optionIndex) {
        if(this.ramsAssessmentSelectField.options && this.ramsAssessmentSelectField) {
            this.ramsAssessmentSelectField.options = [
                {
                    "label": '',
                },
                ...((this.ramsAssessmentSelectField.options || []).filter(option => option.label))
            ];
        }
    }

    removeCustomSelectOption(optionIndex) {
        this.ramsAssessmentSelectField.options.splice(optionIndex, 1);
    }

    onCountryCodeChange($event){
        // console.log('New selection is', $event);
        if(!this.project.custom_field){
            this.project.custom_field = {};
        }
        this.project.custom_field.country_code = $event.code;
        this.postcodeInput = this.featureExclusionUtility.showProjectPostalCode($event.code);
        console.log(this.postcodeInput);
        if(this.project.custom_field.country_code){
            // refresh lists which are country based.
            this.getEmployersList(true);
            this.getNonCscsCompetenciesList();
        }
        this.project.postcode = "";
        this.project.contractor = null;
        this.project.parent_company = null;
        this.resetLocation();
        if(this.postcodeInput?.type === 'address-lookup'){
            this.project.postcode = "00000";
            this.project.custom_field.location.country = $event.name;
        }
    }

    resetLocation(){
        this.project.custom_field.location = {
            lat:null,
            long:null,
            region:'',
            country:'',
            admin_district:'',
        }
    }

    onSCValidityChange(isValid: boolean) {
        this.isSelectedSCValid = isValid;
    }

    getCompanySupplyChainConf() {
        this.resourceService.getCompanySettingByName('company_supply_chain_config', this.projectContractor.id).subscribe((data:any) => {
            if(data.success && data.record && data.record.value) {
                this.company_sc_setting = data.record.value;
                if(this.project.custom_field.has_supply_chain_companies === false && this.company_sc_setting.active_for_all_projects) {
                    // By default, enable project supply chain if it's activated for all projects of company.
                    this.project.custom_field.has_supply_chain_companies = this.company_sc_setting.active_for_all_projects;
                }
                (this.supplyChainSelectorComponentRef) && this.supplyChainSelectorComponentRef.checkApplicableSupplyChain(data.record.value);
            } else {
                console.log('Failed API response ', data);
            }
        });
    }

    private getUniqueIds(items = []) {
        return items.filter((elem, index, self) => index === self.indexOf(elem));
    }

    @ViewChild('districtAndAreaCodesModal')
    private districtAndAreaCodesModalRef: TemplateRef<any>;
    viewDistrictList() {
        this.openModal(this.districtAndAreaCodesModalRef, 'lg', 'modal_v2 l-modal modalHeightAuto');
    }

    @ViewChild('permitConfigModal')
    private permitConfigModalRef: TemplateRef<any>;
    openPermitConfigModal() {
        this.selectedPermitTemplate = {};
        this.showPermitConfig = false;
        if (this.project.id) {
            this.processingRequest = true;
            this.permitService.fetchProjectPermitTemplates(this.project.id).subscribe((data:any) => {
                this.processingRequest = false;
                this.projectPermits = [];
                if (data.success && data.permit_templates && data.permit_templates.length) {
                    this.projectPermitTemplates = data.permit_templates.map(template => {
                        if (template.is_active) {
                            this.activePermits.push(template.id);
                        }
                        this.projectPermits.push({
                            id: template.config_id,
                            permit_ref: template.permit_ref || template.id,
                            is_active: template.is_active,
                            sign_off: template.sign_off || [],
                            master_permit_managers: this.masterPermitManagers
                        })
                        return { ...template, is_field_disabled: true }
                    });

                    this.masterPermitManagers = data.master_permit_managers;
                    this.openModal(this.permitConfigModalRef, 'lg', "permitModal modalHeightAuto modal_v2");
                    return;
                }
                const message = 'No permit templates found for the project.';
                this.toastService.show(this.toastService.types.ERROR, message);
            });
        } else {
            this.openModal(this.permitConfigModalRef, 'lg', "permitModal modalHeightAuto modal_v2");
        }
    }

    saveProjectPermitConfig(cb) {
        let body = {
            project_permits: this.projectPermits,
            master_permit_managers: this.masterPermitManagers
        };
        this.processingRequest = true;
        const subscription = this.permitService.saveProjectPermitConfig(body, this.project.id);
        subscription.subscribe(data => {
            if (data.success) {
                console.log(`Project permit configuration saved successfully.`);

                this.permitService.fetchProjectPermitTemplates(this.project.id).subscribe((data:any) => {
                    this.processingRequest = false;
                    if (data.success && data.permit_templates) {
                        this.projectPermitTemplates = data.permit_templates.map(template => ({ ...template, is_field_disabled: true }));
                        this.masterPermitManagers = data.master_permit_managers;
                    } else {
                        const message = 'Something went wrong while fetching permit templates for project.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                    }
                });
            } else {
                const message = 'Failed to save project permit configuration.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
        cb();
    }

    onDisablingPermit(status, templateId) {
        let findIndex = this.projectPermits.findIndex(pp => pp.permit_ref === templateId);
        if(findIndex > -1) {
            this.selectPermitIndex = findIndex;
            this.projectPermits[findIndex].is_active = status;
        }

        console.log(this.projectPermits, templateId, status);
        if (!status) {
            document.getElementsByClassName('permitModal')[0].children[0].className = 'modal-dialog modal-lg';
            this.showPermitConfig = false;
        }
    }

    togglePermit(isChecked, permitTemplate, index) {
        this.projectPermitTemplates.forEach((template, i) => {
            template.is_field_disabled = i != index;
        })

        this.selectedPermitTemplate = permitTemplate;
        this.selectPermitIndex = this.projectPermits.findIndex(pp => pp.permit_ref === permitTemplate.id);

        if (!this.projectPermits[this.selectPermitIndex].sign_off.length) {
            this.projectPermits[this.selectPermitIndex].sign_off = (permitTemplate.signatures || []).map(sign => ({
                sign_number: sign.sign_number,
                field_name: sign.field_name,
                field_label: sign.field_label,
                is_requestor: sign.is_requestor,
                is_closeout_requestor: sign.is_closeout_requestor,
                is_closeout: sign.is_closeout,
                link_sections: sign.link_sections || [],
                link_fields: sign.link_fields || [],
                signatories: []
            }));
        } else {
            this.projectPermits[this.selectPermitIndex].sign_off = this.projectPermits[this.selectPermitIndex].sign_off.map(sign => {
                let templateSign = (permitTemplate.signatures || []).find(t_sign => t_sign.field_name === sign.field_name);
                sign.field_label = templateSign.field_label;
                return sign;
            });
        }

        let closeoutIndex = (this.projectPermits[this.selectPermitIndex].sign_off || []).findIndex(sign => sign.is_closeout);
        this.permitHasCloseout = (closeoutIndex != -1);

        document.getElementsByClassName('permitModal')[0].children[0].className = (isChecked && this.projectPermits[this.selectPermitIndex].sign_off.length) ? 'modal-dialog modal-xl' : 'modal-dialog modal-lg';
        this.showPermitConfig = (isChecked && this.projectPermits[this.selectPermitIndex].sign_off.length) ? true : false;
    }

    togglePermitFieldDisability($event, index) {
        this.projectPermitTemplates.forEach((template, i) => {
            template.is_field_disabled = i != index;
            /*if (i == index) {
                this.togglePermit(template.is_active, template);
            }*/
        })
    }

    onSelectPermitManager($event) {
        this.masterPermitManagers = $event.selected;
    }

    onSelectSignatories($event, i) {
        this.projectPermits[this.selectPermitIndex].sign_off[i].signatories = $event.selected;
    }

    validatePostcode(pPostcode?){
        this.loadingInProgress = true;
        this.projectService.validatePostcode(this.project.postcode,this.project.custom_field.country_code).subscribe((response: {success:boolean, latLongData: ProjectLocation }) => {
            if(this.isPostcodeRequired){
                if(response.success && response.latLongData){
                    pPostcode.control.setErrors(null);
                    this.project.custom_field.location = response.latLongData;
                } else {
                    pPostcode.control.setErrors({valid: true});
                }
            }
            this.loadingInProgress = false;
        })
    }

    saveObservationCategories() {
        if(this.observationAdditionalCategories && this.observationAdditionalCategories.length) {
            let request = {
                'setting_name': 'observation_setting',
                'setting_value': {
                    categories: this.observationAdditionalCategories,
                }
            };
            this.loadingInProgress = true;
            this.projectService.saveProjectSetting(this.project.id, request).subscribe((data: any) => {
                this.loadingInProgress = false;
                if (!data.success) {
                    const message = 'Failed to save observation categories.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
            });
        }
    }

    onSavingObservationCategories($event) {
        this.observationAdditionalCategories = ($event.records ||[]).filter(r => r.name);
        if (this.project.id) {
            this.saveObservationCategories();
        }
    }

    initToolsSetting() {
        this.loadingInProgress = true;
        this.resourceService.getCompanySettingByName('project_feature_permission', this.projectContractor.id).subscribe((data:any) => {
            console.log('project_feature_permission data:', data);
            this.loadingInProgress = false;
            if(data.success && data.record && data.record.value) {
                this.projectFeaturePermission = data.record.value.tools;
                let setEnableTools = this.provideFilteredObject(this.projectFeaturePermission, 'lock-on', true);
                let setDisableTools = this.provideFilteredObject(this.projectFeaturePermission, 'lock-off', false);
                let autoEnabledTools = this.provideFilteredObject(this.projectFeaturePermission, 'auto-on', true);

                let updatedCompanyTools = Object.assign({}, setEnableTools, setDisableTools);
                this.contractorName = this.project.contractor;
                this.featureSettingToolTipForLockOn = `This tool is currently mandated for all ${this.contractorName} projects. To disable it, please reach out to your teams innDex champion`;
                this.featureSettingToolTipForLockOff = `This tool is currently unavailable for all ${this.contractorName} projects. To enable it, please reach out to your teams innDex champion`;

                if(!this.projectId){
                    if(Object.keys(updatedCompanyTools).length){
                        this.assignValueToProjectSectionAccess(updatedCompanyTools);
                    }

                    if(Object.keys(autoEnabledTools).length){
                        this.assignValueToProjectSectionAccess(autoEnabledTools);
                    }
                }
            }
        });
    }

    assignValueToProjectSectionAccess(sourceValue: any){
        this.project.fatigue_management_status = sourceValue.fatigue_management_status;
        this.project.delivery_management_status = sourceValue.delivery_management_status;
        delete sourceValue.fatigue_management_status;
        delete sourceValue.delivery_management_status;
        this.project.project_section_access = Object.assign(this.project.project_section_access, sourceValue);
    }

    provideFilteredObject(toolsValue, key, value) {
        return Object.keys(toolsValue)
            .filter((ele) => toolsValue[ele] === key)
            .reduce((ele, key) => {
                ele[key] = value;
                return ele;
            }, {});
    }

    disableToolTipStatus(status:string):boolean{
        return (status === 'unlocked' || status === 'auto-on');
    }

    disableToolStatus(status:string):boolean{
        return (status === 'lock-on' || status === 'lock-off');
    }
}
