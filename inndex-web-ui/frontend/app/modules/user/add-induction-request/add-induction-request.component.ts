/**
 * Created by spatel on 7/10/18.
 */
import {Component, ChangeDetectorRef, OnInit, ViewChild, TemplateRef, OnDestroy, ElementRef} from '@angular/core';
import {
    AuthService,
    Comment,
    SkillMatrixService,
    ContactDetail,
    CompanyRtwConfig,
    InductionQuestions,
    InductionAdditionalData,
    InductionRequest,
    Project,
    ProjectService,
    User,
    EmploymentDetail,
    UserService,
    getPendingAssessmentStatus,
    FeatureExclusionUtility,
    InductionQuestionsService, ResourceService, TravelTimeOverride,
    ProjectRamsService,
    HttpService, CompanyCscsConfig, ProfileUtility,
    ToastService
} from "@app/core";
import {NgbMomentjsAdapter} from "@app/core/ngb-moment-adapter";
import {AssetsUrl, GenericConfirmationModalComponent, IModalComponent} from "@app/shared";
import {ActivatedRoute, Router} from "@angular/router";
import {Subscription, zip} from "rxjs";
//import {FormControl} from "@angular/forms";
import * as dayjs from 'dayjs';
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {VgApiService} from '@videogular/ngx-videogular/core';
import {PdfFlipBookComponent} from "@app/modules/common";
import {DomSanitizer, SafeResourceUrl} from "@angular/platform-browser";
import {BeforeInductionStartComponent} from "./../before-induction-start/before-induction-start.component";
import {AppConstant} from "@env/environment";
import {innDexConstant} from "@env/constants";
type MyFunctionType = () => void;

@Component({
    selector: 'add-induction-request',
    templateUrl: './add-induction-request.component.html',
    styleUrls: ['./add-induction-request.component.scss']
})
export class AddInductionRequestComponent implements OnInit, OnDestroy {
    @ViewChild('confirmationModalRef') private confirmationModalReference: GenericConfirmationModalComponent;
    routeQuerySubscription: Subscription;
    getProjectSubscription: Subscription;
    visibleModalsSubscription: Subscription;

    authUser$: User;
    default_avatar_url = AssetsUrl.default_avatar_url;
    projectId: number;
    inductorId: number;
    inductionRequestId: number;
    saveInProgress: boolean = false;
    MANDATORY_COMPETENCY_TAG: string = 'mandatory-competency';
    GROUP_COMPETENCY_TAG: string = 'collaborative-competency';
    group_competencies: Array<string> = [];
    nonGroupKnownCompetencies: Array<any>;
    user_documents = [];
    passportCompetencyName = "Passport/Visa";
    // cscsCompetencyName = "CSCS";
    cscsCompetenciesNames = [];
    // ['CSCS', 'CPCS', 'CISRS', 'ECS', 'EUSR', 'NPORS Operator', 'PTS', 'PTS (AC)', 'PTS (DCCR)', 'CCDO', 'SIA - Security Industry Authority', 'FISA - Forest Industry Safety Accord', 'NPTC - National Proficiency Test Council', 'LANTRA - Traffic Management', 'NEBOSH', 'IOSH', 'SSSTS', 'SMSTS', 'SafePass'];

    project?: Project = {};
    competencies: Array<any> = [];
    contactDetail?: ContactDetail = {};
    // employmentDetail: Observable<{}>;// employmentDetail;
    employment_detail?: EmploymentDetail = {};
    active_skills_rule: any = {};
    invalid_sets = [];
    contractor_cscs_config: CompanyCscsConfig;
    contractor_rtw_config: CompanyRtwConfig = new CompanyRtwConfig();
    ppac_verification_data: any = {};
    has_skills_matrix: boolean = false;
    healthAssessment = {};
    medicalAssessment: Array<any> = [];

    induction_request: InductionRequest = new InductionRequest();
    additional_data: InductionAdditionalData = new InductionAdditionalData();
    travel_time_override: TravelTimeOverride = new TravelTimeOverride;
    extraDeclarations: any = {};
    knownCompetencies: Array<any> = [];
    autoSelectedCompetenciesId: Array<any> = [];
    media_files: Array<any> = [];
    e_media_url: SafeResourceUrl;
    first_media_url: string;
    watched_html_media: Boolean = false;
    hasUnsavedChanges: Boolean = false;
    showChooseInductionSlotModal: Boolean = false;
    hasMoreThanOne: string;
    additionalSectionTitle: string = 'Induction Questions';

    api:VgApiService;

    DEFAULT_DECLARATION: string = `I confirm that I have reviewed all information provided in my profile and on this form and agree it is correct and up to date.`;

    available_travel_methods: Array<string> = [
        /*'Public Transport',
        'Self - Car',
        'Self - Bike',
        'Cycle',*/
        'Car/Van (Driver)',
        'Car/Van (Passenger)',
        'Bicycle',
        'Bus',
        'Motorbike',
        'Train',
        'Walk',
        'Other',
    ];

    selectedLocale: string = innDexConstant.defaultLocale;
    locales: Array<{ label: string; key: string; }> = innDexConstant.labeledLocales;
    activeStage: number = 0;
    formStages: Array<any> = [];
    RIGHT_TO_WORK_STAGE: { name: string; icon: string; };
    SUPERVISOR_STAGE: any ;
    OPERATOR_STAGE: any;
    EMBEDDED_MEDIA_STAGE: any;

    addNewCompetenciesRows: Array<any> = [Math.random()];

    dateFormat: string = AppConstant.defaultDateFormat;
    dateDisplayFormat: string = AppConstant.displayDateFormat;
    dateStorageFormat: string = AppConstant.dateStorageFormat;
    // durationPickerOptions : any = {
    //     showPreview: false,
    //     showNegative: false,
    //     showLetters: false,
    //     showYears: false,
    //     showMonths: false,
    //     showWeeks: false,
    //     showDays: false,
    //     showHours: false,
    //     showSeconds: false
    // };
    travelTimeToWork: number = 0;
    travelTimeToHome: number = 0;

    medicationValidations: Array<any> = [];
    // c_lens_policy: any = {};
    d_and_a_policy: any = {};
    working_hr_agreement: any = {};
    quizSets: Array<InductionQuestions> = [];
    additionalQSets: Array<InductionQuestions> = [];

    correctAnswer: any = [];
    inductionQuestions: any = {};
    pageCount: number;
    additionalInductionQuestions: any = {};
    induction_rams: Array<any> = [];
    ramsPhrase: string = 'RAMS';
    selectedRams: any = {};
    selectedRamsId: number = null;
    ramsBriefingSignature: string = '';
    validRamsBriefingSignature: boolean = false;
    cscs_require_msg: string;
    is_mobile_nav: boolean = false;
    is_drawer_open: boolean = false;
    selectedMedicationDetails: any;
    isMedicationFormValid: boolean = false;
    sidebarIcons= AssetsUrl.inductionProcessSidebarIcons;
    projectInfo: Project = new Project;

    isEditCompetency: boolean = false;
    isChildOfCompetency: boolean = false;
    selectedCompetencie: any;
    childCompetency: any;
    modalRef: NgbModalRef;
    lastVisibleModal: NgbModalRef;
    editIconBlack= AssetsUrl.commonIcons.editIconBlack;
    userDocumentToDelete: any;
    isEditMedication: boolean = false;
    medicationIndex: number;
    newMedication: any;
    selectedUserDocFile: any;
    editStep: string = null;
    isFormValid: boolean = true;
    STAGELIST = {
        rightToWork: 'Right to work',
        travel: 'Travel',
        safetyAssessment: 'Safety Assessment',
        healthAssessment: 'Health Assessment',
        competencies: 'Competencies & Certs',
        eMedia: 'E Media',
        media: 'Media',
        supervisorDeclaration: 'Supervisor Declaration',
        plantMachineryOperatorDeclaration: 'Plant/Machinery Operator Declaration',
        declaration: 'Declaration',
        review: 'Review',
    }
    inductionTitle = "INDUCTION"
    requireMessage = null;
    missingDocumentMessages: Array<string> = [];
    companyLogoFileUrl: string;
    logoPlaceholder = AssetsUrl.siteAdmin.logoPlaceholder;

    showEmploymentStartDate: boolean = true;
    showTypeOfEmployment: boolean = true;
    showMinWage: boolean = true;
    showNIN: boolean = true;
    showEmpNbr: boolean = false;
    showProfileMedicalAssessment: boolean = true;
    showProfileHealthAssessment: boolean = true;
    showInductionHealthAssessment: boolean = true;
    contractor_feature_status: { [key:string]: boolean; } = {};
    enabledProfileAssessment: any = {};
    postcodeInput: any = {};
    labelData = { placeholder: 'Postcode', label: 'Post Code', error: 'postcode' };
    ramsSignatureStatus: number = 0;
    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private authService: AuthService,
        private projectService: ProjectService,
        private userService: UserService,
        private skillMatrixService: SkillMatrixService,
        private profileUtility: ProfileUtility,
        private resourceService: ResourceService,
        private cdRef:ChangeDetectorRef,
        private modalService: NgbModal,
        private sanitizer: DomSanitizer,
        private featureExclusionUtility: FeatureExclusionUtility,
        private inductionQuestionsService: InductionQuestionsService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private projectRamsService: ProjectRamsService,
        private httpService: HttpService,
        private toastService: ToastService,
    ) {
        this.initSidebar();
        this.is_mobile_nav = this.httpService.isMobileDevice();
    }

    ngOnInit(): void {
        this.authService.authUser.subscribe(data =>{
            if(data && data.id){
                this.authUser$ = data;
                this.postcodeInput = this.featureExclusionUtility.showProfilePostalCode(this.authUser$.country_code);
                this.labelData = this.featureExclusionUtility.generateLabels(this.postcodeInput?.type);
                this.additional_data.user_info = data;
            }
        });
        if (!this.induction_request.travel_method) {
            this.induction_request.travel_method = this.available_travel_methods[0];
        }
        window.addEventListener('resize',  this.resizeIframe, false);

        this.routeQuerySubscription = this.route.params
            .subscribe(params => {
                console.log(params);
                this.projectId = +params['projectId'] || undefined;
                this.inductionRequestId = +params['inductionRequestId'] || undefined;
                if (!this.projectId) {
                    return this.router.navigate(['/site-user']);
                }
                this.initialize();
            });
    }


    private initSidebar() {
        this.formStages = [
            {name: 'Before you start', icon: this.sidebarIcons.beforeYouStart},
            {name: 'Travel', icon: this.sidebarIcons.travelDetails},
            {name: 'Safety Assessment', icon: this.sidebarIcons.safetyAssessment},
            {name: 'Health Assessment', icon: this.sidebarIcons.healthAssessment},
            {name: 'Competencies & Certs', icon: this.sidebarIcons.competencies},
            {name: 'Media', icon: this.sidebarIcons.media},
            {name: 'Declaration', icon: this.sidebarIcons.declarations},
            {name: 'Review', icon: this.sidebarIcons.review},
        ];

        this.RIGHT_TO_WORK_STAGE = {name: 'Right to work', icon: this.sidebarIcons.rightToWork};
        this.SUPERVISOR_STAGE = {name: 'Supervisor Declaration', icon: this.sidebarIcons.supervisor, meta_key: 'meta_supervisor_induction_declarations'};
        this.OPERATOR_STAGE = {name: 'Plant/Machinery Operator Declaration', icon: this.sidebarIcons.plantMachine, meta_key: 'meta_operator_induction_declarations'};
        this.EMBEDDED_MEDIA_STAGE = {name: 'E Media', icon: this.sidebarIcons.media};
    }

    ngAfterViewChecked() {
        this.cdRef.detectChanges();
    }
    ngOnDestroy() {
        if (this.routeQuerySubscription) {
            this.routeQuerySubscription.unsubscribe();
        }
        if (this.getProjectSubscription) {
            this.getProjectSubscription.unsubscribe();
        }
        if (this.visibleModalsSubscription) {
            this.visibleModalsSubscription.unsubscribe();
        }
        window.removeEventListener('resize', this.resizeIframe, false);
    }

    initialize() {
        let getProject = this.projectService.getProject(this.projectId, null, false, false, false, false, true, true);
        let getActiveInductionQues = this.inductionQuestionsService.getActiveInductionQuestionsOfProject(this.projectId);
        let getKnownCompetencies = this.resourceService.getCompetencies({country_code: (this.authUser$.country_code || undefined)});
        let getExistingInduction = this.userService.getMyInductionRequest(this.inductionRequestId);
        let getUserDocument = this.userService.getMyDocuments({validOnly: 'false'});
        let getUserContactDetail = this.userService.getMyContactDetail();
        let getUserEmploymentDetail = this.userService.getMyEmploymentDetail();
        let getUserHealthAssessmentAnswers = this.userService.getMyHealthAssessmentAnswers();
        let getUserMedicalAssessmentAnswers = this.userService.getMyMedicalAssessmentAnswers();
        let getRamsForInduction = this.projectRamsService.getRamsForInduction(this.projectId);

        let apiCalls = [getProject, getActiveInductionQues, getKnownCompetencies, getUserDocument, getUserContactDetail,  getUserEmploymentDetail, getUserHealthAssessmentAnswers, getUserMedicalAssessmentAnswers, getRamsForInduction];
        if (this.inductionRequestId) {
            apiCalls.push(getExistingInduction);
        }
        this.processingLoader = true;
        zip(...apiCalls).subscribe((result : any) => {
            this.processingLoader = false;

            let existingInductionData = (this.inductionRequestId && result[9]) ? result[9] : null;

            if (!result[0].success || !result[0].contractor) { this.apiFailedAlert(result[0].message ? result[0].message : 'Something went wrong while fetching the project.'); return; }
            let {ask, enabled} = getPendingAssessmentStatus(this.authUser$.user_onboard_status, result[0].contractor.features_status); // admin level flags
            if(ask.health || ask.medical){
                const message = 'Given project requires you to complete Health / Medical assessment.';
                this.toastService.show(this.toastService.types.INFO, message, { data: ask });
                return this.router.navigate(['/site-user/search-induction'], {queryParams: {search: this.project.record_id}});
            }
            this.ramsSignatureStatus = result[0]?.project?.custom_field?.briefing_signatures?.rams;
            this.enabledProfileAssessment = enabled;
            this.contractor_feature_status = result[0].contractor?.features_status;
            this.showEmploymentStartDate = this.featureExclusionUtility.showEmploymentStartDate();
            this.showTypeOfEmployment = this.featureExclusionUtility.showTypeOfEmployment();
            this.showMinWage = this.featureExclusionUtility.showMinWage();
            this.showNIN = this.featureExclusionUtility.showNIN();
            this.showEmpNbr = this.featureExclusionUtility.showEmpNbr(this.authUser$.country_code);
            this.showProfileMedicalAssessment = this.featureExclusionUtility.showProfileMedicalAssessment();
            this.showProfileHealthAssessment = this.featureExclusionUtility.showProfileHealthAssessment();
            this.showInductionHealthAssessment = this.featureExclusionUtility.showInductionHealthAssessment();
            if(this.contractor_feature_status.health_assessment !== undefined){
                this.showProfileMedicalAssessment = this.contractor_feature_status.medical_assessment;
            }
            if(this.contractor_feature_status.health_assessment !== undefined){
                this.showProfileHealthAssessment = this.contractor_feature_status.health_assessment;
            }
            if(this.contractor_feature_status.induction_health_q !== undefined){
                this.showInductionHealthAssessment = this.contractor_feature_status.induction_health_q;
            }
            if(!this.showInductionHealthAssessment){
                let index = this.formStages.findIndex(n => n.name === 'Health Assessment');
                if(index > -1) {
                    this.formStages.splice(index, 1);
                }
            }
            if (!result[1].success) { this.apiFailedAlert(result[1].message ? result[1].message : 'Something went wrong while fetching ACTIVE induction questions of project.'); return; }
            if (!result[2].success) { this.apiFailedAlert(result[2].message ? result[2].message : 'Failed to fetch competencies list records.'); return; }
            if (!result[3].success) { this.apiFailedAlert(result[3].message ? result[3].message : 'Failed to get user documents.'); return; }
            if (!result[4].success) { this.apiFailedAlert(result[4].message ? result[4].message : 'Failed to find user contact detail.'); return; }
            if (!result[5].success) { this.apiFailedAlert(result[5].message ? result[5].message : 'Failed to find user employment details.'); return; }
            if (!result[6].success && enabled.health_answers_required) { this.apiFailedAlert(result[6].message ? result[6].message : 'Failed to find User Health Assessment Answers.'); return; }
            if (!result[7].success && enabled.medical_answers_required) { this.apiFailedAlert(result[7].message ? result[7].message : 'Failed to find User Medical Assessment Answers.'); return; }
            if (!result[8].success) { this.apiFailedAlert(result[8].message ? result[8].message : 'Something went wrong which fetching RAMS to include during induction process.'); return; }

            if (this.inductionRequestId && !existingInductionData.success) { this.apiFailedAlert(existingInductionData.message ? existingInductionData.message : 'Failed to fetch induction record.'); return; }
            if (this.inductionRequestId && existingInductionData.success && existingInductionData.induction_request && existingInductionData.induction_request.id && existingInductionData.induction_request.status_code != 3) { this.apiFailedAlert('This induction request is in non-editable state'); return; }

            this.enabledProfileAssessment = enabled;
            this.contractor_cscs_config = {
                ...(result[0].contractor.cscs_status),
                _company_ref: result[0].contractor.id,
            };
            this.contractor_rtw_config = result[0].contractor.rtw_status || new CompanyRtwConfig();
            if(this.contractor_rtw_config.enabled){
                this.contractor_rtw_config._company_name = result[0].contractor.name;
                let index = this.formStages.findIndex(n => n.name === this.STAGELIST.competencies)||0;
                this.formStages.splice(index+1, 0, this.RIGHT_TO_WORK_STAGE);
            }
            this.getUserEmploymentDetail_handler(result[5]);
            this.getProject_handler(result[0]);
            this.getActiveInductionQues_handler(result[1]);
            this.getKnownCompetencies_handler(result[2]);
            this.getUserDocument_handler(result[3]);
            this.getUserContactDetail_handler(result[4]);
            this.getUserHealthAssessmentAnswers_handler(result[6]);
            this.getUserMedicalAssessmentAnswers_handler(result[7]);
            this.getRamsForInduction_handler(result[8]);
            if (existingInductionData) {
                this.getExistingInduction_handler(existingInductionData);
            }
            this.assignTravelTime();
        });

        this.induction_request.accepted_further_policies = [];
        this.visibleModalsSubscription = this.modalService.activeInstances.subscribe(visibleModalRefs => {
            let modalsList = (visibleModalRefs || []);
            this.lastVisibleModal = (Object.prototype.toString.call(modalsList) === '[object Array]') ? modalsList.slice(-1)[0] : undefined;
        });
    }

    assignTravelTime() {
        /* If Induction travel time alrady exist */
        if(this.induction_request?.travel_time_to_home) {
            this.travelTimeToHome = this.createTime(this.induction_request.travel_time_to_home, true);
        }
        if(this.induction_request?.travel_time_to_work) {
            this.travelTimeToWork = this.createTime(this.induction_request.travel_time_to_work, true);
        }
    }

    apiFailedAlert(message) {
        this.toastService.show(this.toastService.types.ERROR, message);
        this.router.navigate(['/site-user']);
    }

    getCompanyFrSetting(companyId, custom_field: any = {}){
        if(this.authUser$.profile_pic_ref){
            return true;
        }
        this.resourceService.getCompanySettingByName('facial_recognition_config', companyId).subscribe((fr_config: any) => {
            if (!fr_config.success) {
                return this.apiFailedAlert(fr_config.message ? fr_config.message : 'Failed to get company setting');
            }
            let company_has_photo_required = !(((fr_config.record && fr_config.record.value) || {}).photo_required === false);
            if(company_has_photo_required){
                let message = `Before you submit your ${custom_field?.induction_phrase_singlr} on this project you must upload a valid profile photo first.`;
                this.toastService.show(this.toastService.types.INFO, message);
                //@todo: once we move to new design, below line can be replaced with comment one.
                window.location.replace('/on-board/personal-details');
                // return this.router.navigate(['/on-board/personal-details'], {queryParams: {pending: 'profile.pic'}});
            }
        });
    }

    getProject_handler(data) {
        this.project = data.project;
        this.inductionTitle = (this.project.custom_field.induction_phrase_singlr)?.toUpperCase() ?? 'INDUCTION';
        this.has_skills_matrix = data.contractor?.company_flags?.skills_matrix || false;
        // console.log('has_skills_matrix', this.has_skills_matrix, this.employment_detail);
        this.getCompanyFrSetting(data.contractor?.id, this.project.custom_field);
        this.induction_request.project_ref = data.project.id;
        // @todo: need to validate this inductor id before use
        this.induction_request.inductor_ref = this.inductorId;
        this.additional_data.project = data.project;
        this.beforeInductionStart(data.project, this.has_skills_matrix, this.employment_detail);
        this.induction_request.induction_answers = [];
        //filter default policies
        this.getDefaultPolicies();
        // Get company logo file url from project.
        const projectLogo = this.project?.logo_file_id;
        this.companyLogoFileUrl = projectLogo?.sm_url ?? projectLogo?.md_url ?? projectLogo?.file_url ?? this.logoPlaceholder;
    }

    getActiveInductionQues_handler(data) {
        this.quizSets = data.quiz || [];
        this.additionalQSets = data.question || [];
        this.onQLocaleChange();
        this.onIQLocaleChange();
    }

    getKnownCompetencies_handler(data) {
        this.knownCompetencies = data.competencieslist;

        let {names, mandatory_docs_list} = this.profileUtility.getMandatoryDocNames(this.knownCompetencies, this.MANDATORY_COMPETENCY_TAG);
        this.cscsCompetenciesNames = mandatory_docs_list.map(d => d.name);

        this.cscs_require_msg = `Please add either your ${names.join(', ')} to continue`;
        this.group_competencies = (this.knownCompetencies || []).filter(c => (c.tags || []).includes(this.GROUP_COMPETENCY_TAG)).map(c => c.name);
        this.nonGroupKnownCompetencies = (this.knownCompetencies || []).filter(c => !(c.tags || []).includes(this.GROUP_COMPETENCY_TAG));
    }

    getExistingInduction_handler(data) {
        if(data.induction_request && data.induction_request.id) {
            if (+data.induction_request.project_ref !== this.projectId) {
                this.apiFailedAlert('Invalid record id');
                return;
            }
            data.induction_request.travel_time_to_work = data.induction_request.travel_time ? data.induction_request.travel_time.to_work : this.induction_request.travel_time_to_work;
            data.induction_request.travel_time_to_home = data.induction_request.travel_time ? data.induction_request.travel_time.to_home : this.induction_request.travel_time_to_home;

            this.induction_request = data.induction_request;
            this.induction_request._need_rtw_check = true;
            if(this.induction_request.rtw_doc_code){
                this.induction_request._has_rtw_doc_code = 'yes';
            }
            this.onLongMedicationChanged();
            this.induction_request.accepted_further_policies = [];

            if (this.induction_request.travel_time && this.induction_request.travel_time.overrides) {
                let user_override = this.induction_request.travel_time.overrides.find(o => o.creator_ref === this.authUser$.id);
                if (user_override) {
                    // has override
                    this.travel_time_override = user_override; // || this.enableTravelTimeOverride(true);
                    this.induction_request._postcode_is_correct = 'no';
                } else {
                    this.induction_request._postcode_is_correct = 'yes';
                }
            }
            console.log('Loaded existing induction request', data.induction_request?.induction_question_answers);
            if (data.induction_request?.induction_question_answers?.quiz_lang) {
                // this.quizLocale = data.induction_request.induction_question_answers.quiz_lang;
                // this.onQLocaleChange();
            }
            if (data.induction_request?.induction_question_answers?.additional_qa_lang) {
                // this.additionalQLocale = data.induction_request.induction_question_answers.additional_qa_lang;
                // this.onIQLocaleChange();
            }
        }
    }

    getUserDocument_handler(data) {
        console.log("UserDocument->>>", data.user_documents);
        this.user_documents = this.profileUtility.attachDocIsExpired(data.user_documents);
        this.user_documents = this.attachUserDocumentFlags(this.user_documents);
        this.induction_request.user_doc_ids = data.user_documents.map(d => d.id);
        return data.user_documents;
    }

    private attachUserDocumentFlags(list){
        const v_mandatory = this.contractor_cscs_config.enabled && this.contractor_cscs_config.verification_mandatory;

        let verificationEnableMetaCompetencies = this.knownCompetencies.reduce((names, c) => {
            if(c.name && c.scheme_id){
                names.push(c.name);
            }
            return names;
        }, []);
        // console.log('verificationEnableMetaCompetencies', verificationEnableMetaCompetencies);
        return (list || []).map(d => {
            d._citb_enabled_doc = this.contractor_cscs_config.enabled && verificationEnableMetaCompetencies.includes(d.name);
            d._verification_mandatory = v_mandatory && (d._is_expired || !d.is_verified) && verificationEnableMetaCompetencies.includes(d.name);
            d._is_required = (
                this.autoSelectedCompetenciesId.includes(d.id) ||
                ((d.name == this.passportCompetencyName) && this.project.is_passport_require) ||
                (this.project.other_doc_required && this.project.other_doc_required.length && this.project.other_doc_required.includes(d.name)) ||
                (this.project.is_cscs_require && this.cscsCompetenciesNames.includes(d.name))
            );
            return d;
        });
    }

    getUserContactDetail_handler(data) {
        this.contactDetail = data.contact_detail;
        this.additional_data.contact_detail = data.contact_detail;
        return data.contact_detail;
    }

    getUserEmploymentDetail_handler(data) {
        this.additional_data.employment_detail = data.employment_detail;
        this.employment_detail = data.employment_detail;
        return data.employment_detail;
    }

    getUserHealthAssessmentAnswers_handler(data) {
        this.additional_data.health_assessment_answers = data.health_assessments;
        this.healthAssessment = this.groupByHealthAnswers(data.health_assessments);
    }

    getUserMedicalAssessmentAnswers_handler(data) {
        this.medicalAssessment = data.medical_assessments;
        this.additional_data.medical_assessments_answers = data.medical_assessments;
    }

    getRamsForInduction_handler(data) {
        this.induction_rams = data.induction_rams;
        if (this.induction_rams.length) {
            this.selectedRams = (this.induction_rams.length > 1) ? {} : this.induction_rams[0];
            this.selectedRamsId = (this.selectedRams && this.selectedRams.id) ? this.selectedRams.id : null;
            let numberOfStages = this.formStages.length;
            this.ramsPhrase = data.rams_phrase;
            //push stage before "Review" stage
            if (
              !this.formStages.some((item) => item.name === this.ramsPhrase)
            ) {
              this.formStages.splice(numberOfStages - 1, 0, {
                name: this.ramsPhrase,
                icon: this.sidebarIcons.rams,
              });
            }
        } else {
            const index = this.formStages.findIndex(
              (item) => item.name === this.ramsPhrase
            );
            if (index !== -1) {
              this.formStages.splice(index, 1);
            }
        }
    }

    listenForMessage(e) {
        let d = (new URL(this.first_media_url));
        console.log('got message, from', e.origin, e);
        if (e.origin !== d.origin) { //added security
            return;
        }
        if (e.data.action == 'postCompletion') { //score will be empty for non-scorable content or 0 to 100 for scorable content
            this.confirmationModalReference.openConfirmationPopup({
                headerTitle: '',
                hideHeader: true,
                title: `Thanks for watching, you may now continue with your ${this.project.custom_field.induction_phrase_singlr}.`,
                confirmLabel: 'Ok',
                hasCancel: false,
            });
            this.watched_html_media = true;
            console.log('score: ', e.data.value);
        }
    }

    onQLocaleChange(){
        this.inductionQuestions = this.quizSets.find(q => q.lang === this.selectedLocale) || {};
        if(this.quizSets.length && !this.inductionQuestions.lang){
            console.log('Looking for default quiz');
            this.inductionQuestions = this.quizSets.find(q => q.is_default) || this.quizSets[0];
        }
    }

    onIQLocaleChange(){
        this.additionalInductionQuestions = this.additionalQSets.find(q => q.lang === this.selectedLocale) || {};
        if(this.additionalQSets.length && !this.additionalInductionQuestions.lang){
            console.log('Looking for default IQ');
            this.additionalInductionQuestions = this.additionalQSets.find(q => q.is_default) || this.additionalQSets[0];
        }
    }

    onMediaLanguageChange(){
        let file_media_list = (this.additional_data.project.media_resources || []).filter(mr => mr.type === 'file');
        let url_media_list = (this.additional_data.project.media_resources || []).filter(mr => mr.type === 'url');

        this.media_files = file_media_list.filter(mr => mr.lang === this.selectedLocale);
        let first_url_media = url_media_list.find(mr => mr.lang === this.selectedLocale);

        if(!this.media_files.length && file_media_list.length){
            // check for default one if any.
            console.log('Looking for default media file');
            this.media_files = file_media_list.filter(m => m.is_default);
            if(!this.media_files.length){
                this.media_files = [file_media_list[0]];
            }
        }
        if(!first_url_media && url_media_list.length) {
            // check for default one if any.
            console.log('Looking for default media url');
            first_url_media = url_media_list.find(mr => mr.is_default) || url_media_list[0];
        }

        if(first_url_media) {
            this.first_media_url = first_url_media.content;
            this.e_media_url = this.sanitizer.bypassSecurityTrustResourceUrl(first_url_media.content + `&ap=${this.authUser$.id}@${window.location.hostname}&f=${this.authUser$.first_name}&l=${this.authUser$.last_name}`);
        }
        console.log('filtered media data');
        console.log(this.media_files);
        console.log(this.e_media_url);

        if(!this.media_files.length && (!this.inductionQuestions || !this.inductionQuestions.status)){
            // when, doesn't have media file & induction question
            let index = this.formStages.findIndex(n => n.name === 'Media');
            if(index > -1) {
                this.formStages.splice(index, 1);
            }
        }
    }


    @ViewChild('beforeInductionStartComponentRef') set content(content: BeforeInductionStartComponent) {
        if(content) { // initially setter gets called with undefined
            this.beforeInductionStartComponentRef = content;
        }
    }
    private beforeInductionStartComponentRef: BeforeInductionStartComponent;
    private beforeInductionStart(p, has_skills_matrix, e){
        this.beforeInductionStartComponentRef.initializeModal(p, has_skills_matrix, e);
    }

    onEmploymentDetailUpdate({employment_detail, parent_company, country_code, nin}){
        console.log('Updated Employment details', employment_detail);
        this.employment_detail = employment_detail;
        this.authUser$.parent_company = parent_company;
        this.authUser$.country_code = country_code;
        this.authUser$.nin = nin;
        this.additional_data.employment_detail = employment_detail;
        this.additional_data.user_info.parent_company = parent_company;
        this.additional_data.user_info.country_code = country_code;
        this.additional_data.user_info.nin = nin;
        this.resourceService.getCompetencies({ country_code: (this.authUser$.country_code || undefined) }).subscribe(this.getKnownCompetencies_handler.bind(this));
        this.postcodeInput = this.featureExclusionUtility.showProfilePostalCode(this.authUser$.country_code);
        this.labelData = this.featureExclusionUtility.generateLabels(this.postcodeInput?.type);
        if(this.project.custom_field && this.project.custom_field.has_rams_in_induction) {
            this.projectRamsService.getRamsForInduction(this.projectId).subscribe((data: any) => {
                if(data && data.success) {
                    this.getRamsForInduction_handler(data);
                } else {
                    const message = data.message || 'Something went wrong while fetching RAMS to include during induction process.';
                    this.toastService.show(this.toastService.types.ERROR, message);
                }
            });
        }
    }

    onUserDocsUpdated({all_documents}){
        // console.log('Update All documents', all_documents);
        this.user_documents = this.profileUtility.attachDocIsExpired(all_documents);
        this.invalid_sets = this.validateJobRoleRule();
        this.missingDocumentMessages = this.getMissingDocMessage();
        this.user_documents = this.attachUserDocumentFlags(this.user_documents);
        this.induction_request.user_doc_ids = all_documents.map(d => d.id);
        return all_documents;
    }

    onProfileDetailConfirmation($event){
        console.log('Profile details confirmed', $event);
        // Update feature visibility based on utility methods

        // ideally there is no need to check for existence of new stages already.
        // Handle first aid competency
        if($event.is_first_aid && $event.is_first_aid === 'yes'){
            this.autoSelectedCompetenciesId.push($event.first_aider_doc.id);
        } else if($event?.is_first_aid === 'no') {
            let index = this.autoSelectedCompetenciesId.findIndex(id => id === $event.first_aider_doc?.id);
            if (index !== -1) {
                this.autoSelectedCompetenciesId.splice(index, 1);
            }
        }

        // Handle supervisor competency
        this.handleCompetency($event, 'supervisor', 'is_supervisor', this.SUPERVISOR_STAGE, 'supervisor_doc');
        if (this.induction_request.id && this.induction_request.declarations) {
            this.induction_request.declarations.supervisor = [];
        }

        // Handle machine operator competency
        this.handleCompetency($event, 'm_operator', 'is_m_operator', this.OPERATOR_STAGE, 'operator_doc');
        if (this.induction_request.id && this.induction_request.declarations) {
            this.induction_request.declarations.operator = [];
        }

        // Update active skills rule set
        this.active_skills_rule = $event.skills_rule_set;
        this.onUserDocsUpdated({all_documents: this.user_documents});
        // Handle locale changes
        if($event.locale){
            // selected locale from BYS
            console.log('default locale', this.selectedLocale, ' and locale from BYS', $event.locale);
            this.selectedLocale = $event.locale;
            this.onQLocaleChange();
            this.onIQLocaleChange();
            // this.onMediaLanguageChange();
            this.initLocalizedSteps();
        }
        this.moveNext();
    }

    private handleCompetency($event: any, competencyType: string, stageProperty: string, stage: any, docProperty: string) {
        let index = this.formStages.findIndex(n => n[stageProperty] === true);
        let docId = $event[docProperty]?.id;

        if ($event[`is_${competencyType}`] === 'yes') {
            if (docId && !this.autoSelectedCompetenciesId.includes(docId)) {
                this.autoSelectedCompetenciesId.push(docId);
            }
            if (index === -1) {
                stage[stageProperty] = true;
                let declarationIndex = this.formStages.findIndex(n => n.name === this.STAGELIST.declaration);
                if (declarationIndex !== -1) {
                    this.formStages.splice(declarationIndex, 0, stage);
                }
                this.getExtraDeclarations(stage.meta_key);
            }
        } else if ($event[`is_${competencyType}`] === 'no') {
            let selectedCompetencyIndex = this.autoSelectedCompetenciesId.indexOf(docId);
            if (selectedCompetencyIndex !== -1) {
                this.autoSelectedCompetenciesId.splice(selectedCompetencyIndex, 1);
            }
            if (index !== -1) {
                this.formStages.splice(index, 1);
            }
        }
    }

    private initLocalizedSteps(){
        if(this.inductionQuestions.induction_questions) {
            // this.correctAnswer = Array(this.inductionQuestions.induction_questions.length).fill(false);
            this.correctAnswer = [];
        }
        if(this.inductionQuestions.hasOwnProperty('status')) {
            // Remove media step when NOT enabled. OR not having IQ
            if(this.additional_data.project && !this.additional_data.project.has_media_content && !this.inductionQuestions.status){
                let index = this.formStages.findIndex(n => n.name === 'Media');
                if(index > -1) {
                    this.formStages.splice(index, 1);
                }
            }
        } else {
            // Remove media step when NOT enabled.
            if(this.additional_data.project && !this.additional_data.project.has_media_content){
                let index = this.formStages.findIndex(n => n.name === 'Media');
                if(index > -1) {
                    this.formStages.splice(index, 1);
                }
            }
        }

        if(this.isAdditionalQuestionStageActivated() && !this.hasFormStag(this.additionalInductionQuestions.section_title)) {
            this.additionalSectionTitle = this.additionalInductionQuestions.section_title;
            let additionalSection = { name: this.additionalSectionTitle, icon: this.sidebarIcons.additionalQuestions, additionalSection: true };
            let additionalSectionIndex = this.formStages.findIndex(obj => obj?.additionalSection === true);
            if(additionalSectionIndex > -1) {
                this.formStages[additionalSectionIndex] = additionalSection;
            } else {
                let healthIndex = this.formStages.findIndex(stage => stage.name === 'Health Assessment');
                if(healthIndex > -1) {
                    this.formStages.splice(healthIndex+1, 0, additionalSection);
                } else {
                    let fallback = this.formStages.findIndex(stage => stage.name === 'Safety Assessment');
                    this.formStages.splice(fallback+1, 0, additionalSection);
                }
            }
        }

        if(
            this.additional_data.project &&
            this.additional_data.project.has_media_content
        ){
            // this.filterAvailableLanguages();
            this.onMediaLanguageChange();

            if(this.e_media_url){
                // it has embedding video URL
                // let index = this.formStages.findIndex(n => n.name === 'Media');
                // this.formStages.splice(index, 0, this.EMBEDDED_MEDIA_STAGE);
                // this.watched_html_media = false;
                // window.addEventListener('message', this.listenForMessage.bind(this), false);
            }else{
                this.watched_html_media = true;
            }
        }
    }

    private hasFormStag(targetName: string) {
        return this.formStages.some(obj => obj.name === targetName);
    }

    private getExtraDeclarations(name){
        this.resourceService.getInnDexSettingByName(name).subscribe(data => {
            if(data.success && data.record){
                this.extraDeclarations[name] = data.record.value;
            } else {
                const message = data.message || 'Failed to fetch additional declarations list.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    private emptyMedicationRow(){
        return {
            identifier: (new Date()).getTime()
        };
    }

    private getCompetencies(){
        console.log('Fetching competencies');
        this.userService.getMyDocuments({validOnly: 'false'}).subscribe((data: any) => {
            if (data.user_documents) {
                return this.onUserDocsUpdated({all_documents: data.user_documents});
            } else {
                const message = data.message || 'Failed to fetch competencies data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    /*
    private getEmploymentDetail(){
        this.userService.getMyEmploymentDetail().subscribe(data => {
            if (data && data.employment_detail) {
                this.additional_data.employment_detail = data.employment_detail;
                this.employment_detail = data.employment_detail;
                return data.employment_detail;
            } else {
                console.log('employment detail API response ', data);
                alert(data.message ? data.message : 'Failed to fetch employment detail');
            }
        });
    }*/

    /**
     * create grouped array of answers for easy rendering
     * @param records
     */
    private groupByHealthAnswers(records = []){
        let categories = {};
        records.map(row => {
            let q = row.question_ref || {};
            if(q.category){
                if(!categories[q.category]){
                    categories[q.category] = [];
                }
                categories[q.category].push({
                    question: q.question,
                    answer: row.answer,
                    order: q.order
                });
            }
        });
        return categories;
    }

    checkAnswer($event, questionId, optionId, questionUId) {
        if($event.target.checked) {
            if(!this.inductionQuestions.induction_questions[questionId].multichoice) {
                this.induction_request.induction_answers[questionId] = [this.inductionQuestions.induction_questions[questionId].options[optionId]];
            } else {
                if(this.induction_request.induction_answers[questionId] && this.induction_request.induction_answers[questionId].length >=1) {
                    this.induction_request.induction_answers[questionId] = [...this.induction_request.induction_answers[questionId], this.inductionQuestions.induction_questions[questionId].options[optionId]];
                } else {
                    this.induction_request.induction_answers[questionId] = [this.inductionQuestions.induction_questions[questionId].options[optionId]];
                }
            }

        } else {
            if(!this.inductionQuestions.induction_questions[questionId].multichoice) {
                this.induction_request.induction_answers[questionId] = [];
            } else {
                let ind = this.induction_request.induction_answers[questionId].indexOf(this.inductionQuestions.induction_questions[questionId].options[optionId]);
                this.induction_request.induction_answers[questionId].splice(ind, 1);
            }
        }
        this.checkIfCorrect(questionUId, this.inductionQuestions.lang, this.induction_request.induction_answers[questionId], questionId);
    }

    checkIfCorrect(questionUId, lang, userAnswers, questionId) {
        let answers = ((this.inductionQuestions.induction_answers || []).find(a => a && a.question_id === questionUId) || {}).answers;
        if(!answers || !userAnswers){
            this.correctAnswer[questionId] = false;
            return;
        }
        answers = answers.sort();
        userAnswers = [...userAnswers].sort();
        this.correctAnswer[questionId] = (answers.length === userAnswers.length) && answers.every(function(element, index) {
            return element === userAnswers[index];
        });
    }

    checkIfAnswersCorrect() {
        if(this.inductionQuestions.status && this.inductionQuestions.induction_questions) {
            if(this.inductionQuestions.induction_questions.length === this.correctAnswer.length) {
                if(!this.correctAnswer.includes(false)) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }
    moveNextAfterQuiz(valid: boolean) {
        if(this.checkIfAnswersCorrect()){
            const msg = `You have answered ${this.correctAnswer.filter(v => !v).length} question(s) incorrectly. Please answer all questions correctly to proceed`;
            this.confirmationModalReference.openConfirmationPopup({
                headerTitle: 'Incorrect Answers',
                title: msg,
                confirmLabel: 'Ok',
                hasCancel: false,
            });
            return ;
        }
        if(valid){
            this.moveNext();
        }
    }

    createEmploymentTime(epoch_ms) {
        if(epoch_ms && !isNaN(+epoch_ms)){
            let m = dayjs(+epoch_ms);
            let d = dayjs.duration(dayjs().diff(m));
            return dayjs.isDuration(d) ? (
                (d.years() ? d.years() + ' year': '')
                + ' '
                + (d.months() ? d.months() + ' month': '')
                + ' '
                + (!d.years() ? (d.days() ? d.days() + ' day': ''): '')
            ) : null;
        }
        return epoch_ms;
    }

    isNotThisStage(label) {
        let index = this.formStages.findIndex(v => v.name === label);
        return this.activeStage !== index
    }

    movePrev() {
        if (this.activeStage === 0) { // i would become 0
            this.activeStage = this.formStages.length; // so put it at the other end of the array
        }
        this.activeStage = this.activeStage - 1; // decrease by one
        return this.formStages[this.activeStage]; // give us back the item of where we are now
    }

    moveNext(allow_to_continue = false) {

        if(this.formStages[this.activeStage].name === 'Travel' && this.induction_request._postcode_is_correct === 'no' && this.induction_request.travel_time){
            // copy travel time info into override
            this.travel_time_override.travel_time = {
                to_work: this.induction_request.travel_time_to_work,
                to_home: this.induction_request.travel_time_to_home,
            };
            this.travel_time_override.travel_method = this.induction_request.travel_method;
            this.travel_time_override.vehicle_reg_number = this.induction_request.vehicle_reg_number;
            this.travel_time_override.vehicle_info = this.induction_request.travel_time.vehicle_info;
            this.induction_request.travel_time.overrides = this.getNewTravelTimeOverrides(this.travel_time_override, this.travel_time_override.timestamp);
        }

        if (
            this.formStages[this.activeStage].name === this.STAGELIST.competencies
        ) {
            // Moving next from competencies step.
            let good_to_move = this.validateCompetencyStepBefore(allow_to_continue);
            if (!good_to_move) {
                return false;
            }
        }

        this.activeStage = this.activeStage + 1; // increase i by one
        this.invalid_sets = this.validateJobRoleRule();

        let others_missing = (this.additional_data.project.other_doc_required || []).filter(name => this.checkGivenDocIsMissing(name));

        if(this.formStages[this.activeStage] &&
            this.formStages[this.activeStage].name === 'Media' &&
            this.media_files &&
            this.media_files.length
        ){
            // when media step does have file
            this.induction_request.accepting_media_declaration = false;
        }

        if(this.formStages[this.activeStage].name === 'Competencies & Certs' && (
            (this.additional_data.project.is_passport_require && this.checkPassportDocIsMissing()) ||
            (others_missing.length) ||
            (this.invalid_sets.length) ||
            (this.additional_data.project.is_cscs_require && this.checkCSCSDocIsMissing()))
        ) {
            this.openCscsRequiredModal();
        }
        this.activeStage = this.activeStage % this.formStages.length; // if we've gone too high, start from `0` again
        if (this.formStages[this.activeStage] &&
            this.formStages[this.activeStage].name === this.EMBEDDED_MEDIA_STAGE.name
        ) {
            setTimeout(() => {
                this.resizeIframe();
            }, 0)
        }
        return this.formStages[this.activeStage]; // give us back the item of where we are now
    }

    private moveToNextStep(){
        console.log('Move to next step');
        this.moveNext(true);
    }

    private validateCompetencyStepBefore(allow_to_continue) {
        /*
        Bulk verification logic:
            CSCS verification is mandatory to induct: TRUE
                User has at least one un-verified eligible competency
                    Next button disabled on app (enabled on web), but when clicked, show popup: message #3
                        NO: Do nothing
                        YES: Perform bulk verification:
                            All competencies verified, enable next button, and show message: "All competencies/certifications verified", popup options: ["Continue"], when pressed, takes them to next step
                            Some competencies not verified, show message: "Some of your competencies/certifications are not verified" and popup options: ["Edit"], stay on same screen
            CSCS verification is mandatory to induct: FALSE
                User has at least one un-verified eligible competency
                    Next button enabled on app and web, but when clicked, show popup: message #4 with options ["No", "Yes"]
                        NO: go to next step
                        YES: Perform bulk verification:
                            All competencies verified, show message: "All competencies/certifications verified", popup options: ["Continue"], when pressed, takes them to next step
                            Some competencies not verified, show message: "Some of your competencies/certifications are not verified" and popup options: ["Edit", "Continue], Edit => Stay on same page, Continue => go to next step
*/
        // Moving next from competencies step.
        let expired_docs = this.user_documents.filter(d => d._is_required && d._is_expired);
        let has_unverified_one = this.user_documents.find(d => d._verification_mandatory);
        let optional_unverified_one = this.user_documents.filter(d => d._citb_enabled_doc && !d.is_verified);
        let message = ``;
        let has_cancel = false;
        let verify = false;
        let confirm_label = 'Ok';
        let cancel_move_next = false;
        // let yes_move_next = false;
        if (expired_docs.length > 1) {
            message = `You have ${expired_docs.length} expired competencies/certification, please update to continue`;
        } else if (expired_docs.length === 1) {
            let [first] = expired_docs;
            message = `Your ${first.name} competency/certification has expired, please update to continue`;
        } else if (has_unverified_one) {
            has_cancel = true;
            verify = true;
            message = `Verified competencies are mandatory to induct onto this project. We noticed you currently have some unverified competencies. Would you like to verify them now?`;
        } else if (optional_unverified_one.length && !allow_to_continue) {
            has_cancel = true;
            verify = true;
            cancel_move_next = true;
            // yes_move_next = true;
            message = `We noticed you currently have some unverified competencies. Would you like to verify them now?`;
        }
        if (message) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Competency Verification',
                title: message,
                confirmLabel: verify ? 'Yes' : confirm_label,
                hasCancel: has_cancel,
                cancelLabel: 'No',
                onConfirm: () => {
                    if (!verify) {
                        return;
                    }
                    // make auto verify call.
                    this.autoVerifyMyCompetencies();
                },
                onClose: () => {
                    cancel_move_next && this.moveToNextStep();
                },
            });
            return false;
        }
        return true;
    }

    private autoVerifyMyCompetencies(){
        this.processingLoader = true;
        this.userService.autoVerifyUserDocuments(this.contractor_cscs_config._company_ref, {
            project_id: this.project.id
        }).subscribe((data:any) => {
            this.processingLoader = false;
            if(data.user_documents && this.getUserDocument_handler(data)){
                let has_unverified_one = this.user_documents.find(d => d._verification_mandatory);
                let optional_unverified_one = this.user_documents.filter(d => d._citb_enabled_doc && !d.is_verified);
                if(has_unverified_one){
                    this.confirmationModalRef.openConfirmationPopup({
                        headerTitle: 'Competency Verification',
                        title: `Some of your competencies/certifications are not verified`,
                        confirmLabel: 'Edit',
                        hasCancel: false,
                    });
                }else if(optional_unverified_one.length){
                    this.confirmationModalRef.openConfirmationPopup({
                        headerTitle: 'Competency Verification',
                        title: `Some of your competencies/certifications are not verified`,
                        confirmLabel: 'Continue',
                        cancelLabel: 'Edit',
                        onConfirm: () => {
                            this.moveToNextStep();
                        }
                    });
                }else{
                    this.confirmationModalRef.openConfirmationPopup({
                        headerTitle: 'Competency Verification',
                        title: `All competencies/certifications verified`,
                        confirmLabel: 'Continue',
                        hasCancel: false,
                        onConfirm: () => {
                            this.moveToNextStep();
                        }
                    });
                }
                // onComplete && onComplete();
            } else {
                const message = data.message || 'Failed to verify user competencies.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: data });
            }
        });
    }

    private validateJobRoleRule(){
        // console.log('validate');        console.log(this.active_skills_rule);        console.log(this.user_documents);
        if(!this.has_skills_matrix || this.additional_data?.project._user_excluded_from_mandatory_doc){
            return [];
        }
        if(this.user_documents && this.user_documents.length && this.active_skills_rule){
            let flat_list = (this.skillMatrixService.deserializeChildEntries(this.active_skills_rule.child)).map(c => c.name);
            // console.log(flat_list);
            let auto_select = this.user_documents.filter(d => flat_list.includes(d.name) && !this.autoSelectedCompetenciesId.includes(d.id));
            // console.log('auto_select', auto_select);
            if(auto_select.length){
                this.autoSelectedCompetenciesId.push(...auto_select.map(d => d.id));
            }
        }
        return this.skillMatrixService.validateRuleset(this.active_skills_rule, this.user_documents);
    }

    resizeIframe() {
        let frame = document.getElementById('embeddedVideoFrame');
        if(frame && frame.style){
            if(this.httpService && !this.httpService.isMobileDevice()){
                frame.style.height = frame.clientWidth * .5625 + "px";
            } else {
                frame.style.height = frame.clientWidth / .5625 + "px";
            }
        }
    }

    getKeys(obj) {
        return Object.keys(obj);
    }

    changeModel(ev, list: Array<any>, val: number, isChecked: boolean = false) {
        if (ev?.target?.checked || isChecked) {
            if (!list.includes(val)) {
                list.push(val);
            }
        } else {
            let i = list.indexOf(val);
            if (i > -1) {
                list.splice(i, 1);
            }
        }
        return list;
    }


    storeDeclaration(category, item, answer, shouldRemove: boolean = false){
        let list = (this.induction_request.declarations && this.induction_request.declarations[category]) || [];
        console.log('key', category,'Store selection', answer, 'shouldRemove', shouldRemove);
        let recordIndex = list.findIndex(o => o.id === item.id);
        if(recordIndex !== -1){
            list[recordIndex] = {
                ...item,
                answer
            }
        }else{
            list.push({
                ...item,
                answer
            });
        }
        this.induction_request.declarations[category] = list;
    }

    checkDeclarationValue(category, id, returnKey = 'answer'){
        let row = (((this.induction_request.declarations && this.induction_request.declarations[category]) || []).find(row => row.id === id) || {});
        return returnKey ? row[returnKey] : row;
    }

    toggleImModal($imModal){
        $imModal.isShowing = true;
    }

    isDisabled(key:string){
        return this.induction_request[key] !== 'yes';
    }

    addMedicationBlock(){
        this.induction_request.medications.push(this.emptyMedicationRow());
        this.medicationValidations.push(true);
    }

    onLongMedicationChanged(){
        let isEnabled = !this.isDisabled('on_long_medication');
        if(isEnabled && this.induction_request.medications.length === 0){
            this.addMedicationBlock();
        } else if(isEnabled) {
            this.medicationValidations = this.induction_request.medications.map(a => false);
        } else if(!isEnabled) {
           this.induction_request.medications = [];
           this.medicationValidations = [];
        }
    }

    updateMedicationRow(data: any, index: number){
        if(this.isEditMedication) {
            this.induction_request.medications[this.medicationIndex] = data;
        }
    }

    removeMedicationRow(data: any, index: number){
        // if(this.medicationValidations[index] !== undefined){
        //     this.medicationValidations.splice( index, 1 );
        // }
        return this.induction_request.medications[index] ? this.induction_request.medications.splice( index, 1 ) : null;
    }

    updateValidationStatus($event, i){
        this.isMedicationFormValid = $event;
        console.log('$event', i, '=', $event);
        // if(this.isEditMedication) {
        //     this.medicationValidations[this.medicationIndex] = $event;
        // } else {
        //     const idx = this.induction_request.medications.length;
        //     this.medicationValidations[idx] = $event;
        // }
    }

    medicationValid(index?: number): boolean {
        // console.log(this.medicationValidations);
        // if(index !== undefined){
        //     return this.medicationValidations[index] === false;
        // }
        // return this.medicationValidations.findIndex(r => r === true) !== -1;
        if(!this.isDisabled('on_long_medication')) {
            return this.induction_request.medications.length ? false : true;
        } else {
            return false;
        }
    }

    dayjs(n: any, format?: string) {
        return dayjs(n, format);
    };

    displaySecondsAsDateTime(n: number){
        return dayjs.unix(n).format(AppConstant.dateTime_DD_MM_YYYY_hh_mm_A);
    }

    setValue(val, label) {
        console.log('val', val, label);
        this.induction_request[label] = val;
    }

    enableTravelTimeOverride(no_selected){
        if(no_selected){
            this.travel_time_override = {
                creator_ref: this.authUser$.id,
                creator: this.authUser$.name,
                timestamp: dayjs().valueOf(),
                valid_from: dayjs().clone().startOf('d').unix(),
                valid_till: null,
                _has_indefinite_validity: true
            }
        }
        return this.travel_time_override;
    }

    preFillTravelingToPostcode($event){
        // console.log($event.target);
        if($event.target.checked) {
            this.travel_time_override.to_postcode = this.travel_time_override.from_postcode;
        }else{
            this.travel_time_override.to_postcode = null;
        }
    }

    private getNewTravelTimeOverrides(updateTravelTimeRequest, existingTravelTimeIdentifier = null){

        let overrides = (this.induction_request.travel_time && this.induction_request.travel_time.overrides) || [];
        if(!overrides.length || !existingTravelTimeIdentifier){
            overrides.push(updateTravelTimeRequest);

        }else if(existingTravelTimeIdentifier){
            let oldRecordIndex = overrides.findIndex(r => r.timestamp === existingTravelTimeIdentifier);
            if(oldRecordIndex > -1){
                overrides[oldRecordIndex] = updateTravelTimeRequest;
            }else{
                // shouldn't be the case any time
                overrides.push(updateTravelTimeRequest);
            }
        }
        return overrides;
    }

    fetchVehicleInfo(){
        let regNumber = (this.induction_request.vehicle_reg_number || '').toString().trim();
        console.log('lookout for regNumber', regNumber);
        this.induction_request.travel_time.vehicle_info = {};
        if(!regNumber.length){
            return true;
        }
        this.saveInProgress = true;
        this.resourceService.fetchVehicleRegDetails(regNumber).subscribe(data=> {
            this.saveInProgress = false;
            if(data.vehicle){
                this.induction_request.travel_time.vehicle_info = data.vehicle;
            }else{
                this.induction_request.travel_time.vehicle_info = {
                    errorMessage: data.message ? data.message : 'Unable to fetch vehicle information'
                };
            }
        });
    }

    resetVehicleInfo(){
        if(!this.isVehicleRegRequired(this.induction_request.travel_method)){
            this.induction_request.vehicle_reg_number = undefined;
            this.induction_request.travel_time.vehicle_info = undefined;
        }
    }

    isVehicleRegRequired(travelMethod: string): boolean {
        if (['Car/Van (Driver)','Car (Driver)', 'Motorbike'].indexOf(travelMethod) !== -1) {
            return true;
        }
        return false;
    }

    createTime(iso_string?:any, isReturnNumber = false){
        const d = dayjs.duration(iso_string);
        if(iso_string && !isReturnNumber){
            return dayjs.isDuration(d) ? d.asMinutes() + ' minutes' : null;
        } else {
            return dayjs.isDuration(d) ? d.asMinutes() : null;
        }
        return iso_string;
    }

    storeTravelInfo() {
        if(this.travelTimeToWork) {
            this.induction_request.travel_time_to_work = this.convertToISO(this.travelTimeToWork);
        }
        if(this.travelTimeToHome) {
            this.induction_request.travel_time_to_home =  this.convertToISO(this.travelTimeToHome);
        }
    }

    private convertToISO(enteredMinutes) {
        if (enteredMinutes) {
          // Create a dayjs duration with the entered minutes
          const durationInMinutes = dayjs.duration(enteredMinutes, 'minutes');
          // Format the duration in ISO 8601 duration format (e.g., 20min -> "PT20M")
          const convertedDurationISO = durationInMinutes.toISOString();
          return convertedDurationISO;
        }
    }

    getFilteredRecords(allRecords: any, ids: any = [], key: string){
        return allRecords.filter(c => ids.indexOf(c[key])!== -1);
    }

    find(records: any, search: any, key: string = 'id', defaultVal: any = {}){
        return records.find(r => r[key] === search) || defaultVal;
    }

    numberToYesNo(num) {
        if(+num === 1){
            return 'Yes';
        }else if(+num === 0){
            return 'No';
        }
        else if(num === null){
            return '-';
        }
        return num;
    }

    onHasUnsavedChanges($event){
        this.hasUnsavedChanges = $event.hasUnsavedChanges;
    }

    onSaveUserDocument(record: any) {
        this.getCompetencies();
        this.induction_request.user_doc_ids.push(record.id);
        this.onDeleteDocument(null); // refresh doc uploaded
        this.closeModal(true);
    }

    onDeleteDocument(record: any) {
        // Saving network call to refresh Empty form after delete
        this.addNewCompetenciesRows.pop();
        this.addNewCompetenciesRows.push(Math.random());
    }

    projectHasVideoMedia(medias = []): boolean{
        return (medias || []).findIndex(m => m.file_ref && m.file_ref.file_mime && ['video/mp4', 'video/quicktime'].includes(m.file_ref.file_mime)) !== -1;
    }

    @ViewChild('pdfFlipBookComponentRef', { static: true })
    private pdfFlipBookComponentRef: PdfFlipBookComponent;
    /*openPdfViewer(fileRef){
        this.pdfFlipBookComponentRef.openPdfPreviewWindow({
            title: fileRef.name,
            fileUrl: fileRef.file_url,
            contentText: 'Please read below document carefully (till the end) to continue with this step:',
            onEndOfFileReached: () => {
                console.log('reached to end of pdf');
                this.induction_request.all_media_watched = true;
            }
        });
    }*/

    openPolicyPdfViewer(fileRef){
        this.pdfFlipBookComponentRef.openPdfPreviewWindow({
            title: fileRef.name,
            fileUrl: fileRef.file_url
        });
    }

    onMediaPlayerReady(api:VgApiService){
        this.api = api;

        this.api.getDefaultMedia().subscriptions.ended.subscribe(
            () => {
                // Set the video to the beginning
                console.log('Video playback completed');
                this.induction_request.all_media_watched = true;
                this.api.getDefaultMedia().currentTime = 0;
            }
        );
    }

    induction_slot_selection: {
        day?: any;
        slot?: {
            id: number;
            seconds: number;
            location: string;
        };
    } = {};

    slotRequestInProgress: boolean = false;
    processingLoader: boolean = false;

    induction_slots: any = {};
    induction_slots_days: any = [];
    @ViewChild('chooseInductionSlotModal') private chooseInductionSlotModal: IModalComponent;
    inductionSlotPicker(){
        if(this.additional_data && this.additional_data.project && this.additional_data.project.custom_field && this.additional_data.project.custom_field.induction_slot_booking){
            this.slotRequestInProgress = true;
            this.induction_slot_selection = {};
            return this.projectService.getProjectInductionBookingSlots(this.projectId, {nowSeconds: dayjs().unix()}).subscribe((data: any) => {
                console.log(data);
                this.slotRequestInProgress = false;
                if(data.slots){
                    this.induction_slots = (data.slots || []).reduce((list, element) => {
                        let day = dayjs.unix(+element.slot_date_time).format(this.dateFormat);
                        if (!list[day]) {
                            list[day] = [];
                        }
                        list[day].push({
                            id: element.id,
                            seconds: element.slot_date_time,
                            location: element.location,
                            is_slot_blocked: element.is_slot_blocked || false,
                            total_slots: +element.total_slots || 0,
                            booked_slots: +element.booked_slots || 0,
                            all_booked: element.all_booked,
                            time: dayjs.unix(+element.slot_date_time).format('hh:mm A')
                        });
                        return list;
                    }, {});
                    this.induction_slots_days = this.getKeys(this.induction_slots).map(day => {
                        let has_open_slots = (this.induction_slots[day] || []).find(element => !element.all_booked);
                        return {
                            day,
                            all_booked: !has_open_slots
                        }
                    });
                    console.log("induction_slots_days: ", this.induction_slots_days);
                    this.showChooseInductionSlotModal = true;
                    this.chooseInductionSlotModal.open();
                }else {
                    const message = data.message || 'Failed to get induction slots.';
                    this.toastService.show(this.toastService.types.ERROR, message, { data: data });
                }

            });
        }
        this.saveRequest();
    }

    closeInductionSlotSelection(form) {
        form.reset();
        this.showChooseInductionSlotModal = false;
    }

    slotDayChanged(){
        console.log('Day changed to', this.induction_slot_selection.day);
        this.induction_slot_selection.slot = null;
    }

    submitInductionSlotSelection(event) {
        let {id, seconds, location} = this.induction_slot_selection.slot;
        this.induction_request.induction_slot = {id, seconds, location};
        event.closeFn();
        this.saveRequest();
    }

    saveRequest() {
        this.confirmationModalReference.openConfirmationPopup({
            headerTitle: 'Induction Complete',
            title: `Your submission will now be reviewed and you will be notified with the response. Please keep an eye on your emails.`,
            confirmLabel: 'Submit',
            onConfirm: () => {
                if (this.induction_request.travel_time_to_work) {
                    this.induction_request.travel_time.to_work = this.induction_request.travel_time_to_work;
                }
                if (this.induction_request.travel_time_to_home) {
                    this.induction_request.travel_time.to_home = this.induction_request.travel_time_to_home;
                }
                let isMedicationsBoxDisabled = this.isDisabled('on_long_medication');
                if(isMedicationsBoxDisabled){
                    this.induction_request.medications = [];
                }
                if(!this.induction_request.comments){
                    this.induction_request.comments = [];
                }
                this.induction_request.status_code = 1;

                let comment = new Comment();
                comment.timestamp = dayjs().valueOf();
                comment.user_id = this.authUser$.id;
                comment.name = this.authUser$.name;
                comment.origin = 'system'; // as comment was system generated

                this.additional_data.platform_type = 'web';

                this.induction_request.additional_data = this.additional_data;
                this.induction_request.additional_induction_question = this.additionalInductionQuestions;
                this.induction_request._induction_quiz = this.inductionQuestions;

                if(this.induction_request.id){
                    comment.note = 'Updated Existing Request';
                    this.induction_request.comments.push(comment);
                    this.updateExistingRequest();
                } else {
                    comment.note = `${this.additional_data.project.custom_field.induction_phrase_singlr || 'Induction Request'} Submitted`;
                    this.induction_request.comments.push(comment);

                    if (this.selectedRams.id) {
                        this.induction_request.rams_register = {
                            'rams_id': this.selectedRams.id,
                            'rams_title': this.selectedRams.briefing_title,
                            'rams_phrase': this.ramsPhrase,
                            'induction_phrase': this.additional_data.project.custom_field.induction_phrase_singlr || 'Induction Request',
                            'signature': this.ramsBriefingSignature,
                        }
                    }
                    this.createNewRequest();
                }
            }
        });
    }

    private createNewRequest(){
        this.saveInProgress = true;
        console.log('Save it', this.induction_request);
        this.userService.createInductionRequest(this.induction_request).subscribe(this.handleResponse.bind(this));
    }

    private handleResponse(data){
        this.saveInProgress = false;
        if (data.success) {
            console.log('Saved...!!');
            return this.router.navigate(['/site-user']);
        } else {
            const message = data.message || 'Failed to store data.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: data });
        }
    }

    private updateExistingRequest(){
        this.saveInProgress = true;
        console.log('Save it', this.induction_request);
        this.userService.updateInductionRequest(this.induction_request.id, this.induction_request).subscribe(this.handleResponse.bind(this));
    }

/*    private autoSelectPassportCompetency(user_documents) {
        let matchedDoc = (this.user_documents || []).find(d => d.name && d.name === this.passportCompetencyName) || {};
        if (!matchedDoc.id) {
            return false;
        }
        if (!this.induction_request.user_doc_ids) {
            this.induction_request.user_doc_ids = [];
        }
        this.induction_request.user_doc_ids.push(matchedDoc.id);
    }*/



    checkPassportDocIsMissing() {
        let matchedDoc = (this.user_documents || []).find(d => d.name && d.name === this.passportCompetencyName) || {};
        if (!matchedDoc.id) {
            return true;
        }

        if ((this.induction_request.user_doc_ids || []).findIndex(id => id == matchedDoc.id) === -1) {
            return true;
        }
        return false;
    }

    checkCSCSDocIsMissing() {
        if(this.additional_data?.project._user_excluded_from_mandatory_doc){
            return false;
        }
        let matchedDoc = (this.user_documents || []).find(d => d.name && this.cscsCompetenciesNames.includes(d.name)) || {};
        if (!matchedDoc.id) {
            return true;
        }

        if ((this.induction_request.user_doc_ids || []).findIndex(id => id == matchedDoc.id) === -1) {
            return true;
        }
        return false;
    }

    checkGivenDocIsMissing(name){
        if(this.additional_data?.project._user_excluded_from_mandatory_doc){
            return false;
        }
        let matchedDoc = (this.user_documents || []).find(d => d.name && (name.toString().trim().toLowerCase()) === (d.name.toString().trim().toLowerCase())) || {};
        if (!matchedDoc.id) {
            return true;
        }

        if ((this.induction_request.user_doc_ids || []).findIndex(id => id == matchedDoc.id) === -1) {
            return true;
        }
        return false;
    }

    private getMissingDocMessage(lowerCaseInitials = false){
        this.missingDocumentMessages = [];
        let list = [];
        if(this.additional_data.project.is_cscs_require && this.checkCSCSDocIsMissing()) {

            let {names} = this.profileUtility.getMandatoryDocNames(this.knownCompetencies, this.MANDATORY_COMPETENCY_TAG);

            list.push(`${lowerCaseInitials ? 'a': 'A'}t least one of the following to continue: ${names.join(', ')}`);
        }

        if(this.additional_data.project.is_passport_require && this.checkPassportDocIsMissing()) {
            list.push('Passport/Visa (as part of the right to work check)');
        }
        let others = (this.additional_data.project.other_doc_required || []).filter(name => this.checkGivenDocIsMissing(name));
        if(others.length){
            list.push(...others);
        }
        if(this.invalid_sets.length){
            let missing_messages = this.skillMatrixService.invalidSetsToMessages(this.invalid_sets, lowerCaseInitials);
            list.push(...missing_messages);
        }
        this.missingDocumentMessages = list;
        return list;
    }

    openCscsRequiredModal() {
        this.missingDocumentMessages = this.getMissingDocMessage();
        if(this.missingDocumentMessages && this.missingDocumentMessages.length) {
            this.confirmationModalReference.openConfirmationPopup({
                headerTitle: 'Competency/Certification Requirement',
                title : `<div><p class="mb-2">As part of your ${this.additional_data.project.custom_field.induction_phrase_singlr} you are required to upload and submit the following:</p>
                <div class="mb-0">${this.missingDocumentMessages.map(textMsg => `<p class="d-flex align-items-center mb-0"><span class="d-flex"><i class="fa fa-circle circle-fs-5 mr-2 pt-0"></i></span>${textMsg}</p>`).join('')}</div>
                </div>`,
                hasCancel: false,
                confirmLabel: 'OK',
                onConfirm: () => {
                    this.missingDocumentMessages = this.getMissingDocMessage(true);
                }
            });
        }
        /*
        // old one
        this.modalService.open(this.cscsRequiredRef, {
            backdropClass: 'light-blue-backdrop',
            centered: true,
            windowClass: "modal_info",
            keyboard: true,
            backdrop: 'static',
            // beforeDismiss: () => {},
        }).result.then((result) => {
            this.missingDocumentMessages = this.getMissingDocMessage(true);
        }, reason => {
            this.missingDocumentMessages = this.getMissingDocMessage(true);
        });
        */
    }

    getDefaultPolicies() {
        (this.additional_data.project.further_policies || []).map(fp => {
            /*if (fp.key && fp.key === 'c_lens_policy') {
                this.c_lens_policy = fp;
            }*/

            if(fp.key && fp.key === 'd_and_a_policy') {
                this.d_and_a_policy = fp;
            }

            if(fp.key && fp.key === 'working_hr_agreement') {
                this.working_hr_agreement = fp;
            }
        });
    }

    pageLoaded($event){
    }

    pageRendered($event) {
        console.log('Rendered Page', $event.pageNumber);
    }

    pageChange(num: number) {
        console.log('Current Page', num);

        if (!this.hasMoreThanOne && num >= 2 && this.pageCount > 5) {
            this.hasMoreThanOne = 'yes';
        } else if (!this.hasMoreThanOne) {
            this.hasMoreThanOne = 'no';
        }

        if (this.hasMoreThanOne === 'yes') {
            num = num + 2;
        }

        console.log('Updated Current Page', num);

        if(num >= this.pageCount) {
            console.log('reached to end of pdf.');
            this.induction_request.all_media_watched = true;
        }
    }

    pdfLoaded($event) {
        this.pageCount = $event;
        console.log('Total Pages', this.pageCount);
        if (this.pageCount == 1) {
            console.log('reached to end of pdf');
            this.induction_request.all_media_watched = true;
        }

        this.togglePdfLoadingIndicator(this.pageCount);
    }

    pdfLoading: boolean;
    togglePdfLoadingIndicator($event){
        this.pdfLoading = false;
    }

    addChildCompetency(index){
        let parent_document = this.user_documents[index];
        if(!this.user_documents[index].children){
            this.user_documents[index].children = [];
        }
        // console.log('files are: ', (parent_document.user_files || []).map(f => f.id));
        this.user_documents[index].children.unshift({
            identifier: (new Date()).getTime(),
            user_file_id: parent_document.user_file_id || null,
            user_files: (parent_document.user_files || []).map(f => f.id),
            parent_doc_ref: parent_document.id,
            doc_owner_id: parent_document.doc_owner_id
        });
    }

    onSaveChildDocument(record: any, parent_index){
        console.log('Add this to child competencies', record);
        const index = this.selectedCompetencie?.children.findIndex(c => c.identifier === record.identifier);
        const i = this.user_documents.findIndex(c => c.id === this.selectedCompetencie.id);
        if(index !== -1 && i !== -1){
            this.selectedCompetencie.children[index] = record;
            this.competencies[i] = this.selectedCompetencie;
        }
        this.induction_request.user_doc_ids.push(record.id);
        const message = 'Competency added successfully.';
        this.toastService.show(this.toastService.types.SUCCESS, message);
        this.getCompetencies();
        this.closeModal(true);
        // console.log('Total child competencies updated', this.selectedCompetencie.children);
    }

    onDeleteChildDocument(identifier, parent_index){
        let index = this.user_documents[parent_index].children.findIndex(c => c.identifier === identifier);
        if(index !== -1){
            this.user_documents[parent_index].children.splice(index, 1);
        }
        // console.log('Deleted record from competencies', identifier, 'Now', this.user_documents[parent_index].children);

    }

    jumpToStep(index, label = null){
        if(label){
            this.activeStage = this.formStages.findIndex(s => s.name === label);
            return true;
        }
        this.activeStage = index;
    }

    saveSignature(data) {
        this.induction_request.user_sign = data;
    }

    clearSignature() {
        this.induction_request.user_sign = null;
    }

    signImageIsValid(){
        return (!!this.induction_request.user_sign && this.induction_request.user_sign !== 'data:image/png;base64,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');
    }

    isAdditionalQuestionStageActivated() {
        return !!(this.additionalQSets || []).find(q => q.status) && this.additionalInductionQuestions?.section_title;
    }

    trackByRowIndex(index: number, obj: any){
        return index;
    }

    onAdditionalAnswerChange(index, value) {
        this.additionalInductionQuestions.induction_questions[index].answer = value;
    }

    dateToEpoch(i, j=undefined) {
        if (j != undefined) {
            this.additionalInductionQuestions.induction_questions[i].sub_questions[j].ans_epoch = this.ngbMomentjsAdapter.ngbDateToDayJs(this.additionalInductionQuestions.induction_questions[i].sub_questions[j].answer).valueOf();
        } else {
            this.additionalInductionQuestions.induction_questions[i].ans_epoch = this.ngbMomentjsAdapter.ngbDateToDayJs(this.additionalInductionQuestions.induction_questions[i].answer).valueOf();
        }
        return (j != undefined) ? this.additionalInductionQuestions.induction_questions[i].sub_questions[j].ans_epoch :  this.additionalInductionQuestions.induction_questions[i].ans_epoch;
    }

    getRamsForInduction() {

    }

    saveRamsBriefingSignature(data) {
        this.ramsBriefingSignature = (this.validRamsBriefingSignature) ? data : null;
    }

    clearRamsBriefingSignature() {
        this.ramsBriefingSignature = null;
        this.validRamsBriefingSignature = false;
    }

    pointsChanged(data) {
        let pointsCount = 0;
        (data||[]).forEach(group => {
            pointsCount += group.points.length;
        });
        this.validRamsBriefingSignature = pointsCount > 10;
    }

    isPdfViewerVisible: boolean = true;
    onSelectRams($event) {
        console.log($event);
        this.selectedRams = ($event || {});
        this.isPdfViewerVisible = false;
        this.clearRamsBriefingSignature();
        if (this.selectedRams && !this.selectedRams.id) { return; }
        setTimeout(() => {
            this.isPdfViewerVisible = true;
        });
    }

    @ViewChild('medicationDetailModal', { static: true })
    private medicationDetailModalRef: TemplateRef<any>;
    openMedicationDetailModal(value = null, index: number, isEdit = false) {
        this.isEditMedication = isEdit;
        if(this.isEditMedication) {
            this.medicationIndex = index;
            this.isMedicationFormValid = true;
        }
        const defaultVal = this.emptyMedicationRow();
        this.selectedMedicationDetails = value ?? defaultVal;
        this.modalRef = this.modalService.open(this.medicationDetailModalRef, {
            backdropClass: 'light-blue-backdrop',
            keyboard: true,
            backdrop: 'static',
            windowClass: 'modal_v2',
        });
    }

    dayjsDisplayDate(n: number) {
        const tz = this.projectInfo?.custom_field?.timezone;
        return dayjs(n).format(AppConstant.defaultDateFormat);
    };

    openEditIfExpired($event, document){
        if(document._is_expired || document._verification_mandatory){
            this.openEditCompetenciesModal(document);
            $event.stopPropagation();
            return false;
        }
    }

    openAddCompetenciesModal(parent_document, isChild = false, $event) {
        this.isEditCompetency = false;
        this.isChildOfCompetency = isChild;
        $event && $event.stopPropagation();
        // this.getCompetencies();

        /* Add New Compentemcy */
        const doc_owner_id = this.authUser$.id;
        this.selectedCompetencie = {
            identifier: (new Date()).getTime(),
            doc_owner_id,
        };
        /* End */

        /* Add New Sentinel Competency */
        if(!this.isEditCompetency && this.isChildOfCompetency) {
            const parentDocument = JSON.parse(JSON.stringify(parent_document));
            this.selectedCompetencie = parentDocument;
            if(!this.selectedCompetencie.children){
                this.selectedCompetencie.children = [];
            }
            const child_competency = {
                identifier: (new Date()).getTime(),
                user_file_id: parent_document.user_file_id || null,
                user_files: (parent_document.user_files || []).map(f => f.id),
                parent_doc_ref: parent_document.id,
                doc_owner_id: parent_document.doc_owner_id
            }
            this.childCompetency = child_competency;
            this.selectedCompetencie.children.unshift(child_competency);
        }
        /* End */

        console.log(this.selectedCompetencie);
        this.modalRef = this.modalService.open(this.competenciesModalRef, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            centered: true,
            windowClass: "modal_v2",
        });
    }

    @ViewChild('competenciesModal')
    private competenciesModalRef: TemplateRef<any>;
    openEditCompetenciesModal(competency = {}, childCompetency = {}, isChild = false, $event = null) {
        this.isEditCompetency = true;
        this.isChildOfCompetency = isChild;
        this.selectedCompetencie = JSON.parse(JSON.stringify(competency));
        this.childCompetency = childCompetency;
        this.modalRef = this.modalService.open(this.competenciesModalRef, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            centered: true,
            windowClass: "modal_v2",
        });
        $event && $event.stopPropagation();
    }

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    openConfirmationModal(userDocument: any) {
        this.userDocumentToDelete = userDocument;
        this.confirmationModalRef.openConfirmationPopup({
            confirmLabel: 'Yes',
            cancelLabel: 'No',
            title: `Are you sure you wish delete ${this.userDocumentToDelete.name} competency?`,
            onConfirm: () => {
                if (userDocument?.id) {
                    this.userService.deleteUserDoc(userDocument.id).subscribe(res => {
                        if (res.success) {
                            const index = this.induction_request.user_doc_ids.indexOf(userDocument.id);
                            if (index > -1) {
                                this.induction_request.user_doc_ids.splice(index, 1);
                            }
                            this.getCompetencies();
                        } else {
                            console.log(res.message);
                        }
                    });
                }
            }
        });
    }

    @ViewChild('viewImageModal') private viewImageModalRef: TemplateRef<any>;
    openViewImageModal(selectedImg: any) {
        this.selectedUserDocFile = selectedImg;
        this.modalRef = this.modalService.open(this.viewImageModalRef, {
            backdropClass: 'light-blue-backdrop',
            windowClass: "modal_v2",
            backdrop: 'static',
            centered: true,
            size: 'lg',
        });
    }

    @ViewChild('editReviewModal') private editReviewModalRef: TemplateRef<any>;
    openEditReviewModal(step: string) {
        console.log("EDIT=> ", step);
        this.editStep = step;
        this.modalRef = this.modalService.open(this.editReviewModalRef, {
            backdropClass: 'light-blue-backdrop',
            windowClass: "modal_v2",
            backdrop: 'static',
            centered: true,
            size: 'lg',
        });
        this.modalRef.result.then((data) => {
            console.log("selected edit form >>", data);
        }, (error) => {
            this.editStep = null;
            console.log("error:", error);
        });
    }

    deleteUserDocument(userDocument: any, childDoc: any){
        this.selectedCompetencie = userDocument;
        if (childDoc?.id) {
            this.isChildOfCompetency = true;
            this.openConfirmationModal(childDoc);
        } else {
            this.isChildOfCompetency = false;
            this.openConfirmationModal(userDocument);
        }
    }

    closeModal(isFormValid = false) {
        if (isFormValid) {
            if(this.lastVisibleModal && this.lastVisibleModal.close){
                this.lastVisibleModal.close();
                this.lastVisibleModal = null;
            }else{
                this.modalService.dismissAll();
            }
        } else {
            this.openWarningModal();
        }
    }

    editExistingDocument($event, isChildCompetency){
        this.closeModal(true);
        console.log(`Initiate edit of existing document (isChild?: ${isChildCompetency})`, $event.doc);
        this.openEditCompetenciesModal($event.doc, {}, isChildCompetency);
    }

    onRtwValidation($event){
        this.induction_request._need_rtw_check = $event._need_rtw_check;
        this.induction_request.rtw_doc_code = $event.rtw_doc_code;
        this.induction_request.rtw_check_result = $event.rtw_check_result || {};
    }
    rtwAnswerChanged(isFormValid: boolean) {
        this.induction_request._need_rtw_check = false;
        this.induction_request.rtw_doc_code = null;
        this.induction_request.rtw_check_result = {};
        this.onFormChange(isFormValid);
    }

    saveMedication(event) {
        if(!this.isEditMedication) {
            this.induction_request.medications.push(event);
        } else {
            if (this.medicationIndex !== -1) {
                this.induction_request.medications[this.medicationIndex] = event;
            }
        }
        console.log(this.induction_request.medications);
        this.modalService.dismissAll();
    }

    onFormChange(isFormValid: boolean) {
        if(this.editStep) {
            console.log("onFormChange called:", isFormValid);
            this.isFormValid = isFormValid;
        }
    }

    activeRoute(i) {
        this.activeStage = i;
    }

    @ViewChild('warningModal') private warningModalRef: TemplateRef<any>;
    openWarningModal() {
        this.modalRef = this.modalService.open(this.warningModalRef, {
            backdropClass: 'light-blue-backdrop',
            windowClass: "modal_v2",
            backdrop: 'static',
            centered: true,
        });
    }

    removeLastComma(str: string){
        return str.replace(/,\s*$/, "");
    }

    @ViewChild('cancelBtn') cancelBtn: ElementRef<HTMLButtonElement>;
    triggerClick() {
        if (this.cancelBtn && this.cancelBtn.nativeElement) {
            this.cancelBtn.nativeElement.click();
          }
    }

    // checkMissingCompetencies() {
    //     const flag: Array<any> = [];
    //     if(this.additional_data.project.is_cscs_require && this.checkCSCSDocIsMissing()) {
    //         flag.push(`At least one of: ${this.cscsCompetenciesNames.join(', ')}`);
    //     }
    //     if(this.additional_data.project.is_passport_require && this.checkPassportDocIsMissing()) {
    //         flag.push('Passport/Visa (as part of the right to work check)');
    //     }
    //     let others = (this.additional_data.project.other_doc_required || []).filter(name => {
    //         return this.checkGivenDocIsMissing(name);
    //     });
    //     if(others.length){
    //         flag.push(...others);
    //     }
    //     return (flag.length > 0);
    // }
}
