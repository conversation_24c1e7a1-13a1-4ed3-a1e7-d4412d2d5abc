// Simple test for renderChanges function
const { renderChanges } = require('./inndex-web/api/services/TokenUtil');

// Test data similar to what would come from audit logs
const testChanges = {
  "incident_details": {
    "oldValue": "Original incident description",
    "newValue": "Updated incident description with more details"
  },
  "severity": {
    "oldValue": "Low",
    "newValue": "High"
  },
  "witness_details": {
    "first_name": {
      "oldValue": "<PERSON>",
      "newValue": "<PERSON>"
    },
    "last_name": {
      "oldValue": "Doe", 
      "newValue": "vousden"
    }
  }
};

console.log("Testing NEW 5-column format:");
console.log("================================");
const newFormatResult = renderChanges(testChanges, 0, "Aaron vousden", "15/06/25 13:01:02");
console.log(newFormatResult);

console.log("\n\nTesting OLD 3-column format (backward compatibility):");
console.log("====================================================");
const oldFormatResult = renderChanges(testChanges);
console.log(oldFormatResult);

console.log("\n\nTest completed successfully!");
