const COMPANY_ADMIN_PREFIX = `company-admin/`;
const TimeManagementController = `${COMPANY_ADMIN_PREFIX}TimeManagementController`;
const InductionController = `InductionController`;
const ProjectController = `ProjectController`;
const PermitController = `PermitController`;
const PowerBiController = `PowerBiController`;

// Declaring it as fn, so that it doesn't affect rest of sails.config loading.

module.exports = {

    getCaPolicies: () => {

        /*
         * Refer to config/policies.js file for attached policy details of the following functions.
         *
         * projectInductionsListCA
         * getInductionEmployersCA
         *
         */

        return {
            [TimeManagementController]: {
                getCompanyInductedUsers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
                getInductedUsersTimeLogs: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
            },
        };
    },

    getCaRoutes: () => {

        return {
            'get /api/ca/:employerId/projects/users/list': `${TimeManagementController}.getCompanyInductedUsers`,
            'get /api/ca/:employerId/user/:userId/time-logs': `${TimeManagementController}.getInductedUsersTimeLogs`,
            'get /api/ca/:employerId/project/:projectId/inductions/list': `${InductionController}.projectInductionsListCA`,
            'get /api/ca/:employerId/project/:projectId/inductions/employers': `${InductionController}.getInductionEmployersCA`,
            'get /api/ca/:employerId/project/:projectId/inducted-users': `${ProjectController}.getProjectInductedUsersCA`,
            'post /api/ca/:companyId/permit-template/create': `${PermitController}.createPermitTemplate`,
            'post /api/ca/:companyId/permit-template/update/:id': `${PermitController}.updatePermitTemplate`,
            'post /api/ca/:companyId/permit-template/update/:id/status': `${PermitController}.updatePermitTemplateStatus`,
            'get /api/ca/:companyId/permit-template/list': `${PermitController}.fetchPermitTemplates`,
            'post /api/ca/:employerId/project/:projectId/induction-requests/exportXlsx': `${InductionController}.downloadInductionRecordsXLSXCA`,
            'get /api/ca/:companyId/tool-report/:toolName': `${PowerBiController}.getCompanyToolReport`,
            'post /api/ca/company/:companyId/tool-report/download/:toolName': `${PowerBiController}.exportCompanyToolReport`,
            'post /api/ca/company/:companyId/report/job/:id/update': `${PowerBiController}.updateCompanyExportQueue`,
        };
    },
};
