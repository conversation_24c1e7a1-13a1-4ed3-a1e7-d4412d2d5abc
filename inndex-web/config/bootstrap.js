/**
 * Bootstrap
 * (sails.config.bootstrap)
 *
 * An asynchronous bootstrap function that runs just before your Sails app gets lifted.
 * > Need more flexibility?  You can also do this by creating a hook.
 *
 * For more information on bootstrapping your app, check out:
 * https://sailsjs.com/config/bootstrap
 */

const pg = require('pg');

module.exports.bootstrap = async function(done) {

    if(process.env.APP_ENV !== 'production') {
        const originalQuery = pg.Client.prototype.query;

        pg.Client.prototype.query = function (...args) {

            const sql = typeof args[0] === 'string' ? args[0] : args[0]?.text;

            sails.log.info(`[raw-sql]`, sql, `; Replacements:`, (args.length > 1 ? args[1] : ''));

            return originalQuery.apply(this, args);
        };
    }
  // By convention, this is a good place to set up fake data during development.
  //
  // For example:
  // ```
  // // Set up fake development data (or if we already have some, avast)
  // if (await User.count() > 0) {
  //   return done();
  // }
  //
  // await User.createEach([
  //   { emailAddress: '<EMAIL>', fullName: 'Ryan Dahl', },
  //   { emailAddress: '<EMAIL>', fullName: 'Rachael Shaw', },
  //   // etc.
  // ]);
  // ```

  // Don't forget to trigger `done()` when this bootstrap function's logic is finished.
  // (otherwise your server will never lift, since it's waiting on the bootstrap)

    // the keep-alive duration of the API server should be higher than the idle timeout value of the load balancer.
  sails.hooks.http.server.keepAliveTimeout = 130 * 1000; // Ensure all inactive connections are terminated by the ALB, by setting this a few seconds higher than the ALB idle timeout
  sails.hooks.http.server.headersTimeout = 135 * 1000; // Ensure the headersTimeout is set higher than the keepAliveTimeout due to this nodejs regression bug: https://github.com/nodejs/node/issues/27363
  return done();

};
