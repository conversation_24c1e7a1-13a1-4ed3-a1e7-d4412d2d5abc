/**
 * Policy Mappings
 * (sails.config.policies)
 *
 * Policies are simple functions which run **before** your actions.
 *
 * For more information on configuring policies, check out:
 * https://sailsjs.com/docs/concepts/policies
 */
/*
let auth = require('http-auth');
let basic = auth.basic({
        realm: "innDex Auth"
    }, (username, password, callback) => {
        // Custom authentication
        // Use callback(error) if you want to throw async error.
        callback(username === "imdev" && password === "imdev@3214");
    }
);
*/

module.exports.policies = {

    /***************************************************************************
     *                                                                          *
     * Default policy for all controllers and actions, unless overridden.       *
     * (`true` allows public access)                                            *
     *                                                                          *
     ***************************************************************************/

    // '*': true,
    AuthController: {
        // Browser Auth for HTML request
        //index: [auth.connect(basic)],
        changePassword: ['isAuth'],
        changeEmail: ['isAuth'],
        registerDevice: ['isAuth']
    },
    AnonymousUserController: {
        getProjectInfoByToken: ['isInnTimeAuth'],
        reFetchAnonymousTokenInfo: ['isInnTimeAuth'],
    },
    ...(require('./api-routes/super-admin').getSuperAdminPolicies()),
    ...(require('./api-routes/company-admin').getCaPolicies()),
    ...(require('./api-routes/site-admin').getSiteAdminPolicies()),
    ProCoreIntegrationController: {
        getUserCompaniesList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getCompanyProjectsList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        saveProcoreProjectReference: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        createUsersTimecardEntries: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectPermissionTemplates: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },
    UserRoleController: {
        getCompanyProjectAdmins: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        saveCompanyProjectAdminPermission: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        removeUserCompanyProjectAccess: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectAdmins: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 is called from site-admin routes only.
        getCompanyAdmins: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 isCA_Or_CPA_Auth
        updateCompanyProjectAdminPermission: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2']
    },
    UserController: {
        status: 'isAuth',
        profile: 'isAuth',
        token: 'isAuth',
        updateUser: 'isAuth',
        createShadowUser: ['isAuth', 'isSiteAdminAuth'],                // can be any site-admin
        saveShadowUserProfileDetails: ['isAuth', 'isSiteAdminAuth'],    // can be any site-admin
        getUserContactDetail: ['isAuth'],
        getCompanyUserContactDetail: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/userIsCurrentEmployeeOf'],
        saveContactDetail: ['isAuth'],

        getUserHealthQuestions: ['isAuth'],
        getUserHealthAssessment: ['isAuth'],
        saveUserHealthAssessment: ['isAuth'],
        getCompanyUserHealthAssessment: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/userIsCurrentEmployeeOf'],
        getCompanyUserMedicalAssessment: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/userIsCurrentEmployeeOf'],

        getUserMedicalQuestions: ['isAuth'],
        getUserMedicalAssessment: ['isAuth'],
        saveUserMedicalAssessment: ['isAuth'],

        // createSupportTicket: ['isAuth'],
        getUserEmploymentDetail: ['isAuth'],
        getUserEmploymentDetailById: ['isAuth', 'isSuperAdminAuth'],
        getUserTimeDetails: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 isCA_Or_CPA_Auth
        saveUserEmploymentDetail: ['isAuth', 'isDeprecatedRoute'],
        saveUsrEmplAndPsnlDetail: ['isAuth'],
        storeUserSetting: ['isAuth'],
        getUserSettings: ['isAuth'],
        getAllUsers: ['isAuth', 'isSuperAdminAuth', 'isDeprecatedRoute', 'v2/forceUpdatingApp'],
        getAllUsersV2: ['isAuth', 'isSuperAdminAuth'],
        updateAdminAccess: ['isAuth', 'isSuperAdminAuth'],
        deactiveUser: ['isAuth'],
        deleteUser: ['isAuth', 'isSuperAdminAuth'],
        getUsersByEmployer: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 isCA_Or_CPA_Auth
        getUserDocumentsByEmployer:['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 isCA_Or_CPA_Auth
        validateUserProjectRelation: ['isInnTimeAuth'],
        deleteEmployerUser: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 isCA_Or_CPA_Auth
        getUserCurrentProject: ['isAuth'],
        fetchUsersByEmployer: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        sendEmailVerificationMail: ['isAuth', 'isSuperAdminAuth'],
        getUsersById: ["isAuth", "isMoreThanAUser"],
        downloadCompanyEmployeeInfoV1: ['isAuth', 'v2/isValidDownloadRequest'],
        downloadCompanyEmployeeInfo: ['v2/forceUpdatingApp'],
        getProjectActiveUsers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],

        addDocumentForSign: ['isAuth', 'isMoreThanAUser'],
        resendSignDocument: ['isAuth','isMoreThanAUser'],
        submitDocumentOfSign: ['isAuth','isMoreThanAUser'],
        getDocumentOfSign: ['isAuth','isMoreThanAUser'],
        updateUserEmpDetail: ['isAuth', 'isMoreThanAUser'],
    },
    FacialRecognitionController: {
        // createCollection: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteFaceEnrolment: ['isAuth', 'isSuperAdminAuth'],
        // deleteFaceEnrolmentInnTime: ['isInnTimeAuth'],
        // searchFacesByImage: ['isAuth'],
        searchFacesByImageInnTime: ['isInnTimeAuth', 'projectHasOptimaSetting'],
    },
    ASiteIntegrationController: {
        validateLogin: ['isAuth', 'isSuperAdminAuth'],
        checkIfAsiteConfigured: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getWorkspaceList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getWorkspaceFoldersList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },
    FileController: {
        uploadFile: ['isAuth', 'fileUploadLimit'],
        storeInnTimeUploadFile: ['isInnTimeAuth', 'fileUploadLimit'],
        deleteFile: ['isAuth'],
        deleteInnTimeFile: ['isInnTimeAuth'],
        seedGenerateFileTranslationById: ['isValidRestKeyAuth'],
        downloadFile: ['isAuth', 'isDeprecatedRoute', 'v2/forceUpdatingApp'],
        mergePDFs: ['isAuth'],
        convertWordDocsToPdf: ['isAuth'],
    },
    SeedingController: {
        attachProjectLocationKey: ["isValidRestKeyAuth"],
        attachQrImageWithProjects: ["isValidRestKeyAuth"],
        refreshUserTravelDistance: ["isValidRestKeyAuth"],
        reprocessPunchLogs: ["isValidRestKeyAuth"],
        seedHazardCategory: ["isValidRestKeyAuth"],
        seedInspectionTourChecklist: ["isValidRestKeyAuth"],
        seedLightingConditions: ["isValidRestKeyAuth"],
        seedUserHealthAssessment: ["isValidRestKeyAuth"],
        seedUserMedicalAssessment: ["isValidRestKeyAuth"],
        removeMedicalAssessmentQuestion: ["isValidRestKeyAuth"],
        exportInductionsToSmartsheet: ["isValidRestKeyAuth"],
        exportOldTimeLogsToSmartsheet: ["isValidRestKeyAuth"],

        seedProjectInductionSlots: ['isValidRestKeyAuth'],
        seedInductionsWithUserDocs: ['isValidRestKeyAuth'],
        exportInductedUsersToProcore: ['isValidRestKeyAuth'],
        exportManpowerLogsToProcore: ['isValidRestKeyAuth'],
        seedMetaDataByKey: ['isValidRestKeyAuth'],
        seedVehicleAssetInspectionChecklist: ['isValidRestKeyAuth'],
        correctVehicleAssetRecords: ['isValidRestKeyAuth'],
        seedMobileNumberWithCodeInInductionRequest: ['isValidRestKeyAuth'],
        seedTypeOfAssets: ['isValidRestKeyAuth'],
        seedAssetEquipmentItems: ['isValidRestKeyAuth'],
        seedEquipmentAssetInspectionChecklist: ['isValidRestKeyAuth'],
        fixCompanyMergeRecords: ['isValidRestKeyAuth'],
        seedingAssetTables: ['isValidRestKeyAuth'],
        incidentReportActionCategories: ['isValidRestKeyAuth'],
        clerkOfWorksCategory: ['isValidRestKeyAuth'],
        fillPhraseFields: ['isValidRestKeyAuth'],
        seedItpCustomFields: ['isValidRestKeyAuth'],
        seedingIBScoreType: ['isValidRestKeyAuth'],
        importExportItems: ['isValidRestKeyAuth'],
        replaceIbWording: ['isValidRestKeyAuth'],
        seedIncidentCauseTypAndOptions: ['isValidRestKeyAuth'],
        fixGCRecordRefs: ['isValidRestKeyAuth'],
        fixingIbInspectionRating: ['isValidRestKeyAuth'],
        updateIbClQuestion: ['isValidRestKeyAuth'],
        assetFaultCorrection: ['isValidRestKeyAuth'],
        associateFaultIdToAssetInspectionFaults: ['isValidRestKeyAuth'],
        ibReportRatingPercentage: ['isValidRestKeyAuth'],
        inspectionTourRatingPercentage: ['isValidRestKeyAuth'],
        seedMultiTaggedCompanies: ['isValidRestKeyAuth'],
        convertSupplyChainCompanyNamesToIds: ['isValidRestKeyAuth'],
        inductionQueIdsCorrection: ['isValidRestKeyAuth'],
        transferObservationToCloseCall: ['isAuth', 'isValidRestKeyAuth'],
        assetInspectionRecordCorrection: ['isAuth', 'isValidRestKeyAuth'],
        saveOldBriefingsToNewTables: ['isAuth', 'isValidRestKeyAuth'],
        saveOldTake5sBriefingsToNewTables: ['isAuth', 'isValidRestKeyAuth'],
        seedWeatherLogsForDailyActivities: ['isAuth', 'isValidRestKeyAuth'],
        saveInductionRequestCoordinate: ['isAuth', 'isValidRestKeyAuth'],
        seedTemporaryWorksAssetInspectionChecklist: ['isValidRestKeyAuth'],
        seedCountriesAndNationalityMetaData: ['isValidRestKeyAuth'],
        importDistricts: ['isValidRestKeyAuth'],
        seedDistrictIntoInductions: ['isValidRestKeyAuth'],
        seedPermitTypes: ['isValidRestKeyAuth'],
        seedTravelTimeData: ['isValidRestKeyAuth'],
        incidentReportInsertUserRefInPersonAffected: ['isValidRestKeyAuth'],
        updateInvalidPostcodeProjects: ['isValidRestKeyAuth'],
        seedingParentCompanyToProject: ['isValidRestKeyAuth'],
        updateProjectDataForCompanyChange: ['isValidRestKeyAuth'],
        seedIbChecklistIntoProject: ['isValidRestKeyAuth'],
        seedFaceCollectionForProjects: ['isValidRestKeyAuth'],
        convertDocToPdf: ['isValidRestKeyAuth'],
        rollCallUpdateUserColumns: ['isValidRestKeyAuth'],
        seedCloseCallDefaults: ['isValidRestKeyAuth'],
        correctingIbReports: ['isValidRestKeyAuth'],
        updateProjectPermitConfig: ['isValidRestKeyAuth'],
        updatePermitRequests: ['isValidRestKeyAuth'],
        seedMaterials: ['isValidRestKeyAuth'],
    },
    TouchByteController: {
        getBioMetricSetting: ['isAuth'], //  mobile app is calling it for all users.
        getProjectTbEvents: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'isDeprecatedRoute'],
        getTBUserById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting', 'hasTouchByteSetting', 'isDeprecatedRoute'], // atm feature is for site-admins only
        grantUserSiteAccess: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting', 'hasTouchByteSetting', 'isDeprecatedRoute'], // atm feature is for site-admins only
        revokeUserSiteAccess: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting', 'hasTouchByteSetting', 'isDeprecatedRoute'], // atm feature is for site-admins only
    },
    NotificationController: {
        inviteToInduction: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getUserNotifications: ['isAuth'],
        getUserNotificationsCount: ['isAuth'],
        updateNotificationStatus: ['isAuth'],
        deleteNotificationById: ['isAuth'],
        markAllNotificationsAsSeen: ['isAuth'],
    },
    VisitorController: {
        storeVisitorInfo: ['isInnTimeAuth'],
        storeVisitorInfoAlias: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateVisitorById: ['isInnTimeAuth'],
        updateVisitorByIdAlias: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getVisitorTimeLogs: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 need new policy
        getVisitorTimeLogsByDay: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 need new policy
        searchVisitorByEmail: ['isInnTimeAuth'],
        searchVisitorByEmailAlias: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getOnSiteVisitorOfProject: ['isInnTimeAuth'],
        getOnSiteVisitorCountOfProject: ['isInnTimeAuth'],
        getOnSiteVisitorOfProjectAlias: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getVisitorsOfProject: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectVisitorEvents: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getVisitorEventDetail: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getPreviousVisitorsOfProject: ['isInnTimeAuth'],
    },
    VehicleController: {
        fetchVehicleRegDetails: ['isInnTimeAuth'],
        storeVehicleInfoInnTime: ['isInnTimeAuth'],
        searchVehicleInnTime: ['isInnTimeAuth'],
        storeVehicleLogInnTime: ['isInnTimeAuth'],
        getOnSiteVehicleInnTime: ['isInnTimeAuth'],
        getVehicleTimeLogsByDay: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getVehicleLogsByID: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadVehicleDailyLogsExport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        updateVehicleAndLogs: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getDistanceBwPostcodes: ['isAuth', 'isSiteAdminAuth'],
    },
    UserDocumentController: {
        getMyDocuments: ['isAuth'],
        getCompanyUserDocuments: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/userIsCurrentEmployeeOf'],
        saveDocument: ['isAuth'],
        updateDocument: ['isAuth'],
        deleteDocument: ['isAuth'],
        getAdditionalOwnedDocuments: ['isAuth', 'isMoreThanAUser'], // RC:541 need new policy
        searchCitbAchievement: ['isAuth'],
        getCardInfoFromCSCS: ['isAuth'],
        autoVerifyUserDocuments: ['isAuth'],
        getPPACIdStatus: ['isAuth'],
    },

    MultiFactorAuthController: {
        generateMfaSecret: ['isAuth'],
        activateMfaDevice: ['isAuth'],
    },

    ProjectController: {
        getUserProjects: ['isAuth', 'isSiteAdminAuth'],
        checkCompanyProjectStatus: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        createProject: ['isAuth', 'isMoreThanAUser'], // RC:541 need new policy
        getProject: ['isAuth'],
        siteAdmin_getProject: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        validatePostCode: ['isAuth', 'isMoreThanAUser'],
        innTimeValidatePostCode: ['isInnTimeAuth'],
        getProjectIfInducted: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateProject: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 need new policy
        updateProjectLiveTvFooter: ['isInnTimeAuth'],
        getProjectInductionBookingSetting: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        saveProjectInductionBookingSetting: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInductionBookingSlots: ['isAuth'],
        searchAllProject: ['isAuth', 'isSuperAdminAuth'],
        getnewProject: ['isAuth', 'isSuperAdminAuth'],
        getOnSiteUsers: ['isAuth', 'projectHasOptimaSetting', 'isDeprecatedRoute'],
        getOnSiteUsersOfProject: ['isAuth'],
        getOnSiteUsersGroupByCompany: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadCompanyBreakdown: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getOnSiteUsersInnTime: ['isInnTimeAuth'],
        getOnSiteUsersCountInnTime: ['isInnTimeAuth'],
        getOnSiteEntitiesOfProjectWithLocation: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        saveRollCall: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // from mobile all admins can save it except site management // RC:541 need new policy
        projectstatusUpdate: ['isAuth', 'isSuperAdminAuth'],
        //getSiteHealthAssessmentQuestions: ['isAuth'],
        getUserInfo: ['isAuth', 'isMoreThanAUser'],                // can be any admin
        getGateByProject: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 is called from site-admin routes only.
        getGateByProjectV1: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 is called from site-admin routes only.
        getCompanyProjectsForUser: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getCompanyProjects: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateProjectPartially: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 need companyId into route, so that isCA_Or_CPA_Auth can be applied
        deleteProject: ['isAuth', 'isSuperAdminAuth'],
        addUpdateGateSupervisors: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 is called from site-admin routes only.
        getGateSupervisors: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // RC:541 is called from site-admin routes only.
        getProjectInductedUsers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'isDeprecatedRoute'],
        getProjectInductedUsersCA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'isDeprecatedRoute'],
        getProjectWeatherInfo: ['isAuth'],
        enableSmartSheetForProjectId: ['isValidRestKeyAuth'],
        getClockedInUsersOfProject: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        saveProjectResourcePlans: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        bulkUploadProjectResourcePlans: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadResourcePlannerExport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        getProjectPlannedResources: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateProjectResourcePlanById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteProjectResourcePlanById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInductedOrToBeUsers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        saveProjectSetting: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadProjectRollCallReport: ['isAuth', 'isSiteAdminAuth', 'v2/isValidDownloadRequest'],
        downloadRandomOnSiteOperativesReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
    },
    FormTemplateController: {
        getFormTemplates: ['isAuth'],
        //previewTemplate: ['isAuth'],
    },
    InductionController: {
        getMyInductions: ['isAuth'],
        searchProjectsForInduction: ['isAuth'],
        createInduction: ['isAuth'],
        fetchVehicleRegDetails: ['isAuth'],
        getProjectInductionRequests: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInductionRequestsAlias: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInductions: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'isDeprecatedRoute'], // @deprecated: in favor of `getProjectInductedUsers`
        getProjectInductedUsers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInductedUsersCA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        searchProjectInductedUsers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInductionsInnTime: ['isInnTimeAuth', 'isDeprecatedRoute'], // @deprecated: in favor of `getInductionsListInnTime`
        getInductionsListInnTime: ['isInnTimeAuth'],
        getProjectInductionsWithOnSiteStatus: ['isInnTimeAuth'],
        getBlackListedUserByCompany: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateInductionRequest: ['isAuth'],
        checkDuplicateFaceIntoFR: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        enrolInductionIntoFR: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        updateInductionToBlacklistUser: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateInductionWithNewCompetencies: ['isAuth'],
        overrideInductionTravelTime: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateTravelTimeDetails: ['isAuth'],
        getInductionRequest: ['isAuth'],
        getInductionMedications: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateInductionMedications: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateUserEmployment: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        viewOrDownloadInduction: ['isAuth', 'v2/isValidDownloadRequest'],
        getProjectsAndCompaniesAsSeparateLists: ['isAuth'],
        // sendInviteToInductionAlert: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute', 'v2/forceUpdatingApp'], // @deprecated: spatel: infavor of `NotificationController.inviteToInduction`,
        getProjectInductionsUsersEmployer: ['isAuth'],
        downloadInductionRecords: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        getReceivedBriefings: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        projectInductionsListCA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getInductionEmployersCA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getActiveUsersTimesheetList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getUserTimesheetDetails: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        projectInductionsListSA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getInductionEmployersSA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getInductionJobRolesSA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInductionById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadInductionRecordsXLSX: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadInductionRecordsXlsxCA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getHeatMapDataSA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getHeatMapDataCA:['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getHeatMapDataLiveTv:['isInnTimeAuth', 'isValidProjectInnTimeAuth'],
        downloadCompanyInductionsXLSX: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadQRPoster: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInductedAdmins: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    OptimaController: {
        // getProjectOptimaSetting: ['isAuth'], // site admin only
        createUserWorkingShift: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],   // RC:541 need new policy
        removeUserShiftConfig: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],    // RC:541 need new policy
        getUserActiveWorkingShift: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],                           // RC:541 need new policy
        getProjectGeoFenceEvents: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],  // site admin only.
        getClockingDeclarationAnswers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],  // site admin only.
        getProjectBadgeEvents: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],  // site admin only.
        downloadBadgeEvents: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],  // site admin only.
        // getProjectMemberTimeData: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'isDeprecatedRoute'],  // site admin only.
        getProjectMemberDataForDays: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],  // site admin only.
        updateUserDailyLog: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],                                  // RC:541 need new policy
        updateBadgeEventById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],                                // RC:541 need new policy
        updateGeoFenceEventById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        createOptimaBadge: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], //  site admin only.
        remoteEnrolUserToOptima: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        reGenerateBadges: ['isAuth', 'isSuperAdminAuth','projectHasOptimaSetting'],
        getTotalSiteTimeReport: ['isAuth', 'callerTypeCheck'], // RC:541 need new policy, mobile user also use it
        testBiometricConnection: ['isAuth', 'isSuperAdminAuth'],
        checkOptimaStatus: ['isAuth', 'isSuperAdminAuth', 'projectHasOptimaSetting'],
        getAllOptimaSetting: ['isAuth', 'isSuperAdminAuth'],
        addUpdateOptimaSetting: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        removeOptimaSetting: ['isAuth', 'isSuperAdminAuth'],
        getOptimaBadgeDetail: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'], //  site admin only
        storeUserTimeLog: ['isAuth', 'projectHasOptimaSetting'], // spatel: has geo-fence enabled ONLY
        storeInnTimeAppLog: ['isInnTimeAuth'],
        storeBulkInnTimeAppLogs: ['isInnTimeAuth'],
        addManualGeoFenceEntries: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        storeVisitorInnTimeAppLog: ['isInnTimeAuth'],
        getProjectUsersGeoFenceTimeLogs: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // v2
        getProjectMembers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectOnSiteUsersAccessPoints: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectMemberDailyEvents: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectUserGeoFenceLog: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],                                // RC:541 need new policy
        deleteProjectTimeLogById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getCompanyProjectTimeLogs: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getLiveTvTotalSiteTimeReport: ['isInnTimeAuth', 'isValidProjectInnTimeAuth', 'callerTypeCheck', 'projectHasOptimaSetting'],
        getMembersTimeLogs: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],                                // RC:541 ?? need more detail of user
        getOptimaAccessGroupAndReader: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        unlockAllReaders: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        toggleOptimaAccess: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        optimaFingerprintEnrolment: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        deleteBadgeNumber: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        retrieveBadgeInformation: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        deleteFingerprintEnrolment: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        getEnrollmentStatus: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        importMissingOptimaEvents: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'projectHasOptimaSetting'],
        getUserEventByDay: ['isAuth'],
        getInnTimeUserEventByDay: ['isInnTimeAuth'],
        getOptimaSettingByProjectId: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadProjectCarbonEmissionsReportXLSX: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
    },

    CompanyController: {
        addEmployer: ['isAuth'/*, 'isSuperAdminAuth'*/],
        addEmployerViaInnTime: ['isInnTimeAuth'],
        getEmployer: ['isAuth'],
        companiesList: ['isAuth'],
        companiesListV3: ['isAuth'],
        companiesSearchesList: ['isAuth', 'isSuperAdminAuth'],
        companiesListForInnTimeApp: ['isInnTimeAuth'],
        getEmployersListForInnTimeApp: ['isInnTimeAuth'],
        getUserCompanies: ['isAuth', 'isMoreThanAUser'], // RC:541 need companyId into route, so that isCA_Or_CPA_Auth can be applied
        deleteEmployer: ['isAuth','isSuperAdminAuth'],
        deleteCompanyUser: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getCompanyTimezone: ['isAuth', 'isCompanyAdminAuth'],
        editEmployer: ['isAuth','isSuperAdminAuth'],
        getEmployerById: ['isAuth', 'isMoreThanAUser'],                // can be any admin
        saveNewCompanyUsers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], // is CA or CPA
        // getEmployerLogoByName: ['isAuth', 'isDeprecatedRoute'],
        // getEmployersLogo: ['isAuth', 'isDeprecatedRoute'],
        getEntitiesCountByCompany: ['isAuth','isSuperAdminAuth'],
        mergeCompany: ['isAuth','isSuperAdminAuth'],
        createOrUpdateCompanyDivisions: ['isAuth','isSuperAdminAuth'],
        deleteCompanyDivision: ['isAuth','isSuperAdminAuth'],
        importSupplyChainCompanies: ['isAuth', 'isSuperAdminAuth'],
        supplyChainCompaniesList: ['isAuth'],
        getCompanyProjects: ['isAuth', 'isCompanyAdminAuth'],
    },

    JobRoleController:{
        addJobRole: ['isAuth', 'isSuperAdminAuth'],
        getJobRoles: ['isAuth'],
        getJobRolesForInnTimeApp: ['isInnTimeAuth'],
        deleteJobRole: ['isAuth','isSuperAdminAuth'],
    },

    ResourceController:{
        addCompetency: ['isAuth', 'isSuperAdminAuth'],
        updateMetaCompetency: ['isAuth', 'isSuperAdminAuth'],
        getCompetencies: ['isAuth'],
        deleteCompetency: ['isAuth','isSuperAdminAuth'],
        addWork: ['isAuth', 'isSuperAdminAuth'],
        getTypeOfWorks: ['isAuth'],
        deleteWork: ['isAuth','isSuperAdminAuth'],
        uploadQRImage: ['isAuth','isSuperAdminAuth'],
        getQRImage: ['isAuth'],
        getConversationCategory: ['isAuth'],
        getMatesInMind: ['isAuth'],
        getCountries: ['isAuth'],
        getLightingConditions: ['isAuth'],
        timezones: ['isAuth'],
        getSettingByName: ['isAuth'],
        getSettings: ['isAuth'],
        getSettingsInnTime: ['isInnTimeAuth'],
        updateForceUpdateConfig: ['isAuth','isSuperAdminAuth'],
        updateUsefulInfoFilesData: ['isAuth','isSuperAdminAuth'],
        saveIncidentActionCategories: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2'],
        saveCompanySettingFromAdmin: ['isAuth','isSuperAdminAuth'],
        saveCompanySetting: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2'],
        getCompanySettingByName: ['isAuth'],
        getProjectSettingsByName: ['isAuth'],
        deleteProjectSetting: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getAccessLogs: ['isAuth','isSuperAdminAuth'],
        getMetaDistrict: ['isAuth'],
        getInteractionLogs: ['isAuth','isSuperAdminAuth'],
    },

    Take5sController:{
        createTake5: ['isAuth'],
        getTake5: ['isAuth'],
        getUserTake5s: ['isAuth', 'isSiteAdminFeatureAllowed', 'isDeprecatedRoute'],
        downloadTake5: ['isDeprecatedRoute', 'v2/forceUpdatingApp'], // @todo: Satyam Hardia: deprecated as of Jun 15, 2021.
        downloadTake5V2: ['isAuth', 'v2/isValidDownloadRequest'],
        downloadTake5XlSX: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
    },

    ReportController:{
        getProjectDashboard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getUserTimeSheet: ['isAuth'],
        sendUserTimeSheet: ['isAuth'],
        downloadInductionInviteReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        exportDailyDeclarationReportOfUser: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        exportTotalTimeReportOfProjectForAll: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        getCompanyProjectTimeSheetByWeek: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'], // RC:541 need companyId into route, so that isCA_Or_CPA_Auth can be applied
        getCompanyProjectTimeSheetByWeekV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectUsersPostcode: ['isAuth'],
        getProjectPostcodes: ['isAuth', 'isDeprecatedRoute', 'v2/forceUpdatingApp'],
        getProjectInfo: ['isAuth'],
        getProjectDashboardLiveTv: ['isInnTimeAuth', 'isValidProjectInnTimeAuth'],
        getInducteeTrainingsByProject:['isAuth'] // @todo: spatel check for isMoreThanAUser
    },

    CloseCallController:{
        createCloseCall: ['isAuth'],
        updateCloseCall: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        updateCloseCallV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        closeOutCloseCallAction: ['isAuth'],
        getCloseCall: ['isAuth'],
        getProjectCloseCalls: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProjectCloseCall: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProjectBasedCloseCallUtils: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getUserCloseCallList: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getUserCloseCalls: ['isAuth'],
        projectCloseCallDashboard: ['isAuth', 'isSiteAdminFeatureAllowed'],
        companyCloseCallDashboard: ['isAuth'],
        downloadCloseCallV1: ['isAuth', 'v2/isValidDownloadRequest'],
        downloadCloseCall: ['v2/forceUpdatingApp'],
        downloadCloseCallReport: ['isAuth', 'v2/isValidDownloadRequest'],
    },

    ProjectGateBookingController:{
        createBooking: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        createBookingV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateBooking: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        updateBookingV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteBooking: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        deleteBookingV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateProjectGate: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        exportBookings: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        updateBookingStatus: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        updateBookingStatusV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        exportBookingsReportPDF: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        updateMultipleBookingsStatus: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2']
    },

    RollCallController: {
        getProjectRollCallRecords: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadRollCall: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },
    /*
     * isValidProjectAdminAuth: checks if user has any of these following
     * ['SITE_ADMIN', 'COMPANY_ADMIN', 'COMPANY_PROJECT_ADMIN'] roles to access project admin.
     */
    ToolboxTalkController:{
        createToolboxTalk: ['isAuth'], // isValidProjectAdminAuth
        updateToolboxTalk: ['isAuth'], // isValidProjectAdminAuth
        getProjectToolboxTalks: ['isAuth', 'isSiteAdminFeatureAllowed'], // isValidProjectAdminAuth
        searchTalks: ['isAuth'], // isValidProjectAdminAuth
        getCompanyToolboxTalks: ['isAuth'], // isValidProjectAdminAuth
        getCompanyTBTForInvite: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], 
        inviteToToolboxTalk: ['isAuth', 'isSiteAdminFeatureAllowed'], // isValidProjectAdminAuth
        inviteToToolboxTalkForCompanyTBTs: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadToolboxTalk: ['isDeprecatedRoute', 'v2/forceUpdatingApp'], // @todo: Satyam Hardia: deprecated as of Jun 16, 2021.
        downloadToolboxTalkV2: ['isAuth', 'v2/isValidDownloadRequest'],
        downloadToolboxTalkXLSX: ['isAuth', 'isSiteAdminFeatureAllowed', 'v2/isValidDownloadRequest'],
        downloadToolboxTalkRegister: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
    },

    UserFavoriteController:{
        addProjectToFavorite: ['isAuth'],
        deleteFavoriteProject: ['isAuth'],
        getUserFavoriteProjects: ['isAuth'],
        searchProject: ['isAuth'],
    },

    ProgressPhotosController:{
        addProgressPhotos: ['isAuth'],
        getProgressPhotos: ['isAuth'],
        updateProgressPhotos: ['isAuth'],
        getAllProgressPhotos: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getUserProgressPhotos: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProgressPhotosList: ['isAuth', 'isSiteAdminFeatureAllowed', 'populateAccessVariables'],
        downloadProgressPhotos: ['isDeprecatedRoute', 'v2/forceUpdatingApp'], // @todo: Satyam Hardia: deprecated as of Jun 15, 2021.
        downloadProgressPhotosV2: ['isAuth', 'v2/isValidDownloadRequest'],
        fetchProgressPhotosReportRecords: ['isAuth', 'isSiteAdminFeatureAllowed'],
        downloadProgressPhotosReport: ['isAuth', 'v2/isValidDownloadRequest'],
        downloadProgressPhotosXLSX: ['isAuth', 'v2/isValidDownloadRequest'],
        createProgressPhotosAlbum: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateProgressPhotosAlbum: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteProgressPhotosAlbum: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getAllProgressPhotosAlbums: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        savePhotosAlbumsUserPreference: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    DeliveryNotesController:{
        addDeliveryNote: ['isAuth'],
        updateDeliveryNote: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        updateDeliveryNoteV2: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2'],
        getDeliveryNote: ['isAuth','isMoreThanAUser', 'isDeprecatedRoute'],
        getDeliveryNoteV2: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2'],
        getProjDeliveryNotes: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getUserDeliveryNotes: ['isAuth', 'isSiteAdminFeatureAllowed'],
        fetchDeliveryNotesReportRecords: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2'],
        downloadDeliveryNotesReport: ['isAuth','isMoreThanAUser', 'isDeprecatedRoute', 'v2/isValidDownloadRequest'],
        downloadDeliveryNotesReportV2: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadDeliveryNote: ['isAuth','isMoreThanAUser', 'isDeprecatedRoute', 'v2/isValidDownloadRequest'],
        downloadDeliveryNoteV2: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadDeliveryNotesReportXLSX: ['isAuth','isMoreThanAUser', 'isDeprecatedRoute', 'v2/isValidDownloadRequest'],
        downloadDeliveryNotesReportXLSXV2: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest']
    },

    ProjectDailyActivitiesController:{
        createDailyActivity: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        createDailyActivityV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateDailyActivity: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        updateDailyActivityV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getDailyActivity: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        getDailyActivityV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getDailyActivities: ['isAuth', 'isSiteAdminFeatureAllowed'],
        deleteDailyActivity: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        deleteDailyActivityV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadDailyActivitiesReportXLSX: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute', 'v2/isValidDownloadRequest'],
        downloadDailyActivitiesReportXLSXV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadDailyActivity: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute', 'v2/isValidDownloadRequest'],
        downloadDailyActivityV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadDailyActivitiesReport: ['v2/forceUpdatingApp', 'v2/isValidDownloadRequest'],
        downloadWorkforceHoursReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadHoursComparisonReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadDailyActivities: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        shareDailyActivityReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadActivityHoursBreakdownReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
    },

    ClerkOfWorksController: {
        createClerkOfWorks: ['isAuth'],
        updateClerkOfWorks: ['isAuth'],
        getClerkOfWorks: ['isAuth'],
        getProjectClerkOfWorks: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProjectCowFilterData: ['isAuth', 'isSiteAdminFeatureAllowed'],
        downloadClerkOfWorksReport: ['isAuth', 'isSiteAdminFeatureAllowed', 'v2/isValidDownloadRequest'],
        getAddComment: ['isAuth'],
        deleteCow: ['isAuth'],
        populateTaggedNameEmail: ['isAuth'],
        downloadClerkOfWorks: ['isDeprecatedRoute', 'v2/forceUpdatingApp'], // @todo: Satyam Hardia: deprecated as of Jun 11, 2021.
        getUserClerkOfWorks: ['isDeprecatedRoute', 'v2/forceUpdatingApp'], // @todo: Satyam Hardia: deprecated as of Jun 14, 2021.
        downloadCowV2: ['isAuth', 'v2/isValidDownloadRequest'],
        getUserCoWsV2: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getLevelSiteDrawing: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        savePinCoordinates: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        savePinCoordinatesV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadCowPinnedLevelMap: ['isAuth', 'isSiteAdminFeatureAllowed', 'v2/isValidDownloadRequest'],
        getProjectClerkOfWorksListSA:  ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectClerkOfWorksList:  ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    ProjectPowraController:{
        createPowra: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        createPowraV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updatePowra: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        updatePowraV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getPowra: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        getPowraV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getAllPowra: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deletePowra: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        deletePowraV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadPowraReport: ['isAuth', 'v2/isValidDownloadRequest'],
        sharePowraReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    ProjectIncidentReportController:{
        createIncidentReport: ['isAuth', 'isSiteAdminFeatureAllowed', 'isDeprecatedRoute'],
        partiallyUpdateIncidentReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateIncidentReport: ['isAuth', 'isDeprecatedRoute'],
        createOrUpdateIncidentReport: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getPendingIncidentReport: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getIncidentReport: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProjectIncidentReports: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getCompanyIncidentReports: ['isAuth', 'isDeprecatedRoute'],
        getCompanyIncidentList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getUserIncidentReports: ['isAuth', 'isSiteAdminFeatureAllowed'],
        downloadIncidentReport: ['isDeprecatedRoute', 'v2/forceUpdatingApp'], // @todo: Satyam Hardia: deprecated as of Jun 15, 2021.
        downloadIncidentReportV2: ['isAuth', 'v2/isValidDownloadRequest'],
        shareIncidentReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        closeOutIncidentReport: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        closeOutIncidentReportV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        closeOutIncidentAction: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        closeOutAssignedIncidentAction: ['isAuth'],
        createOrUpdateIncidentAlertRecipients: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getIncidentAlertRecipients: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteIncidentAlertRecipient: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getIncidentActionsToCloseout: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectIncidentListSA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectIncidentList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    ProjectInspectionTourController:{
        createInspectionTour: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'isDeprecatedRoute', 'v2/forceUpdatingApp'], // @todo: Satyam Hardia: deprecated as of Aug 12, 2021.
        updateInspectionTour: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'isDeprecatedRoute', 'v2/forceUpdatingApp'],
        partialCreateOrUpdate: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getInspectionTour: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getPendingInspectionTour: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInspectionToursList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInspectionTours: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getInspectionTourMetaChecklist: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        getInspectionTourMetaChecklistV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectInspectionToursByUser: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateChecklistToCloseOutItem: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        closeOutChecklistItemAction: ['isAuth'],
        dashboardOfInspectionTour: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        companyDashboardOfInspectionTour: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadInspectionTourV1:  ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadInspectionTour: ['v2/forceUpdatingApp'],
        downloadParticipantsList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        subContractorDashboard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        taggedOwnerDashboard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getItemsToCloseout: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        shareInspectionTourReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    ELearningController:{
        createELearningModule: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateELearningModule: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getAllELearningModule: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getELearningModule: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        inviteToELearningModule: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    GoodCallController:{
        getUserGoodCallList: ['isAuth', 'isSiteAdminFeatureAllowed'],
        createGoodCall: ['isAuth', 'isSiteAdminFeatureAllowed'],
        updateGoodCall: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2'],
        closeOutGoodCallAction: ['isAuth'],
        getGoodCall: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProjectBasedGoodCallUtils: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProjectGoodCalls: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getUserGoodCalls: ['isAuth', 'isSiteAdminFeatureAllowed'],
        downloadGoodCallV1: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        projectObservationDashboard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getUserGoodCallListV3: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    InductionQuestionsController: {
        saveInductionQuestions: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getAllQuestionsOfProject: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getActiveInductionQuestionsOfProject: ['isAuth'],
        getInductionQuestions: ['isAuth', 'isDeprecatedRoute'],
        getProjectInductionQuestions: ['isAuth', 'isDeprecatedRoute'],
        createOrUpdateInductionQuestions: ['isAuth', 'isDeprecatedRoute'],
        correctAnswer: ['isAuth', 'isDeprecatedRoute'],
        getProjectInductionQuestionsForAdmins: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'isDeprecatedRoute'],
    },

    TaskBriefingsController:{
        createTaskBriefing: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateTaskBriefing: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        addToolBriefing: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getToolBriefingsList: ['isAuth'], //@todo need to cross-check the feature allowed policy for both APIs
        getBriefingToolRecord: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], //@todo need to cross-check the feature allowed policy for both APIs
        getProjectBriefingToolRecords: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'], //@todo need to cross-check the feature allowed policy for both APIs
        searchBriefings: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectTaskBriefings: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectTaskBriefingsByUser: ['isAuth', 'isSiteAdminFeatureAllowed'],
        inviteToTaskBriefing: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadTaskBriefingReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadTaskBriefingXLSX: ['isAuth', 'isSiteAdminFeatureAllowed', 'v2/isValidDownloadRequest'],
        downloadTaskBriefingV1: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadTaskBriefing: ['v2/forceUpdatingApp'],
        downloadTaskBriefingRegister: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        getBriefingToolRecordForInvite: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2']
    },

    WorkPackagePlansController : {
        createWorkPackagePlan: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateWorkPackagePlan: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectWorkPackagePlans: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'isDeprecatedRoute'],
        searchWorkPackagePlans: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectWorkPackagePlansByUser: ['isAuth', 'isSiteAdminFeatureAllowed'],
        inviteToWorkPackagePlans:['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadWorkPackagePlansReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadWorkPackagePlansXLSX: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadWorkPackagePlans: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadWorkPackagePlansRegister: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
    },

    ProjectRamsController : {
        createRams: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateRams: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        partiallySaveAssessmentForm: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectRamsById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectRams: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'isDeprecatedRoute'],
        getProjectRamsV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectRamsList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectRamsByUser: ['isAuth', 'isSiteAdminFeatureAllowed'],
        searchRams: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        inviteToRams: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadRams:['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadRamsReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadRamsXLSX: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        downloadRamsRegister: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        approveDeclineRams: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadDocumentPreview: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getRamsForInduction: ['isAuth'],
        archiveUnarchiveRams: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getArchivedRams: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        copyAssessmentRamsForm: ['isAuth', 'isSuperAdminAuth'],
        ramsRevisionList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        searchRamsByRevision: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        ramsRecentRevision: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    MetaDailyActivitiesController:{
        addActivity: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectActivitiesList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        importProjectActivities: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'fileUploadLimit'],
        deleteAllMetaActivities: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteMetaActivity: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    MetaProjectPlantMachineryController: {
        addPlant: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectPlantsList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        importProjectPlants: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'fileUploadLimit'],
        deleteAllMetaPlants: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteMetaPlant: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    ProjectAssetsController:{
        addVehicleAsset: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateVehicleAsset: ['isAuth'],
        //getVehicleAsset: ['isAuth'],
        getAssetTaggedOwners: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProjectAssetVehicles: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProjectAssetVehiclesV2: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProjectAssetFaults: ['isAuth', 'isSiteAdminFeatureAllowed'],
        addAssetVehicleInspection: ['isAuth'],
        //updateAssetVehicleInspection: ['isAuth'],
        //getAssetVehicleInspections: ['isAuth'],
        archiveUnarchiveVehicleAsset: ['isAuth'],
        getMostRecentVehicleInspection: ['isAuth'],
        //downloadWeeklyInspections: ['isAuth', 'v2/isValidDownloadRequest'],

        addEquipmentAsset: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateEquipmentAsset: ['isAuth'],
        //getEquipmentAsset: ['isAuth'],
        getProjectAssetEquipments: ['isAuth', 'isSiteAdminFeatureAllowed'],
        getProjectAssetEquipmentsV2: ['isAuth', 'isSiteAdminFeatureAllowed'],
        addAssetEquipmentInspection: ['isAuth'],
        //updateAssetEquipmentInspection: ['isAuth'],
        //getAssetEquipmentInspections: ['isAuth'],
        archiveUnarchiveEquipmentAsset: ['isAuth'],
        // downloadAssetWeeklyInspections: ['isAuth', 'v2/isValidDownloadRequest'],

        downloadRegisterXLSX: ['isAuth', 'v2/isValidDownloadRequest'],
        getProjectAssetTemporaryWorks: ['isAuth', 'isSiteAdminFeatureAllowed'],
        addTemporaryWorkAsset: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateTemporayrWorkAsset: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        addAssetTemporaryWorkInspection: ['isAuth',],
        getAsset: ['isAuth'],
        getAssetInspections: ['isAuth'],
        updateAssetInspection: ['isAuth'],
        downloadAssetWeeklyInspections: ['isAuth', 'v2/isValidDownloadRequest'],
        getAssetFromCodeString: ['isAuth'],
    },

    QualityChecklistController: {
        createChecklist: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        createChecklistV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        createCompanyChecklist: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateChecklist: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        updateChecklistV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateCompanyChecklist: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        createChecklistInspection: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        createChecklistInspectionV2: ['isAuth', 'isMoreThanAUser'], //@todo:  needs to be replaced with 'isSA_OR_CA_OR_CPA_Of_v2' by Oct 1st 2024
        updateChecklistInspection: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteChecklistInspection: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getChecklistInspectionReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateITPToCloseOutItem: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        closeoutITPItemAction: ['isAuth'],
        getPartialCompletedReportCountCA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getPartialCompletedReportCountSA: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        signOffInspectionSection: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getAssignedSignOffList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        searchChecklist: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        searchChecklistV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getChecklistInspections: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectChecklists: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getUserCLInspections: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getItpOpenDefectsCount: ['isAuth'],
        reviewAndCloseoutList: ['isAuth'],
        getuserclinspectionsV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getCLInspectionsByQclId: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadQclReport: ['isAuth', 'v2/isValidDownloadRequest'],
        downloadQclReportV2: ['isAuth', 'v2/isValidDownloadRequest'],
        shareItpReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    ProjectFatigueViolationsController:{
        getProjectViolations: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadReport: ['isAuth', 'v2/isValidDownloadRequest'],
        updatePartially: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        updatePartiallyV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    InspectionBuilderController:{
        createIBChecklist: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        createIBChecklistV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        createCompanyIBChecklistV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateIBChecklist: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        updateIBChecklistV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateCompanyIBChecklistV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        createIBReport: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        createIBReportV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateIBReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteInspectionReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteInspectionReports: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectIBChecklists: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getCompanyIBChecklists: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectIBReports: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteInspectionReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getUserIBReports: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        closeOutIbClReportItem: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        closeOutIbClReportItemV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        closeOutIbClReportAction: ['isAuth'],
        downloadIbClReport: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute', 'v2/isValidDownloadRequest'],
        downloadIbClReportV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        getIBItemsToCloseout: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        inviteToNMsOnInspection: ['isAuth', 'isMoreThanAUser', 'isDeprecatedRoute'],
        inviteToNMsOnInspectionV2: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        dashboardOfInspectionBuilder: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        companyDashboardOfInspectionBuilder: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadParticipantsList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2', 'v2/isValidDownloadRequest'],
        subContractorDashboard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        taggedOwnerDashboard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        shareInspectionBuilderReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    SiteMessagingController: {
        sendMessage: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        resendMessage: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        sendCompanyMessage:['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        editCompanyMessage:['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        resendCompanyMessage:['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        markAsMessage: ['isAuth'],
        getProjectMessages: ['isAuth'],
        getUserMessages: ['isAuth'],
        getUserSentMessages: ['isAuth'],
        getUserReceivedMessages: ['isAuth'],
        getMessage: ['isAuth'],
        getCompanyMessagesCA: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2'],
        getCompanyMessagesRecievedByUser: ['isAuth'],
        getConsolidatedMessagesList: ['isAuth'],
        getConsolidatedUnreadMessageCount: ['isAuth'],
        getUserMessagesCount: ['isAuth'],
        deleteCompanyMessage:['isAuth','isSA_OR_CA_OR_CPA_Of_v2'],
        markAsCompanyMessage:['isAuth'],
        getCompanyMessage:['isAuth'],
        getCompanyMessageRecipientsDetails: ['isAuth','isSA_OR_CA_OR_CPA_Of_v2'],
    },

    ActionsController: {
        getAllActions: ['isAuth'], // can a non-admin is allowed to get project actions??
    },
    SkillMatrixController: {
        getCompanySkillMatrixList: ['isAuth'],
        companySkillsList: ['isAuth'],
        companySkillsToAdd: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        saveSkillMatrixRecord: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteSkillMatrixRecordById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        processBulkImportCompetencyMatrix: ['isAuth', 'isSuperAdminAuth'],
    },
    CompanyInductionController: {
        createCompanyInduction: ['isAuth'],
        getCompanyInductionsList: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getCompanyInductionById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateCompanyInductionStatus: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },
    ConductCardController: {
        createCard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateCard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getCards: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getCardById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        deleteCard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        unassignedConductCard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getAssignedConductCard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadAssignedConductCard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadAssignedProjectConductCard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],

        getCardsByProjectCompany: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        assignConductCard: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        unassignedConductCardPp: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    CompanyAssetConfigController: {
        getCompanyAssetConfig: ['isAuth', 'isCA_Or_CPA_Auth'],
        saveCompanyAssetConfig: ['isAuth', 'isCA_Or_CPA_Auth'],
        seedCompanyAssetConfig: ['isAuth', 'isSuperAdminAuth'],
        getProjectAssetConfig: ['isAuth'],
    },

    PermitController: {
        createPermitTemplate: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updatePermitTemplate: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updatePermitTemplateStatus: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        fetchPermitTemplates: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        saveProjectPermitConfig: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectPermitConfig: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        fetchProjectPermitTemplates: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getProjectActivePermits: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        createPermitRequest: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        resubmitPermitRequest: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        signOffPermitRequest: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        closeoutSignOffPermitRequest: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getPermitRequestsByRequestor: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getPermitRequestsByProject: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getPermitRequestById: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        fetchPermitSignOffUsers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        permitRequestsToSignOff: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        permitRequestAction: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        requestCloseoutNotify: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getNextSignOffUsers: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        viewPermitDocument: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        downloadPermitRequests: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        sharePermit: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        copyPermitTemplates: ['isAuth', 'isSuperAdminAuth'],
        getBriefedPermitsByUser: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getPermitStatus: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },

    PowerBiController: {
        getProjectToolReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        getCompanyToolReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        exportProjectToolReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateProjectExportQueue: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        exportCompanyToolReport: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
        updateCompanyExportQueue: ['isAuth', 'isSA_OR_CA_OR_CPA_Of_v2'],
    },
};
