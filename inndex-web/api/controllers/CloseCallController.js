const dayjs = require('dayjs');
const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const {DEFAULT_PAGE_SIZE} = sails.config.constants;
const _uniq = require('lodash/uniq');
const _groupBy = require('lodash/groupBy');
const {
    closeCallFn,
    inductionFn: {
        getUserInductionEmployer,
    }
} = require('./../sql.fn');
const {
    TokenUtil: {
        allProjectAdminsByOneOfDesignations,
        filterProjectUsersEmailEligibility,
        getCompanyInfo
    },
    UserRevisionService: {
        getLatestUserRevision
    },
    DataProcessingService: {
        getUserFullName,
        buildCloseCallStatus,
        getProjectTimezone,
        attachProfilePicWithUserRefInfo,
        expandProjectClosecalls,
        sendMailToNominatedManagerCPA,
        getDailyTimeEventV2,
        populateUserRefs,
        populateProjectRefs,
        populateEmployerRefs,
    },
    EmailService: {
        sendMail
    },
    ChartService: {
        getOpenClosedBarChart,
        getStackedBarChart,
        getScatterPlot,
        getStackedBarWithLineChart,
        getHoriLollipopChart,
        getDonutChartV2,
    },
    SharedService: {
        instantPdfGenerator,
        downloadPdfViaGenerator,
    },
    ExcelService: {
        streamExcelDownload,
        closeCallReport,
    },
    NotificationService: {
        NOTIFICATION_CATEGORY,
        sendPushNotification
    },
    ResponseService: {successResponse, errorResponse},
    ProcoreService: { exportCloseCallOnCloseOut },
    ASiteService : { checkIfAsiteEnabled, getAsiteProjectToolMapping, uploadDocOnAsite },
    HttpService: {
        decodeURIParam,
    }
} = require('./../services');
const { v4: uuid4 } = require('uuid');

const cleanRecord = (record) => {
    sails.log.info("cleaning cc_images", record.cc_images);
    if (record.cc_images && record.cc_images.length) {
        record.cc_images = (record.cc_images || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
    }

    sails.log.info("cleaning corrective_images", record.corrective_images);
    if (record.corrective_images && record.corrective_images.length) {
        record.corrective_images = (record.corrective_images || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
    }

    if(record.tagged_owner) {
        record.tagged_owner = (record.tagged_owner && record.tagged_owner.id) ? record.tagged_owner.id : record.tagged_owner;
        sails.log.info('Tagged owner id is', record.tagged_owner);
    }

    return record;
}

const sendMailToTaggedOwner = async (closeCall, projectInfo, project_name, cc_phrase, subject) => {
    if (closeCall.tagged_owner && projectInfo.closecall_setting.tagged_companies && projectInfo.closecall_setting.tagged_companies.length) {
        let tz = getProjectTimezone(projectInfo);
        let taggedOwners = (projectInfo.closecall_setting.tagged_companies || []).filter(owner => owner.company_id == closeCall.tagged_owner);
        sails.log.info("taggedOwners", taggedOwners);
        for (let key in taggedOwners) {
            let taggedOwner = taggedOwners[key];
            if (taggedOwner && taggedOwner.email) {
                let cc_images = await sails.models.userfile.find({
                    where: {id: closeCall.cc_images},
                    select: ['file_url', 'sm_url']
                });

                let taggedCompany = await sails.models.createemployer
                    .findOne({
                        where: {id: taggedOwner.company_id},
                        select: ['name']
                    });
                let emailHtml = await sails.renderView('pages/mail/mail-content', {
                    title: subject,
                    mail_body: 'cc-notify-tagged-owner',
                    project_name,
                    receiver_name: '',
                    company_name: taggedCompany.name,
                    cc_phrase,
                    cc_images,
                    custom_fields: closeCall.custom_fields,
                    hazard_category: closeCall.hazard_category,
                    location_and_description: closeCall.location_and_description,
                    lighting_conditions: closeCall.lighting_conditions,
                    additional_detail: closeCall.additional_detail,
                    raised_date_time: dayjs(closeCall.createdAt).tz(tz).format('DD-MM-YYYY HH:mm:ss'),
                    layout: false
                });
                sails.log.info('Sending mail to', taggedOwner.email);
                await sendMail(subject, [taggedOwner.email], emailHtml);
                sails.log.info('Email has been sent to tagged owner.');
            }
        }
    }
}

const sendPushNotificationToAssignedTo = async (req, projectInfo, closeCall, receiverUser, ccPlrPhrase, cc_phrase) => {
    let message = `${ccPlrPhrase}: ${getUserFullName(req.user, true)} has assigned you an action on ${projectInfo.name}`;
    const category = NOTIFICATION_CATEGORY.CLOSE_CALL;
    const notificationData = {
        category,
        document_id: closeCall.id,
        project_id: closeCall.project_ref,
        assigned_by: `${req.user.first_name} ${req.user.last_name}`
    }
    let firebaseMsgData = {
        category: category,
        document_id: closeCall.id.toString(),
        project_id: closeCall.project_ref.toString(),
        assigned_by: `${req.user.first_name} ${req.user.last_name}`
    };

    await sendPushNotification({
        message,
        messageTitle: `Tagged in ${cc_phrase}`,
        recipientUserInfo: receiverUser,
        submittedByUserInfo: req.user,
        category,
        notificationData,
        firebaseMsgData
    });
}

const sendMailToAssignedTo = async (closeCall, projectInfo, assignedToUser, raisedByUser, assignedByUser, cc_phrase) => {
    let subject = `${cc_phrase}: ${projectInfo.name}`;
    let emailHtml = await sails.renderView('pages/mail/mail-content', {
        title: subject,
        mail_body: 'close-call-assigned-to',
        project_name: projectInfo.name,
        cc_phrase,
        closeCall,
        raised_datetime: dayjs(closeCall.createdAt).format('DD-MM-YYYY HH:mm:ss'),
        assigned_to_name: getUserFullName(assignedToUser),
        raised_by_name: (closeCall.is_anonymous) ? 'Anonymous' : getUserFullName(raisedByUser),
        assigned_by_name: getUserFullName(assignedByUser),
        custom_fields: closeCall.custom_fields,
        additional_detail: closeCall.additional_detail,
        layout: false
    });
    sails.log.info('Sending mail to', assignedToUser.email);
    let sentStatus = await sendMail(subject, [assignedToUser.email], emailHtml);
    sails.log.info('Close out email has been sent');
}

const prepareWeeksData = (fromEpoch, toEpoch) => {
    let a = dayjs(fromEpoch).startOf('isoWeek');
    let b = dayjs(toEpoch).endOf('isoWeek');

    let daysBetweenDates = b.diff(a, 'days');
    let startOfWeeksArr = [a.valueOf()];

    if (daysBetweenDates > 6) {
        for (let i = 0; i < (daysBetweenDates/6); i++) {
            let nextVal = dayjs(startOfWeeksArr[i]).add(7, 'days').valueOf();
            if (nextVal > b.valueOf()) {
                break;
            }
            startOfWeeksArr.push(nextVal);
        }
    }

    let data = [];
    for (let i in startOfWeeksArr) {
        let item = {
            'week': +i + 1,
            'total_close_call': 0,
            'closedout_close_calls': [],
            'start_of_week': startOfWeeksArr[i],
            'end_of_week': dayjs(startOfWeeksArr[i]).add(6, 'days').valueOf()
        };
        data.push(item);
    }

    return data;
};

const getTop10CloseCallChartData = (closeCalls) => {
    let top10CloseCallChartData = [];
    let top10CloseCallChartMax = 0;
    let ccGroupByHazardCategory = closeCalls.reduce((obj, cc) => {
        obj[cc.hazard_category] = (obj[cc.hazard_category] || 0) + 1;
        return obj;
    }, {});

    for (let hazard_category in ccGroupByHazardCategory) {
        top10CloseCallChartData.push({
            group: hazard_category,
            Count: ccGroupByHazardCategory[hazard_category]
        });
        top10CloseCallChartMax =  (top10CloseCallChartMax < ccGroupByHazardCategory[hazard_category]) ? ccGroupByHazardCategory[hazard_category] : top10CloseCallChartMax
    }
    top10CloseCallChartData = (top10CloseCallChartData || []).sort((a,b) => (a.Count < b.Count) ? 1 : ((b.Count < a.Count) ? -1 : 0)).slice(0, 10);

    return { top10CloseCallChartData, top10CloseCallChartMax };
};

const getWeeklyCloseCallChartData = (from_date, to_date, closeCalls) => {
    let weeklyCloseCallsChartData = prepareWeeksData(from_date, to_date);
    for (let index in closeCalls) {
        let closeCall= closeCalls[index];
        let weeklyCloseCall = weeklyCloseCallsChartData.find(item => (+closeCall.createdAt >= item.start_of_week && +closeCall.createdAt <= item.end_of_week));
        let weeklyCloseCallIndex = weeklyCloseCallsChartData.findIndex(item => (closeCall.createdAt >= item.start_of_week && closeCall.createdAt <= item.end_of_week ));
        if (weeklyCloseCall && weeklyCloseCallIndex != -1) {
            weeklyCloseCall['total_close_call'] += 1;
            weeklyCloseCallsChartData[weeklyCloseCall] = weeklyCloseCall;
        }
    }

    let weeklyCloseCallsChartMax = 0;
    let weeklyCloseCallsChartXAxisValues = [''];
    weeklyCloseCallsChartData = weeklyCloseCallsChartData.reduce((arr, item) => {
        if (item.total_close_call) {
            weeklyCloseCallsChartXAxisValues.push(item['week']);
            weeklyCloseCallsChartMax = (weeklyCloseCallsChartMax < item.total_close_call) ? item.total_close_call : weeklyCloseCallsChartMax;
            item.week = arr.length + 1;
            arr.push(item);
        }
        return arr;
    }, []);

    return { weeklyCloseCallsChartData, weeklyCloseCallsChartXAxisValues, weeklyCloseCallsChartMax};
};

const getContractorChartData = async (closeCalls, topItems = 0) => {
    let contractorRatingsBarChartMax = 0;
    let contractorRatingsBarChartData = [];
    let contractorRatingsLineChartData = [];
    let taggedOwnerIds = [];
    let userEmployerIds = closeCalls.reduce((arr, cc) => {
        if (cc.user_employer && cc.user_employer.employer) {
            //preparing chart data
            let existingItem = contractorRatingsBarChartData.find(item => (item.group == cc.user_employer.employer));
            let existingItemIndex = contractorRatingsBarChartData.findIndex(item => (item.group == cc.user_employer.employer));
            if (existingItem && existingItemIndex != -1) {
                existingItem.Rating += 1;
                contractorRatingsBarChartData[existingItemIndex] = existingItem;
            } else {
                contractorRatingsBarChartData.push({
                    group: cc.user_employer.employer, Rating: 1
                });
            }
        }

        if (cc.tagged_owner) {
            taggedOwnerIds.push(cc.tagged_owner);

            //preparing chart data
            let existingItem = contractorRatingsLineChartData.find(item => (item.company == cc.tagged_owner));
            let existingItemIndex = contractorRatingsLineChartData.findIndex(item => (item.company == cc.tagged_owner));
            if (existingItem && existingItemIndex != -1) {
                existingItem.Rating += 1;
                contractorRatingsLineChartData[existingItemIndex] = existingItem;
            } else {
                contractorRatingsLineChartData.push({
                    company: cc.tagged_owner, Rating: 1
                });
            }
        }
        return arr;
    }, []);

    let companiesInfo = await sails.models.createemployer.find({
        where: {id: _uniq([...userEmployerIds, ...taggedOwnerIds])},
        select: ['id', 'name']
    });

    let taggedOwners = companiesInfo.filter(company => taggedOwnerIds.includes(company.id));

    contractorRatingsBarChartData.map(item => {
        contractorRatingsBarChartMax = (contractorRatingsBarChartMax < item.Rating) ? item.Rating : contractorRatingsBarChartMax;
        return item;
    });

    contractorRatingsLineChartData.map(item => {
        let company = taggedOwners.find(emp => emp.id == item.company);
        if(company) {
            item.company = company.name;
            contractorRatingsBarChartMax = (contractorRatingsBarChartMax < item.Rating) ? item.Rating : contractorRatingsBarChartMax;

        }
        return item;
    });

    for (let index in contractorRatingsLineChartData) {
        let item = contractorRatingsLineChartData[index];
        let existingItemIndex = contractorRatingsBarChartData.findIndex(bItem => (bItem.group == item.company));
        if (existingItemIndex == -1) {
            contractorRatingsBarChartData.push({
                group: item.company, Rating: 0
            })
        }
    }

    if (topItems) {
        contractorRatingsBarChartData =  (contractorRatingsBarChartData || []).sort((a,b) => (a.Rating < b.Rating) ? 1 : ((b.Rating < a.Rating) ? -1 : 0)).slice(0, topItems);
    }

    contractorRatingsBarChartData =  (contractorRatingsBarChartData || []).sort((a,b) => (a.group > b.group) ? 1 : ((b.group > a.group) ? -1 : 0));

    let tempContractorRatingsLineChartData = contractorRatingsBarChartData.reduce((arr, item) => {
        let tempItem = contractorRatingsLineChartData.find(lItem => lItem.company == item.group);
        if (tempItem) {
            arr.push(tempItem);
        }
        return arr;
    }, []);

    contractorRatingsLineChartData = tempContractorRatingsLineChartData;

    return { contractorRatingsBarChartData, contractorRatingsLineChartData, contractorRatingsBarChartMax };
}

const syncCloseCallToAsite = async (req, res, projectInfo, ccId, recordId, localsObj, userEmp = 0) => {
    sails.log.info(`[syncCloseCallToAsite] Starting execution for close call ${ccId} employer ID ${userEmp}`);

    let {workspace_id, matched_tool} = await getAsiteProjectToolMapping(projectInfo.id, 'close_calls', userEmp);
    if(!workspace_id || !matched_tool) {
        sails.log.info(`[syncCloseCallToAsite] Aborting execution for close call ${ccId} workspace_id or matched_tool not found.`);
        return;
    }

    let { employer } = await getCompanyInfo(projectInfo, null, ['id', 'company_initial']);

    sails.log.info(`[syncCloseCallToAsite] preparing PDF for close call ${ccId}`);
    res.locals = localsObj || res.locals;

    let fileData = await prepareDataToDownload(req, res, {id: ccId}, userEmp, 'path');
    sails.log.info(`[syncCloseCallToAsite] PDF prepared, starting asite upload for close call ${ccId}`);

    //File name format -> {PROJECT/CONTRACT NUMBER} ({INNDEX PROJECT ID NUMBER})-{COMPANY INITIALS}-{TOOL NAME}-{REPORT TYPE}-{REPORT #}
    let toolPhraseForAsite = (projectInfo.custom_field.cc_phrase || '').replace(/\s/g, '_');
    fileData.name = `${projectInfo.project_number} (${projectInfo.id})-${employer.company_initial}-${toolPhraseForAsite}-CloseCall_Report-${recordId}.pdf`;
    sails.log.info(`[syncCloseCallToAsite] Filename to be used on asite ${fileData.name}`);

    await uploadDocOnAsite(employer.id, workspace_id, matched_tool.folder_id, fileData);
};

const prepareDataToDownload = async (req, res, whereClause, companyId, responseType = 'url') => {
    let closeCall =  await sails.models.closecall.findOne(whereClause)
        .populate('user_ref')
        .populate('project_ref')
        .populate('tagged_owner')
        .populate('assigned_to');
    if (closeCall && closeCall.id) {
        sails.log.info('got record, id', closeCall ? closeCall.id : undefined);
        if (closeCall.cc_images && closeCall.cc_images.length) {
            sails.log.info('Expanding close call images.');
            try {
                closeCall.cc_images = await sails.models.userfile.find({id: closeCall.cc_images});
            } catch (e) {
                closeCall.cc_images = [];
                sails.log.info('Failed to expand close call images.');
            }
        }

        if (closeCall.corrective_images && closeCall.corrective_images.length) {
            sails.log.info('Expanding close call corrective images.');
            try {
                closeCall.corrective_images = await sails.models.userfile.find({id: closeCall.corrective_images});
            } catch (e) {
                closeCall.corrective_images = [];
                sails.log.info('Failed to expand close call corrective images.');
            }
        }

        let { project_logo_file, companyName } = await getCompanyInfo(closeCall.project_ref, (companyId ? {id: companyId} : null));

        let cc_images = [];
        let corrective_images = [];
        closeCall.cc_images.map(row => {
            if (row.img_translation && row.img_translation.length) {
                cc_images.push(...row.img_translation);
            } else if (row.file_url) {
                cc_images.push(row.sm_url || row.md_url || row.file_url);
            }
        });

        closeCall.corrective_images.map(row => {
            if (row.img_translation && row.img_translation.length) {
                corrective_images.push(...row.img_translation);
            } else if (row.file_url) {
                corrective_images.push(row.sm_url || row.md_url || row.file_url);
            }
        });

        let reportedBy = 'Anonymous';
        let employer = '';
        if (!closeCall.is_anonymous) {
            reportedBy = closeCall.user_ref.first_name+' '+closeCall.user_ref.last_name;

            let induction_requests = await sails.models.inductionrequest_reader.find({
                where:{ project_ref: closeCall.project_ref.id, user_ref: closeCall.user_ref.id, status_code: [2, 6] },
                select: ['additional_data'],
                sort:[
                    {id: 'DESC'},
                ],
                limit: 1
            });
            employer = ((induction_requests[0] && induction_requests[0].additional_data && induction_requests[0].additional_data.employment_detail && induction_requests[0].additional_data.employment_detail.employer) || '').toString().trim();
        }
        let tz = getProjectTimezone(closeCall.project_ref);
        let form_template = `pages/closecall-form-page`;
        const date_line = `Report Date: ${dayjs(closeCall.createdAt).format('DD-MM-YYYY HH:mm:ss')}`;
        const project_line = `${closeCall.project_ref.name} (#${closeCall.project_ref.id}): ${closeCall.project_ref.contractor}`;
        let title = closeCall.project_ref.custom_field.cc_phrase_singlr;
        let html = await sails.renderView(form_template, {
            title: title,
            heading_line: title,
            date_line: date_line,
            project_line: project_line,
            cc_number: closeCall.cc_number,
            reported_by: reportedBy,
            reported_by_employer: (employer) ? employer : '',
            hazard_category: closeCall.hazard_category,
            lighting_conditions: closeCall.lighting_conditions,
            location_and_description: closeCall.location_and_description,
            additional_detail: closeCall.additional_detail,
            tagged_owner: (closeCall.tagged_owner && closeCall.tagged_owner.name) ? closeCall.tagged_owner.name : 'N/A',
            isSiteAdminProject: (closeCall.project_ref.project_category === 'default'),
            cc_detail: closeCall.cc_detail,
            cc_images: cc_images,
            custom_fields: closeCall.custom_fields ? closeCall.custom_fields : [],
            assigned_to: (closeCall.assigned_to && closeCall.assigned_to.id) ? getUserFullName(closeCall.assigned_to) : '',
            project_logo_file: project_logo_file,
            project: closeCall.project_ref,
            corrective_images: corrective_images,
            corrective_detail: closeCall.corrective_detail,
            closed_out_by: closeCall.closed_out_by,
            closed_out_date: closeCall.closed_out_date ? dayjs(+closeCall.closed_out_date).tz(tz).format('DD-MM-YYYY HH:mm:ss') : null,
            layout: false,
            raised_date_time: dayjs(closeCall.createdAt).tz(tz).format('DD-MM-YYYY HH:mm:ss'),
            location: closeCall.location,
            toFixed(number) {
                return (+number) ? (+number).toFixed(4) : 0;
            }
        });

        const desiredFilename = `${title}-Report-${dayjs().format('MM-DD-YYYY')}`;
        return await downloadPdfViaGenerator({
            req,
            res,
            html,
            tool: 'close-call',
            file_name: desiredFilename,
            heading_line: title,
            project_line,
            date_line,
            logo_file: project_logo_file,
            has_cover: true,
            has_one_page: true,
            responseType
        });
    }

    sails.log.info('Failed to find close call.');
    return ResponseService.errorResponse(res, 'Something went wrong, Failed to find close call.');
};

const arrangeOrderCustomFields = (customFields) => {
    let fieldsOrder = ['hazard_category','lighting_conditions','location','location_and_description','additional_detail','cc_detail','is_anonymous'];
    let rearrangedCustomFields = [];
    fieldsOrder.map(dField => {
        let field = customFields.find(cField => cField.field && cField.field == dField);
        if(field) {
            let fieldIndex = customFields.findIndex(cField => cField.field && cField.field == dField);
            customFields.splice(fieldIndex, 1);
            rearrangedCustomFields.push(field);
        }
    });

    return [...rearrangedCustomFields, ...customFields];
}

const processCloseCallUpdate = async (req, res, updateRequest, closeOutRequest, ccId, existingCloseCall, additionalInfo) => {

    if (closeOutRequest && closeOutRequest.closed_out_by) {
        updateRequest.closed_out_by = closeOutRequest.closed_out_by;
        updateRequest.closed_out_date = dayjs().valueOf();
    }

    if (updateRequest.custom_fields && updateRequest.custom_fields.length) {
        updateRequest.custom_fields = arrangeOrderCustomFields(updateRequest.custom_fields);
    }

    updateRequest = cleanRecord(updateRequest);
    if(updateRequest.corrective_images && updateRequest.corrective_images.length && existingCloseCall.project_ref && existingCloseCall.project_ref.closecall_setting && existingCloseCall.project_ref.closecall_setting.photo_disallowed){
        sails.log.warn(`creating close call with photos not allow for given project, ${existingCloseCall.project_ref.id}`, updateRequest);
        return errorResponse(res, sails.__('photos_not_allowed'));
    }
    let closeCall = await sails.models.closecall.updateOne({id: ccId}).set(updateRequest);
    let projectInfo = existingCloseCall.project_ref;
    let project_name = projectInfo.name;
    let cc_phrase = projectInfo.custom_field.cc_phrase_singlr;
    let ccPlrPhrase = projectInfo.custom_field.cc_phrase;
    if(closeCall && closeCall.id) {
        if(additionalInfo && existingCloseCall.project_ref.id && additionalInfo.userData) {
            let cc_phrase = existingCloseCall.project_ref.custom_field.cc_phrase_singlr;
            let project_name = existingCloseCall.project_ref.name;
            let subject = `${cc_phrase} Closeout: ${project_name}`;
            let user_name = additionalInfo.userData.name;
            let cc_number = closeCall.cc_number;
            let corrective_images = additionalInfo.correctiveImages;
            let toUser = additionalInfo.userData.email;
            let corrective_detail = closeOutRequest.corrective_detail;
            let hazard_category = closeCall.hazard_category;
            let location_and_description = closeCall.location_and_description;
            let lighting_conditions = (closeCall.lighting_conditions) ? closeCall.lighting_conditions : '';
            let emailHtml = await sails.renderView('pages/mail/close-out', {
                title: subject,
                user_name,
                cc_number,
                project_name,
                cc_phrase,
                corrective_detail,
                corrective_images,
                hazard_category,
                location_and_description,
                lighting_conditions,
                custom_fields: closeCall.custom_fields,
                additional_detail: closeCall.additional_detail,
                layout: false
            });
            sails.log.info('Sending mail to', toUser);
            let sentStatus = await sendMail(subject, [toUser], emailHtml);
            sails.log.info('Close out email has been sent');
        }

        if (existingCloseCall.tagged_owner != closeCall.tagged_owner) {
            let subject = cc_phrase+': Project - '+project_name;
            await sendMailToTaggedOwner(closeCall, projectInfo, project_name, cc_phrase, subject);
        }

        let imagesObj = await sails.models.userfile_reader.find({
            where: {id: _uniq([...closeCall.cc_images, ...closeCall.corrective_images])},
            select: ['id', 'sm_url', 'md_url', 'file_url', 'name']
        });

        closeCall.cc_images = (imagesObj || []).filter(obj => closeCall.cc_images.includes(obj.id));
        closeCall.corrective_images = (imagesObj || []).filter(obj => closeCall.corrective_images.includes(obj.id));

        if (existingCloseCall.assigned_to != closeCall.assigned_to) {
            sails.log.info('Sending push notification to user on assigning on close call.');
            const receiverUser = await sails.models.user_reader.findOne({
                where: {id: closeCall.assigned_to},
                select: ['id', 'first_name', 'last_name', 'email']
            });

            //push notification
            await sendPushNotificationToAssignedTo(req, projectInfo, closeCall, receiverUser, ccPlrPhrase, cc_phrase);

            //mail
            await sendMailToAssignedTo(closeCall, projectInfo, receiverUser, existingCloseCall.user_ref, req.user, cc_phrase);
        }

        if (closeOutRequest && closeOutRequest.closed_out_by) {
            let hasAsite = await checkIfAsiteEnabled(projectInfo.id);
            if(hasAsite) {
                sails.log.info(`Project has asite enabled trying to sync close call document for project ${projectInfo.id}`);
                let userEmployer = await getUserInductionEmployer({id: existingCloseCall.user_ref.id}, existingCloseCall.project_ref.id);
                // Check if user's induction employer not there, Load employer from profile data.
                if(!userEmployer) {
                    userEmployer = existingCloseCall.user_ref.parent_company;
                }
                // Not awaiting syncCloseCallToAsite fn, to run the PDF generation and asite upload in backend.
                let localsObj = Object.assign({}, res.locals); // Cloning the res.locals, as the object gets cleared after the API response sent.
                syncCloseCallToAsite(req, res, projectInfo, ccId, existingCloseCall.cc_number, localsObj, userEmployer).catch(sails.log.error);
            }

            closeCall.procore_raisedby = (closeCall.is_anonymous) ? 'Anonymous' : getUserFullName(existingCloseCall.user_ref);
            await exportCloseCallOnCloseOut(projectInfo.id, closeCall);
        }
        sails.log.info('updated close call successful, id', closeCall ? closeCall.id : undefined);
        return ResponseService.successResponse(res, closeCall);
    }
    sails.log.info('Failed to update the close call', failure);
    return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, failure);
};

const updateCloseCallFn = async (req, res) => {
        let ccId = +req.param('ccId');
        sails.log.info('update close call request', ccId);
        if (!ccId) {
            return ResponseService.errorResponse(res, 'close call id is required');
        }

        let additionalInfo = (req.body.additional_data) ? req.body.additional_data : {};
        let closeOutRequest = req.body.close_out_request ;
        let updateRequest = _.pick((closeOutRequest || req.body.update_request || {}), [
            'cc_number',
            'cc_detail',
            'corrective_detail',
            'cc_images',
            'corrective_images',
            'hazard_category',
            'lighting_conditions',
            'location_and_description',
            'status',
            'closed_out_by',
            'closed_out_date',
            'is_anonymous',
            'tagged_owner',
            'additional_detail',
            'custom_fields',
            'assigned_to'
        ]);

        let existingCloseCall = await sails.models.closecall.findOne({
            where: {id: ccId},
            select: ['user_ref', 'project_ref', 'tagged_owner','assigned_to', 'cc_number']
        });
        existingCloseCall = await populateUserRefs([existingCloseCall], 'user_ref', []);
        [existingCloseCall] = await populateProjectRefs(existingCloseCall, 'project_ref', []);

        return processCloseCallUpdate(req, res, updateRequest, closeOutRequest, ccId, existingCloseCall, additionalInfo);
    };

module.exports = {

    createCloseCall: async (req, res) => {
        sails.log.info('Create close call for project, by', req.user.id);
        let createRequest = _.pick((req.body || {}), [
            'project_ref',
            'company_ref',
            'cc_detail',
            'corrective_detail',
            'cc_images',
            'corrective_images',
            'hazard_category',
            'lighting_conditions',
            'location_and_description',
            'status',
            'closed_out_by',
            'closed_out_date',
            'is_anonymous',
            'tagged_owner',
            'location',
            'additional_detail',
            'custom_fields',
            'assigned_to',
        ]);

        createRequest.user_ref = req.user.id;
        createRequest.company_ref = (req.user.parent_company && req.user.parent_company.id) || req.user.parent_company || null;

        let revision = await getLatestUserRevision(createRequest.user_ref);
        createRequest.user_revision_ref = revision.id;

        if (createRequest.custom_fields && createRequest.custom_fields.length) {
            createRequest.custom_fields = arrangeOrderCustomFields(createRequest.custom_fields);
        }
        sails.log.info('creating close call with', createRequest);
        createRequest = cleanRecord(createRequest);
        let projectInfo = await sails.models.project.findOne({where: {id: createRequest.project_ref}, select: ['name','custom_field','project_category','parent_company','closecall_setting']});
        if(createRequest.cc_images && createRequest.cc_images.length && projectInfo.closecall_setting && projectInfo.closecall_setting.photo_disallowed){
            sails.log.warn(`creating close call with photos not allow for given project, ${projectInfo.id}`, createRequest);
            return errorResponse(res, sails.__('photos_not_allowed'));
        }
        let closeCall = await sails.models.closecall.create(createRequest);
        if(closeCall)  {
            let project_name = projectInfo.name;
            let cc_phrase = projectInfo.custom_field.cc_phrase_singlr;
            let ccPlrPhrase = projectInfo.custom_field.cc_phrase;
            let subject = cc_phrase+': Project - '+project_name;
            let userInfo = await sails.models.user_reader.findOne({
                select: ['first_name', 'last_name'],
                where:{
                    id: closeCall.user_ref
                }
            });

            let close_call_user = 'and set to anonymous';
            if (!closeCall.is_anonymous) {
                close_call_user = 'by '+userInfo.first_name +' '+ userInfo.last_name;
            }

            let projUsrResult = await allProjectAdminsByOneOfDesignations(closeCall.project_ref, ['nominated', 'custom']);
            projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, 'close_calls');
            sails.log.info('Send email to nom managers, count:', projUsrResult.length);
            for (let j = 0, len = projUsrResult.length; j < len; j++) {
                let nomManager = projUsrResult[j].user_ref || {};
                try {
                    let user_name = getUserFullName(nomManager);
                    let emailHtml = await sails.renderView('pages/mail/close-call', {
                        title: subject,
                        user_name,
                        close_call_user,
                        project_name,
                        cc_phrase,
                        layout: false
                    });
                    sails.log.info('Sending mail to', nomManager.email);
                    await sendMail(subject, [nomManager.email], emailHtml);
                    sails.log.info('mail has been sent to nominated manager.');
                } catch (e) {
                    sails.log.info('Unable to send email to nom manager.', e);
                    return false;
                }
            }
            //send mail to company project admins(Nominated Managers)
            if (projectInfo.project_category === 'company-project' && projectInfo.parent_company) {
                await sendMailToNominatedManagerCPA(projectInfo.parent_company, projectInfo, req.user, 'Close Call', 'raised a Close Call', closeCall.is_anonymous);
            } else {
                //send email to tagged owner
                await sendMailToTaggedOwner(closeCall, projectInfo, project_name, cc_phrase, subject);
            }

            if (closeCall.assigned_to) {
                closeCall.cc_images = await sails.models.userfile_reader.find({id: closeCall.cc_images});

                const receiverUser = await sails.models.user_reader.findOne({
                    where: {id: closeCall.assigned_to},
                    select: ['id', 'first_name', 'last_name', 'email']
                });

                //Push Notification
                await sendPushNotificationToAssignedTo(req, projectInfo, closeCall, receiverUser, ccPlrPhrase, cc_phrase);

                //mail
                await sendMailToAssignedTo(closeCall, projectInfo, receiverUser, req.user, req.user, cc_phrase);
            }
            return ResponseService.successResponse(res, closeCall);
        }
        sails.log.info('Failed to create close call');
        return ResponseService.errorResponse(res, sails.__('Failed to create close call'));
    },

    updateCloseCall: updateCloseCallFn,

    updateCloseCallV2: updateCloseCallFn,

    closeOutCloseCallAction: async (req, res) => {
        let ccId = +req.param('ccId');
        sails.log.info('update close call request', ccId);
        if (!ccId) {
            return ResponseService.errorResponse(res, 'close call id is required');
        }

        let additionalInfo = (req.body.additional_data) ? req.body.additional_data : {};
        let closeOutRequest = req.body.close_out_request ;
        let updateRequest = _.pick((req.body.close_out_request || {}), [
            'corrective_detail',
            'corrective_images',
            'status',
            'closed_out_by',
            'closed_out_date',
        ]);

        sails.log.info(`[closeOutCloseCallAction] Fetch project close call ID: ${ccId}`);

        let existingCloseCall = await sails.models.closecall.findOne({
            where: {id: ccId},
            select: ['user_ref', 'project_ref', 'tagged_owner','assigned_to', 'cc_number']
        });
        existingCloseCall = await populateUserRefs([existingCloseCall], 'user_ref', []);
        [existingCloseCall] = await populateProjectRefs(existingCloseCall, 'project_ref', []);

        if(existingCloseCall.assigned_to !== req.user.id) {
            sails.log.info('User not allowed to closed out close call.', req.user.id);
            return successResponse(res, {message: 'Unable to closeout, Access denied.'});
        }

        return processCloseCallUpdate(req, res, updateRequest, closeOutRequest, ccId, existingCloseCall, additionalInfo);
    },

    getCloseCall: async (req, res) => {
        let ccId = +req.param('ccId');
        sails.log.info('Fetch close call, id:', ccId);

        if (!ccId) {
            return ResponseService.errorResponse(res, 'close call id is required');
        }

        sails.models.closecall.findOne({id: ccId})
            .populate('user_ref')
            .populate('project_ref')
            .populate('tagged_owner')
            .populate('assigned_to')
            .exec(async function findCallback(findError, closeCall) {
                if (findError) {
                    sails.log.info('Failed to find close call', findError);
                    return ResponseService.errorResponse(res, sails.__('internal server error'), findError);
                }
                sails.log.info('got record, id', closeCall ? closeCall.id : undefined);
                if(closeCall && closeCall.id){
                    if(closeCall.cc_images && closeCall.cc_images.length){
                        sails.log.info('Expanding close call images.');
                        try{
                            closeCall.cc_images = await sails.models.userfile.find({id: closeCall.cc_images});
                        }catch (e) {
                            sails.log.info('Failed to expand close call images.');
                        }
                    }

                    if(closeCall.corrective_images && closeCall.corrective_images.length){
                        sails.log.info('Expanding close call corrective images.');
                        try{
                            closeCall.corrective_images = await sails.models.userfile.find({id: closeCall.corrective_images});
                        }catch (e) {
                            sails.log.info('Failed to expand close call corrective images.');
                        }
                    }

                    if(closeCall.user_ref.id){
                        sails.log.info('Expanding user detail.');
                        let induction_requests = await sails.models.inductionrequest_reader.find({
                            where:{ project_ref: closeCall.project_ref.id, user_ref: closeCall.user_ref.id, status_code: [2, 6] },
                            select: ['additional_data'],
                            sort:[
                                {id: 'DESC'},
                            ],
                            limit: 1
                        });
                        closeCall.user_employer = ((induction_requests[0] && induction_requests[0].additional_data && induction_requests[0].additional_data.employment_detail) || {});
                    }
                    return ResponseService.successResponse(res, {closeCall});
                }
                return ResponseService.successResponse(res, {closeCall: null});
            });
    },

    getProjectCloseCalls: async (req, res) => {
        let projectId = +req.param('projectId');
        let companyId = +req.param('companyId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let category = req.param('category') ? decodeURIParam((req.param('category', '')).toString().trim()).split(",") : [];
        let assigned_to = req.param('assigned_to') ? req.param('assigned_to').split(",") : [];
        let raised_by = req.param('raised_by') ? req.param('raised_by').split(",") : [];
        let tagged_owner = req.param('tagged_owner') ? req.param('tagged_owner').split(',').map(a=>+a): [];
        let status = (req.param('status') && req.param('status').toLowerCase() != 'null') ? req.param('status').split(",").map(a=>+a): [];
        let search = decodeURIParam((req.param('search', '')).toString().trim());
        let download = req.param('download', 'false') === 'true';

        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');

        if (download) {
            try {
                let filter;
                filter = { project_ref: projectId };
                if (companyId && !is_inherited_project) {
                    filter.company_ref = companyId;
                }
                sails.log.info('fetch all close-calls with filter:', filter, `is_inherited_project: ${is_inherited_project}`);
                let projectCloseCalls = await sails.models.closecall.find(filter)
                    .sort([
                        {status: 'ASC'},
                        {id: 'DESC'}
                    ])
                    .populate('tagged_owner');
                projectCloseCalls = await populateUserRefs(projectCloseCalls, 'user_ref', ['title', 'first_name', 'middle_name', 'last_name', 'email']);
                projectCloseCalls = await expandProjectClosecalls(projectCloseCalls, false);
                return ResponseService.successResponse(res, {project_close_calls: projectCloseCalls});
            } catch (e) {
                sails.log.info('Failed to fetch all close call ?', e);
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, e);
            }
        }

        let {
            total: openCallsCount,
            records: _
        } = await closeCallFn.getProjectCloseCalls(projectId, companyId && !is_inherited_project ? companyId : null, [1], category, assigned_to, raised_by, tagged_owner, search);

        let {
            total: totalCount,
            records: projectCloseCalls
        } = await closeCallFn.getProjectCloseCalls(projectId, companyId && !is_inherited_project ? companyId : null, status, category, assigned_to, raised_by, tagged_owner, search, pageSize, (pageSize * pageNumber));

        projectCloseCalls = projectCloseCalls.map(closeCall => ({
            ...closeCall,
            createdAt:  +closeCall.createdAt,
            updatedAt: +closeCall.updatedAt,
        }));

        projectCloseCalls = await populateUserRefs(projectCloseCalls, 'user_ref', ['title', 'first_name', 'middle_name', 'last_name', 'email']);
        projectCloseCalls = await populateUserRefs(projectCloseCalls, 'assigned_to', ['first_name', 'last_name', 'email']);
        projectCloseCalls = await populateEmployerRefs(projectCloseCalls, 'tagged_owner', ['name']);
        projectCloseCalls = await attachProfilePicWithUserRefInfo(projectCloseCalls);
        projectCloseCalls = await expandProjectClosecalls(projectCloseCalls, true);

        // NOTE: Adding backward compatibility for `hazardTypes`, `taggedOwners` and `employer`.
        // @todo: rugved To remove hazard types, taggedOwners and employers internal calls and remove it later on from response
        // hazard types
        let hazardTypes = await closeCallFn.getProjectCloseCallsHazardTypes(projectId);

        hazardTypes = (hazardTypes || []).map(a => a.hazard_category).filter(hc => hc != "");
        sails.log.info(`Fetched hazard types: ${hazardTypes.length}, projectId: ${projectId}`);

        // tagged owners
        sails.log.info(`Fetch tagged owners based on close call, projectId: ${projectId}`);

        let taggedOwners = await closeCallFn.getProjectCloseCallsTaggedOwners(projectId);

        sails.log.info(`Fetched tagged owners: ${taggedOwners.length}, projectId: ${projectId}`);

        // employers
        sails.log.info(`Fetch employer based on close call, projectId: ${projectId}`);
        let employer = ['Anonymous'];

        let _employer = await closeCallFn.getProjectCloseCallsEmployer(projectId);

        _employer = (_employer || []).map(a => a.employer);
        employer = [...employer, ..._employer];

        sails.log.info(`Fetched employer: ${employer.length}, projectId: ${projectId}`);

        return ResponseService.successResponse(res, {
            project_close_calls: projectCloseCalls,
            total_record_count: totalCount,
            totalCloseCall: [],
            openCalls: openCallsCount,
            hazardTypes: hazardTypes,
            employer: employer,
            taggedOwners: taggedOwners,
        });
    },

    getProjectCloseCall: async(req, res) => {
        let projectId = +req.param('projectId');
        let ccId = +req.param('ccId');

        sails.log.info(`Fetch close call projectId: ${projectId}, ccId: ${ccId}`);

        let projectCloseCall = await sails.models.closecall_reader.find({ project_ref: projectId, id: ccId });

        projectCloseCall = projectCloseCall.map(closeCall => ({
            ...closeCall,
            createdAt:  +closeCall.createdAt,
            updatedAt: +closeCall.updatedAt,
        }));

        projectCloseCall = await populateUserRefs(projectCloseCall, 'user_ref', ['title', 'first_name', 'middle_name', 'last_name', 'email']);
        projectCloseCall = await populateUserRefs(projectCloseCall, 'assigned_to', ['first_name', 'last_name', 'email']);
        projectCloseCall = await populateEmployerRefs(projectCloseCall, 'tagged_owner', ['name']);
        projectCloseCall = await attachProfilePicWithUserRefInfo(projectCloseCall);
        projectCloseCall = await expandProjectClosecalls(projectCloseCall, true);

        if (projectCloseCall && projectCloseCall.length > 0) {
            projectCloseCall = projectCloseCall[0];
        }

        return ResponseService.successResponse(res, {"close_call": projectCloseCall});
    },

    getProjectBasedCloseCallUtils: async (req, res) => {
        const projectId = +req.param('projectId');
        const hazardTypesBool = req.param('hazardTypes') === 'true';

        sails.log.info(`Fetching close call utils: projectId: ${projectId}, hazardTypesBool = ${hazardTypesBool}`);

        let hazardTypes = [];
        let taggedOwners = [];
        let employer = ['Anonymous'];
        let assignedTo = [];
        let raisedBy = [{id: 0, name: "Anonymous"}];

        if (hazardTypesBool) {
            [
                hazardTypes,
                taggedOwners,
                _employer,
                assignedTo,
                _raisedBy,
            ] = await Promise.all([
                closeCallFn.getProjectCloseCallsHazardTypes(projectId),
                closeCallFn.getProjectCloseCallsTaggedOwners(projectId),
                closeCallFn.getProjectCloseCallsEmployer(projectId),
                closeCallFn.getProjectCloseCallsAssignedTo(projectId),
                closeCallFn.getProjectCloseCallsRaisedBy(projectId),
            ]);
        } else {
            [
                taggedOwners,
                _employer,
                assignedTo,
                _raisedBy,
            ] = await Promise.all([
                closeCallFn.getProjectCloseCallsTaggedOwners(projectId),
                closeCallFn.getProjectCloseCallsEmployer(projectId),
                closeCallFn.getProjectCloseCallsAssignedTo(projectId),
                closeCallFn.getProjectCloseCallsRaisedBy(projectId),
            ]);
        }

        if (hazardTypesBool) {
            hazardTypes = (hazardTypes || []).map(a => a.hazard_category).filter(hc => hc != "");
        }

        _employer = (_employer || []).map(a => a.employer);
        employer = [...employer, ..._employer];
        raisedBy = [...raisedBy, ..._raisedBy];

        sails.log.info(`Fetched ${hazardTypesBool ? `hazard types: ${hazardTypes.length},` : ''} tagged owners: ${taggedOwners.length}, employer: ${employer.length}, projectId: ${projectId}`);

        let resObj = {}

        if (hazardTypesBool) {
            resObj.total_hazard_types = hazardTypes.length;
            resObj.hazard_types = hazardTypes;
        }

        resObj.total_tagged_owners = taggedOwners.length;
        resObj.tagged_owners = taggedOwners;
        resObj.total_employer = employer.length;
        resObj.employer = employer;
        resObj.assigned_to = assignedTo;
        resObj.raised_by = raisedBy;

        return ResponseService.successResponse(res,resObj);
    },

    // @todo: vshal: deprecated api, need to remove on 1st Dec
    downloadCloseCall: async (req, res) => {
        let ccId = +req.param('ccId');
        let updatedAt = +req.param('timestamp');
        let companyId = +req.param('companyId', 0);

        sails.log.info('Fetch close call, id:', ccId, "Requesting company:", companyId);

        let whereClause = {id: ccId, updatedAt: updatedAt};
        return await prepareDataToDownload(req, res, whereClause, companyId);
    },

    downloadCloseCallV1: async (req, res) => {
        let ccId = +req.param('ccId');
        let companyId = +req.body.companyId || 0;
        let createdAt = +req.body.createdAt;
        sails.log.info('Fetch close call, id:', ccId, "Requesting company:", companyId);

        let whereClause = {id: ccId, createdAt: createdAt};
        return await prepareDataToDownload(req, res, whereClause, companyId);
    },

    // @deprecated: @spatel: deprecated since 13th March 2023, in favor of `getUserCloseCallList`
    // still might be in use on ionic mobile app side
    getUserCloseCalls: async (req, res) => {
        let userId = +req.param('userId');
        let projectId = +req.param('projectId');
        let assignTo = (req.query.user_type || '').toString().trim() === 'assign_to';

        let filter;
        if (userId) {
            filter = (assignTo) ? {assigned_to:userId} : {user_ref:userId};
        } else {
            return ResponseService.errorResponse(res, 'user id is required');
        }

        if (projectId) {
            filter.project_ref = projectId;
        }

        try {
            var userCloseCalls = await sails.models.closecall.find(filter)
                .sort([
                    {status: 'ASC'},
                    {id: 'DESC'}
                ])
                .populate('tagged_owner');
            userCloseCalls = await populateProjectRefs(userCloseCalls, 'project_ref', []);
            userCloseCalls = await populateUserRefs(userCloseCalls, 'user_ref', []);
            userCloseCalls = await populateUserRefs(userCloseCalls, 'assigned_to', ['first_name', 'last_name', 'email']);
        } catch (e) {
            sails.log.info('Failed to fetch all close call ?', e);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, e);
        }

        userCloseCalls = await expandProjectClosecalls(userCloseCalls, false);
        return ResponseService.successResponse(res, {project_close_calls: userCloseCalls});
    },

    projectCloseCallDashboard: async(req, res) => {
        let projectId = +req.param('projectId');
        let companyId = +req.param('companyId', 0);
        let type = req.param('type');
        let totalPages = 1;
        let where = {
            project_ref: projectId,
        };

        if (req.body.from_date && req.body.to_date) {
            where.createdAt = {'>=': +req.body.from_date, '<=': +req.body.to_date};
        }

        sails.log.info('Fetch close calls with filter: ', where, "Requesting company:", companyId);
        let closeCalls = await sails.models.closecall.find(where)
            .sort([
                {id: 'ASC'}
            ]);
        closeCalls = await populateProjectRefs(closeCalls, 'project_ref', []);
        closeCalls = await populateUserRefs(closeCalls, 'user_ref', []);
        closeCalls = await populateUserRefs(closeCalls, 'assigned_to', ['first_name', 'last_name', 'email']);

        let projectInfo = await sails.models.project.findOne({
            where: {id: projectId},
            select: ['name', 'project_number', 'project_category', 'parent_company', 'client', 'contractor', 'project_type', 'custom_field', 'createdAt']
        });

        let ccAlternetPhrase = (projectInfo.custom_field.cc_phrase) ? projectInfo.custom_field.cc_phrase : `Close Call`;

        if (closeCalls.length) {
            let { project_logo_file, companyName } = await getCompanyInfo(projectInfo, (companyId ? {id: companyId} : null));

            //form close call/day count
            let daysBetweenDateRange = dayjs(+req.body.to_date).diff(dayjs(+req.body.from_date), 'days');
            let closeCallsPerDay = parseFloat(closeCalls.length/daysBetweenDateRange).toFixed(2);

            //Open & Closed Chart
            let openCloseCalls = (closeCalls || []).filter(cc => cc.status == 1);
            sails.log.info(`openCloseCall: ${openCloseCalls.length}`);

            let openClosedChartData = [
                { name: '   Open', value: openCloseCalls.length, type: 'Rating: Poor' },
                { name: 'Closed', value: closeCalls.length - openCloseCalls.length, type: '' }];
            let openClosedChartWidth = 750;
            let openClosedChartHeight = 10;
            let openClosedLabelFontSize = '.4em';
            let hasAllClosedOut = !(openCloseCalls.length) ? `All ${closeCalls.length} items closed out` : '';

            let openClosedAdjustments = {
                adj1: -2,
                adj2: 10,
                adj3: -14,
                adj4: 6,
                adj5: -2.5,
                adj6: .5,
                adj7: 18,
                adj8: (hasAllClosedOut) ? 5.2 : 4.7,
                adj9: (hasAllClosedOut) ? 8 : 17,
                adj10: (hasAllClosedOut) ? 1 : 0,
                adj11: -4.5,
                adj12: -3,
                adj13: 1.5,
            };
            let openClosedMarginTop = '0px';

            //Top 10 Close Call Chart
            let top10CloseCallChartWidth = 472;
            let top10CloseCallChartHeight = 250;
            let  { top10CloseCallChartData, top10CloseCallChartMax } = getTop10CloseCallChartData(closeCalls);

            //Weekly Close Calls Chart
            let weeklyCloseCallsChartWidth = 472;
            let weeklyCloseCallsChartHeight = 250;
            let { weeklyCloseCallsChartData, weeklyCloseCallsChartXAxisValues, weeklyCloseCallsChartMax } = getWeeklyCloseCallChartData(+req.body.from_date, +req.body.to_date, closeCalls);

            //Contractor Ratings Chart
            let contractorRatingsChartWidth = 472;
            let contractorRatingsChartHeight = 250;
            let contractorXAxisLabelAdj = 0;
            let lineChartAdj = 25;
            let contractorRatingsChartLegendAdj = 252;
            let { contractorRatingsBarChartData, contractorRatingsLineChartData, contractorRatingsBarChartMax } = await getContractorChartData(closeCalls, 0);

            //sails.log.info('contractorRatingsBarChartData: ', contractorRatingsBarChartData);
            //sails.log.info('contractorRatingsLineChartData: ', contractorRatingsLineChartData);

            //Average Closeout Time Chart
            let averageCloseoutTimeChartData = prepareWeeksData(+req.body.from_date, +req.body.to_date);
            for (let index in closeCalls) {
                let closeCall= closeCalls[index];
                let weeklyCloseCall = averageCloseoutTimeChartData.find(item => (+closeCall.createdAt >= item.start_of_week && +closeCall.createdAt <= item.end_of_week));
                let weeklyCloseCallIndex = averageCloseoutTimeChartData.findIndex(item => (closeCall.createdAt >= item.start_of_week && closeCall.createdAt <= item.end_of_week ));
                if (weeklyCloseCall && weeklyCloseCallIndex != -1 && closeCall.status == 2) {
                    weeklyCloseCall['closedout_close_calls'].push(closeCall);
                    averageCloseoutTimeChartData[weeklyCloseCall] = weeklyCloseCall;
                }
            }

            let averageCloseoutTimeChartWidth = 472;
            let averageCloseoutTimeChartHeight = 250;
            let averageCloseoutTimeChartMax = 0;
            averageCloseoutTimeChartData = averageCloseoutTimeChartData.map(item => {
                let tempArr = [];
                if (item.closedout_close_calls && item.closedout_close_calls.length) {
                   for (let index in item.closedout_close_calls) {
                       let cc = item.closedout_close_calls[index];
                       let day = ((+cc.closed_out_date - +cc.createdAt) / (60*60*1000)) / 24; //(hours)/24
                       tempArr.push(day);
                   }
               }
               item.average_closedout_time = (tempArr.length) ? (tempArr.reduce(function(a, b){return a + b;}, 0))/tempArr.length : 0;
               averageCloseoutTimeChartMax = (averageCloseoutTimeChartMax < item.average_closedout_time) ? item.average_closedout_time : averageCloseoutTimeChartMax;
               return item;
            });

            //removed week with 0 values
            let averageCloseoutTimeChartXAxisValues = [''];
            averageCloseoutTimeChartData = averageCloseoutTimeChartData.reduce((arr, item) => {
                if (item.average_closedout_time) {
                    averageCloseoutTimeChartXAxisValues.push(item['week']);
                    item.week = arr.length + 1;
                    arr.push(item);
                }
                return arr;
            }, []);

            //change setting for pdf
            if(type == 'pdf') {
                openClosedChartWidth = 1000;
                openClosedChartHeight = 7;
                openClosedAdjustments = {
                    adj1: -2,
                    adj2: 7,
                    adj3: -10,
                    adj4: 6,
                    adj5: -2.5,
                    adj6: .5,
                    adj7: 18,
                    adj8: 6,
                    adj9: 14,
                    adj10:0,
                    adj11: -4.5,
                    adj12: -3,
                    adj13: 1.5,
                };

                top10CloseCallChartWidth = 700;
                top10CloseCallChartHeight = 265;

                weeklyCloseCallsChartWidth = 700;
                weeklyCloseCallsChartHeight = 265;

                contractorRatingsChartWidth = 700;
                contractorRatingsChartHeight = 280;
                contractorXAxisLabelAdj = -60;
                lineChartAdj = 40;
                contractorRatingsChartLegendAdj = 400;

                averageCloseoutTimeChartWidth = 700;
                averageCloseoutTimeChartHeight = 280;
            }

            let form_template = `pages/close-call-dashboard-page`;
            let html = await sails.renderView(form_template, {
                title: `${ccAlternetPhrase}`,
                report_from: (+req.body.from_date) ? dayjs(+req.body.from_date).format('DD/MM/YYYY'): '',
                report_to: (+req.body.to_date) ? dayjs(+req.body.to_date).format('DD/MM/YYYY'): '',
                company_name: companyName,
                project_number: projectInfo.project_number,
                project_name: projectInfo.name,
                project_logo_file,
                report_type: type,
                total_close_call: closeCalls.length,
                open_cc_count: openCloseCalls.length,
                closed_cc_count: closeCalls.length - openCloseCalls.length,
                closeCallsPerDay,
                openClosedBarChart: await getOpenClosedBarChart(openClosedChartData, hasAllClosedOut, openClosedChartWidth, openClosedChartHeight, openClosedLabelFontSize, openClosedMarginTop, openClosedAdjustments, ["#D60707", "#1FA61B"], []),
                top10CloseCallChart: await getStackedBarChart(top10CloseCallChartData, ['group', 'Count'], top10CloseCallChartMax, top10CloseCallChartWidth, top10CloseCallChartHeight, ["#4472C4"], '7px','Number Raised', false, false, 0),
                weeklyCloseCallChart: await getScatterPlot(weeklyCloseCallsChartData, 'week', weeklyCloseCallsChartXAxisValues, ['total_close_call'], weeklyCloseCallsChartMax, weeklyCloseCallsChartWidth, weeklyCloseCallsChartHeight, '7px', 'Week', `Number Raised`, 0, false, ["#ffc36a"], false, ""),
                contractorRatingsChart: await getStackedBarWithLineChart({
                    data: contractorRatingsBarChartData,
                    columns: [ 'group', 'Rating' ],
                    lineChartData: contractorRatingsLineChartData,
                    lineChartItemName: 'company',
                    lineChartAllGroup: [ 'Rating' ],
                    lineChartAdj,
                    maxRating: contractorRatingsBarChartMax,
                    width: contractorRatingsChartWidth,
                    height: contractorRatingsChartHeight,
                    colors: ['#D0CECE'],
                    axisFontSize: '7px',
                    yAxisLabel: `Number Raised`,
                    xAxisLabel: "Contractor",
                    xAxisLabelAdj: contractorXAxisLabelAdj,
                    legendData: [{key: 'Raised by', color: "#D0CECE"}, {key: 'Owner', color: "#ffc36a"}],
                    legendsAdj: contractorRatingsChartLegendAdj
                }),
                averageCloseoutTimeChart: await getScatterPlot(averageCloseoutTimeChartData, 'week', averageCloseoutTimeChartXAxisValues, ['average_closedout_time'], averageCloseoutTimeChartMax, averageCloseoutTimeChartWidth, averageCloseoutTimeChartHeight, '7px', 'Week', 'Days to Closeout', 0, false, ["#ffc36a"], true, ""),
                totalPages,
                layout: false,
            });
            if (type === 'pdf') {
                let fileName = `${ccAlternetPhrase}-Dashboard-${dayjs().format('MM-DD-YYYY')}`;
                return await instantPdfGenerator(req, res, html, 'close-call-p-dashboard', fileName, req.headers['user-agent'], { format: 'A3', landscape: true }, 'url');
            }

            sails.log.info('Rendering html view');
            return res.send(html);
        }
        sails.log.info('No close calls found in selected criteria: ', where);
        return res.send(`<p style='text-align: center;margin-top: 100px;margin-bottom: auto;'>No ${ccAlternetPhrase} found in selected criteria.</p>`);
    },

    companyCloseCallDashboard: async (req, res) => {
        let companyId = +req.param('companyId');
        let type = req.param('type');
        let totalPages = 2;
        let where = {
            project_ref: (req.body.selected_projects || []),
        };
        let cc_phrase = req.body.cc_phrase;

        if (req.body.from_date && req.body.to_date) {
            where.createdAt = {'>=': +req.body.from_date, '<=': +req.body.to_date};
        }

        sails.log.info('Fetch close call filter.', where, "Requesting company:", companyId);

        let closeCalls = await sails.models.closecall.find({
            where,
            select: ['status', 'closed_out_date', 'cc_number', 'hazard_category', 'createdAt', 'user_ref', 'project_ref', 'tagged_owner']
        })
            .sort([
                {id: 'ASC'}
            ]);

        closeCalls = await populateProjectRefs(closeCalls, 'project_ref', []);
        closeCalls = await populateUserRefs(closeCalls, 'user_ref', []);
        closeCalls = await populateUserRefs(closeCalls, 'assigned_to', ['first_name', 'last_name', 'email']);

        sails.log.info(`${closeCalls.length} close calls found.`);

        if (closeCalls.length) {
            let {project_logo_file, companyName} = await getCompanyInfo({}, {id: companyId});

            //form close call/day count
            let daysBetweenDateRange = dayjs(+req.body.to_date).diff(dayjs(+req.body.from_date), 'days');
            let closeCallsPerDay = parseFloat(closeCalls.length/daysBetweenDateRange).toFixed(2);

            //Close Calls / Hours Worked
            let projectIds = (closeCalls || []).reduce((arr, item) => {
                arr.push(item.project_ref.id);
                return arr;
            }, []);
            let daily_logs = await getDailyTimeEventV2(
                _uniq(projectIds),
                dayjs(req.body.from_date).format('YYYY-MM-DD'),
                dayjs(req.body.to_date).format('YYYY-MM-DD')
            );

            let totalWorkedHours = (daily_logs || []).reduce((hours, item) => {
                hours += (item.duration_in_sec) ? (+item.duration_in_sec / (60*60)) : 0;
                return hours;
            }, 0);

            let closeCallsPerHourWorked = 0;
            if (totalWorkedHours) {
                closeCallsPerHourWorked = closeCalls.length / totalWorkedHours;
            }

            //Open & Closed Chart
            let openCloseCalls = (closeCalls || []).filter(cc => cc.status == 1);
            sails.log.info(`openCloseCall: ${openCloseCalls.length}`);

            let openClosedChartData = [
                { name: '   Open', value: openCloseCalls.length, type: 'Rating: Poor' },
                { name: 'Closed', value: closeCalls.length - openCloseCalls.length, type: '' }];
            let openClosedChartWidth = 750;
            let openClosedChartHeight = 10;
            let openClosedLabelFontSize = '.4em';
            let hasAllClosedOut = !(openCloseCalls.length) ? `All ${closeCalls.length} items closed out` : '';
            let openClosedAdjustments = {
                adj1: -2,
                adj2: 10,
                adj3: -14,
                adj4: 6,
                adj5: -2.5,
                adj6: .5,
                adj7: 18,
                adj8: (hasAllClosedOut) ? 5.2 : 4.7,
                adj9: (hasAllClosedOut) ? 8 : 17,
                adj10: (hasAllClosedOut) ? 1 : 0,
                adj11: -4.5,
                adj12: -3,
                adj13: 1.5,
            };
            let openClosedMarginTop = '0px';

            //Top 5 most active projects
            let mostActiveProjectsChartWidth = 550;
            let mostActiveProjectsChartHeight = 340;
            let centroidAdj = -40;
            let totalCCInProjects = 0;
            let closeCallsGroupedByProject = _groupBy(closeCalls, (l) => l.project_ref.name);
            closeCallsGroupedByProject = (Object.entries(closeCallsGroupedByProject).sort((a,b) => b[1].length-a[1].length)).slice(0, 5);
            let mostActiveProjectsChartData = [];
            let colors = ['#4472C4', '#B3C6E7', '#D0CECE', '#A5A5A5', '#7F7F7F'];
            let mostActiveProjects = [];
            for (let key in closeCallsGroupedByProject) {
                if (closeCallsGroupedByProject[key][0] && closeCallsGroupedByProject[key][1] && closeCallsGroupedByProject[key][1].length) {
                    mostActiveProjectsChartData.push({
                        name: `${closeCallsGroupedByProject[key][0]} (${closeCallsGroupedByProject[key][1].length})`,
                        label: `${closeCallsGroupedByProject[key][0]}`,
                        count: `${closeCallsGroupedByProject[key][1].length}`,
                        color: colors[key]
                    });
                    totalCCInProjects += closeCallsGroupedByProject[key][1].length;
                    mostActiveProjects.push(closeCallsGroupedByProject[key][0]);
                }
            }
            let mostActiveProjectsChartDataObj = {
                data: mostActiveProjectsChartData,
                total_count: totalCCInProjects,
                columns: mostActiveProjects,
                pie_colors: colors
            };

            //sails.log.info(mostActiveProjectsChartDataObj);

            //Close Call per project Chart
            let closeCallPerProjectChartData = [];
            let closeCallPerProjectChartMax = 1;
            let closeCallPerProjectChartWidth = 472;
            let closeCallPerProjectChartHeight = 250;
            for (let index in closeCalls) {
                let closeCall = closeCalls[index];
                let existingItem = closeCallPerProjectChartData.find(item => (item.group == closeCall.project_ref.name));
                let existingItemIndex = closeCallPerProjectChartData.findIndex(item => (item.group == closeCall.project_ref.name));
                if (existingItem && existingItemIndex != -1) {
                    existingItem.Count = existingItem.Count + 1;
                    closeCallPerProjectChartData[existingItemIndex] = existingItem;

                    closeCallPerProjectChartMax = (closeCallPerProjectChartMax < existingItem.Count) ? existingItem.Count : closeCallPerProjectChartMax
                } else {
                    closeCallPerProjectChartData.push({
                        group: closeCall.project_ref.name,
                        Count: 1
                    });
                }
            }

            //Top 10 close calls Chart
            let top10CloseCallChartWidth = 472;
            let top10CloseCallChartHeight = 250;
            let { top10CloseCallChartData, top10CloseCallChartMax } = getTop10CloseCallChartData(closeCalls);

            //Contractor Ratings Chart
            let contractorRatingsChartWidth = 472;
            let contractorRatingsChartHeight = 260;
            let contractorXAxisLabelAdj = 0;
            let lineChartAdj = 25;
            let contractorRatingsChartLegendAdj = 252;
            let { contractorRatingsBarChartData, contractorRatingsLineChartData, contractorRatingsBarChartMax } = await getContractorChartData(closeCalls, 20);

            //Average closeout time chart
            let avgCloseoutTimePerProjectData = [];
            let avgCloseoutTimePerProjectWidth = 1120;
            let avgCloseoutTimePerProjectHeight = 400;
            let projectsGroup = [];
            for (let index in closeCalls) {
                let closeCall = closeCalls[index];
                if (projectsGroup.indexOf(closeCall.project_ref.name) == -1) {
                    projectsGroup.push(closeCall.project_ref.name);
                }

                if(closeCall.closed_out_date) {
                    let days = ((+closeCall.closed_out_date - +closeCall.createdAt) / (60 * 60 * 1000)) / 24; //(hours)/24
                    let existingRecord = avgCloseoutTimePerProjectData.find(record => record.group === closeCall.project_ref.name);
                    let existingRecordIndex = avgCloseoutTimePerProjectData.findIndex(record => record.group === closeCall.project_ref.name);
                    if (existingRecord && existingRecordIndex != -1) {
                        existingRecord["rating"].push(days);
                        avgCloseoutTimePerProjectData[existingRecordIndex] = existingRecord;
                    } else {
                        avgCloseoutTimePerProjectData.push({
                            "group": closeCall.project_ref.name,
                            "rating": [days]
                        })
                    }
                }
            }

            let maxCloseoutTime = 0;
            avgCloseoutTimePerProjectData = (avgCloseoutTimePerProjectData || []).reduce((arr, item) => {
                let ratCount = item['rating'].reduce((a, b) => a + b, 0)/item['rating'].length;
                if (ratCount) {
                    item['rating'] = ratCount;
                    maxCloseoutTime = (maxCloseoutTime < item['rating']) ? item['rating'] : maxCloseoutTime;
                    arr.push(item);
                }
                return arr;
            }, []);

            sails.log.info("maxCloseoutTime real ", maxCloseoutTime, "maxCloseoutTime ceiled ", Math.ceil(maxCloseoutTime));

            //Weekly close calls chart
            let weeklyCloseCallsChartWidth = 1040;
            let weeklyCloseCallsChartHeight = 320;
            let { weeklyCloseCallsChartData, weeklyCloseCallsChartXAxisValues, weeklyCloseCallsChartMax } = getWeeklyCloseCallChartData(+req.body.from_date, +req.body.to_date, closeCalls);

            //change setting for pdf
            let tbdChartWidth = '550px';
            let tbdChartHeight = '365px';
            if(type == 'pdf') {
                openClosedChartWidth = 1000;
                openClosedChartHeight = 7;
                openClosedMarginTop = '0px';
                openClosedAdjustments = {
                    adj1: -2,
                    adj2: 7,
                    adj3: -10,
                    adj4: 6,
                    adj5: -2,
                    adj6: 1,
                    adj7: 18,
                    adj8: 6.7,
                    adj9: 14,
                    adj10:0,
                    adj11: -4.5,
                    adj12: -3,
                    adj13: 1.5,
                };

                mostActiveProjectsChartWidth = 750;
                mostActiveProjectsChartHeight = 360;
                centroidAdj = -80;

                closeCallPerProjectChartWidth = 770;
                closeCallPerProjectChartHeight = 260;

                top10CloseCallChartWidth = 700;
                top10CloseCallChartHeight = 260;

                contractorRatingsChartWidth = 700;
                contractorRatingsChartHeight = 260;
                contractorXAxisLabelAdj = -100;
                lineChartAdj = 35;
                contractorRatingsChartLegendAdj = 322;

                avgCloseoutTimePerProjectWidth = 1500;
                avgCloseoutTimePerProjectHeight = 460;

                weeklyCloseCallsChartWidth = 1450;
                weeklyCloseCallsChartHeight = 370;

                tbdChartWidth = '700px';
                tbdChartHeight = '400px';
            }

            let contractorChartMax = 0;
            let contractorChartXAxisValues = [''];
            contractorRatingsLineChartData = (contractorRatingsLineChartData || []).map((item, index) => {
                contractorChartXAxisValues.push(item.company);
                contractorChartMax = (contractorChartMax < item.Rating) ? item.Rating : contractorChartMax;
                item.company = index + 1;
                return item;
            });

            let form_template = `pages/close-call-company-dashboard-page`;
            let html = await sails.renderView(form_template, {
                title: `${cc_phrase}`,
                layout: false,
                report_from: (+req.body.from_date) ? dayjs(+req.body.from_date).format('DD/MM/YYYY') : '',
                report_to: (+req.body.to_date) ? dayjs(+req.body.to_date).format('DD/MM/YYYY') : '',
                company_name: companyName,
                company_logo: project_logo_file,
                report_type: type,
                totalPages,
                total_close_call: closeCalls.length,
                closeCallsPerDay,
                closeCallsPerHourWorked: (closeCallsPerHourWorked) ? parseFloat(closeCallsPerHourWorked).toFixed(2) : 0,
                openClosedBarChart: await getOpenClosedBarChart(openClosedChartData, hasAllClosedOut, openClosedChartWidth, openClosedChartHeight, openClosedLabelFontSize, openClosedMarginTop, openClosedAdjustments, ["#D60707", "#1FA61B"], []),
                mostActiveProjectsChart: await getDonutChartV2(mostActiveProjectsChartDataObj, mostActiveProjectsChartWidth, mostActiveProjectsChartHeight, centroidAdj, 100, 50, "50 -42 290 235", -35),
                closeCallPerProjectChart: await getStackedBarChart(closeCallPerProjectChartData, ['group', 'Count'], closeCallPerProjectChartMax, closeCallPerProjectChartWidth, closeCallPerProjectChartHeight, ["#B3C6E7"], '7px','Number Raised', false, false, 0),
                top10CloseCallChart: await getStackedBarChart(top10CloseCallChartData, ['group', 'Count'], top10CloseCallChartMax, top10CloseCallChartWidth, top10CloseCallChartHeight, ["#4472C4"], '7px','Number Raised', false, false, 0),
                /*contractorRatingsChart: await getStackedBarWithLineChart({
                    data: contractorRatingsBarChartData,
                    columns: [ 'group', 'Rating' ],
                    lineChartData: contractorRatingsLineChartData,
                    lineChartItemName: 'company',
                    lineChartAllGroup: [ 'Rating' ],
                    lineChartAdj,
                    maxRating: contractorRatingsBarChartMax,
                    width: contractorRatingsChartWidth,
                    height: contractorRatingsChartHeight,
                    colors: ['#D0CECE'],
                    axisFontSize: '7px',
                    yAxisLabel: `Number Raised`,
                    xAxisLabel: "Contractor/Owner",
                    xAxisLabelAdj: contractorXAxisLabelAdj,
                    legendData: [{key: 'Owner', color: "#ffc36a"}],
                    legendsAdj: contractorRatingsChartLegendAdj
                }),*/
                contractorRatingsChart: await getScatterPlot(contractorRatingsLineChartData, 'company', contractorChartXAxisValues, ['Rating'], contractorChartMax, contractorRatingsChartWidth, contractorRatingsChartHeight, '7px', '', `Number Raised`, contractorRatingsChartLegendAdj, false, ["#ffc36a"], false, ""),
                avgCloseoutTimePerProject: await getHoriLollipopChart(projectsGroup, avgCloseoutTimePerProjectData, [], Math.ceil(maxCloseoutTime), avgCloseoutTimePerProjectWidth, avgCloseoutTimePerProjectHeight,"Days to closeout", '7px', [], 0),
                weeklyCloseCallChart: await getScatterPlot(weeklyCloseCallsChartData, 'week', weeklyCloseCallsChartXAxisValues, ['total_close_call'], weeklyCloseCallsChartMax, weeklyCloseCallsChartWidth, weeklyCloseCallsChartHeight, '7px', 'Week', `Number Raised`, 0, false, ["#ffc36a"], false, ""),
                tbdChartWidth,
                tbdChartHeight,
                layout: false,
            });
            if (type === 'pdf') {
                let fileName = `${cc_phrase}-Dashboard-${dayjs().format('MM-DD-YYYY')}`;
                return await instantPdfGenerator(req, res, html, 'close-call-c-dashboard', fileName, req.headers['user-agent'], { format: 'A3', landscape: true }, 'url');
            }

            sails.log.info('Rendering html view');
            return res.send(html);
        }

        sails.log.info('No close calls found in selected criteria: ', where);
        return res.send(`<p style='text-align: center;margin-top: 100px;margin-bottom: auto;'>No ${cc_phrase} found in selected criteria.</p>`);

    },

    downloadCloseCallReport: async (req, res) => {
        let projectId = +req.param('projectId');
        let fromDate = +(req.body.fromDate);
        let toDate = +(req.body.toDate);
        let openStatus = req.body.open_status;
        let closedStatus = req.body.closed_status;
        let hazardCategory = req.body.hazard_category;
        let taggedOwner = req.body.tagged_owner;
        let employer = req.body.employer;
        let isSiteAdmin = req.body.is_site_admin

        if (!fromDate || !toDate) {
            return ResponseService.errorResponse(res, 'from date and to date are required.');
        }

        let filter;
        if (projectId) {
            filter = { project_ref: projectId };
        }

        if (employer == 'Anonymous') {
            filter.is_anonymous = true;
        }

        if (hazardCategory) {
            filter.hazard_category = hazardCategory;
        }

        if (+openStatus || +closedStatus) {
            let status = [];
            if (+openStatus) {
                status.push(openStatus)
            }

            if (+closedStatus) {
                status.push(closedStatus)
            }
            filter.status = status;
        }

        if (+taggedOwner) {
            filter.tagged_owner = +taggedOwner;
        }
        filter.createdAt = {'>=': fromDate, '<=': toDate};

        sails.log.info('fetch all close calls with filter:', filter);
        let closeCalls = await sails.models.closecall.find(filter)
            .sort([
                {status: 'ASC'},
                {id: 'DESC'}
            ])
            .populate('tagged_owner');
        sails.log.info(`Found ${closeCalls.length} Close Calls.`);
        closeCalls = await populateUserRefs(closeCalls, 'user_ref', []);
        closeCalls = await populateUserRefs(closeCalls, 'assigned_to', ['first_name', 'last_name', 'email']);

        closeCalls = await expandProjectClosecalls(closeCalls, true);
        if (employer && employer != 'Anonymous') {
            closeCalls = closeCalls.filter(t=> !t.is_anonymous && t.user_employer && t.user_employer.employer && t.user_employer.employer === employer);
        }

        let projectInfo = await sails.models.project_reader.findOne({where: {id: projectId}, select: ['name', 'project_number', 'custom_field','project_category','parent_company','closecall_setting', 'contractor']});

        let workbook = await closeCallReport(closeCalls, projectInfo, req.user.timezone, isSiteAdmin);
        let fileName = `${projectInfo.custom_field.cc_phrase_singlr}-Report-${dayjs().format('DD-MM-YYYY')}.xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },

    getUserCloseCallList: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let pageSize = +req.param('pageSize', 50);
        let pageNumber = +req.param('pageNumber', 0);
        let sortKey = req.param('sortKey', 'status');
        let sortDir = req.param('sortDir', 'asc');
        let category = req.param('category', null);
        let searchTerm = (req.param('q', '')).toString().trim();
        let statusCodes = (req.param('statusCodes', '')).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
        let onlyAssignToMe = req.param('assignTo', '').toString().trim() === 'me';

        let defaultResponse = {
            close_calls: [],
            totalCount: 0,
            q: searchTerm,
            pageSize,
            pageNumber,
            sortKey,
            sortDir,
            category,
            statusCodes,
            onlyAssignToMe,
        };
        let {
            total: totalCount,
            records: close_calls
        } = await closeCallFn.getUserCloseCallsPage(projectId, pageSize, (pageSize * pageNumber), sortKey, sortDir, {
            searchTerm, statusCodes, category,
            ...(onlyAssignToMe ? {
                assignedTo: req.user.id
            } : {
                userId: req.user.id
            }),
        });
        if (!close_calls.length) {
            sails.log.info(`No record found, for projectId: ${projectId}, q: "${searchTerm}" pageNumber: ${pageNumber} pageSize: ${pageSize} totalCount: ${totalCount}`);
            return successResponse(res, {...defaultResponse, totalCount});
        }

        let {cc_images, corrective_images} = await closeCallFn.expandCCFiles(close_calls.map(cc => cc.id));

        for (let i = 0; i < close_calls.length; i++) {
            let cc_images_row = (cc_images || []).find(row => row.id === close_calls[i].id) || {};
            let corrective_images_row = (corrective_images || []).find(row => row.id === close_calls[i].id) || {};

            close_calls[i].cc_images = cc_images_row.cc_images;
            close_calls[i].corrective_images = corrective_images_row.corrective_images;
            close_calls[i].status_message = buildCloseCallStatus(close_calls[i].status);
        }

        return successResponse(res, {
            ...defaultResponse,
            close_calls,
            totalCount,
        });
    },
};
