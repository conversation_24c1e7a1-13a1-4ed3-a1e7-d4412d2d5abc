/**
 * Created by spatel on 24/9/18.
 */
'use strict';

const DEFAULT_PROJECT_CATEGORY = 'default';
const dbDateFormat = 'YYYY-MM-DD';
const moment = require('moment');
const dayjs = require('dayjs');
const momentTz = require('moment-timezone');
const _uniq = require('lodash/uniq');
const _groupBy = require('lodash/groupBy');
const {
    COMPANY_SETTING_KEY,
    displayDateFormat_DD_MM_YYYY,
    dbDateFormat_slash_DDMMYYYY,
    fall_back_timezone,
    dbDateFormat_YYYY_MM_DD,
    EMAIL_NOTIFICATION_FEATURE_CODES,
    PROJECT_SETTING_KEY: {INDUCTION_SLOTS_INFO, MANDATORY_COMPETENCY_EXCEPTION_USERS, PROCORE_INTEGRATION_PROJECT_INFO, A_SITE_PROJECT_CONFIG},
    INNDEX_SLS_ARN: { CONSTANT_SYNC_DAILY_BRIEFINGS_TO_ASITE, CONSTANT_LAMBDA_TEST_EMAIL_FN },
    FEATURES: { PERMIT_REGISTER }
} = sails.config.constants;
const {
    AccessLogService,
    DataToSeedService:{
        defaultDataForCloseCallCustomField
    },
    PdfUtil: {
        getPDFPageSizes,
    },
    RightToWorkService: {
        extractRtwStatus,
    },
    SmartSheetService: {
        enableSmartSheetForProject,
    },
    ExcelService: {
        streamExcelDownload,
        exportProjectResourcePlans,
    },
    TimeLogService: {
        EVENT_TYPE,
        VALID_SOURCES,
    },
    DataProcessingService: {
        isStillOnSite, eventOfToday, getDailyTimeEventV2, getDailyTimeEventForDay,
        getUserFullName,
        getActiveTravelTime,
        getTotalTravelDuration,
        populateInductionEmptySlots,
        expandUsersByIds,
        getMandatoryCompetencyExceptionList,
        attachProfilePicWithUsersInfo,
        populateUserRefs,
        getVisitorsTimeLogForDates,
        getProjectTimezone,
        populateToolsMappingCompany,
        deriveOnSiteUsersLocation,
        prepareRollCallUsersInfo,
    },
    TokenUtil: {
        getCompanyInfo,
        allProjectAdminsByOneOfDesignations,
        validateCPAPermissions,
        getAllowedDefaultProjectsOfUser,
        deserializeUAC,
        getAllProjectUsers,
        saveProjectUsers,
        ROLES,
        UACCompanyDesignations,
        resourceIdentifier,
        hasOnlySiteManagementAccess,
    },
    ResponseService: {
        sendResponse,
        errorResponse,
        successResponse
    },
    HttpService: {
        typeOf,
        executeInParallelLimit
    },
    SharedService: {
        rectifyDateFormat,
        instantPdfGenerator,
        downloadPdfViaGenerator,
        scheduleEventBridgeCron,
        deleteEventBridgeScheduleCron,
        scheduleEvent,
    },
    EmailService: {
        queueEmailNotifications,
        sendRawEmail,
    },
    WeatherSyncService: {
        attachLocationKey,
        postcodeToLatLong
    },
    RekognitoService: {
        triggerCreateCollection
    }
} = require('./../services');
const {
    inductionFn: {
        getProjectInductions,
        getInductionEmployerByUserIds,
        getInductionUserEmployers,
    },
    deliveryManagementFn
} = require('./../sql.fn');

const {
    ProjectValidator,
} = require('./../validators');
const EmailService = require('./../services/EmailService');
const ResponseService = require('./../services/ResponseService');
const QRCode = require('qrcode');
const PdfUtil = require('./../services/PdfUtil');
const SharedService = require('./../services/SharedService');

const isEnabledForProject = (inspection, projectId) => {
    let projectEntry = (inspection.activate_on_projects || []).find(item => item.project_id == projectId);
    return (!projectEntry || projectEntry.enabled);
}

const storeMandatoryCompetencyExceptionList = async (project_ref, list = []) => {
    let cleanListOfIds = list.map(row => (row && row.id) || row).filter(id => (+id));
    sails.log.info(`Storing mandatory competency exception list, project: ${project_ref}, user ids: ${cleanListOfIds}`);

    let existingSetting = await sails.models.projectsetting.findOne({
        project_ref,
        name: MANDATORY_COMPETENCY_EXCEPTION_USERS,
    });
    if (existingSetting && existingSetting.id) {
        sails.log.info('existing project setting found, ref:', existingSetting.id);
        existingSetting = await sails.models.projectsetting.updateOne({id: existingSetting.id}).set({value: cleanListOfIds});
    } else if (cleanListOfIds.length) {
        sails.log.info('creating new project setting record');
        existingSetting = await sails.models.projectsetting.create({
            project_ref,
            name: MANDATORY_COMPETENCY_EXCEPTION_USERS,
            value: cleanListOfIds,
        });
    } else {
        sails.log.info('List is empty, skipping store setting call')
    }

    return (existingSetting && existingSetting.value) || [];
};

let addProjectUsers = (project, project_users, created_by, cb) => {

    (async () => {
        let {newly_added_users} = await saveProjectUsers(project, project_users);
        if (newly_added_users.length) {
            let userIds = newly_added_users.map(pu => pu.user_ref);
            sails.log.info('newly added users are:', userIds);
            let userEmails = await sails.models.user_reader.find({where: {id: userIds}, select: ['email']});
            await sendProjectAccessAlert(project, userEmails.map(u => u.email));
        }

        return cb(null, { newly_added_users});

    })().catch(er => {
        sails.log.info('Failed to notify new project SITE-ADMINs', er);
        return cb(er);
    });
};

const updateProjectCustomField = async (project_ref, updated_custom_fields = {}, project = null) => {
    if(!project){
        project = await sails.models.project_reader.findOne({
            where: {id: project_ref},
            select: ['custom_field']
        });
    }

    let custom_field = {
        ...(project.custom_field || {}),
        ...updated_custom_fields,
    };

    let projectInfo = await sails.models.project.updateOne({id: project_ref}).set({custom_field});
    sails.log.info(`project(${project_ref}) custom field has been updated`);
    return projectInfo;
};

let addProjectDeclarations = (project_id, declarations, cb) => {
    // we can't delete existing declaration mapping,
    // else it will affect user who as submitted induction form
    sails.models.projectdeclaration.find({project_id}).exec((findError, records) => {
        if (findError) {
            sails.log.error('Failed to find existing project declaration', findError);
            return cb(findError);
        }
        sails.log.info('found declaration records', records && records.length);
        // add new records
        // update existing ones
        // delete absent ones
        let newOnes = (declarations || []).filter(r => !r.id).map(r => {
            return {
                project_id,
                content: r.content
            }
        });

        let existingOnes = (declarations || []).filter(r => r.id).map(r => {
            return {
                id: r.id,
                //project_id,
                content: r.content,
            }
        });

        let declarationIds = (declarations || []).filter(r => r.id).map(r => r.id);
        let deletedOnes = (records || []).filter(row => declarationIds.indexOf(row.id) === -1).map(row => row.id);

        // Building a async block for cleaner code
        (async () => {
            try{
                let project_declarations_records = [];
                let deletedOneRecords = [];
                // new one
                if(newOnes.length){
                    sails.log.info('create project declarations, count', newOnes.length);
                    project_declarations_records = await sails.models.projectdeclaration.createEach(newOnes);
                    sails.log.info('Created project declarations');
                }

                // Deleted one
                if(deletedOnes.length){
                    sails.log.info('delete project declarations, count', deletedOnes.length);
                    deletedOneRecords = await sails.models.projectdeclaration.destroy({
                        project_id,
                        id: deletedOnes,
                    });
                    sails.log.info('Deleted project declarations');
                }

                // update each by id
                if(existingOnes.length){
                    sails.log.info('update project declarations, count', existingOnes.length);
                    for(let i = 0, len = existingOnes.length; i < len; i++) {
                        sails.log.info('Updating project declaration', existingOnes[i].id);
                        let updatedRow = await sails.models.projectdeclaration.updateOne({
                            id: existingOnes[i].id,
                            project_id
                        }).set({
                            content: existingOnes[i].content,
                        });
                    }
                }

                cb(null, {
                    project_declarations_records,
                    deletedOneRecords
                });
            }catch(e){
                sails.log.info('Failed to update project declarations', e);
                return cb(e);
            }
        })();
    });
};

let sendProjectAccessAlert = async (project, emails) => {

    try {
        sails.log.info('Access Alert Mail will be sent to', emails);
        //let sendToUsers = await sails.models.user.find({id: sendToIds, is_active: 1});
        for (let i = 0, len = emails.length; i < len; i++) {
            let toUser = emails[i];
            // #${induction_request.id}
            let subject = `Access granted for project: "${project.name}"`;
            let html = await sails.renderView('pages/mail/project-access-granted', {
                title: subject,
                project,
                layout: false
            });

            sails.log.info('Sending mail to', toUser);
            try{
                let sentStatus = await EmailService.sendMail(subject, [toUser], html);
            }catch(failure){
                sails.log.info('Failed to send mail', failure);
            }

        }

    } catch (e) {
        sails.log.info('Failed to send all alert ?', e);
    }
};

const validatePostCode = async (req, res) => {
    let {postcode, countryCode} = req.body;
    postcode = (postcode || '').trim();
    countryCode = (countryCode || '').trim();
    if (!postcode || !countryCode) {
        sails.log.info('Invalid Request', {postcode, countryCode});
        return errorResponse(res, 'postcode and countryCode is required.');
    }
    sails.log.info(`[validatePostCode]: postcode - "${postcode}", country code: "${countryCode}"`);
    let latLongData = await postcodeToLatLong(postcode, countryCode);
    return successResponse(res, {latLongData});
};

let storeProjectData = async (project, project_users_req, project_declarations_req, project_gates_req, currentUserEmail) => {
    return new Promise(function saveProjectDataPromise(resolve, reject) {
        addProjectUsers(project, project_users_req, project.created_by, (projectUsersError, project_users) => {
            if (projectUsersError) {
                return reject(projectUsersError);
            }
            addProjectDeclarations(project.id, project_declarations_req, (projectDeclarationError, projectDeclarations) => {
                if (projectDeclarationError) {
                    return reject(projectDeclarationError);
                }

                addProjectGates(project.id, project_gates_req, (projectGatesError, project_gates) => {
                    if (projectGatesError) {
                        return reject(projectGatesError);
                    }
                    return resolve({project});
                });
            });
        });
    });
};

let addProjectGates = (project_id, project_gates, cb) => {

    sails.log.info('found project_gates', project_gates.length);
    // we can't delete existing gate mapping,
    // else it will affect user who as submitted induction form
    sails.models.projectgate.find({project_id}).exec((findError, records) => {
        if (findError) {
            sails.log.error('Failed to find existing project gate', findError);
            return cb(findError);
        }
        sails.log.info('found gate records', records && records.length);
        // add new records
        // update existing ones
        // delete absent ones
        let newOnes = (project_gates || []).filter(r => !r.id).map(r => {
            return {
                project_id,
                gate_name: r.gate_name,
                time_slot: r.time_slot,
                route_map_file_id: r.route_map_file_id,
            }
        });

        let existingOnes = (project_gates || []).filter(r => r.id).map(r => {
            return {
                id: r.id,
                //project_id,
                gate_name: r.gate_name,
                time_slot: r.time_slot,
                route_map_file_id: r.route_map_file_id,
            }
        });

        let gateIds = (project_gates || []).filter(r => r.id).map(r => r.id);
        let deletedOnes = (records || []).filter(row => gateIds.indexOf(row.id) === -1).map(row => row.id);

        /*sails.log.info('Gates Update request says', {
            newOnes,
            existingOnes,
            deletedOnes
        });*/

        // Building a async block for cleaner code
        (async () => {
            try{
                let project_gates_records = [];
                let deletedOneRecords = [];
                // new one
                if(newOnes.length){
                    sails.log.info('create project gates, count', newOnes.length);
                    project_gates_records = await sails.models.projectgate.createEach(newOnes);
                    sails.log.info('created gates, count', project_gates_records.length);

                    //replicate same gates to selected projects if available
                    let gateSetting = (await sails.models.projectsetting.findOne({where: {project_ref: project_id, name: 'gate_booking_setting', value: {'!=': null}}, select: ['value']})) || {};
                    if (gateSetting && gateSetting.id && gateSetting.value.length) {
                        let projectIds = gateSetting.value;
                        sails.log.info('Replicating gate on following project: ', projectIds);

                        for (let i in project_gates_records) {
                            let projectGate = project_gates_records[i];
                            delete projectGate.id;
                            delete projectGate.createdAt;
                            delete projectGate.updatedAt;
                            for (let j in projectIds) {
                                projectGate.project_id = projectIds[j];
                                let payload = Object.assign({}, projectGate);
                                await sails.models.projectgate.create(payload);
                            }
                        }
                    }
                    sails.log.info('Created project gates');
                }

                // Deleted one
                if(deletedOnes.length){
                    sails.log.info('delete project gates, count', deletedOnes.length);
                    deletedOneRecords = await sails.models.projectgate.destroy({
                        project_id,
                        id: deletedOnes,
                    });

                    //Destroy replicated gates from selected projects if available
                    await destroyReplicatedGates(deletedOneRecords, project_id);

                    sails.log.info('Deleted project gate');
                }

                // update each by id
                if(existingOnes.length){
                    sails.log.info('update project gates, count', existingOnes.length);
                    let gateSetting = (await sails.models.projectsetting.findOne({where: {project_ref: project_id, name: 'gate_booking_setting', value: {'!=': null}}, select: ['value']})) || {};
                    for(let i = 0, len = existingOnes.length; i < len; i++) {
                        sails.log.info('Updating project gate', existingOnes[i].id);
                        let updatePayload = {
                            gate_name: existingOnes[i].gate_name,
                            time_slot: existingOnes[i].time_slot,
                            route_map_file_id: existingOnes[i].route_map_file_id,
                        };
                        let updatedRow = await sails.models.projectgate.updateOne({
                            id: existingOnes[i].id,
                            project_id
                        }).set(updatePayload);

                        //update gates based on identifier if gate setting available
                        if (gateSetting && gateSetting.id && gateSetting.value.length) {
                            let projectIds = gateSetting.value;
                            sails.log.info('Update replicated gate on following project: ', projectIds);

                            let replicatedGates = await sails.models.projectgate.find({
                                where: {
                                    identifier: updatedRow.identifier,
                                    project_id: projectIds
                                },
                                select: ['id']
                            });
                            if (replicatedGates.length) {
                                for (let j in replicatedGates) {
                                    let gate = replicatedGates[j];
                                    await sails.models.projectgate.updateOne({
                                        id: gate.id,
                                    }).set(updatePayload);
                                }
                            }
                        }
                    }
                }

                cb(null, {
                    project_gates_records,
                    deletedOneRecords
                });
            }catch(e){
                sails.log.info('Failed to update project gates', e);
                return cb(e);
            }
        })();
    });
};

let destroyReplicatedGates = async(deletedGates, project_id) => {
    let gateSetting = (await sails.models.projectsetting.findOne({where: {project_ref: project_id, name: 'gate_booking_setting', value: {'!=': null}}, select: ['value']})) || {};
    if (gateSetting && gateSetting.id && gateSetting.value.length) {
        let projectIds = gateSetting.value;
        sails.log.info('Deleting replicate gate on following project: ', projectIds);

        if (projectIds.length) {
            for (let i in deletedGates) {
                let projectGate = deletedGates[i];
                await sails.models.projectgate.destroy({
                    where: {
                        project_id: projectIds,
                        identifier: projectGate.identifier,
                    }
                });
            }
        }
    }
    return;
}
const cleanFields = (data) => {

    // if(!data.has_c_lens_policy){
    //     data.c_lens_policy = null;
    // }
    // if(!data.has_d_and_a_policy){
    //     data.d_and_a_policy = null;
    // }
    // if(!data.has_working_hr_agreement){
    //     data.working_hr_agreement = null;
    // }
    if(data.media_file_ids && data.media_file_ids.length){
        data.media_file_ids = data.media_file_ids.map(record => {
            if(record && record.id){
                return record.id;
            }
            return record;
        }).filter(id => !isNaN(+id))
        sails.log.info('Project media now is', data.media_file_ids);
    }
    if(data.media_resources && data.media_resources.length){
        data.media_resources = data.media_resources.map(record => {
            if(record && record.type === 'file' && record.file_ref && record.file_ref.id){
                record.file_ref = record.file_ref.id;
            }
            return record;
        }).filter(record => (record.type))
        sails.log.info('Project media resources now is', data.media_resources);
    }

    if(data.further_policies && data.further_policies.length){
        data.further_policies = data.further_policies.map(further_policy => {
            if (!further_policy.is_text) {
                further_policy.policy_ref = further_policy.policy_ref.map(file => file.id || file);
            }
            return further_policy;
        })
    }
    if(data.logo_file_id && data.logo_file_id.id){
        data.logo_file_id = data.logo_file_id.id;
    }
    if(data.division_ref && data.division_ref.id){
        data.division_ref = data.division_ref.id;
    }
    if(!data.pin){
        data.pin = Math.floor(Math.random() * 1000000 + 1);
    }
    return data;
};

const attachProjectQrImage = async (project) => {
    project.record_id = project.id.toString();
    if(project.project_initial && project.use_prefix){
        project.record_id = `${project.project_initial.toString().trim().toUpperCase()}${project.record_id}`;
    }
    let imageData = await QRCode.toDataURL(`https://www.inndex.co.uk/app?p=${project.record_id}&t=scan`);
    let uploadedFile = {
        imageData: imageData,
        type: 'image/png'
    }
    let s3Url =  await SharedService.uploadToS3(uploadedFile, 'project-qr-'+project.id+'.png', 'qr-code-pic');
    project =  await sails.models.project.updateOne({id: project.id}).set({qrcode_image: s3Url})
    sails.log.info('Attach QR code image with project successful, id', project.id);
};



const addCloseCallDefaultToCustomField = async ( projectId ) => {
    await sails.models.projectsetting.create({ project_ref: projectId, name: 'close_call_custom_fields', value: defaultDataForCloseCallCustomField});
}

const projectCreatedNotification = async (req, project) => {
    let userEmpDetail = await sails.models.userempdetail.findOne({
        user_ref: req.user.id
    });

    let sendToUsers = (sails.config.custom.NEW_PROJECT_MAIL_ALERT_ADDRESSES || '').split(',');
    let len = (sendToUsers || []).length;
    sails.log.info('Total alerts to send', len);
    let recipientsInfo = sendToUsers.map(e => ({id: 0, name: ' ', email: e}));
    let creator = req.user;

    return await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.NEW_PROJECT_CREATED, recipientsInfo, {
        "messageInfo": {
            "mail_title": `New project created - "${project.project_number} - ${project.name}"`,
            "link_label": "View project",
            "project_link": `${sails.config.custom.PUBLIC_URL}site-admin`,
            "creator": {
                "id": creator.id,
                "name": getUserFullName(creator),
                "employer": (userEmpDetail ? userEmpDetail.employer : '')
            }
        },
        // need to pass selective info to avoid bulky sqs msg.
        "project": {
            "id": project.id,
            "name": project.name,
            "contractor": project.contractor || '',     // not present for company portal project
            "project_category": project.project_category,
            "project_initial": project.project_initial,
            "use_prefix": project.use_prefix
        },
    });
};

const notifyRollCallAdmin = async (roll_call, originatingUser) => {

    try {
        sails.log.info('Roll-call Alert Mail will be sent');
        let project = await sails.models.project.findOne({
            select: ['name', 'created_by', 'is_active', 'custom_field', 'contractor', 'project_number'],
            where: {
                id: roll_call.project_ref,
                is_active: 1
            }
        }).populate('created_by');

        if (!project || !project.id) {
            sails.log.warn('roll-call project not found', roll_call.project_ref);
            return false;
        }
        let project_admins = await allProjectAdminsByOneOfDesignations(project.id, [ 'other']);
        let receivers = [project.created_by];
        (project_admins || []).map(u => {
            if(!u.designation.includes('delivery_management')) {
                receivers.push(u.user_ref);
            }
        });
        receivers = _.uniq(receivers, 'email');
        sails.log.info('Total Receiver: ', receivers.length);

        let {present_users, un_accounted_users, present_visitors, un_accounted_visitors} = await prepareRollCallUsersInfo(roll_call, project);

        let form_template = `pages/roll-call-attachment`;
        let title = "Roll Call";
        let dateTimeFormat = 'DD/MM/YYYY HH:mm:ss';
        let { project_logo_file, companyName } = await getCompanyInfo(project);
        let all_present = [...present_users, ...present_visitors];
        let present_users_group = groupByKey(all_present, 'employerName');
        let companies = Object.keys(present_users_group);
        let allUnAccountedUsers = [...un_accounted_users, ...un_accounted_visitors];
        let un_accounted_users_group = groupByKey(allUnAccountedUsers, 'employerName');
        let unAccountedUserCompanies = Object.keys(un_accounted_users_group);
        let comment = (roll_call.comments || []).find(c => c.note) || {};
        let totalPages = 1;
        totalPages = Math.ceil(((all_present.length + allUnAccountedUsers.length) + companies.length + 7) / 35);
        let attachmentName = `${title}-${dayjs().format('MM-DD-YYYY')}.pdf`;
        let subject = `Roll-call completed for project: "${project && project.name || ''}"`;
        let parallelJobs = [];
        let timezone = getProjectTimezone(project);
        for(let i = 0, len = receivers.length; i < len; i++) {
            let receivingUser = receivers[i];
            let pdfHtml = await sails.renderView(form_template, {
                title: title,
                records: present_users_group,
                project_logo_file: project_logo_file,
                companies,
                totalPages,
                originatingUser,
                createdAt: dayjs(roll_call.createdAt).tz(timezone).format(dateTimeFormat),
                project_name: project.name,
                un_accounted_users: allUnAccountedUsers,
                un_accounted_users_group: un_accounted_users_group,
                totalOperatives: all_present.length,
                comment: comment,
                unAccountedUserCompanies: unAccountedUserCompanies,
                layout: false,
                companyName: companyName,
                project: project,
                getUserFullName(userInfo) {
                    return getUserFullName(userInfo);
                },
            });

            let html = await sails.renderView('pages/mail/roll-call-completion-mail', {
                title: subject,
                project,
                roll_call,
                comment: comment,
                present_users: all_present,
                present_users_group: present_users_group,
                un_accounted_users: allUnAccountedUsers,
                originatingUser,
                receivingUser,
                moment:momentTz,
                timezone,
                dateTimeFormat: dateTimeFormat,
                layout: false
            });

            parallelJobs.push((function (receivingUser, html, subject, pdfHtml, attachmentName, count) {
                return async function (callback) {
                    sails.log.info('Sending mail to', receivingUser.email);
                    try {
                        let pdfBuffer = await instantPdfGenerator({}, {}, pdfHtml, 'roll-call', attachmentName, '', {format: 'A4'}, 'pdfBuffer');
                        await sendRawEmail(subject, [receivingUser.email], html, pdfBuffer, attachmentName, true);
                        callback(null, []);
                    } catch (e) {
                        sails.log.info('Failed to send mail.', e);
                        callback(null, []);
                        return false;
                    }
                };
            })(receivingUser, html, subject, pdfHtml, attachmentName, i));
        }

        if(parallelJobs.length){
            sails.log.info('Roll call email notifications, parallel jobs started...');
            executeInParallelLimit(parallelJobs, 3).then(data => {
                sails.log.info(`Roll call email notifications, parallel jobs completed.`, data);
            }).catch(sails.log.error);

        }
        return true;
    }catch (e) {
        sails.log.info('Failed to send all alert ?', e);
        return false;
    }
};

const attachRouteMapWithEachProjectGate = async (project) => {
    for (let index = 0; index < project.project_gates.length; index++) {
        let project_gate = project.project_gates[index];
        if (project_gate.route_map_file_id) {
            let logoFile = await sails.models.userfile.findOne({
                where: {id: project_gate.route_map_file_id},
                select: ['file_url']
            });

            if (logoFile && logoFile.file_url) {
                project_gate.route_map_file_url = logoFile.file_url;
            }
        }
        project.project_gates[index] = project_gate;
    }
    return project;
};

const validateMaxProjectLimit = async ({parent_company, project_category}) => {
    sails.log.info('Validating Company max project limit, company', parent_company);
    if(!parent_company || isNaN(+parent_company)){
        return {
            error: true,
                message: 'parent company details missing',
        };
    }
    let company = await sails.models.createemployer.findOne({
        where: {id: +parent_company},
        select: ['name', 'max_projects']
    });
    if(!company || !company.id){
        return {
            error: true,
            message: 'Incorrect company id',
        };
    }
    // Active company project count
    let active_company_projects = await sails.models.project.count({parent_company: company.id ,is_active: 1, disabled_on: null, project_category});
    if(active_company_projects >= company.max_projects){
        sails.log.error(`Company max project limit ${company.max_projects} is reached/exceeded, count: ${active_company_projects}`);
        return {
            error: true,
            max_reached: true,
            message: sails.__('max_company_projects_limit_reached', company.max_projects),
        };
    }

    sails.log.info(`Company max project limit ${company.max_projects} is NOT reached, count: ${active_company_projects}`);
    return {
        success: true,
        message: 'OK',
        active_company_projects
    };
};

const getOnSiteVisitorOfProject = async (projectId) => {
    let nowMs = moment().valueOf();
    let for_date = moment();
    let target_day = for_date.clone().format(dbDateFormat);
    sails.log.info(`Visitors On-site for day => ${target_day} (${nowMs})`);
    let prev_day = for_date.clone().subtract(1, 'd').format(dbDateFormat);
    let next_day = for_date.clone().add(1, 'd').format(dbDateFormat);
    let two_days_visitor_logs = await getVisitorsTimeLogForDates(projectId, prev_day, next_day);
    let visitor_groups = _groupBy(two_days_visitor_logs, l => l.visitor_id);
    let visitors_on_site = Object.keys(visitor_groups).map(visitor_id => {
        return eventOfToday(target_day, visitor_groups[visitor_id], for_date);
    }).filter(l => l && l.visitor_id).filter(isStillOnSite);
    sails.log.info(`Total visitors of project:${projectId} On-site:`, visitors_on_site.length);
    let visitors = [];
    if (visitors_on_site.length) {
        let unique_visitor_ids = _uniq(visitors_on_site.map(l => +l.visitor_id));
        sails.log.info('fetch visitor info of', unique_visitor_ids);
        visitors = await sails.models.visitor_reader.find({
            where: {id: unique_visitor_ids},
        }).populate('job_role_ref').populate('employer_ref');
    }
    let on_site_visitors = visitors_on_site.map(log => {
        let v = visitors.find(v => v.id === log.visitor_id) || {};
        log.name = `${v.first_name ? v.first_name : ''}${v.last_name ? ' '+v.last_name : '' }`.trim();
        log.employer_id = v.employer_ref && v.employer_ref.id;
        log.job_role_id = v.job_role_ref && v.job_role_ref.id;
        log.employerName = v.employer_ref && v.employer_ref.name;
        log.jobRole = v.job_role_ref && v.job_role_ref.name;
        return log;
    });
    return on_site_visitors;
};


const getProjectById = async (req, res) => {
    let projectId = +(req.param('projectId', 0));
    let all = (req.query.all || '').toString().trim() === 'true';
    let include_pdf_media_dimensions = (req.query.media_dimension || '').toString().trim() === 'true';
    let expand_gates = (req.query.expand_gates || false).toString().trim() === 'true';
    let from_induction = (req.query.from_induction || 'false').toString().trim() === 'true';    // Call is from create/update induction flow (normal user)
    let from_ir = (req.query.from_ir || 'false').toString().trim() === 'true';                  // Call is from inductions list (site-admin)
    let include_admins = (req.query.include_admins || false).toString().trim() === 'true';
    let populate_divisions = (req.query.populate_divisions || '').toString().trim() === 'true';
    let check_ib_availability = (req.query.check_ib_availability || false).toString().trim() === 'true';
    let expand_cel = (req.query.expand_cel || false).toString().trim() === 'true';
    sails.log.info(`Fetch project, id: ${projectId}, all: ${all}, from_induction:${from_induction} from_ir:${from_ir}, include_admins: ${include_admins}, check_ib: ${check_ib_availability}`);
    let filter = {
        id: projectId,
        //disabled_on: null,
        is_active: 1,
    };
    if (all ) {
        filter = {
            id: projectId
        };
    }
    sails.log.info('get Project for filter', filter, 'include_pdf_media_dimensions:', include_pdf_media_dimensions, 'expand_gates:', expand_gates);
    let projectPromise = sails.models.project_reader.findOne(filter)
        .populate('created_by')
        .populate('declarations');

    if (expand_gates) {
        projectPromise.populate('project_gates');
    }
    if (populate_divisions) {
        projectPromise.populate('division_ref');
    }

    let project = await projectPromise;
    if(project && project.id) {
        project._company_admin = false;
        if(all) {
            let uacRecord = (req.user.raw_uac).findIndex(uac => (
                uac.resource
                && uac.role === 'COMPANY_ADMIN'
                && resourceIdentifier.getResourceId(resourceIdentifier.COMPANY_PREFIX, uac.resource) === project.parent_company
            ));
            project._company_admin = (uacRecord > -1) ? true : false;
        }

        if(project.declarations) {
            project.declarations = (project.declarations || []).sort((a, b) => a.id - b.id);
        }
        //@todo: IF ADMIN of current project then only admins list is needed. Otherwise do not add it.

        if(project.project_category === DEFAULT_PROJECT_CATEGORY){

            let admins = await getAllProjectUsers(project.id, include_admins);
            // sails.log.info('project.admins', project.admins);
            //@todo: 541 can be extracted from req.user.uac.projects, instead of deserializeUAC
            let permission = deserializeUAC(Object.assign({}, ((admins || []).find(up => ((up.user_ref && up.user_ref.id)||up.user_ref) === req.user.id) || {})));
            project._my_access_id = (permission && permission.id) || undefined;
            project._my_designations = (permission && permission.designation) || [];
            project._my_flags = (permission && permission.flags) || {};
            project._my_permission = (permission && permission.permission) || [];

            // @todo: spatel: below line can be removed after 1st Jan 2024.
            project.admins = admins;

            let employer = await sails.models.createemployer_reader.findOne({
                where: {id : +project.parent_company},
                select: ['features_status']
            }) || {};
            //If yes, Restrict project level admins to assign Site Management access only
            project._limited_access_change = (employer && employer.id && employer.features_status.restrict_project_level_admins);
        }

        if(project.media_resources && project.media_resources.length) {
            sails.log.info('Expanding project media resource records');
            let file_refs = project.media_resources.reduce((ref, mr) => {
                if(mr && mr.type === 'file' && typeOf(mr.file_ref, 'number')){
                    ref.push(mr.file_ref);
                }
                return ref;
            }, []);

            // Keeping it backward compatible
            project.html_media_url = (project.media_resources.find(mr => mr && mr.type === 'url') || {}).content;
            if(project.html_media_url && project.html_media_url.length){
                project.has_html_media = true;
            }
            if(file_refs.length){

                let files = await sails.models.userfile_reader.find({id: file_refs});
                sails.log.info(`Populating project media resource records, file_ref: ${file_refs}`);
                for (let i = 0; i < project.media_resources.length; i++) {
                    let mr = project.media_resources[i];
                    if(mr && mr.type === 'file' && typeOf(mr.file_ref, 'number')){
                        let ref = files.find(f => f.id === mr.file_ref);
                        if(include_pdf_media_dimensions && ref && ref.file_mime === 'application/pdf'){
                            ref.dimensions =  await getPDFPageSizes(ref.file_url);
                        }
                        mr.file_ref = ref || null;
                    }
                }

                // Keeping it backward compatible
                project.media_file_ids = files;
            }
        }

        // @deprecated: Below logic is deprecated in favor of `media_resources` field.
        else if(project.media_file_ids && project.media_file_ids.length){
            sails.log.info('Expanding project media records');
            try{
                project.media_file_ids = await sails.models.userfile_reader.find({id: project.media_file_ids});
                if(include_pdf_media_dimensions){
                    sails.log.info('Attaching pdf media dimensions');
                    for (let i = 0; i < project.media_file_ids.length; i++) {
                        if(project.media_file_ids[i].file_mime === 'application/pdf'){
                            project.media_file_ids[i].dimensions =  await getPDFPageSizes(project.media_file_ids[i].file_url);
                        }
                    }
                }
            }catch (e) {
                sails.log.error('Failed to expand project media record', e);
            }
        }

        if(project.further_policies && project.further_policies.length){
            sails.log.info('Expanding policy file if available');
            if(project.further_policies && project.further_policies.length) {
                for (const index in project.further_policies) {
                    if (!project.further_policies[index].is_text && project.further_policies[index].policy_ref.length) {
                        project.further_policies[index].policy_ref = await sails.models.userfile_reader.find({id: project.further_policies[index].policy_ref});
                    }
                }
            }
        }

        if (expand_gates && project.delivery_management_status && project.project_gates.length) {
            project = await attachRouteMapWithEachProjectGate(project);
        }
        let { project_logo_file, companyName, employer: contractor } = await getCompanyInfo(project, null, ['features_status'], true);
        project.logo_file_id = project_logo_file; // this needs to be coming form `parent company OR contractor` logo
        if (check_ib_availability) {
            let where = [{ project_ref: projectId }];
            if (contractor && contractor.id) {
                where.push({ company_ref: contractor.id, ib_type: 'company' });
            }
            let ibChecklists = await sails.models.inspectionbuilder_reader.find({
                where: {
                    or: where,
                    enabled: true
                },
                select: ['id', 'company_ref', 'ib_type', 'project_ref', 'activate_on_projects']
            });

            ibChecklists = (ibChecklists || []).filter(inspection => isEnabledForProject(inspection, project.id));
            project.has_ib_checklists = (ibChecklists.length > 0);
            if (ibChecklists.length > 0) {
                project.first_ib_id = ibChecklists[0].id;
            }
        }

        let medication_disabled_projects = await sails.models.inndexsetting_reader.findOne({name: "medication_disabled_projects"});
        let hide_medication_block = false;
        if (medication_disabled_projects && medication_disabled_projects.value && medication_disabled_projects.value.length) {
            sails.log.info('hide medications details for projects:', medication_disabled_projects.value);
            hide_medication_block = medication_disabled_projects.value.includes(+projectId);
        }

        let project_settings = {};

        if (expand_cel) {
            if (req.is_verified_sa) {
                // API call is from site-admin route
                project.competency_exception_list = await getMandatoryCompetencyExceptionList(project.id);
            }else{
                // API call is not a from site-admin route OR user is not site-admin.
                // Only respond with bool status
                let competency_exception_list_ids = await getMandatoryCompetencyExceptionList(project.id, false);
                project._user_excluded_from_mandatory_doc = competency_exception_list_ids.includes(req.user.id);
            }
        }

        let procore_permissions_disabled = (project.custom_field.disable && project.custom_field.disable.procore_permissions);
        /**
         * When caller is mobile app and "procore permissions" is disabled for project,
         * we are marking `procore_integration` to hide procore section from app.
         */
        if (req.is_mobile_platform && procore_permissions_disabled) {
            project.custom_field.procore_integration = false;
        }
        if(from_ir){
            sails.log.info('Loading induction-list screen specific data');
            contractor = {
                ...(contractor || {}),
            };
            let project_company_settings = await sails.models.companysetting_reader.find({
                where: {
                    name: [
                        COMPANY_SETTING_KEY.FACIAL_RECOGNITION_CONFIG,
                        COMPANY_SETTING_KEY.CSCS_CONFIG,
                        COMPANY_SETTING_KEY.RTW_CHECK_CONFIG
                    ],
                    company_ref: project.parent_company,
                },
                select: ['name', 'value', 'enabled_on']
            });
            let rtw_setting = project_company_settings.find(cs => cs.name === COMPANY_SETTING_KEY.RTW_CHECK_CONFIG && cs.enabled_on);
            let fr_setting = project_company_settings.find(cs =>
                cs.name === COMPANY_SETTING_KEY.FACIAL_RECOGNITION_CONFIG &&
                cs.value &&
                cs.value.status &&
                (
                    (cs.value.excluded_projects || []).length === 0 ||
                    !(cs.value.excluded_projects || []).includes(project.id)
                )
            );
            let {enabled, verification_mandatory} = (project_company_settings.find(cs => cs.name === COMPANY_SETTING_KEY.CSCS_CONFIG) || {}).value || {};
            contractor.cscs_status = {
                enabled: (enabled || false),
                verification_mandatory: (verification_mandatory || false),
            };
            contractor.fr_setting = {
                enabled: !!(fr_setting && fr_setting.value.status),
                optima_auto_enrol: !!(fr_setting && fr_setting.value.optima_auto_enrol),
            };

            contractor.rtw_status = extractRtwStatus(rtw_setting, projectId);
        }
        else if(from_induction){
            contractor = {
                ...(contractor || {}),
            };
            sails.log.info('Loading induction flow specific data');

            let companySetting = await sails.models.companysetting_reader.find({
                where: {
                    name: [
                        COMPANY_SETTING_KEY.FEATURES_STATUS,
                        COMPANY_SETTING_KEY.CSCS_CONFIG,
                        COMPANY_SETTING_KEY.RTW_CHECK_CONFIG,
                    ],
                    company_ref: project.parent_company,
                    // enabled_on: {'!=': null},
                },
                select: ['name', 'value', 'enabled_on']
            });
            const settingGroups = companySetting.reduce((group, row) => {
                group[row.name] = row.value;
                return group;
            }, {});
            contractor.company_flags = (settingGroups[COMPANY_SETTING_KEY.FEATURES_STATUS] || {});
            let {enabled, verification_mandatory} = (settingGroups[COMPANY_SETTING_KEY.CSCS_CONFIG] || {});
            const rtw_setting = (companySetting.find(s => s.name === COMPANY_SETTING_KEY.RTW_CHECK_CONFIG && s.enabled_on));
            contractor.rtw_status = extractRtwStatus(rtw_setting, projectId);
            contractor.cscs_status = {
                enabled: (enabled || false),
                verification_mandatory: (verification_mandatory || false),
            };
        }
        else if (req.is_super_admin){
            sails.log.info('Loading additional info specific for super-admin UI');
            let procore_setting_row = await sails.models.projectsetting_reader.findOne({
                project_ref: projectId,
                name: [PROCORE_INTEGRATION_PROJECT_INFO]
            });

            if(procore_setting_row && procore_setting_row.value){
                let {me, procore_meta, user_ref} = procore_setting_row.value || {};
                project_settings[PROCORE_INTEGRATION_PROJECT_INFO] = {
                    added_by: me,
                    procore_meta: procore_meta,
                    user_ref,
                }
            }

        }

        return ResponseService.successResponse(res, {
            project,
            // below will be adding for super-admin only
            ...(req.is_super_admin ? {project_settings} : {}),
            // below field will be used to determine certain feature are enabled at company level or not.
            ...(project.project_category === DEFAULT_PROJECT_CATEGORY ? {contractor, medication_block_disabled: hide_medication_block} : {contractor,})
        });
    }
    return ResponseService.errorResponse(res, 'Something went wrong while fetching the project');
};

const sortProperties = async (associatedRecords) => {
    associatedRecords = Object.keys(associatedRecords).reduce((list, key) => {
        list.push({
            key,
            value: associatedRecords[key]
        });
        return list;
    }, []);
    return associatedRecords.sort((a, b) => (a.value < b.value) ? 1 : -1);
};

const getUsersList = async (projectId, user) => {
    //check if user has only site management access on project
    let hasOnlySiteManagement = await hasOnlySiteManagementAccess(user, projectId);
    sails.log.info("hasOnlySiteManagement: ", hasOnlySiteManagement);
    let employers_filter = [];
    if (hasOnlySiteManagement) {
        employers_filter = await getInductionUserEmployers(projectId, {statusCodes: [], userIds: [user.id]}, true);
        sails.log.info("hasOnlySiteManagement employers_filter", employers_filter);
    }

    let induction_requests = [];
    let statusCodes = [2, 6];
    let variables = [projectId, ...statusCodes];
    if (employers_filter.length) {
        variables.push(...employers_filter);
    }
    let variableIndex = 0;
    let sql = `SELECT ir.status_code status_code, ir.project_ref project_ref, ir.user_ref user_ref, ir.additional_data->'employment_detail' as employer,
                          (CASE WHEN u1.id IS NOT NULL THEN json_build_object('id', u1.id, 'first_name', u1.first_name, 'middle_name', u1.middle_name, 'last_name', u1.last_name, 'name', CONCAT(u1.first_name, ' ', u1.last_name), 'profile_pic_ref', u1.profile_pic_ref) ELSE NULL END) as user_ref
                              FROM induction_request ir
                               LEFT JOIN users u1 ON u1.id = ir.user_ref
                              WHERE ir.project_ref = ${`$${++variableIndex}`}
                                AND status_code IN (${statusCodes.map(() => `$${++variableIndex}`).join(',')})
                                ${((employers_filter.length) ? `AND additional_data->'employment_detail'->> 'employer' IN (${employers_filter.map(() => `$${++variableIndex}`).join(',')})` : '')}
                              ORDER BY ir.id DESC`;
    let inductionRequestsResult = await sails.sendNativeQuery(sql, variables);
    if (HttpService.typeOf(inductionRequestsResult.rows, 'array') && inductionRequestsResult.rows.length) {
        induction_requests = inductionRequestsResult.rows;
    }
    if(!induction_requests.length) {
        return [];
    }

    let userIds = [];
    let usersProfilePicIds = [];
    let induction_users = (induction_requests || []).reduce((iu, ir) => {
        if (ir.user_ref && ir.user_ref.id && !userIds.includes(ir.user_ref.id)) {
            ir.user_ref.employer = ir.employer
            iu.push(ir.user_ref);
            userIds.push(ir.user_ref.id);
            if (ir.user_ref.profile_pic_ref) {
                usersProfilePicIds.push(ir.user_ref.profile_pic_ref);
            }
        }
        return iu
    }, []);
    /*if(userIds.length) {
        let employers = await sails.models.userempdetail_reader.find({
            where: {user_ref: userIds}
        });
        induction_users = induction_users.map(operative_user => {
            operative_user.employer = employers.find(e => e.user_ref === operative_user.id);
            return operative_user;
        });
    }*/
    sails.log.info('User profile pic Ids count ', usersProfilePicIds.length);

    induction_users = await attachProfilePicWithUsersInfo(induction_users, usersProfilePicIds);
    return induction_users;
}

const getCompanyBreakdown = async (req, project) => {
    let tz = (project && project.custom_field && project.custom_field.timezone) || fall_back_timezone;
    let is_default_project = (project && project.project_category === 'default');
    let nowMs = +req.param('nowMs', 0);
    let now = nowMs ? momentTz(nowMs).tz(tz) : momentTz().tz(tz);
    let operative_users = await getOnSiteUsers(project.id, now, undefined, is_default_project);
    sails.log.info(`Found ${operative_users.length} on-site users.`);
    let onSiteOperatives = [];
    if (!operative_users.length) {
        return onSiteOperatives;
    }
    for (let i=0; i < operative_users.length; i++) {
        let employer = (operative_users[i] && operative_users[i].employer && operative_users[i].employer.employer) || null;
        let operative_name = (operative_users[i] && operative_users[i].user_id && operative_users[i].user_id.name) || null;

        if (!employer || !operative_name) {
            continue;
        }

        let job_role = (operative_users[i] && operative_users[i].employer && operative_users[i].employer.job_role) || null;
        let profile_pic_ref =  (operative_users[i] && operative_users[i].user_id && operative_users[i].user_id.profile_pic_ref) || {};
        let id = (operative_users[i] && operative_users[i].user_id && operative_users[i].user_id.id) || null;
        let findIndex = onSiteOperatives.findIndex(item => item.employer === employer);
        if (findIndex > -1) {
            onSiteOperatives[findIndex].operatives.push({id, operative_name, job_role, profile_pic_ref});
        } else {
            onSiteOperatives.push({employer, "operatives": [{id, operative_name, job_role, profile_pic_ref}]});
        }
    }

    for (let i=0; i < onSiteOperatives.length; i++) {
        (onSiteOperatives[i].operatives).sort((a,b) => (a.operative_name.toLowerCase() > b.operative_name.toLowerCase()) ? 1 : ((b.operative_name.toLowerCase() > a.operative_name.toLowerCase()) ? -1 : 0));
    }
    return onSiteOperatives;
};

const getOnSiteUsers = async(projectId, for_date, user_ref = undefined, user_induction_source = false, extraInclude=[]) => {
    let target_users = [];
    if(user_ref){
        target_users.push(user_ref);
    }
    let time_log_for_day = await getDailyTimeEventForDay(projectId, for_date, target_users, false, null, false, extraInclude.includes('location'));
    let operative_users = time_log_for_day.filter(isStillOnSite);

    if (extraInclude.includes('location') && operative_users.length) {
        let users_locations = await deriveOnSiteUsersLocation(operative_users, projectId);
        operative_users = (operative_users || []).map(log => {
            log.location = (users_locations[log.user_id] || '')
            return log;
        });
    }

    let userIds = _uniq((operative_users || []).map(ou => ou.user_id));
    sails.log.info('Total on-site User Ids to expand :', userIds.length);
    sails.log.info(`Project: ${projectId} date: ${for_date.format()} has on-site users: ${userIds}`);
    if(userIds.length) {
        if(user_induction_source){
            sails.log.info('Fetching inductions of on-site users');
            let inductions = await sails.models.inductionrequest_reader.find({
                where: {user_ref: userIds, project_ref: projectId},
                select: ['id', 'user_ref', 'additional_data'],
            });
            sails.log.info('Number of users on-site', userIds.length);
            // map users and employers
            operative_users = operative_users.map(operative_user => {
                let ir = inductions.find(i => i.user_ref === operative_user.user_id) || {};
                if(!ir.id){
                    ir = {additional_data: {}};
                }
                let {additional_data: {user_info, employment_detail}} = ir;
                operative_user.employer = employment_detail || null;
                operative_user.user_id = user_info || null;
                return operative_user;
            });
        }else {
            sails.log.info('Using profile as information source for on-site users');
            let users = await sails.models.user_reader.find({
                where: {
                    id: userIds
                },
                // select: user_select
            });
            users = await attachProfilePicWithUsersInfo(users, [], true);
            let employers = await sails.models.userempdetail_reader.find({
                where: {user_ref: userIds},
                // select: employment_column
            });
            sails.log.info('Number of users on-site', users.length);
            // map users and employers
            operative_users = operative_users.map(operative_user => {
                operative_user.employer = employers.find(e => e.user_ref === operative_user.user_id);
                operative_user.user_id = users.find(u => u.id === operative_user.user_id);
                return operative_user;
            });
        }
    }
    return operative_users;
}

const expandProjectsDivision = async (projects) => {
    let projectDivisionIds = (projects || []).reduce((arr, project) => {
        if (project.division_ref && typeof project.division_ref == 'number') {
            arr.push(project.division_ref);
        }
        return arr;
    }, []);

    let divisions = [];
    if (projectDivisionIds.length) {
        sails.log.info("Expanding project division: ", projectDivisionIds);
        divisions = await sails.models.companydivision.find({
            where: {id: _uniq(projectDivisionIds)},
            select: ['name']
        });

        /*if (divisions && divisions.length) {
            (projects || []).map(project => {
                if (project.division_ref && typeof project.division_ref == 'number') {
                    project.division_ref = (divisions || []).find(division => division.id == project.division_ref);
                }
                return project;
            });
        }*/
    }

    return divisions;
}

const groupByKey = (xs, key) => {
    return xs.reduce(function(rv, x) {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
};

/**
 * @deprecated, got a better alternative of it, InductionController.getProjectInductedUsers
 * @param req
 * @param res
 * @returns {Promise<*>}
 */
const projectInductedUsers = async (req, res) => {
    let projectId = +req.param('projectId');
    let induction_requests = await sails.models.inductionrequest_reader.find({
        select: ['status_code', 'project_ref', 'user_ref', 'additional_data'],
        where: {
            status_code: [2, 6], // `approved` OR `In review` one only
            project_ref: projectId
        }
    });
    induction_requests = await populateUserRefs(induction_requests, 'user_ref', []);

    let userIds = [];
    let usersProfilePicIds = [];
    let induction_users = (induction_requests || []).reduce((iu, ir) => {
        if (ir.user_ref && ir.user_ref.id && !userIds.includes(ir.user_ref.id)) {
            let {additional_data: {user_info, employment_detail}} = ir;
            ir.user_ref.user_employer = employment_detail;
            iu.push(ir.user_ref);
            userIds.push(ir.user_ref.id);
            if (ir.user_ref.profile_pic_ref) {
                usersProfilePicIds.push(ir.user_ref.profile_pic_ref);
            }
        }
        return iu
    }, []);

    sails.log.info('User profile pic Ids count ', usersProfilePicIds.length);

    induction_users = await attachProfilePicWithUsersInfo(induction_users, usersProfilePicIds);

    return successResponse(res, {induction_users: induction_users});
}

const onSiteOperativesList = async (projectId) => {
    let time_log_for_day = await getDailyTimeEventForDay(projectId, moment(), [], false, null, false, true);
    let daily_logs = time_log_for_day.filter(isStillOnSite);
    sails.log.info('Total on site operatives', daily_logs.length);
    let onSiteVisitors = await getOnSiteVisitorOfProject(projectId);
    if(!daily_logs.length && !onSiteVisitors.length) {
        return {success: false, message: 'Operatives are not present on site.'};
    }
    let location_based_events = [];
    let optima_based_events = [];
    (daily_logs || []).map((row) => {
        if (!row.recent_in) {
            return row;
        }
        for (let i = 0, len = (row.events || []).length; i < len; i++) {
            let event = (row.events || [])[i];
            let e = {
                event_date_time: row.recent_in,
                user_ref: row.user_id,
            };
            if(event.id){
                e = {
                    id: event.id,
                };
            }
            if (event.event_type === EVENT_TYPE.IN &&
                event.event_date_time === row.recent_in &&
                event.source === VALID_SOURCES.GEO_FENCE) {
                location_based_events.push(e);
                row.event_source = VALID_SOURCES.GEO_FENCE;
                break;
            }
            if (event.event_type === EVENT_TYPE.IN &&
                event.event_date_time === row.recent_in &&
                event.source === VALID_SOURCES.OPTIMA) {
                optima_based_events.push(e);
                row.event_source = VALID_SOURCES.OPTIMA;
                break;
            }
        }
       return row;
    });
    if(!location_based_events.length && !optima_based_events.length) {
        return {success: false, message: 'Download failed.'};
    }
    let operative_users = [];
    if(location_based_events.length) {
        operative_users = await sails.models.usertimelog_reader.find({
            where: {
                event_type: [EVENT_TYPE.IN, EVENT_TYPE.INTERACTION],
                project_ref: +projectId,
                ...(location_based_events.length > 1 ? {or: location_based_events} : location_based_events.pop())
            },
            select: ['temperature', 'event_date_time', 'user_ref', 'user_location']
        });
        operative_users.map(b => {
            b.location = (b && b.user_location && b.user_location.name) ? b.user_location.name: '';
            return b;
        });
    }
    let optima_users = [];
    if(optima_based_events.length) {
        optima_users = await sails.models.badgeevent_reader.find({
            select: ['event_date_time', 'user_ref', 'reader_name'],
            where: {
                event_type: [EVENT_TYPE.IN, EVENT_TYPE.INTERACTION],
                project_ref: projectId,
                ...(optima_based_events.length > 1 ? {or: optima_based_events} : optima_based_events.pop())
            }
        });
    }
    let userIds = (daily_logs || []).map(ou => ou.user_id);
    operative_users = [...operative_users, ...optima_users];
    sails.log.info('Total User Ids to expand :', userIds.length);
    if(userIds.length) {
        let additionalCols = [
            `user_ref`,
            `creator_name`,
            `additional_data -> 'employment_detail' ->> 'employer' as employer`,
            `additional_data -> 'employment_detail' ->> 'job_role' as job_role`
        ];
        let searchUserIds =  _uniq(userIds);

        let {
            records: usersInductionInfo
        } = await getProjectInductions(projectId, {
            statusCodes: [],
            limit: -1,
            searchUserIds,
        }, additionalCols);

        sails.log.info('Number of users', usersInductionInfo.length);
        daily_logs = daily_logs.map(d => {
            if(d.event_source === VALID_SOURCES.GEO_FENCE) {
                let a = operative_users.find(o=> o.event_date_time === d.recent_in && o.user_ref === d.user_id);
                d.location = (a && a.user_location && a.user_location.name) ? a.user_location.name: '';
            } else if(d.event_source === VALID_SOURCES.OPTIMA) {
                let a = optima_users.find(o=> o.event_date_time === d.recent_in && o.user_ref === d.user_id);
                d.location = a.reader_name;
            }
            d.employer = usersInductionInfo.find(e => e.user_ref === d.user_id);
            d.user_id = usersInductionInfo.find(u => u.user_ref === d.user_id);
            return d;
        });
    }
    let onSiteUsers = daily_logs.map(u => {
        u.employerName = (u.employer && u.employer.employer) ? u.employer.employer : '';
        u.jobRole = (u.employer && u.employer.job_role) ? u.employer.job_role: '';
        return u;
    });
    if(onSiteVisitors.length) {
        onSiteUsers = [...onSiteUsers, ...onSiteVisitors];
    }
    return {success: true, onSiteUsers: onSiteUsers};
};

const getCompanyProjectIds = async (user) => {
    let companyIds = (user.raw_uac || []).reduce((arr, ur) => {
        let isUserProjectAdmin = (ur.designation) ? ur.designation.split(',').includes(UACCompanyDesignations.PROJECT_ADMIN) : false;
        if (ur.role === ROLES.COMPANY_ADMIN && isUserProjectAdmin) {
            ur.resource = resourceIdentifier.getResourceId(resourceIdentifier.COMPANY_PREFIX, ur.resource);
            arr.push(ur.resource);
        }
        return arr;
    }, []);
    sails.log.info(`Found company admin's companies: ${companyIds}`);
    if (!companyIds.length) {
        return [];
    }
    let projects = await sails.models.project_reader.find({
        where: {parent_company: [...companyIds]},
        select: ['id']
    });

    return projects.map(p => p.id);
};

const convertArrayOfNumbersToObjects = (arrayOfNumbers, numberKey) => {
    let arrayOfObjects = arrayOfNumbers.reduce((arr, number) => {
        if (+number) {
            let object = {};
            object[numberKey] = number;
            arr.push(object);
        }
        return arr;
    }, []);

    return (arrayOfObjects.length) ? arrayOfObjects : arrayOfNumbers;
};

module.exports = {

    getUserProjects: async (req, res) => {
        let limited = (req.param('limited', false).toString() === 'true');

        sails.log.info('Querying project for user', req.user.id, 'project_category: default', `limited: ${limited}`);
        let user_projects = await getAllowedDefaultProjectsOfUser(req.user.id);
        let allowedProjectIds = user_projects.map(r => r.resource);
        let companyProjectIds = [];
        if (req.user.projectAdminDesignation) {
            companyProjectIds = await getCompanyProjectIds(req.user);
            allowedProjectIds = _.uniq([...allowedProjectIds, ...companyProjectIds]);
        }
        sails.log.info(`Allowed project ids: ${allowedProjectIds}, including companies project ${companyProjectIds}`);
        let projects = [];
        if (!limited) {
            projects = await sails.models.project_reader.find({
                where: {
                    or: [
                        // {created_by: req.user.id,}, // This is not required, still adding for safe side.
                        {id: allowedProjectIds}
                    ],
                    disabled_on: null,
                    project_category: DEFAULT_PROJECT_CATEGORY
                },
            }).sort([
                {disabled_on: 'DESC'},
                {is_active: 'DESC'},
                //{name: 'ASC'},
                {id: 'ASC'},
            ]);
                // .populate('declarations')
                // .populate('logo_file_id')
                // .populate('project_gates');
            let all_project_declarations = await sails.models.projectdeclaration_reader.find({where: {project_id: allowedProjectIds}});
            let all_project_gates = await sails.models.projectgate_reader.find({where: {project_id: allowedProjectIds}});
            projects = await populateUserRefs(projects, 'created_by', [])
            sails.log.info('got projects, total', projects.length);
            projects = projects.map(row => {
                let permission = user_projects.find(up => up.resource === row.id);
                row._my_access_id = (permission && permission.id) || undefined;
                row._my_designations = (permission && permission.designation) || [];
                row._my_flags = (permission && permission.flags) || {};
                row._my_permission = (permission && permission.permission) || [];
                row.declarations = all_project_declarations.filter(pd => pd.project_id === row.id);
                row.project_gates = all_project_gates.filter(pg => pg.project_id === row.id);
                return row;
            });

            // Populate project logo (logo_file_id) with contractor's logo ref, DEFAULT project doesn't need parent_company logo
            let distinct_contractors = projects.reduce((ids, p) => {
                if(p.parent_company && !ids.includes(p.parent_company)) {
                    ids.push(p.parent_company);
                }
                return ids;
            }, []);

            if(distinct_contractors.length){
                sails.log.info('get logo of distinct_contractors:', JSON.stringify(distinct_contractors));
                let employer_logos = await sails.models.createemployer.find({
                    where: {id: distinct_contractors},
                    select: ['id', 'name', 'logo_file_id', 'country_code']
                }).populate('logo_file_id');

                projects = projects.map(p => {
                    let logo = employer_logos.find(el => el.id === p.parent_company);
                    p.logo_file_id = logo && logo.logo_file_id;
                    return p;
                });
            }
        } else {
            projects = await sails.models.project_reader.find({
                where: {
                    or: [
                        {id: allowedProjectIds}
                    ],
                    disabled_on: null,
                    delivery_management_status: true,
                    project_category: DEFAULT_PROJECT_CATEGORY
                },
                select: ['id', 'name']
            });
        }

        //Expand divisions
        let divisions = await expandProjectsDivision(projects);

        return ResponseService.successResponse(res, {projects, projects_division: divisions});
    },

    checkCompanyProjectStatus: async (req, res) => {
        let companyId = req.param('companyId');
        let outcome = await validateMaxProjectLimit({parent_company: companyId, project_category: 'company-project'});
        return (outcome.error) ? ResponseService.errorResponse(res, '', outcome) : ResponseService.successResponse(res, outcome);
    },

    createProject: async (req, res) => {

        sails.log.info('Create request for project, by', req.user.id);
        let createRequest = _.pick((req.body || {}), [
            'name',
            'use_prefix',
            'project_number',
            'description',
            // 'company_name',
            // 'logo_file_id', // this needs to be coming form `parent company OR contractor` logo
            'contractor',
            'is_active',
            'main_contact_name',
            'main_contact_number',
            'main_contact_number_obj',
            'template_identifier',
            'has_media_content',
            'media_declaration_content',
            // 'media_file_ids',
            'media_resources',
            'html_media_url',
            'has_html_media',
            //'disabled_on',
            'start_date',
            'end_date',
            'value',
            'project_type',
            'type_of_works',
            'site_main_risks',
            'further_policies',
            'client',
            'main_contract_type',
            'designer',
            'stakeholder',
            'postcode',
            'is_passport_require',
            'is_cscs_require',
            'other_doc_required',
            'blacklist_user_on_expiry',
            'is_fors_compliant',
            'delivery_management_status',
            'default_in_duration',
            'project_category',
            'parent_company',
            'pin',
            'induction_pin',
            'project_initial',
            'project_section_access',
            'company_additional_project_section_access',
            'fatigue_management_status',
            'site_hours_daily',
            'total_hours_daily',
            'site_hours_weekly',
            'total_hours_shifts',
            'total_duty_periods_biweekly',
            // 'close_call_phrase',
            // 'induction_questions',
            'induction_answers',
            'closecall_setting',
            'goodcall_setting',
            'custom_field',
            'cow_setting',
            'division_ref',
        ]);
        createRequest = cleanFields(createRequest);

        // Backward compatibility
        if (createRequest.main_contact_number) {
            createRequest.main_contact_number_obj = { "code": null, "number": createRequest.main_contact_number }
        } else {
            createRequest.main_contact_number = createRequest.main_contact_number_obj.number
        }

        let project_users_req = req.body.admins || [];
        let competency_exception_list_req = req.body.competency_exception_list || [];
        let project_declarations_req = req.body.declarations || [];
        let project_gates_req = req.body.project_gates || [];
        if(createRequest.project_category && createRequest.project_category.toString().toLowerCase().trim() === 'company-project'){
            let outcome = await validateMaxProjectLimit(createRequest);
            if(outcome.error){
                return ResponseService.errorResponse(res, '', outcome);
            }
        }

        sails.log.info('create project with', createRequest);
        createRequest.created_by = req.user.id;
        createRequest.edited_by = req.user.id;
        let project = await sails.models.project.create(createRequest);

        //attach QR code with project async in background
        attachProjectQrImage(project).catch(sails.log.error);

        attachLocationKey(project).catch(sails.log.error);

        sails.log.info('created successful, id', project.id);
        addCloseCallDefaultToCustomField( project.id ).catch(sails.log.error);
        projectCreatedNotification(req, project).catch(sails.log.error);
        triggerCreateCollection(project.id).catch(sails.log.error);

        let data = await (storeProjectData(project, project_users_req, project_declarations_req, project_gates_req, req.user.email));
        let list = await storeMandatoryCompetencyExceptionList(project.id, competency_exception_list_req);
        return ResponseService.successResponse(res, data);
    },

    updateProject: async (req, res) => {
        let called_at = (new Date()).getTime();

        let projectId = req.param('projectId');
        sails.log.info('update project request', projectId);
        if (!projectId) {
            return ResponseService.errorResponse(res, 'project id is required');
        }
        let updateRequest = _.pick((req.body || {}), [
            'name',
            'use_prefix',
            'project_number',
            'description',
            // 'company_name',
            // 'logo_file_id', // this needs to be coming form `parent company OR contractor` logo
            'contractor',
            'is_active',
            'main_contact_name',
            'main_contact_number',
            'main_contact_number_obj',
            'disabled_on',
            'template_identifier',
            'has_media_content',
            'media_declaration_content',
            // 'media_file_ids',
            'media_resources',
            'html_media_url',
            'has_html_media',
            'start_date',
            'end_date',
            'value',
            'project_type',
            'type_of_works',
            'site_main_risks',
            'further_policies',
            'client',
            'main_contract_type',
            'designer',
            'stakeholder',
            'postcode',
            'is_passport_require',
            'is_cscs_require',
            'other_doc_required',
            'blacklist_user_on_expiry',
            'is_fors_compliant',
            'delivery_management_status',
            'default_in_duration',
            'project_category',
            'parent_company',
            'pin',
            'induction_pin',
            'project_initial',
            'project_footer',
            'project_section_access',
            'company_additional_project_section_access',
            'fatigue_management_status',
            'site_hours_daily',
            'total_hours_daily',
            'site_hours_weekly',
            'total_hours_shifts',
            'total_duty_periods_biweekly',
            // 'close_call_phrase',
            'closecall_setting',
            'goodcall_setting',
            'custom_field',
            'cow_setting',
            'division_ref',
        ]);

        updateRequest = cleanFields(updateRequest);
        // Need to support complete project updating here
        updateRequest.edited_by = req.user.id;

        let projectExistingInfo = await sails.models.project.findOne({
            select: ['postcode', 'custom_field'],
            where: {id: projectId}
        });
        let lastPostcode = projectExistingInfo.postcode;
        let lastLocation = projectExistingInfo.custom_field.location;
        let lastBookingSlot = projectExistingInfo.custom_field.default_booking_slot;
        // Backward compatibility
        if (updateRequest.main_contact_number) {
            updateRequest.main_contact_number_obj = { ...updateRequest.main_contact_number_obj, "number": updateRequest.main_contact_number }
        } else {
            updateRequest.main_contact_number = updateRequest.main_contact_number_obj.number
        }

        let project = await sails.models.project.updateOne({id: projectId}).set(updateRequest);
        sails.log.info('updated project successful, id', project ? project.id : undefined);

        //update weather key on updating postcode
        if (
            project.postcode !== lastPostcode ||
            (
                project.custom_field.country_code === 'AE' &&
                (
                    project.custom_field.location.lat !== lastLocation.lat ||
                    project.custom_field.location.long !== lastLocation.long
                )
            )
        ) {
            sails.log.info('Updating weather location key & location');
            attachLocationKey(project).catch(sails.log.error);
        }
        //delete future bookings when default timeslot is changed
        if(lastBookingSlot && lastBookingSlot != project.custom_field.default_booking_slot) {
            let tz = req.user.timezone || fall_back_timezone;
            let fromDate = dayjs().tz(tz).format("DD-MM-YYYY");
            let futureBookings = await deliveryManagementFn.getFutureBookings(fromDate, projectId);
            let futureBookingIds = futureBookings.map(b => b.id);
            let deleted = await sails.models.projectgatebooking.destroy({id: futureBookingIds, project_id: projectId});
            sails.log.info('Deleted future bookings count', deleted ? deleted.length : null);
        }

        //attach QR code with project async in background
        attachProjectQrImage(project).catch(sails.log.error);

        let project_users_req = req.body.admins || [];
        let competency_exception_list_req = req.body.competency_exception_list || [];
        let project_declarations_req = req.body.declarations || [];
        let project_gates_req = req.body.project_gates || [];

        let data = await (storeProjectData(project, project_users_req, project_declarations_req, project_gates_req, req.user.email));
        if(req.body.competency_exception_list){
            // No need to add/update when key is not present into payload.
            let list = await storeMandatoryCompetencyExceptionList(project.id, competency_exception_list_req);
        }

        let admins = (req.body.admins || []).map(admin => {
            return {
                id: (admin.user_ref && admin.user_ref.id) || admin.user_ref,
                role: admin.role,
                designation: admin.designation
            }
        })

        let finished_at = (new Date()).getTime();

        let lastSupplyChain = projectExistingInfo.custom_field.supply_chain_companies;
        let updatedSupplyChain = updateRequest.custom_field && updateRequest.custom_field.supply_chain_companies;

        AccessLogService.interactionLog(AccessLogService.EXTERNAL_SERVICE_ID.INNDEX, projectId, true, 200, {
            method: req.method,
            url: req.path,
            payload: {userId: req.user.id, userName: req.user.name, admins: admins, last_supply_chain: lastSupplyChain, updated_supply_chain: updatedSupplyChain},
            headers: req.headers,
            response_body: {},
            response_headers: (res.headers|| {})
        }, (finished_at - called_at), 'update-project', 'rest').catch(sails.log.error);

        return ResponseService.successResponse(res, data);
    },

    updateProjectLiveTvFooter: async (req, res) => {
        let projectId = req.param('projectId');
        sails.log.info('update project update LiveTv Footer', projectId);
        if (!projectId) {
            return ResponseService.errorResponse(res, 'project id is required');
        }
        let updateRequest = _.pick((req.body || {}), [
            'project_footer'
        ]);
        updateRequest = cleanFields(updateRequest);

        sails.log.info('update project with', updateRequest);
        try {
            let project = await sails.models.project.updateOne({id: projectId}).set(updateRequest);
            if (project && project.id) {
                sails.log.info('updated project successful, id', project ? project.id : undefined);
                return ResponseService.successResponse(res, project);
            } else {
                sails.log.info('Failed to update project');
                return ResponseService.errorResponse(res, sails.__('internal server error'));
            }
        } catch (ex) {
            sails.log.info('Failed to update live tv footer', ex);
            return ResponseService.errorResponse(res, sails.__('internal server error'), ex);
        }
    },

    getProject: getProjectById,
    siteAdmin_getProject: getProjectById,
    getProjectIfInducted: getProjectById,

    getnewProject: async (req, res) => {

      let filter = {
        is_active: 0,
      };

      sails.models.project_reader.find().sort('id ASC').exec(function fetchRecords(error, projectsListing) {
        if (error) {
          sails.log.info('Error while fetching', error);
          return ResponseService.errorResponse(res, sails.__('internal server error'), error);
        }
        return ResponseService.successResponse(res, {project_det:projectsListing});
      });

    },

    projectstatusUpdate: async (req, res) => {
        let projectId = req.param('projectId');
        if (!projectId) {
            return ResponseService.errorResponse(res, 'project id is required');
        }

        let data = _.pick((req.body || {}), [
            'is_active',
            'project_section_access',
            'company_additional_project_section_access',
        ]);
        sails.log.info('updating project status flags');
        let project = await sails.models.project.updateOne({id: projectId}).set(data);
        sails.log.info('updated project successful, id', project ? project.id : undefined);
        return ResponseService.successResponse(res, {project});
    },

    searchAllProject: async (req, res) => {
        let q = (req.query.q || '').toString().trim();
        if (!q || !q.length) {
            return ResponseService.errorResponse(res, 'search query is requires');
        }
        sails.log.info(`search project for optima, q: '${q}', user: ${req.user.id}`);
        // prepare search filters set
        let searchFilters = [
            {name: {contains: q}},
        ];
        if (!isNaN(q)) {
            searchFilters.push({id: q});
        }
        let projects = await sails.models.project_reader.find({
            select: [
                'id',
                'name',
                'project_initial',
                'use_prefix',
                'project_category',
                'parent_company',
                'contractor',
                'custom_field'
            ],
            where: {
                or: searchFilters,
            }
        }).sort([
            {name: 'ASC'},
            {id: 'ASC'},
        ]).limit(3);
        for (let i = 0; i < (projects || []).length; i++) {
            let {project_logo_file, companyName, employer} = await getCompanyInfo(projects[i], null, ['country_code']);
            projects[i]._contractor = employer;
        }
        sails.log.info('got projects, total', projects.length);
        return ResponseService.successResponse(res, {projects});
    },

    // @deprecated
    /*getSiteHealthAssessmentQuestions: async (req, res) => {
        sails.models.sitehealthassessmentq.find({
            is_active: 1
        }).sort([
            {category: 'ASC'},
            {order: 'ASC'},
        ]).exec(function allHealthAssessQues(queryError, health_assessment_questions) {
            if (queryError) {
                sails.log.info('Failed to fetch health_assessment_questions', queryError);
                return ResponseService.errorResponse(res, sails.__('internal server error'), queryError);
            }
            sails.log.info('got health_assessment_questions, total', health_assessment_questions.length);
            return ResponseService.successResponse(res, {health_assessment_questions: _.groupBy(health_assessment_questions || [], (i) => i.category)});
        });
    },*/

    getUserInfo: async (req, res) => {
        let email = (req.body.email || '').toString().trim();
        let verified = req.body.verified === true;

        if (!email.length) {
            return ResponseService.errorResponse(res, 'email id is required');
        }

        try {
            sails.log.info('Search user info, email:', email);

            let userResult = await sails.sendNativeQuery(`SELECT id, first_name, middle_name, last_name
                FROM users
                WHERE email::text ILIKE $1
                ${verified ? ' AND email_verified_on IS NOT NULL' : ''}`,
                [`${email}`]
            );
            let userInfo = {};
            if (typeOf(userResult.rows, 'array') && userResult.rows.length) {
                userInfo = userResult.rows[0];
                userInfo.name = getUserFullName(userInfo);
                sails.log.info(`got userInfo, total:`, userInfo);
            }
            return ResponseService.successResponse(res, {userInfo: userInfo});
        } catch (fetchError) {
            sails.log.info('Failed to fetch projects', fetchError);
            return ResponseService.errorResponse(res, sails.__('internal server error'), fetchError);
        }
    },

    // getAllSiteOfProject: async (req, res) => {
    //     //  get distinct `unit_name` for `req.project_optima_setting.host`
    // },

    // @todo: Add validation, only user having access to this project can ONLY request this
    // not being used on app side.
    // Not being used on web as well.
    /**
     * @deprecated: nowMs is not using project's timezone to create time object.
     * @param req
     * @param res
     * @returns {Promise<*>}
     */
    getOnSiteUsers: async (req, res) => {
        // RC 277: Roll Call feature
        let projectId = +req.param('projectId');
        let nowMs = +req.param('nowMs');
        if (!projectId || isNaN(projectId) || isNaN(nowMs)) {
            return ResponseService.errorResponse(res, 'project id & nowMs is required');
        }

        sails.log.info('get on site users for project', projectId, 'now', nowMs);
        try{
            let induction_requests = await sails.models.inductionrequest.find({
                select: ['optima_badge_number', 'status_code', 'project_ref', 'user_ref'],
                where: {
                    // optima_badge_number: {'!=': null},
                    status_code: [2, 6], // `approved` OR `In review` one only
                    project_ref: projectId
                }
            });
            induction_requests = await populateUserRefs(induction_requests, 'user_ref', []);

            let next_day = moment(nowMs).clone().add(1, 'd').format(dbDateFormat);
            let prev_day = moment(nowMs).clone().subtract(1, 'd').format(dbDateFormat);
            let day_of_year = moment(nowMs).format(dbDateFormat);

            let userIds = _.uniq(induction_requests.map(ir => ir.user_ref && ir.user_ref.id || 0));
            let profilePicIds = _.uniq(induction_requests.map(ir => ir.user_ref && ir.user_ref.profile_pic_ref || 0));

            // spatel: This covers users who did login before 00:00
            // fetching extra days's log
            let badge_logs = await getDailyTimeEventV2(projectId, prev_day, next_day, userIds);

            // Process logs to get, who is still online.
            induction_requests = (induction_requests || []).map(ir => {
                let user_logs = badge_logs.filter(r => (r.user_id &&  ir.user_ref && (+r.user_id === ir.user_ref.id)));

                ir.event_of_today = eventOfToday(day_of_year, user_logs, moment(nowMs));

                //ir.has_out_punch_today = ir.event_of_today && ir.event_of_today.clock_out || null;
                ir.is_still_on_site = isStillOnSite(ir.event_of_today); // && !ir.event_of_today.clock_out || false;

                return ir;
            }).filter(ir => ir.is_still_on_site);

            sails.log.info('getting employer info for : ' + userIds);
            let employers = await sails.models.userempdetail.find({
                where: {user_ref: userIds}
            });

            let profile_pic_refs = await sails.models.userfile.find({
                where: {id: profilePicIds}
            });

            sails.log.info('Merging employer info');
            induction_requests = induction_requests.map(ir => {
                let user_id = ir.user_ref && ir.user_ref.id || 0;
                let user_file_id = ir.user_ref && ir.user_ref.profile_pic_ref || 0;
                ir.employer = employers.find(e => e.user_ref === user_id) || null;
                ir.profile_pic_ref = profile_pic_refs.find(p => p.id === user_file_id) || null;
                return ir;
            });

            return ResponseService.successResponse(res, {
                induction_requests,
                //badge_logs,
            });
        } catch (apiError) {
            sails.log.info('Failed to fetch site users', apiError);
            return ResponseService.errorResponse(res, sails.__('internal server error'), apiError);
        }
    },

    // @return on-site users of any project(site-admin/company)
    getOnSiteUsersOfProject: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let nowMs = +req.param('nowMs', 0);
        let extraInclude = req.param('include', '').split(',');
        sails.log.info('get on site users for project', projectId, ',now', nowMs, 'and extraInclude', extraInclude);

        let project = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['id', 'name', 'project_category', 'custom_field']
        });
        let tz = (project && project.custom_field && project.custom_field.timezone) || fall_back_timezone;
        let is_default_project = (project && project.project_category === 'default');
        let now = nowMs ? momentTz(nowMs).tz(tz) : momentTz().tz(tz);
        let operative_users = await getOnSiteUsers(projectId, now, undefined, is_default_project, extraInclude);
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
        if (hasOnlySiteManagement) {
            let [userInfo] = await getInductionEmployerByUserIds([req.user.id], projectId, [6, 2, 4, 5]);
            operative_users = operative_users.filter(user => user.employer && user.employer.employer && user.employer.employer === userInfo.user_employer);
        }
        return successResponse(res, {operative_users: operative_users});
    },

    getOnSiteUsersGroupByCompany: async (req, res) => {
        sails.log.info("Request to fetch company breakdown.");
        let projectId = +req.param('projectId');
        sails.log.info('get on-site users for project', projectId);
        let project = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['id', 'name', 'project_category', 'custom_field']
        });
        let onSiteOperatives = await getCompanyBreakdown(req, project);
        sails.log.info(`Found ${onSiteOperatives.length} companies with on-site users.`);
        return successResponse(res, {on_site_operatives: onSiteOperatives});
    },

    downloadCompanyBreakdown: async (req, res) => {
        sails.log.info("Request to download company breakdown.");
        let projectId = +req.param('projectId');
        sails.log.info('get on-site users for project', projectId);
        let project = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['id', 'name', 'project_category', 'custom_field', 'contractor', 'parent_company']
        });
        let onSiteOperatives = await getCompanyBreakdown(req, project);
        sails.log.info(`Found ${onSiteOperatives.length} companies with on-site users.`);

        let { project_logo_file, companyName } = await getCompanyInfo(project);
        let tz = getProjectTimezone(project);
        let form_template = `pages/company-breakdown-form`;
        let title = 'Company Breakdown';
        let html = await sails.renderView(form_template, {
            title,
            onSiteOperatives,
            layout: false
        });
        let file_name = title+' Report-' + moment().format('DD-MM-YYYY');

        return await downloadPdfViaGenerator({
            req,
            res,
            html,
            tool: 'company-breakdown',
            file_name,
            heading_line: title,
            project_line: project.name,
            date_line: momentTz().tz(tz).format('DD/MM/YYYY HH:mm:ss'),
            additional_line: getUserFullName(req.user),
            logo_file: project_logo_file,
            has_cover: false,
            has_one_page: false,
            responseType:'url'
        });
    },

    getOnSiteUsersInnTime: async (req, res) => {
        let nowMs = +req.param('nowMs', 0);
        let user_ref = +req.param('user_id', 0);
        if (!nowMs) {
            return ResponseService.errorResponse(res, 'nowMs is required');
        }
        sails.log.info(`innTime: get on-site users for project : ${req.project.id}, nowMs: ${nowMs}`);
        let tz = (req.project && req.project.custom_field && req.project.custom_field.timezone) || fall_back_timezone;
        let is_default_project = (req.project && req.project.project_category === 'default');
        let operative_users = await getOnSiteUsers(req.project.id, momentTz(nowMs).tz(tz), user_ref, is_default_project);
        return successResponse(res, {operative_users: operative_users});
    },

    getOnSiteUsersCountInnTime: async (req, res) => {
        let projectId = req.project.id;
        let nowMs = +req.param('nowMs', 0);
        if (!nowMs) {
            return ResponseService.errorResponse(res, 'nowMs is required');
        }

        sails.log.info(`innTime: get on-site users count for project : ${req.project.id}, nowMs: ${nowMs}`);
        let tz = (req.project && req.project.custom_field && req.project.custom_field.timezone) || fall_back_timezone;
        let for_date = momentTz(nowMs).tz(tz);

        let time_log_for_day = await getDailyTimeEventForDay(projectId, for_date);
        let operative_users = time_log_for_day.filter(isStillOnSite);
        let userIds = _uniq((operative_users || []).map(ou => ou.user_id))
        sails.log.info(`innTime: on-site users count is ${userIds.length} for project ${req.project.id} and nowMs ${nowMs}`);
        return successResponse(res, {operative_users_count: userIds.length});
    },

    getOnSiteEntitiesOfProjectWithLocation: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let nowMs = +req.param('nowMs', 0);
        if (!projectId || !nowMs) {
            return ResponseService.errorResponse(res, 'project id & nowMs is required');
        }
        sails.log.info('get on site entities for project', projectId, 'now', nowMs);
        let time_log_for_day = await getDailyTimeEventForDay(projectId, moment(nowMs), [], false, null, false, true);
        let daily_logs = time_log_for_day.filter(isStillOnSite);
        sails.log.info('Total on site operatives', daily_logs.length);
        if(!daily_logs.length){
            return ResponseService.successResponse(res, {onsite_events: []});
        }

        // we have location info with geo-fence only...
        // So filter to use those only
        let location_based_events = (daily_logs || []).reduce((geo_fence_events, row) => {

            // No need to continue Loop, If there is no IN event.
            if (!row.recent_in) {
                return geo_fence_events;
            }
            // Find first clock IN & with geo-fence source
            for (let i = 0, len = (row.events || []).length; i < len; i++) {
                let event = (row.events || [])[i];
                if (event.event_type === EVENT_TYPE.IN &&
                    event.event_date_time === row.recent_in &&
                    event.source === VALID_SOURCES.GEO_FENCE) {
                    let e = {
                        event_date_time: +event.event_date_time,
                        user_ref: row.user_id,
                        // event_type: event.event_type // sails doesn't support search value with keyword `IN`
                    };
                    if(event.id){
                        e = {
                            id: event.id,
                        };
                    }
                    geo_fence_events.push(e);
                    break;
                }
            }
            return geo_fence_events;
        }, []);
        if(!location_based_events.length){
            // no location based event found.
            return ResponseService.successResponse(res, {onsite_events: []});
        }

        let onsite_events = await sails.models.usertimelog_reader.find({
            where: {
                event_type: [EVENT_TYPE.IN, EVENT_TYPE.INTERACTION],
                project_ref: +projectId,
                ...(location_based_events.length > 1 ? {or: location_based_events} : location_based_events.pop())
            },
            select: ['temperature', 'event_date_time', 'user_ref', 'user_location']
        });

        sails.log.info('Total location based On-Site events are', onsite_events.length);
        return ResponseService.successResponse(res, {onsite_events});
    },

    saveRollCall: async (req, res) => {
        let projectId = +req.param('projectId');
        if (!projectId || isNaN(projectId)) {
            return ResponseService.errorResponse(res, 'project id is required');
        }
        sails.log.info('Create request for roll-call, by', req.user.id, ' project:', projectId);
        let createRequest = _.pick((req.body || {}), [
            'present_users',
            'un_accounted_users',
            'present_visitors',
            'un_accounted_visitors',
            'comments'
        ]);

        // @todo: @vshal: convertArrayOfNumbersToObjects once all mobile app user have updated version
        createRequest.present_users = (createRequest.present_users.length) ? convertArrayOfNumbersToObjects(createRequest.present_users, 'user_ref') : [];
        createRequest.un_accounted_users = (createRequest.un_accounted_users.length) ? convertArrayOfNumbersToObjects(createRequest.un_accounted_users, 'user_ref') : [];
        createRequest.present_visitors = (createRequest.present_visitors.length) ? convertArrayOfNumbersToObjects(createRequest.present_visitors, 'visitor_ref') : [];
        createRequest.un_accounted_visitors = (createRequest.un_accounted_visitors.length) ? convertArrayOfNumbersToObjects(createRequest.un_accounted_visitors, 'visitor_ref') : [];
        createRequest.project_ref = projectId;
        createRequest.created_by = req.user.id;

        try {
            let roll_call = await sails.models.rollcall.create(createRequest);
            let notification_status = await notifyRollCallAdmin(roll_call, req.user);
            return ResponseService.successResponse(res, {roll_call, notification_status});
        } catch (createError) {
            sails.log.info('Error while creating record', createError);
            return ResponseService.errorResponse(res, sails.__('internal server error'), createError);
        }

    },

    /*Deprecated*/
    getGateByProject: async (req, res) => {
        let projectId = req.param('projectId');
        sails.log.info('Fetch project gates, id:', projectId);

        if (!projectId) {
            return ResponseService.errorResponse(res, 'project id is required');
        }
        let filter = {
            project_id: projectId
        };

        try {
            let project_gates = await sails.models.projectgate.find(filter)
                .sort([
                    { id: 'DESC' },
                ])
                .populate('project_gate_booking')
                .populate('route_map_file_id');
            let imageIds = [];
            let userIds = [];
            let createdByIds = [];
            let deliveryNoteIds = [];
            project_gates.map(project_gate => {
                (project_gate.project_gate_booking || []).map(booking => {
                    if (booking.id && booking.booking_closeout && booking.booking_closeout.closeout_by_user_ref) {
                        imageIds.push(...(booking.booking_closeout.delivery_note_images || []));
                        imageIds.push(...(booking.booking_closeout.additional_images || []));
                        userIds.push(booking.booking_closeout.closeout_by_user_ref);
                    }
                    if(booking.id && booking.delivery_notes_ref) {
                        deliveryNoteIds.push(booking.delivery_notes_ref);
                    }
                    if (booking.user_id) {
                        createdByIds.push(booking.user_id);
                    }
                    return booking;
                });
                return project_gate;
            });
            let imageFiles = [];
            if (imageIds.length) {
                imageFiles = await sails.models.userfile.find({id: imageIds});
            }
            let deliveryNotes = []
            if(deliveryNoteIds.length) {
                deliveryNotes = await sails.models.deliverynote_reader.find({id: deliveryNoteIds});
            }
            sails.log.info('booking closeout image ids', imageIds, 'Found files', imageFiles.length);

            sails.log.info('booking created by user ids', createdByIds, 'Found users', createdByIds.length);

            let users = [];
            let employers = [];
            if (userIds.length || createdByIds.length) {
                users = await sails.models.user_reader.find({
                    where: {id: _.uniq([...userIds, ...createdByIds])},
                    select: ['first_name', 'middle_name', 'last_name']
                });

                employers = await sails.models.userempdetail.find({
                    where: {user_ref: _.uniq(createdByIds)},
                    select: ['employer', 'user_ref']
                });
            }

            sails.log.info('Created by user employer Found employers', employers.length);

            sails.log.info('booking closeout by user ids', userIds, 'Found users', users.length);

            if (imageIds.length || userIds.length || createdByIds.length) {
                project_gates.map(project_gate => {
                    (project_gate.project_gate_booking || []).map(booking => {
                        if (booking.id && booking.booking_closeout && booking.booking_closeout.closeout_by_user_ref) {
                            booking.booking_closeout.delivery_note_images = imageFiles.filter(imgFile => (booking.booking_closeout.delivery_note_images || []).includes(imgFile.id));
                            booking.booking_closeout.additional_images = imageFiles.filter(imgFile => (booking.booking_closeout.additional_images || []).includes(imgFile.id));
                            let closeout_by_user_ref = users.find(user => booking.booking_closeout.closeout_by_user_ref == user.id);
                            booking.booking_closeout.user_fullname = (closeout_by_user_ref.id) ? getUserFullName(closeout_by_user_ref): '';
                        }
                        if(booking.id && booking.delivery_notes_ref) {
                            booking.delivery_notes_ref = deliveryNotes.find(d=>d.id === booking.delivery_notes_ref);
                        }

                        let user_ref = users.find(user => booking.user_id == user.id);
                        if (user_ref && user_ref.id) {
                            booking.created_by_user = user_ref;
                            booking.created_by_user.user_emp = employers.find(employer => user_ref.id == employer.user_ref);
                        }
                        return booking;
                    });
                    return project_gate;
                });
            }

            return ResponseService.successResponse(res, {project_gates});
        } catch (apiError) {
            sails.log.info('Failed to fetch project gates.', apiError);
            return ResponseService.errorResponse(res, sails.__('internal server error'), apiError);
        }
    },

    getGateByProjectV1: async (req, res) => {
        let projectId = req.param('projectId');
        let appVersion = req.param('version', 'v1');
        let selectedDate = (req.query.selected_date || '').toString().trim();
        selectedDate = moment(selectedDate, displayDateFormat_DD_MM_YYYY).format(dbDateFormat_YYYY_MM_DD);
        if (req.query.for_date) {
            selectedDate = (req.query.for_date || '').toString().trim();
            selectedDate = moment(selectedDate, dbDateFormat_YYYY_MM_DD).format(dbDateFormat_YYYY_MM_DD);
        }
        let status = req.query.status || null;
        sails.log.info(`Fetching project gates for project ${projectId} with booking on ${selectedDate}`);

        let project_gates = await sails.models.projectgate_reader.find({project_id: projectId})
            .sort([
                { id: 'DESC' },
            ])
            .populate('route_map_file_id');
        //attaching bookings with each gate
        let gateIds = (project_gates || []).map(gate => gate.id).filter(Boolean);
        let filter = {
            gate_id: gateIds,
            booking_date: selectedDate
        };
        if(status != null) {
            filter['status'] = status;
        }

        sails.log.info(filter);
        let allGateBookings = await sails.models.projectgatebooking_reader.find(filter).populate('delivery_notes_ref');

        for (let i = 0, len = project_gates.length; i < len; i++) {
            project_gates[i].project_gate_booking = (allGateBookings || []).filter(booking => booking.gate_id === project_gates[i].id);
        }

        let imageIds = [];
        let userIds = [];
        let createdByIds = [];
        let statusUpdateUserIds = [];
        project_gates.map(project_gate => {
            (project_gate.project_gate_booking || []).map(booking => {
                if (booking.id && booking.booking_closeout && booking.booking_closeout.closeout_by_user_ref) {
                    imageIds.push(...(booking.booking_closeout.delivery_note_images || []));
                    imageIds.push(...(booking.booking_closeout.additional_images || []));
                    userIds.push(booking.booking_closeout.closeout_by_user_ref);
                }

                if (booking.user_id) {
                    createdByIds.push(booking.user_id);
                }

                if (booking.status_details && booking.status_details.userId) {
                    statusUpdateUserIds.push(booking.status_details.userId);
                }

                //@todo: Vishal: This condition block can be removed by Jan 11, 2025.
                if (appVersion === 'v1' && booking.booking_date) {
                    booking.booking_date = dayjs(booking.booking_date, 'YYYY-MM-DD').format('DD-MM-YYYY');
                }
                return booking;
            });
            return project_gate;
        });
        let imageFiles = [];
        if (imageIds.length) {
            imageFiles = await sails.models.userfile_reader.find({id: imageIds});
        }

        sails.log.info('booking closeout image ids, Found files', imageFiles.length);

        sails.log.info('booking created by user ids', _.uniq(createdByIds));
        sails.log.info('booking status updated by user ids', _.uniq(statusUpdateUserIds));

        let users = [];
        let employers = [];
        let inductionRequestsList = []
        if (userIds.length || createdByIds.length) {
            users = await sails.models.user_reader.find({
                where: {id: _.uniq([...userIds, ...createdByIds])},
                select: ['first_name', 'middle_name', 'last_name']
            });

            employers = await sails.models.userempdetail_reader.find({
                where: {user_ref: _.uniq([...createdByIds, ...statusUpdateUserIds])},
                select: ['employer', 'user_ref']
            });
        }

        const userRefs = _.uniq([...createdByIds, ...statusUpdateUserIds]);

        if (userRefs.length) {
            inductionRequestsList = await getInductionEmployerByUserIds(userRefs, projectId, [2, 6]);
        }

        sails.log.info('Created by user employer Found employers', employers.length);

        sails.log.info('booking closeout by user ids', _.uniq(userIds));

        if (imageIds.length || userIds.length || createdByIds.length || statusUpdateUserIds.length) {
            project_gates.map(project_gate => {
                (project_gate.project_gate_booking || []).map(booking => {
                    if (booking.id && booking.booking_closeout && booking.booking_closeout.closeout_by_user_ref) {
                        booking.booking_closeout.delivery_note_images = imageFiles.filter(imgFile => (booking.booking_closeout.delivery_note_images || []).includes(imgFile.id));
                        booking.booking_closeout.additional_images = imageFiles.filter(imgFile => (booking.booking_closeout.additional_images || []).includes(imgFile.id));
                        let closeout_by_user_ref = users.find(user => booking.booking_closeout.closeout_by_user_ref === user.id);
                        booking.booking_closeout.user_fullname = (closeout_by_user_ref.id) ? getUserFullName(closeout_by_user_ref): '';
                    }

                    let user_ref = users.find(user => booking.user_id === user.id);
                    if (user_ref && user_ref.id) {
                        let createdByUserRef = inductionRequestsList.find(inductionReq => booking.user_id === inductionReq.user_ref);
                        booking.created_by_user = user_ref;
                        booking.created_by_user.user_emp = employers.find(employer => user_ref.id === employer.user_ref);
                        booking.created_by_user.induction_request = createdByUserRef;
                    }
                    if (booking.status_details && booking.status_details.userId) {
                        let statusUpdateUserRef = inductionRequestsList.find(inductionReq => booking.status_details.userId === inductionReq.user_ref);
                        booking.status_details.induction_request = statusUpdateUserRef;
                    }
                    return booking;
                });
                return project_gate;
            });
        }

        return ResponseService.successResponse(res, {project_gates});
    },

    // to retrieve any user's projects with a company other than loggedin user
    getCompanyProjectsForUser: async(req, res) => {
        let employerId = req.param('employerId');
        let userId = req.param('userId');
        let user = await sails.models.user_reader.findOne({
            where: {
                id: userId,
                is_active: 1
            },
            select: ['id']
        }).populate('user_roles');
        user.companyAdminRole = (user.user_roles || []).find(r => r.role === ROLES.COMPANY_ADMIN);
        user.companyProjectAdminRole = validateCPAPermissions(user.user_roles, employerId);
        let allProjectsNotAllowed = (!user.companyAdminRole && user.companyProjectAdminRole);

        let adminProjectIds = [];
        if(allProjectsNotAllowed) {
            adminProjectIds = user.companyProjectAdminRole;
            sails.log.info('User has limited permission to projects:', adminProjectIds);
        }

        let employerInfo = await sails.models.createemployer.findOne({
            where: {id: employerId},
            select: ['name']
        });
        let allowedProjectIds = [...adminProjectIds];
        let additionalProjectIds = [];
        if(employerInfo && employerInfo.id) {
            let employer_name = employerInfo.name;
            let indRequests = await sails.models.inductionrequest_reader.find({where: {status_code: 2, user_ref: userId}});
            let projectIds = [];
            indRequests.filter(item => {
                if(item && item.additional_data.employment_detail.employer === employer_name && (!allProjectsNotAllowed || allowedProjectIds.includes(item.project_ref))) {
                    projectIds.push(item.project_ref);
                }
            });
            additionalProjectIds = [...new Set(projectIds)];
            let company_project_filter = {parent_company: employerId,project_category: 'company-project'};
            if(allProjectsNotAllowed) {
                company_project_filter = {
                    ...company_project_filter,
                    id: allowedProjectIds
                };
            }
            sails.log.info('company projects filter is:', company_project_filter);
            let additional_projects = await sails.models.project_reader.find({id: additionalProjectIds});
            let employer_projects = await sails.models.project_reader.find(company_project_filter);

            return ResponseService.successResponse(res, {employer_projects, employer_name, additional_projects});
        }
    },

    getCompanyProjects: async(req, res) =>{
        let employerId = +req.param('employerId', 0);
        let include_disabled = req.query['include_disabled'];
        sails.log.info('Fetch projects by employer id:', employerId);

        // not having company admin role
        // AND
        // is having company project admin role.
        let allProjectsNotAllowed = (!req.user.companyAdminRole && req.user.companyProjectAdminRole);
        let allowedProjectIds = [];
        if(allProjectsNotAllowed){
            allowedProjectIds = req.user.companyProjectAdminRole;
            sails.log.info('User has limited permission to projects:', allowedProjectIds);
        }

        let employerInfo = await sails.models.createemployer_reader.findOne({
            where: {id: employerId},
            select: ['name']
        });

        if(employerInfo && employerInfo.id) {
            let employer_name = employerInfo.name;

            let company_project_filter = {parent_company: employerId,is_active: 1,project_category:'company-project'};
            let additional_projects_filter = {contractor: employer_name}; // Inherited projects
            if(include_disabled === 'false') {
                company_project_filter = {
                    ...company_project_filter,
                    disabled_on: null
                }
                additional_projects_filter = {
                    ...additional_projects_filter,
                    is_active: 1,
                    disabled_on: null
                }
            }
            if(allProjectsNotAllowed){
                company_project_filter = {
                    ...company_project_filter,
                    id: allowedProjectIds
                };
            }
            sails.log.info('company projects filter is:', company_project_filter);
            let additional_projects = await sails.models.project_reader.find(additional_projects_filter);
            let employer_projects = await sails.models.project_reader.find(company_project_filter);

            // Skip disabled projects from visible projects list
            sails.log.info('Skipping disabled projects from visible projects list');
            additional_projects = additional_projects.filter(p => p.custom_field && (!p.custom_field.disable || !p.custom_field.disable.company_portal));
            employer_projects = employer_projects.filter(p => p.custom_field && (!p.custom_field.disable || !p.custom_field.disable.company_portal));
            let data = {
                employer_name: employer_name,
                projects: {
                    employer_projects: employer_projects,
                    additional_projects: additional_projects
                }
            }
            if(include_disabled === 'true') {
                let add_projects = additional_projects.filter(p=> p.is_active);
                let disabled_add_projects = additional_projects.filter(p=> !p.is_active);
                let emp_projects = employer_projects.filter(p=> p.disabled_on === null);
                let disabled_emp_projects = employer_projects.filter(p=> p.disabled_on != null);
                data = {
                    employer_name: employer_name,
                    projects: {
                        employer_projects: emp_projects,
                        employer_name: employer_name,
                        additional_projects: add_projects
                    },
                    disabled_projects: {
                        employer_projects: disabled_emp_projects,
                        additional_projects: disabled_add_projects
                    }
                }
            }

            let liveProjectIds = [];
            let disabledProjectIds = [];
            (data.projects.employer_projects || []).map(project => {
                liveProjectIds.push(project.id);
            });

            (data.projects.additional_projects || []).map(project => {
                liveProjectIds.push(project.id);
            });

            if(data && data.disabled_projects) {
                (data.disabled_projects.employer_projects || []).map(project => {
                    disabledProjectIds.push(project.id);
                });

                (data.disabled_projects.additional_projects || []).map(project => {
                    disabledProjectIds.push(project.id);
                });
            }

            let projectIds = [...liveProjectIds, ...disabledProjectIds];
            let inspectionTours = await sails.models.projectinspectiontour_reader.find({
                where: {project_ref: projectIds},
                select: ['record_id', 'createdAt', 'project_ref']
            });
            data.hasInspectionTours = !!(inspectionTours.length);

            let closeCalls = await sails.models.closecall_reader.find({
                where: {project_ref: projectIds},
                select: ['project_ref']
            });
            data.hasCloseCalls = !!(closeCalls.length);

            let goodCalls = await sails.models.goodcall_reader.find({
                where: {project_ref: projectIds, data_type: "good-call"},
                select: ['project_ref']
            });
            data.hasGoodCalls = !!(goodCalls.length);

            let observations = await sails.models.goodcall_reader.find({
                where: {project_ref: projectIds, data_type: "observation"},
                select: ['project_ref']
            });
            data.hasObservations = !!(observations.length);

            let liveProjectInspectionTours = (inspectionTours || []).filter(inspectionTour => liveProjectIds.includes(inspectionTour.project_ref));
            let disabledProjectInspectionTours = (inspectionTours || []).filter(inspectionTour => disabledProjectIds.includes(inspectionTour.project_ref));

            data.liveProFirstInspectionCreatedAt = (liveProjectInspectionTours.length) ? liveProjectInspectionTours[0].createdAt : 0;
            data.disabledProFirstInspectionCreatedAt = (disabledProjectInspectionTours.length) ? disabledProjectInspectionTours[0].createdAt : 0;

            //fetching company inspection builders with activated dashboard
            data.company_inspection_builders = await sails.models.inspectionbuilder_reader.find({
                where: {
                    company_ref: employerId,
                    ib_type: 'company',
                    dashboard_enabled: true,
                    enabled: true
                },
                select: ['id', 'ib_title', 'scoring_system', 'createdAt']
            });

            //fetching feature setting for company side nav
            sails.log.info('Fetching company features status.');
            let companySetting = await sails.models.companysetting_reader.findOne({
                where: {
                    name: 'features_status',
                    company_ref: employerId
                },
                select: ['value']
            });
            data.company_features_status = (companySetting) ? companySetting.value : {};

            sails.log.info('Fetched company inspection builders.', data.company_inspection_builders.length);

            return ResponseService.successResponse(res, data);
        }
        sails.log.info('Failed to fetch employer projects.');
        return ResponseService.errorResponse(res, sails.__('internal server error'), 'Failed to fetch employer projects.');
    },

    updateProjectPartially: async (req, res) => {
        let projectId = req.param('projectId');
        sails.log.info('update project request', projectId);

        let updateRequest = _.pick((req.body || {}), [
            'company_additional_project_section_access',
            'custom_field',
            'disabled_on',
        ]);
        // Need to support complete project updating here
        updateRequest.edited_by = req.user.id;
        sails.log.info('update project with', updateRequest);

        try {
            let projectExistingInfo = await sails.models.project.findOne({
                where: {id: projectId},
                select: ['custom_field']
            });

            updateRequest.custom_field = {...projectExistingInfo.custom_field, ...updateRequest.custom_field};

            let project = await sails.models.project.updateOne({id: projectId}).set(updateRequest);
            sails.log.info('project has been updated, id', project ? project.id : undefined);
            return ResponseService.successResponse(res, {project});
        } catch(failure) {
            sails.log.info('Failed to updated project.', failure);
            return ResponseService.errorResponse(res, sails.__('internal server error'), failure);
        }
    },

    //LAST CONSIDERED MIGRATION NUMBER: 298, DATE: 15-July-2024
    deleteProject: async (req, res) => {
        let projectId = +req.param('projectId');

        sails.log.info("[ADMIN] Processing to delete/get project", projectId);
        let delete_as_well = (req.query.delete === 'yes');

        let project = await sails.models.project.findOne({
            where: {id: projectId}, select: ['id', 'is_active']
        });

        if (!project || project.is_active) {
            sails.log.info("Can't delete active/published project.");
            return ResponseService.errorResponse(res, "Can't delete active/published project.");
        }

        const PROJECT_INDIRECT_LINKED = [
            {
                "model": "clerkofworkcomments", "table": "clerk_of_work_comments", "col": "project_ref",
                // "pivot_table": "clerk_of_works", "pivot_by_col": "cow_id",
                "count": `SELECT count(*) as c FROM clerk_of_work_comments where cow_id IN (select id from clerk_of_works where project_ref = $1);`,
                "deleteSql": `DELETE FROM clerk_of_work_comments where cow_id IN (select id from clerk_of_works where project_ref = $1);`
            },
            {
                "model": "longmedicationdetail", "table": "long_medication_detail", "col": "project_ref",
                "count": `SELECT count(*) as c FROM long_medication_detail where induction_ref IN (select id from induction_request where project_ref = $1);`,
                "deleteSql": `DELETE FROM long_medication_detail where induction_ref IN (select id from induction_request where project_ref = $1);`
            },
            {
                "model": "invitationlog", "table": "invitation_log", "col": "source_component",
                getParamFn: (id) => [`%:${id}`],
                "count": `SELECT count(*) as c FROM invitation_log where source_component LIKE $1;`,
                "deleteSql": `DELETE FROM invitation_log where source_component LIKE $1;`
            },
            {
                "model": "usersign", "table": "user_sign", "col": "project_ref",
                "count": `SELECT count(*) as c FROM user_sign where briefing_ref IN (select id from tool_briefings where project_ref = $1);`,
                "deleteSql": `DELETE FROM user_sign where briefing_ref IN (select id from tool_briefings where project_ref = $1);`
            },
            {
                "model": "assetequipmentinspection", "table": "asset_equipment_inspection", "col": "project_ref",
                "count": `SELECT count(*) as c FROM asset_equipment_inspection where equipment_ref IN (select id from project_asset_equipment where project_ref = $1);`,
                "deleteSql": `DELETE FROM asset_equipment_inspection where equipment_ref IN (select id from project_asset_equipment where project_ref = $1);`
            },
            {
                "model": "assettemporaryworkinspection", "table": "asset_temporary_work_inspection", "col": "project_ref",
                "count": `SELECT count(*) as c FROM asset_temporary_work_inspection where temporary_work_ref IN (select id from project_asset_temporary_work where project_ref = $1);`,
                "deleteSql": `DELETE FROM asset_temporary_work_inspection where temporary_work_ref IN (select id from project_asset_temporary_work where project_ref = $1);`
            },
            {
                "model": "assetvehicleinspection", "table": "asset_vehicle_inspection", "col": "project_ref",
                "count": `SELECT count(*) as c FROM asset_vehicle_inspection where vehicle_ref IN (select id from project_asset_vehicles where project_ref = $1);`,
                "deleteSql": `DELETE FROM asset_vehicle_inspection where vehicle_ref IN (select id from project_asset_vehicles where project_ref = $1);`
            },
            // {"model": "weatherlog", "table": "weather_log", "col": "project_ref"},
        ];

        const PROJECT_LINKED_ENTITIES = [
            {"model": "projectdeclaration", "table": "project_declaration", "col": "project_id"},
            {"model": "projectinductionslot", "table": "project_induction_slot", "col": "project_ref"},
            {"model": "metadailyactivities", "table": "meta_project_daily_activities", "col": "project_ref"},
            {"model": "rollcall", "table": "roll_call", "col": "project_ref"},
            {"model": "clerkofworks", "table": "clerk_of_works", "col": "project_ref"},
            {"model": "closecall", "table": "close_call", "col": "project_ref"},
            {"model": "deliverynote", "table": "delivery_note", "col": "project_ref"},
            {"model": "goodcall", "table": "good_call", "col": "project_ref"},
            {"model": "inductionrequest", "table": "induction_request", "col": "project_ref"},
            {"model": "inductionquestions", "table": "induction_questions", "col": "project_ref"},
            {"model": "userconductcard", "table": "user_conduct_card", "col": "project_ref"},
            {"model": "progressphotos", "table": "progress_photos", "col": "project_ref"},
            {"model": "projectdailyactivities", "table": "project_daily_activities", "col": "project_ref"},
            {"model": "projectgate", "table": "project_gate", "col": "project_id"},
            {"model": "projectgatebooking", "table": "project_gate_booking", "col": "project_id"},
            {"model": "projectincidentreport", "table": "project_incident_report", "col": "project_ref"},
            {"model": "projectinspectiontour", "table": "project_inspection_tour", "col": "project_ref"},
            {"model": "projectpowra", "table": "project_powra", "col": "project_ref"},
            {"model": "projecttaskbriefings", "table": "project_task_briefings", "col": "project_ref"},
            {"model": "take5s", "table": "take_5s", "col": "project_ref"},
            {"model": "toolboxtalks", "table": "toolbox_talks", "col": "project_ref"},
            {"model": "touchbytelog", "table": "touch_byte_log", "col": "project_ref"},
            {"model": "toolbriefings", "table": "tool_briefings", "col": "project_ref"},
            {"model": "userfavoriteproject", "table": "user_favorite_project", "col": "project_ref"},
            {"model": "userworkingshift", "table": "user_working_shift", "col": "project_ref"},
            {"model": "visitor", "table": "visitor", "col": "project_ref"},
            {"model": "optimasetting", "table": "optima_setting", "col": "project_ref"},
            {"model": "projectassetequipment", "table": "project_asset_equipment", "col": "project_ref"},
            {"model": "projectassetvehicles", "table": "project_asset_vehicles", "col": "project_ref"},
            {"model": "projectassettemporarywork", "table": "project_asset_temporary_work", "col": "project_ref"},
            {"model": "projectworkpackageplans", "table": "project_work_package_plans", "col": "project_ref"},
            {"model": "metaprojectplantmachinery", "table": "meta_project_plant_machinery", "col": "project_ref"},
            {"model": "qualitychecklist", "table": "quality_checklist", "col": "project_ref"},
            {"model": "checklistinspection", "table": "checklist_inspection", "col": "project_ref"},
            {"model": "inspectionbuilder", "table": "ib_checklist", "col": "project_ref"},
            {"model": "inspectionbuilderreport", "table": "ib_inspection_report", "col": "project_ref"},
            {"model": "projectfatigueviolations", "table": "project_fatigue_violations", "col": "project_ref"},
            {"model": "projectsitemessaging", "table": "project_site_messaging", "col": "project_ref"},
            {"model": "projectpermitconfig", "table": "project_permit_config", "col": "project_ref"},
            {"model": "permitrequest", "table": "permit_request", "col": "project_ref"},
            {"model": "projectrams", "table": "project_rams", "col": "project_ref"},
            {"model": "projectresourceplan", "table": "project_resource_plan", "col": "project_ref"},
            {"model": "projecttimesheet", "table": "project_timesheet", "col": "project_ref"},
            {"model": "vehicleevent", "table": "vehicle_event", "col": "project_ref"},
            {"model": "vehicletimelog", "table": "vehicle_time_log", "col": "project_ref"},
            {"model": "vehicledailylog", "table": "vehicle_daily_log", "col": "project_ref"},
            {"model": "vehicle", "table": "vehicle", "col": "project_ref"},
            {"model": "badgeevent", "table": "badge_event", "col": "project_ref"},
            {"model": "usertimelog", "table": "user_time_log", "col": "project_ref"},
            {"model": "userdailylog", "table": "user_daily_log", "col": "project_ref"},

            // should be deleted at last
            {"model": "projectsetting", "table": "project_setting", "col": "project_ref"},
        ]

        if(!delete_as_well) {
            sails.log.info(`[ADMIN] Total ${PROJECT_LINKED_ENTITIES.length} entities to get count, no-delete`);
            let associatedRecords = {};
            for (let i = 0; i < PROJECT_INDIRECT_LINKED.length; i++) {
                let {model, count, getParamFn} = PROJECT_INDIRECT_LINKED[i];
                sails.log.info(`[ADMIN] counting entity: "${model}"`);
                let params = getParamFn ? getParamFn(project.id) : [project.id];
                let {rows: count_rows} = await sails.sendNativeQuery(count, params);
                associatedRecords[model] = ((count_rows || []).pop() || {}).c || 0;
            }

            for (let i = 0; i < PROJECT_LINKED_ENTITIES.length; i++) {
                let {model, table, col} = PROJECT_LINKED_ENTITIES[i];
                sails.log.info(`[ADMIN] counting entity: "${model}"`);
                associatedRecords[model] = await sails.models[model].count({
                    where: {[col]: project.id},
                    // select: ['id']
                });
            }

            associatedRecords = await sortProperties(associatedRecords);

            sails.log.info('[ADMIN] Responding with count, no-delete', associatedRecords);

            return ResponseService.successResponse(res, {associatedRecords: associatedRecords});
        } else {
            sails.log.info('[ADMIN] Deleting project and associated records.');
            let associatedRecords = {};
            for (let i = 0; i < PROJECT_INDIRECT_LINKED.length; i++) {
                let {model, deleteSql, getParamFn} = PROJECT_INDIRECT_LINKED[i];
                sails.log.info(`[ADMIN] Deleting entity: "${model}"`);
                let params = getParamFn ? getParamFn(project.id) : [project.id];
                let {rows: deleted_rows} = await sails.sendNativeQuery(deleteSql, params);
                associatedRecords[model] = (deleted_rows || []).length;
            }

            for (let i = 0; i < PROJECT_LINKED_ENTITIES.length; i++) {
                let {model, table, col} = PROJECT_LINKED_ENTITIES[i];
                sails.log.info(`[ADMIN] Deleting entity: "${model}"`);
                let deletedRecords = await sails.models[model].destroy({
                    where: {[col]: project.id},
                });
                if(model === 'projectgate' && deletedRecords.length){
                    await destroyReplicatedGates(deletedRecords, projectId);
                }
                associatedRecords[model] = deletedRecords.length;
            }
            sails.log.info(`[ADMIN] Deleting entity: "project" where id: ${project.id}`);
            await sails.models.project.destroy({
                id: projectId
            });

            sails.log.info('[ADMIN] Responding with count, delete', associatedRecords);

            return ResponseService.successResponse(res, {associatedRecords});
        }
    },

    addUpdateGateSupervisors: async (req, res) => {
        let projectId = +req.param('projectId');
        let supervisorIds = req.param('userIds');
        sails.log.info('updating supervisors, project id', projectId, 'supervisorIds ', supervisorIds);

        let project = await sails.models.project.updateOne({id: projectId}).set({gate_supervisors: supervisorIds});
        if (project && project.id) {
            sails.log.info('supervisors has been updated.');
            project.gate_supervisors = await expandUsersByIds(project.gate_supervisors, ['id', 'first_name', 'middle_name', 'last_name', 'email']);
            return ResponseService.successResponse(res, {projectGateSupervisors: project});
        }

        sails.log.info('Failed to updated gate supervisors.');
        return ResponseService.errorResponse(res, 'Failed to updated gate supervisors.');
    },

    getGateSupervisors: async (req, res) => {
        let projectId = +req.param('projectId');
        sails.log.info('fetching supervisors, project id', projectId);

        let project = await sails.models.project.findOne({
            where: {id: projectId},
            select: ['id', 'name', 'gate_supervisors']
        });
        if (project && project.id) {
            sails.log.info('supervisors has been fetched.');
            project.gate_supervisors = await expandUsersByIds(project.gate_supervisors, ['id', 'first_name', 'middle_name', 'last_name', 'email']);
            return ResponseService.successResponse(res, {projectGateSupervisors: project});
        }
        sails.log.info('Failed to fetch gate supervisors.');
        return ResponseService.errorResponse(res, 'Failed to fetch gate supervisors.');
    },

    /**
     * @deprecated, got a better alternative of it, InductionController.getProjectInductedUsers
     * @param req
     * @param res
     * @returns {Promise<*>}
     */
    getProjectInductedUsers: async(req, res) => {
        return await projectInductedUsers(req, res);
    },

    getProjectInductedUsersCA: async(req, res) => {
        return await projectInductedUsers(req, res);
    },

    enableSmartSheetForProjectId: async(req, res) => {
        let projectId = +req.param('projectId');
        let existingWorkspaceId = +req.param('workspace_id', 0);
        let SS_API_KEY = req.param('SS_API_KEY', 0);

        if(!projectId || !SS_API_KEY){
            return ResponseService.errorResponse(res, 'project id & token is required');
        }

        sails.log.info(`Enable Smart sheet for ${projectId}, workspace_id: ${existingWorkspaceId}`);
        let projectSettingOrError = await enableSmartSheetForProject(projectId, SS_API_KEY, existingWorkspaceId);
        if(projectSettingOrError.error){
            return sendResponse(res, projectSettingOrError);
        }
        let {id, name, project_ref} = projectSettingOrError;
        return successResponse(res, {project_setting: {id, name, project_ref}});
    },

    getProjectWeatherInfo: async (req, res) => {
        let projectId = +req.param('projectId');
        let request_date = moment((req.query.for_date || '--'), dbDateFormat);
        if (!request_date.isValid()) {
            sails.log.info('Invalid Request');
            return errorResponse(res, 'for_date query parameter not found');
        }
        let day_of_yr_today = request_date.format(dbDateFormat);

        let project = await sails.models.project.findOne({
            select: ['id', 'weather_location_key'],
            where: {
                id: projectId,
                weather_location_key: {'!=': null}
            }
        });
        if(!project || !project.weather_location_key){
            sails.log.info('Weather location key not found');
            return successResponse(res, {weather_log: null, message: 'Weather location key not found'});
        }
        sails.log.info(`get Weather log, project: ${projectId}, date: ${day_of_yr_today}`);
        let existingOne = await sails.models.weatherlog.findOne({
            location_key: project.weather_location_key,
            day: day_of_yr_today,
            type: 'future-forecast',
            source: 'accuweather',
        });
        return successResponse(res, {weather_log: ((existingOne && existingOne.forecast) || null)});

    },

    getClockedInUsersOfProject: async (req, res) => {
        let project_id = req.param('projectId');
        sails.log.info('get all clocked IN users of project', project_id);
        let rawResult = await sails.sendNativeQuery(`SELECT distinct user_ref
                                                     FROM user_daily_log
                                                     WHERE (project_ref = $1 AND user_ref IS NOT null)`,
            [project_id]
        );
        let user_ids = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows.map(r => r.user_ref) : [];
        sails.log.info('fetch users info of IDs: ', user_ids);
        let project_clocked_in_users = [];
        if (user_ids.length) {
            project_clocked_in_users = await sails.models.user_reader.find({
                where: {'id': user_ids},
                select: ['email', 'first_name', 'last_name', 'parent_company']
            });

            project_clocked_in_users = project_clocked_in_users.sort((a,b) => (a.first_name > b.first_name) ? 1 : ((b.first_name > a.first_name) ? -1 : 0));
        }

        sails.log.info(`Found ${project_clocked_in_users.length} users who clocked IN on the project ${project_id}`);
        return successResponse(res, {project_clocked_in_users});
    },

    getProjectInductionBookingSetting: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        sails.log.info(`get INDUCTION_SLOTS_INFO, project: ${projectId}`);
        let induction_slots_info = await sails.models.projectsetting_reader.findOne({
            where: {project_ref: projectId, name: INDUCTION_SLOTS_INFO},
        });
        return successResponse(res, {induction_slots_info: (induction_slots_info && induction_slots_info.value)});
    },

    saveProjectInductionBookingSetting: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let updateRequest = _.pick((req.body || {}), [
            'status',
            'value',
            'deleted',
        ]);
        sails.log.info(`save INDUCTION_SLOTS_INFO, project: ${projectId}, feature status: ${updateRequest.status}`);
        let induction_slots_info = await sails.models.projectsetting_reader.findOne({
            where: {project_ref: projectId, name: INDUCTION_SLOTS_INFO},
        });

        let result = {};
        if(!updateRequest.status){
            // feature disabled
            if (induction_slots_info && induction_slots_info.id) {
                await sails.models.projectsetting.destroy({
                    id: induction_slots_info.id,
                    project_ref: projectId
                });
            }
            await updateProjectCustomField(projectId, {
                induction_slot_booking: false,
            });

            // delete all future slots
            await sails.models.projectinductionslot.destroy({
                project_ref: projectId,
                slot_date_time: {
                    '>=': dayjs().unix(),
                },
            });
            return successResponse(res, {});
        }

        // feature is active.
        if (induction_slots_info && induction_slots_info.id) {
            result = await sails.models.projectsetting.updateOne({id: induction_slots_info.id}).set({value: updateRequest.value});
        } else {
            result = await sails.models.projectsetting.create({
                project_ref: projectId,
                name: INDUCTION_SLOTS_INFO,
                value: updateRequest.value,
            });
        }
        let projectInfo = await updateProjectCustomField(projectId, {
            induction_slot_booking: true,
        });

        if(updateRequest.deleted && updateRequest.deleted.length){
            let deleted_one = await sails.models.projectinductionslot.destroy({
                project_ref: projectId,
                id: (updateRequest.deleted || []),
            });
            sails.log.info(`Removed existing slots, project: ${projectId}, count: ${deleted_one.length}`);
        }
        let tz = (projectInfo && projectInfo.custom_field && projectInfo.custom_field.timezone) || fall_back_timezone;
        populateInductionEmptySlots(projectId, dayjs(), tz, 28).catch(sails.error);

        return successResponse(res, {result});
    },

    saveProjectResourcePlans: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        sails.log.info(`Saving project resource plans ${projectId}`);
        let {validationError, payload: {resource_plans}} = ProjectValidator.saveProjectResourcePlans(req);
        if(validationError){
            return errorResponse(res, 'All fields required.', {validationError});
        }
        for (let i = 0; i < resource_plans.length; i++) {
            resource_plans[i].project_ref = projectId;
        }
        let saved_resource_plans = await sails.models.projectresourceplan.createEach(resource_plans);
        sails.log.info(`Saved project resource plans ${saved_resource_plans.length}`);
        return successResponse(res, {resource_plans: saved_resource_plans});
    },

    bulkUploadProjectResourcePlans: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        sails.log.info(`Bulk import project resource plans ${projectId}`);
        let {validationError, payload: {resource_plans}} = ProjectValidator.bulkUploadProjectResourcePlans(req);
        if(validationError){
            return errorResponse(res, 'All fields required.', {validationError});
        }
        let unique_users = _uniq(resource_plans.map(r => r.resource_ref));
        let inductions = await sails.models.inductionrequest_reader.find({
            select: ['user_ref', 'additional_data', 'travel_time', 'status_code'],
            where: {
                project_ref: projectId,
                user_ref: unique_users
            },
        });
        let create_request = [];
        for (let i = 0; i < resource_plans.length; i++) {
            let p = resource_plans[i];
            sails.log.info('Processing', JSON.stringify(p));
            let day_of_yr = moment((p.day_of_yr || '--'), displayDateFormat_DD_MM_YYYY);
            let day_of_yr2 = moment((p.day_of_yr || '--'), dbDateFormat_slash_DDMMYYYY);
            if (!day_of_yr.isValid() && !day_of_yr2.isValid()) {
                sails.log.info('Invalid day of year provided');
                return errorResponse(res, 'Import failed, Invalid day_of_yr provided.',{p});
            }
            day_of_yr = (day_of_yr.isValid() ? day_of_yr : day_of_yr2);
            let [ps_h, ps_m] = p.planned_start.split(":").map( val => parseInt(val) );
            let p_start = day_of_yr.clone().set('hour', ps_h).set('minute', ps_m);

            let [pe_h, pe_m] = p.planned_end.split(":").map( val => parseInt(val) );
            let p_end = day_of_yr.clone().set('hour', pe_h).set('minute', pe_m);
            if(p_end < p_start){
                sails.log.info('Changing day for end of shift');
                p_end = p_end.add(1, 'day');
            }

            let ir = inductions.find(r => r.user_ref === p.resource_ref) || {};
            if(!ir.user_ref){
                sails.log.info(`Import failed, Induction record not found for ID: ${p.resource_ref}`);
                return errorResponse(res, 'Import failed, Induction record not found for ID: ' + p.resource_ref);
            }
            let travelTimeOverride = getActiveTravelTime(ir, day_of_yr);
            let travel_minutes = getTotalTravelDuration(travelTimeOverride.travel_time || {});
            if(!travel_minutes){
                sails.log.info(`Import failed, Induction record of user ${p.resource_ref} doesn't have travel info`);
                return errorResponse(res, `Import failed, Induction record of user ${p.resource_ref} doesn't have travel info`);
            }
            create_request.push({
                resource_ref: p.resource_ref,
                day_of_yr: day_of_yr.format(dbDateFormat_YYYY_MM_DD),
                planned_start: p_start.unix(),
                planned_end: p_end.unix(),
                travel_minutes: travel_minutes,
                project_ref: projectId
            });
        }
        // sails.log.info('final create request is', JSON.stringify(create_request, null, 4));
        try {
            let saved_resource_plans = await sails.models.projectresourceplan.createEach(create_request);
            sails.log.info(`Saved project resource plans ${saved_resource_plans.length}`);
            return successResponse(res, {resource_plans: saved_resource_plans});
        }catch (e) {
            if(e.code === 'E_UNIQUE'){
                sails.log.info(`Import failed, Request generating duplicate records`);
                return errorResponse(res, 'Import failed, Request generating duplicate records');
            }else {
                throw e;
            }
        }
    },

    downloadResourcePlannerExport: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let from_date = moment((req.body.from_date || '--'), dbDateFormat);
        let to_date = moment((req.body.to_date || '--'), dbDateFormat);

        if (!from_date.isValid() || !to_date.isValid()) {
            sails.log.info('Invalid Request receive for resource planner export');
            return ResponseService.errorResponse(res, 'Invalid date filter provided.',{from_date, to_date});
        }
        let from_date_str = from_date.format(dbDateFormat);
        let to_date_str = to_date.add(1, 'days').format(dbDateFormat);
        sails.log.info(`download resource planner export from, to: ${from_date_str}  <= date < ${to_date_str}`);
        let project = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: [
                'project_number', 'name', 'fatigue_management_status', 'site_hours_daily', 'total_hours_daily'
            ]
        });
        let processed_resource_plans = [];
        let resources = await sails.models.projectresourceplan_reader.find({
            where: {
                project_ref: projectId,
                day_of_yr: {
                    '>=': from_date_str,
                    '<': to_date_str
                },
            },
            sort: ['day_of_yr desc','planned_start desc'],
        });

        if(resources.length){
            let {fatigue_management_status, site_hours_daily, total_hours_daily} = project;
            let all_unique_users = _uniq(resources.map(r => r.resource_ref));
            let users = await sails.models.user_reader.find({
                where:{
                    id: all_unique_users,
                },
                select: ['first_name', 'middle_name', 'last_name']
            });
            let days_group = _groupBy(resources, (r) => r.day_of_yr);
            let unique_days = Object.keys(days_group);

            sails.log.info(`all unique days are`, JSON.stringify(unique_days));

            for (let i = 0; i < unique_days.length; i++) {
                let target_day = unique_days[i];
                let unique_users_of_day = _uniq((days_group[target_day] || []).map(r => r.resource_ref));

                let time_logs = await sails.models.userdailylog_reader.find({
                    where: {
                        project_ref: projectId,
                        day_of_yr: target_day,
                        user_ref: unique_users_of_day
                    },
                    select: ['first_in', 'last_out', 'total_in_sec', 'user_ref']
                });
                let planned_resources = (days_group[target_day]).map(r => {
                    let travel_seconds = ((+r.travel_minutes || 0) * 60);
                    let u = users.find(u => u.id === r.resource_ref) || {};
                    let utl = time_logs.find(t => t.user_ref === r.resource_ref) || {};
                    let planned_total = (r.planned_start && r.planned_end) ? ((+r.planned_end - (+r.planned_start)) + travel_seconds) : null;
                    let travel_minutes = (r.travel_minutes ? PdfUtil.showDurationAsHours(travel_seconds) : null);
                    let violation = false;
                    if(fatigue_management_status && utl.total_in_sec){
                        if(site_hours_daily && ((+utl.total_in_sec/3600) > site_hours_daily)){
                            violation = true;
                        }else if(total_hours_daily && (((((+utl.total_in_sec) + travel_seconds))/3600) > total_hours_daily)){
                            violation = true;
                        }
                    }
                    return {
                        // ...r,
                        day_of_yr: moment(r.day_of_yr, dbDateFormat).format(displayDateFormat_DD_MM_YYYY),
                        name: getUserFullName(u),
                        planned_start: r.planned_start ? dayjs.unix(+r.planned_start).format('HH:mm:ss') : null,
                        planned_end: r.planned_end ? dayjs.unix(+r.planned_end).format('HH:mm:ss') : null,
                        travel_minutes,
                        planned_total: planned_total ? PdfUtil.showDurationAsHours(planned_total) : null,
                        actual_start: utl.first_in ? dayjs.unix(+utl.first_in).format('HH:mm:ss') : null,
                        actual_end: utl.last_out ? dayjs.unix(+utl.last_out).format('HH:mm:ss') : null,
                        travel_minutes2: travel_minutes,
                        actual_total: utl.total_in_sec ? PdfUtil.showDurationAsHours((+utl.total_in_sec) + travel_seconds) : null,
                        violation,
                    }
                })

                sails.log.info(`processed planned resources records, count`, planned_resources.length);
                processed_resource_plans.push(...planned_resources);
            }
        }

        sails.log.info(`Total planned resources records, count`, processed_resource_plans.length);
        let workbook = await exportProjectResourcePlans(project, processed_resource_plans);
        let fileName = `Resource Planner - ${project.project_number}-${project.name} - ${from_date.format(displayDateFormat_DD_MM_YYYY)} - ${to_date.format(displayDateFormat_DD_MM_YYYY)}.xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },

    getProjectPlannedResources: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let day_of_yr = dayjs((req.query.day_of_yr || '--'), dbDateFormat);
        if (!day_of_yr.isValid()) {
            sails.log.info('Invalid Request');
            return errorResponse(res, 'Invalid day_of_yr filter provided.',{day_of_yr});
        }
        sails.log.info(`get all planned resource of project: ${projectId} and day_of_yr: ${day_of_yr}`);
        let resources = await sails.models.projectresourceplan_reader.find({
            where:{
                project_ref: projectId,
                day_of_yr: day_of_yr.format(dbDateFormat_YYYY_MM_DD)
            },
            sort: ['planned_start asc'],
        });

        if(!resources.length){
            return successResponse(res, {planned_resources: []});
        }
        let project = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: [
                'project_number', 'name', 'fatigue_management_status', 'site_hours_daily', 'total_hours_daily'
            ]
        });
        let {fatigue_management_status, site_hours_daily, total_hours_daily} = project;
        let unique_users = _uniq(resources.map(r => r.resource_ref));
        let users = await sails.models.user_reader.find({
            where:{
                id: unique_users,
            },
            select: ['first_name', 'middle_name', 'last_name']
        });
        let time_logs = await sails.models.userdailylog_reader.find({
            where: {
                project_ref: projectId,
                day_of_yr: day_of_yr.format(dbDateFormat_YYYY_MM_DD),
                user_ref: unique_users
            },
            select: ['first_in', 'last_out', 'total_in_sec', 'user_ref']
        });
        // sails.log.info(`got time logs of project: ${projectId} and day_of_yr: ${day_of_yr}`, JSON.stringify(time_logs, null, 4));
        let planned_resources = resources.map(r => {
            let travel_seconds = ((+r.travel_minutes || 0) * 60);
            let u = users.find(u => u.id === r.resource_ref) || {};
            let utl = time_logs.find(t => t.user_ref === r.resource_ref) || {};
            let planned_total = (r.planned_start && r.planned_end) ? ((+r.planned_end - (+r.planned_start)) + travel_seconds) : null;
            let violation = false;
            if(fatigue_management_status && utl.total_in_sec){
                if(site_hours_daily && (((+utl.total_in_sec)/3600) > site_hours_daily)){
                    violation = true;
                }else if(total_hours_daily && (((((+utl.total_in_sec) + travel_seconds))/3600) > total_hours_daily)){
                    violation = true;
                }
            }
            return {
                ...r,
                name: getUserFullName(u),
                planned_total,
                actual_start: utl.first_in,
                actual_end: utl.last_out,
                actual_total: utl.total_in_sec ? ((+utl.total_in_sec) + travel_seconds) : null,
                violation,
            }
        })

        return successResponse(res, {planned_resources: planned_resources});

    },

    deleteProjectResourcePlanById: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let recordId = +req.param('recordId', 0);
        sails.log.info(`delete request project: ${projectId}, resource plan id: ${recordId}, by user_id: ${req.user.id}`);
        let deleted_records = await sails.models.projectresourceplan.destroy({
            project_ref: projectId,
            id: recordId
        });
        sails.log.info(`deleted resource plan, count:`, deleted_records.length);
        return successResponse(res, {deleted_records: deleted_records});
    },

    updateProjectResourcePlanById: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let recordId = +req.param('recordId', 0);
        sails.log.info(`edit request project: ${projectId}, resource plan id: ${recordId}, by user_id: ${req.user.id}`);
        let {validationError, payload: {resource_plan}} = ProjectValidator.updateProjectResourcePlans(req);
        if(validationError){
            return errorResponse(res, 'All fields required.', {validationError});
        }
        /*
        sails.log.info(`update with`, {
            planned_start: resource_plan.planned_start,
            planned_end: resource_plan.planned_end,
            travel_minutes: resource_plan.travel_minutes
        });*/
        let updated_resource_plan = await sails.models.projectresourceplan.updateOne({
            project_ref: projectId,
            id: recordId
        }).set({
            planned_start: resource_plan.planned_start,
            planned_end: resource_plan.planned_end,
            travel_minutes: resource_plan.travel_minutes
        });
        sails.log.info(`updated resource plan`);
        return successResponse(res, {resource_plans: [updated_resource_plan]});
    },

    getProjectInductionBookingSlots: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let nowSeconds = +req.param('nowSeconds', 0);
        let forDays = +req.param('forDays', 28);
        let from = dayjs.unix(nowSeconds).startOf('day');
        let to = from.add(forDays, 'day').endOf('day');
        sails.log.info(`get next available induction slots of project: ${projectId}, from: ${from.format(dbDateFormat_YYYY_MM_DD)} for days: ${forDays}`);

        let all_slots = await sails.models.projectinductionslot_reader.find({
            select: ['location', 'slot_date_time', 'cutoff_seconds', 'total_slots', 'booked_slots'],
            where: {
                project_ref: projectId,
                slot_date_time: {
                    '>=': from.unix(),
                    '<': to.unix()
                },
            }
        }).sort([
            {slot_date_time: 'ASC'},
        ]);

        all_slots = (all_slots || []).map(slot => {
            slot.is_slot_blocked = ((nowSeconds + (+slot.cutoff_seconds)) > slot.slot_date_time) ? true : false;
            slot.all_booked = ((+slot.total_slots || 0) <= (+slot.booked_slots || 0));
            return slot;
        });
        return ResponseService.successResponse(res, {
            slots: all_slots,
        });
    },

    getProjectInductedOrToBeUsers: async(req, res) => {
        let projectId = +req.param('projectId');
        let category = (req.query.category || '').toString().trim();
        let list_type = (req.query.list_type || '').toString().trim();
        sails.log.info('get on getProjectInductedOrToBeUsers for project', projectId, category);
        let categoryList = ['toolbox_talks', 'task_briefings', 'wpp', 'rams', PERMIT_REGISTER];
        if(!category || !categoryList.includes(category)) {
            return errorResponse(res, sails.__('internal server error'), {});
        }
        let project = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['id', 'name', 'custom_field', 'project_category']
        });
        if(category === 'toolbox_talks' && project.custom_field.alternate_userlist_toolboxtalks) {
            let induction_users = await getUsersList(projectId, req.user);
            return successResponse(res, {operative_users: induction_users});
        } else if(category === 'task_briefings' && project.custom_field.alternate_userlist_taskbriefings) {
            let induction_users = await getUsersList(projectId, req.user);
            return successResponse(res, {operative_users: induction_users});
        } else if(category === 'wpp' && project.custom_field.alternate_userlist_wpps) {
            let induction_users = await getUsersList(projectId, req.user);
            return successResponse(res, {operative_users: induction_users});
        } else if(category === 'rams' && project.custom_field.alternate_userlist_rams) {
            let induction_users = await getUsersList(projectId, req.user);
            return successResponse(res, {operative_users: induction_users});
        } else if (category === PERMIT_REGISTER && list_type === 'inducted_users') {
            let induction_users = await getUsersList(projectId, req.user);
            return successResponse(res, {operative_users: induction_users});
        }
        let is_default_project = (project && project.project_category === 'default');
        let operative_users = await getOnSiteUsers(projectId, moment(), undefined, is_default_project);
        return successResponse(res, {operative_users: operative_users});
    },

    /*Save project settings*/
    saveProjectSetting: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        sails.log.info(`Saving/Updating "${req.body.setting_name}" project setting for project ${projectId}.`);

        let where = {project_ref: projectId, name: req.body.setting_name};
        let projectSetting = await sails.models.projectsetting_reader.findOne({
            where,
            select: ['id']
        });

        let settingValue = req.body.setting_value;
        if (typeOf(req.body.setting_value, 'array')) {
            let customFields = [];
            let defaultFields = [];
            (req.body.setting_value || []).map(field => {
                if (field.is_default) {
                    defaultFields.push(field);
                } else {
                    customFields.push(field);
                }
            });

            settingValue = [...defaultFields, ...customFields];
        }

        if (projectSetting && projectSetting.id) {
            projectSetting = await sails.models.projectsetting.updateOne({id: projectSetting.id}).set({value: settingValue});
        } else {
            projectSetting = await sails.models.projectsetting.create({
                project_ref: projectId,
                name: req.body.setting_name,
                value: settingValue
            });
        }

        if (req.body.setting_name == A_SITE_PROJECT_CONFIG && projectSetting.value && projectSetting.value.tools_mapping.length) {
            console.log(CONSTANT_SYNC_DAILY_BRIEFINGS_TO_ASITE);
            let toolsToProcessOnSchedule = [
                { tool_key:'rams', schedule_expr: '0 0 * * ? *', target: CONSTANT_SYNC_DAILY_BRIEFINGS_TO_ASITE },
                { tool_key:'toolbox_talks', schedule_expr: '0 0 * * ? *', target: CONSTANT_SYNC_DAILY_BRIEFINGS_TO_ASITE },
                // { tool_key:'progress_photos', schedule_expr: '0 5 * * ? *', target: CONSTANT_LAMBDA_TEST_EMAIL_FN }
            ];

            // projectSetting.value.tools_mapping.map(m => {
            //     console.log(m);
            // });

            // Result array to hold combined tools
            let combinedTools = [];

            // Process toolsToProcessOnSchedule
            toolsToProcessOnSchedule.forEach(scheduledTool => {
                let tool_key = scheduledTool.tool_key;
                let target = scheduledTool.target;
                let schedule = `cron(${scheduledTool.schedule_expr})`;

                // Filter the tools from projectSetting that match the tool_key
                let matchingTools = projectSetting.value.tools_mapping.filter(tool => tool.tool_key === tool_key);

                if (matchingTools.length > 0) {
                    combinedTools.push({ tool_key, target, schedule });
                }
            });

            if(combinedTools.length) {
                sails.log.info(`Asite scheduled tools number of tools ${combinedTools.length} for project ${projectId}`);
                let project = await sails.models.project_reader.findOne({
                    where: {id: projectId},
                    select: ['id', 'name', 'custom_field']
                });
                let tz = (project && project.custom_field && project.custom_field.timezone) || fall_back_timezone;

                const ebCronPromises = combinedTools.map(t => scheduleEventBridgeCron(project.id, t.tool_key, t.target, t.schedule, tz, 'inndex_asite_integration_project'));
                const ebCronResults = await Promise.all(ebCronPromises);
                // const ebDeleteCron = await deleteEventBridgeScheduleCron(projectId, 'rams', 'asite_project_test1');
                // const ebUpdateCron = await scheduleEventBridgeCron(projectId, 'rams', CONSTANT_SYNC_DAILY_BRIEFINGS_TO_ASITE, `cron(0 5 * * ? *)`, 'Pacific/Auckland', 'asite_project_test1');
                // const ebAtCron = await scheduleEvent(projectId, 'daily_activity', CONSTANT_SYNC_DAILY_BRIEFINGS_TO_ASITE, "1727872638000", 'test_sat'); // Wednesday, 2 October 2024 6:07:18 PM GMT+05:30
                sails.log.info(ebCronResults);
            }

            projectSetting.value.tools_mapping = await populateToolsMappingCompany(projectSetting.value.tools_mapping);
        }

        if (req.body.setting_name == "gate_booking_setting" && customFields.length) {
            let projectIds = customFields;
            let projectGates = await sails.models.projectgate_reader.find({project_id: projectId})
            sails.log.info(`Found ${projectGates.length} gates to replicate on projects ${projectIds}`);

            if (projectGates && projectGates.length) {
                for (let projectGate of projectGates) {
                    let projectGateBookings = await sails.models.projectgatebooking_reader.find({gate_id: projectGate.id})
                    sails.log.info(`Found ${projectGateBookings.length} booking to replicate.`);

                    delete projectGate.id;
                    delete projectGate.createdAt;
                    delete projectGate.updatedAt;
                    for (let projectId of projectIds) {
                        projectGate.project_id = projectId;
                        let payload = Object.assign({}, projectGate);
                        //check if gate already exist
                        let replicatedGate = await sails.models.projectgate_reader.findOne({
                            where: {project_id: payload.project_id, identifier: payload.identifier},
                            select: ['id']
                        });
                        sails.log.info("Is gate already exist: ", (replicatedGate && replicatedGate.id) ? true : false);
                        if (replicatedGate && replicatedGate.id) {
                            sails.log('Found existing gate with id: ' + replicatedGate.id);
                            replicatedGate = await sails.models.projectgate.updateOne({
                                id: replicatedGate.id
                            }).set(payload);
                        } else {
                            replicatedGate = await sails.models.projectgate.create(payload);
                            sails.log('Created a new gate with id: ' + replicatedGate.id);
                        }

                        //replicate bookings
                        if (projectGateBookings && projectGateBookings.length) {
                            for (let gateBooking of projectGateBookings) {
                                delete gateBooking.id;
                                delete gateBooking.createdAt;
                                delete gateBooking.updatedAt;
                                gateBooking.project_id = replicatedGate.project_id;
                                gateBooking.gate_id = replicatedGate.id;
                                let bookingPayload = Object.assign({}, gateBooking);
                                //check if gate already exist
                                let replicatedBooking = await sails.models.projectgatebooking_reader.findOne({
                                    where: {gate_id: bookingPayload.gate_id, identifier: bookingPayload.identifier},
                                    select: ['id']
                                });
                                sails.log.info("Is gate already exist: ", (replicatedBooking && replicatedBooking.id) ? true : false);
                                if (replicatedBooking && replicatedBooking.id) {
                                    sails.log('Found existing gate with id: ' + replicatedBooking.id);
                                    replicatedBooking = await sails.models.projectgate.updateOne({
                                        id: replicatedBooking.id
                                    }).set(bookingPayload);
                                } else {
                                    replicatedBooking = await sails.models.projectgatebooking.create(bookingPayload);
                                    sails.log('Created a new gate with id: ' + replicatedBooking.id);
                                }
                            }
                        }
                    }
                }
            }
        }

        sails.log.info(`Project setting "${req.body.setting_name}" for project ${projectId} has been saved.`);
        return successResponse(res, {project_setting: projectSetting});
    },

    downloadProjectRollCallReport: async(req, res) => {
        let projectId = req.param('projectId');
        let result = await onSiteOperativesList(projectId);
        if(result.success) {
            let onSiteUsers = result.onSiteUsers;
            let totalPages = 1;
            let usersByCompany = groupByKey(onSiteUsers, 'employerName');
            let projectInfo = await sails.models.project_reader.findOne({id: projectId})
            let { project_logo_file, companyName } = await getCompanyInfo(projectInfo);
            let form_template = `pages/roll-call-attendence`;
            let title = "On-site operatives";
            let dateTimeFormat = 'DD/MM/YYYY HH:mm:ss';
            let timezone = req.user.timezone || fall_back_timezone;
            let companies = Object.keys(usersByCompany);
            totalPages = Math.ceil((onSiteUsers.length + companies.length + 6) / 35);
            let html = await sails.renderView(form_template, {
                title: title,
                totalOperatives: onSiteUsers.length,
                records: usersByCompany,
                project_logo_file: project_logo_file,
                companies,
                totalPages,
                layout: false,
                getUserFullName(userInfo) {
                    return getUserFullName(userInfo);
                },
                createdAt: moment().tz(timezone).format(dateTimeFormat),
                unix(n, format) {
                    return momentTz.unix(n).tz((projectInfo && projectInfo.custom_field && projectInfo.custom_field.timezone) || fall_back_timezone).format(format);
                },
                project: projectInfo,
                companyName: companyName,
            });
            let fileName = `${title}-Attendance-${dayjs().format('MM-DD-YYYY')}`;
            return await instantPdfGenerator(req, res, html, 'roll-call', fileName, req.headers['user-agent'], {format: 'A4'}, 'url');
        }
        return ResponseService.errorResponse(res, result.message);
    },
    validatePostCode: validatePostCode,
    innTimeValidatePostCode: validatePostCode,

    downloadRandomOnSiteOperativesReport: async(req, res) => {
        let projectId = req.param('projectId');
        let operativeResult = await onSiteOperativesList(projectId);
        if(operativeResult.success) {
            let onSiteUsers = operativeResult.onSiteUsers;
            let noOfOperatives = ~~+req.param('noOfOperatives', 0);
            let result = new Array(noOfOperatives),
            len = onSiteUsers.length,
            taken = new Array(len);
            if (noOfOperatives > len) {
                return ResponseService.errorResponse(res, `Download failed - ${noOfOperatives} operatives are not present on site.`);
            }
            while(noOfOperatives >= 1) {
                noOfOperatives--;
                var x = Math.floor(Math.random() * len);
                result[noOfOperatives] = onSiteUsers[x in taken ? taken[x] : x];
                taken[x] = --len in taken ? taken[len] : len;
            }
            onSiteUsers = result;
            let totalPages = 1;
            let usersByCompany = groupByKey(onSiteUsers, 'employerName');
            let projectInfo = await sails.models.project_reader.findOne({id: projectId})
            let { project_logo_file, companyName } = await getCompanyInfo(projectInfo);
            let form_template = `pages/roll-call-attendence`;
            let title = "On-site operatives";
            let dateTimeFormat = 'DD/MM/YYYY HH:mm:ss';
            let timezone = req.user.timezone || fall_back_timezone;
            let companies = Object.keys(usersByCompany);
            totalPages = Math.ceil((onSiteUsers.length + companies.length + 6) / 35);
            let html = await sails.renderView(form_template, {
                title: title,
                totalOperatives: onSiteUsers.length,
                records: usersByCompany,
                project_logo_file: project_logo_file,
                companies,
                totalPages,
                layout: false,
                createdAt: moment().tz(timezone).format(dateTimeFormat),
                unix(n, format) {
                    return momentTz.unix(n).tz((projectInfo && projectInfo.custom_field && projectInfo.custom_field.timezone) || fall_back_timezone).format(format);
                },
                project: projectInfo,
                companyName: companyName,
            });
            let fileName = `${title}-Attendance-${dayjs().format('MM-DD-YYYY')}`;
            return await instantPdfGenerator(req, res, html, 'roll-call', fileName, req.headers['user-agent'], {format: 'A4'}, 'url');
        }
        return ResponseService.errorResponse(res, operativeResult.message);
    },
};
