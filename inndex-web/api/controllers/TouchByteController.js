/**
 * Created by spatel on 01/07/20.
 */

const {
    dbDateFormat_YYYY_MM_DD,
    touchByteAPI,
} = sails.config.constants;
// const moment = require('moment');
// const _pick = require('lodash/pick');
// const _uniq = require('lodash/uniq');
const {
    DataProcessingService:{
        getFilteredInductionBySearch,
        populateUserRefs,
    },
    OptimaSyncService: {
        callOptimaBoxGateway,
    },
    TouchByteService: {
        getUserNameOf,
        touchByteUserResponse,
        createTouchByteUserRequest,
    },
    HttpService: {
        // typeOf,
        makeGET,
        makePOST,
        makeDELETE,
    },
    ResponseService: {
        errorObject,
        successObject,
        sendResponse,
        errorResponse,
        successResponse
    }
} = require('./../services');

const fetchTBUser = async (innDexUserId, {t_b_host}) => {
    let username = getUserNameOf(innDexUserId);
    sails.log.info('get touch-byte user', username);
    let api_response = await makeGET(`${t_b_host}${touchByteAPI.GET_user_info(username)}`, {}, {}, true, 15000);
    sails.log.info('Response from GET user info n/w call', api_response.status);
    if (api_response.success) {

        return successObject({
            user: touchByteUserResponse(api_response.data),
            comment: api_response.data.comment
        });
    } else if (api_response.status === null) {
        throw new Error('Unable to reach touch-byte server');
    }
    return errorObject('User not found on touch-byte', api_response.data);
};

const createTBUser = async (innDexUserId, {t_b_host}, profile_pic_ref = undefined) => {
    sails.log.info('creating touch-byte user for', innDexUserId);
    let user = undefined;
    // Do not query profile pic if override provided already
    if (profile_pic_ref) {
        user = await sails.models.user.findOne({
            select: ['first_name', 'middle_name', 'last_name', 'profile_pic_ref'],
            where: {id: innDexUserId}
        });
    } else {
        user = await sails.models.user.findOne({
            select: ['first_name', 'middle_name', 'last_name', 'profile_pic_ref'],
            where: {
                id: innDexUserId,
                profile_pic_ref: {'!=': null},
            }
        }).populate('profile_pic_ref');
    }

    if (!user) {
        sails.log.info('user not found / profile pic not found', innDexUserId);
        return errorObject('Unable to find valid user info on innDex', {reason: 'Invalid profile pic'});
    }
    if (profile_pic_ref) {
        user.profile_pic_ref = profile_pic_ref;
    }
    let body = await createTouchByteUserRequest(user);

    if (!body.base64image) {
        sails.log.info('valid profile pic not found', innDexUserId);
        return errorObject('Unable to find valid user info on innDex', {reason: 'Invalid profile pic'});
    }
    let create_response = await makePOST(`${t_b_host}${touchByteAPI.POST_create_user()}`, body, {
        'Content-Type': 'application/json',
    }, true, 30000);
    sails.log.info('Response from POST create user n/w call', create_response.status);
    if (create_response.success) {

        sails.log.info('Response from POST create user n/w call', create_response.data);
        return successObject({
            user: touchByteUserResponse(create_response.data),
            created: true,
            comment: create_response.data.comment
        })
    }
    return errorObject('Unable to create user on touch-byte', create_response.data);
};

const getTBUserByIdOrCreate = async (innDexUserId, force_create, project_optima_setting, profile_pic_ref = undefined) => {
    let touchByteUser = await fetchTBUser(innDexUserId, project_optima_setting);
    if (touchByteUser.success || !force_create) {
        return touchByteUser;
    }

    return await createTBUser(innDexUserId, project_optima_setting, profile_pic_ref);
};

const getTbUserPermissions = async (innDexUserId, {t_b_host, site_id}) => {
    let username = getUserNameOf(innDexUserId);
    let permissions_response = await makeGET(`${t_b_host}${touchByteAPI.GET_user_access(site_id, username)}`, {}, {
        'Content-Type': 'application/json',
    }, true, 15000);
    sails.log.info('Response from GET permissions of user n/w call', permissions_response.status);
    if (permissions_response.success) {

        sails.log.info('Total allowed doors:', (permissions_response.data.permissions || []).length);
        return successObject({
            permissions: (permissions_response.data.permissions),
            permissions_comment: permissions_response.data.comment
        });
    }
    return errorObject('Failed while fetching permissions', permissions_response);
};

const updateInductionRecordWithBadge = async (induction_id, innDexUserId) => {
    sails.log.info('Updating induction id:', induction_id, 'with optima_badge_number as:', innDexUserId);
    return await sails.models.inductionrequest.updateOne({id: induction_id}).set({
        // @todo: spatel: we need to get rid of using optima_badge_number here.
        optima_badge_number: innDexUserId,
    });
};

const getProfilePicRef = async (induction_id, innDexUserId) => {
    let induction = await sails.models.inductionrequest.findOne({where: {id: induction_id, user_ref: innDexUserId}, select: ['additional_data']});
    if (!induction) {
        sails.log.info('Given induction record not found for given user, id:', induction_id);
        return errorObject('Unable to find given induction record');
    }
    let user_ref = (induction.additional_data || {}).user_info || {};
    let profile_pic_ref = user_ref.profile_pic_ref;
    if (!profile_pic_ref || !profile_pic_ref.file_url) {
        sails.log.info('Unable to find valid profile pic in given induction form, profile_pic_ref:', profile_pic_ref);
        return errorObject('Unable to find valid profile pic in given induction form');
    }
    sails.log.info('profile pic from induction form', profile_pic_ref.file_url);
    return profile_pic_ref;
};

module.exports = {

    getBioMetricSetting: async (req, res) => {
        let projectId = req.param('projectId');
        let expand_optima = (req.query.expand_optima === 'true');
        sails.log.info('fetching bio-metric setting of project:', projectId);
        let optima_setting = await sails.models.optimasetting_reader.findOne({
            where: {
                project_ref: projectId,
            },
        });
        sails.log.info('got setting', optima_setting ? optima_setting.id : null);
        if (!optima_setting) {
            return errorResponse(res, sails.__('no_bio_metric_configuration_found'));
        }
        let bm_setting = {
            has_optima_source: !!(optima_setting.biometric_source && optima_setting.biometric_source === 'optima'),
            has_touch_byte_source: !!(optima_setting.biometric_source && !(optima_setting.biometric_source === 'optima')),
            has_geo_fence_source: !!(optima_setting.geo_fence_locations && optima_setting.geo_fence_locations.length),
            enrolment_type: optima_setting.enrolment_type,
            allow_cards: optima_setting.allow_cards,
            has_turnstile: optima_setting.has_turnstile,
            force_onsite_for_an_out: optima_setting.force_onsite_for_an_out,
            selfie_required: optima_setting.selfie_required,
            has_worker_clocking: optima_setting.has_worker_clocking,
            has_bulk_clocking: optima_setting.has_bulk_clocking,
            has_vehicle_clocking: optima_setting.has_vehicle_clocking,
            has_fr: optima_setting.has_fr,
            kiosk_mode: optima_setting.kiosk_mode,
            clock_in_mode: optima_setting.clock_in_mode,
            liveness_check: optima_setting.liveness_check,
            site_id: optima_setting.site_id,
            geo_fence_locations: optima_setting.geo_fence_locations,
        };
        if(bm_setting.has_geo_fence_source){
            // fallback logic for OLD mobile app.
            let [first_location] = bm_setting.geo_fence_locations || [];
            bm_setting.geo_fencing_acceptable_distance = (first_location ? first_location.radius : null);
            bm_setting.site_geo_location = (first_location ? {
                'name': first_location.name,
                'lat': first_location.lat,
                'long': first_location.long
            } : {});
        }
        if (bm_setting.has_touch_byte_source || !bm_setting.has_optima_source) {
            return successResponse(res, {...bm_setting, has_data: true});
        }

        // make this optima call ONLY when requested.
        let api_response = {};
        if (expand_optima) {
            api_response = await callOptimaBoxGateway(optima_setting, {
                endpoint: 'Badge/0',
                method: 'GET',
            }, {});
        }
        return successResponse(res, {
            ...bm_setting,
            optima: api_response.data,
            ...((api_response.success || bm_setting.has_geo_fence_source) && {has_data: true}),
            optima_online: (!!api_response.success)
        });
    },

    getTBUserById: async (req, res) => {
        let innDexUserId = req.param('userId');
        let force_create = (req.query.force_create === 'true');
        let include_profile = (req.query.include_profile === 'true');
        let include_permissions = (req.query.include_permissions === 'true');
        let response = await getTBUserByIdOrCreate(innDexUserId, force_create, req.project_optima_setting);
        if (include_profile) {
            let user_profile = await sails.models.user.findOne({
                where: {id: innDexUserId},
                select: ['first_name', 'middle_name', 'last_name', 'profile_pic_ref']
            }).populate('profile_pic_ref');
            response.profile = user_profile;
        }
        if (include_permissions && response.user) {
            let permissions = await getTbUserPermissions(innDexUserId, req.project_optima_setting);
            if (permissions.success) {
                response = {
                    ...response,
                    ...permissions
                };
            }
        }
        return sendResponse(res, response);
    },

    getReadersList: async (req, res) => {
    },

    grantUserSiteAccess: async (req, res) => {
        let innDexUserId = req.param('userId');
        let force_create = (req.body.force_create === true);
        let induction_id = +(req.body.induction_id || 0);
        let induction_image_source = (req.body.imageSource || '').toString().toLowerCase().trim() === 'induction';
        let username = getUserNameOf(innDexUserId);
        sails.log.info(`grant "${username}" user site access to: ${req.project_optima_setting.site_id}, imageSource: ${req.body.imageSource}, force_create: ${force_create}`);
        let profile_pic_ref = undefined;
        if (induction_id && induction_image_source) {
            // get profile pic from induction form
            profile_pic_ref = await getProfilePicRef(induction_id, innDexUserId);
            if (profile_pic_ref.error) {
                return sendResponse(res, profile_pic_ref);
            }
        }
        let touchByteUser = await getTBUserByIdOrCreate(innDexUserId, force_create, req.project_optima_setting, profile_pic_ref);
        if (!touchByteUser.success) {
            return sendResponse(res, touchByteUser);
        }

        let {t_b_host} = req.project_optima_setting;

        let api_response = await makePOST(`${t_b_host}${touchByteAPI.POST_grant_user_access()}`, {
            site: req.project_optima_setting.site_id,
            username: username
        }, {
            'Content-Type': 'application/json',
        }, true, 30000);

        sails.log.info('Response from POST grant site access n/w call', api_response.status);
        if (api_response.success) {
            let induction_request = {};
            if (induction_id) {
                induction_request = await updateInductionRecordWithBadge(induction_id, innDexUserId);
            }
            return successResponse(res, {
                username,
                // ...induction_id && {induction_request},
                message: `The user has been allowed access to all doors in the site ${req.project_optima_setting.site_id}`,
                ...api_response.data
            });
        }
        return errorResponse(res, `Request failed while allowing access`, {
            username,
            ...api_response.data
        });
    },

    revokeUserSiteAccess: async (req, res) => {
        let innDexUserId = req.param('userId');
        let induction_id = +(req.query.induction_id || 0);
        let username = getUserNameOf(innDexUserId);
        sails.log.info(`revoke "${username}" user site access to: ${req.project_optima_setting.site_id}`);

        let {t_b_host} = req.project_optima_setting;
        let api_response = await makeDELETE(`${t_b_host}${touchByteAPI.DELETE_revoke_user_access()}`, {
            data: {
                site: req.project_optima_setting.site_id,
                username: username
            }
        }, {
            'Content-Type': 'application/json',
        }, true, 30000);

        sails.log.info('Response from revoke site access n/w call', api_response.status);
        if (api_response.success) {

            let induction_request = {};
            if (induction_id) {
                induction_request = await updateInductionRecordWithBadge(induction_id, null);
            }
            return successResponse(res, {
                message: `The user access has been revoked from all doors in the site ${req.project_optima_setting.site_id}`,
                ...api_response.data
            });
        }
        return errorResponse(res, `Request failed while revoked access`, {
            ...api_response.data
        });
    },

    getProjectTbEvents: async (req, res) => {
        let projectId = req.param('projectId');
        let pageNumber = +(req.query.pageNumber || 1);
        let limit = +(req.query.limit || 50);
        let text = (req.param('q', '')).trim();

        if (isNaN(projectId) || isNaN(pageNumber) || isNaN(limit)) {
            sails.log.info('Invalid Request');
            return errorResponse(res, 'Invalid request required.');
        }

        let skip = ((pageNumber - 1) * limit);
        sails.log.info('fetch touch-byte events for project', projectId, `page: ${pageNumber} skip: ${skip} search: ${text}`);
        let filteredInductions = await getFilteredInductionBySearch(projectId, text);
        if(text && !filteredInductions.length){
            // no matching result
            return ResponseService.successResponse(res, {
                events: [],
                pageNumber,
                limit,
                total: 0,
            });
        }
        let user_id_filter = {};
        if(filteredInductions.length){
            user_id_filter = {
                user_ref: (filteredInductions.map(induction => induction.user_ref))
            }
        }
        let total = await sails.models.touchbytelog.count({
            project_ref: projectId,
            ...(user_id_filter),
        });
        let events = await sails.models.touchbytelog.find({
            select: ['event_date_time', 'user_ref', 'event_type', 'external_id', 'door', 'site', 'note'], // 'user_location'
            where: {
                project_ref: projectId,
                ...(user_id_filter),
            },
            skip,
            limit,
            sort: 'event_date_time DESC'
        });
        events = await populateUserRefs(events, 'user_ref', []);
        sails.log.info(`responding with all touch-byte events, count:${events.length}`);
        return successResponse(res, {
            events,
            pageNumber,
            limit,
            total,
        });
    },
};
