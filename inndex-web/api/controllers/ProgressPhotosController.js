const INTERNAL_SERVER_ERROR = sails.__('Internal server error.');
const {DEFAULT_PAGE_SIZE} = sails.config.constants;
const moment = require('moment');
const _uniq = require('lodash/uniq');
const momentTz = require('moment-timezone');
const {ResponseService} = require("../services");
const {
    ExcelService: {
        streamExcelDownload,
        progressPhotosReport,
    },
    TokenUtil: {
        allProjectAdminsByOneOfDesignations,
        filterProjectUsersEmailEligibility,
        getCompanyInfo
    },
    UserRevisionService: {
        getLatestUserRevision
    },
    DataProcessingService: {
        getUserFullName,
        getProjectTimezone,
        attachProfilePicWithUserRefInfo,
        expandProgressImages,
        totalProgressImagesCount,
        groupByAlbumProgressImages,
        sendMailToNominatedManagerCPA,
        populateUserRefs,
        populateEmployerRefs,
        populateProjectRefs,
    },
    EmailService: {
        sendMail
    },
    SharedService: {
        instantPdfGenerator,
        downloadZippedImages
    },
} = require('./../services');
const {
    PPValidator: {
        createOrUpdatePhotoRecord
    }
} = require('./../validators');
const {progressPhotoFn} = require('./../sql.fn');
const { companyFn: { getTaggedOwnersList }} = require('../sql.fn');

const processPPDownload  = async (req, res, whereClause) => {

    sails.log.info('Fetch warning, id:', whereClause.id);

    if (!whereClause.id) {
        return ResponseService.errorResponse(res, 'Progress Photos id is required');
    }

    let progressPhotos =  await sails.models.progressphotos.findOne(whereClause)
        .populate('user_ref')
        .populate('project_ref')
        .populate('tagged_owner');

    if (progressPhotos && progressPhotos.id) {
        sails.log.info('got record, id', progressPhotos.id);
        if (progressPhotos.pp_images && progressPhotos.pp_images.length) {
            sails.log.info('Expanding Progress Photos images.');
            try {
                progressPhotos.pp_images = await sails.models.userfile.find({id: progressPhotos.pp_images});
            } catch (e) {
                progressPhotos.pp_images = [];
                sails.log.info('Failed to expand Progress Photos images.');
            }
        }

        let { project_logo_file, companyName } = await getCompanyInfo(progressPhotos.project_ref);

        let pp_images = [];
        progressPhotos.pp_images.map(row => {
            if (row) {
                // pp_images.push(row.sm_url || row.file_url);
                if (row.img_translation && row.img_translation.length) {
                    pp_images.push(...row.img_translation);
                } else if (row.file_url) {
                    pp_images.push(row.sm_url || row.file_url);
                }
            }
        });

        //name for download file
        let fileName = 'Progress Photos Report-' + moment().format('YYYY-MM-DD');

        //build PDF
        let addedBy = progressPhotos.user_ref.first_name+' '+progressPhotos.user_ref.last_name;
        let project = progressPhotos.project_ref;
        let project_line = `${(project.project_number != null) ? project.project_number + ' - ' + project.name : project.name} (#${project.id}): ${project.contractor}`;

        let tz = getProjectTimezone(project);
        let form_template = `pages/progress-photos-page`;
        let html = await sails.renderView(form_template, {
            title: `Progress Photos`,
            subtitle: project_line,
            date_range:null,
            added_by: addedBy,
            added_on: momentTz(progressPhotos.createdAt).tz(tz).format('D/MMM/Y HH:mm:ss'),
            pp_location: progressPhotos.pp_location,
            pp_description: progressPhotos.pp_description,
            pp_owner: progressPhotos.tagged_owner? progressPhotos.tagged_owner.name: '',
            pp_title: progressPhotos.pp_title,
            pp_images: pp_images,
            project_logo_file: project_logo_file,
            project,
            layout: false
        });
        return await instantPdfGenerator(req, res, html, 'progress-photos', fileName, req.headers['user-agent'], {format: 'A4'}, 'url');

    }
    sails.log.info('Failed to find Progress Photos');
    return ResponseService.errorResponse(res, sails.__('internal server error'));
}
module.exports = {

    addProgressPhotos: async (req, res) => {
        sails.log.info('Add Progress Photos for project, by', req.user.id);
        let createRequest = _.pick((req.body || {}), [
            'project_ref',
            'pp_title',
            'pp_images',
            'pp_location',
            'pp_description',
            'tagged_owner',
            'location',
            'album_ref',
        ]);

        let {validationError, payload} = createOrUpdatePhotoRecord(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'Validation Error: ', {validationError});
        }

        createRequest.user_ref = req.user.id;

        let revision = await getLatestUserRevision(createRequest.user_ref);
        createRequest.user_revision_ref = revision.id;
        createRequest.album_ref = createRequest.album_ref === 0 ? null: createRequest.album_ref;
        sails.log.info('adding Progress Photo with', createRequest);
        let progressPhotos = await sails.models.progressphotos.create(createRequest);
        if(progressPhotos)  {
            let projectInfo = await sails.models.project.findOne({where: {id: progressPhotos.project_ref}, select: ['name','project_category','parent_company']});
            let project_name = projectInfo.name;
            let subject = 'Progress Photo on '+project_name+ ' project';
            let userInfo = await sails.models.user_reader.findOne({
                select: ['first_name', 'last_name'],
                where:{
                    id: progressPhotos.user_ref
                }
            });
            let added_by = getUserFullName(userInfo);
            let projUsrResult = await allProjectAdminsByOneOfDesignations(progressPhotos.project_ref, [ 'nominated', 'custom']);
            projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, 'progress_photos');
            sails.log.info('Send email to nom managers, count:', projUsrResult.length);
            for (let j = 0, len = projUsrResult.length; j < len; j++) {
                let nomManager = projUsrResult[j];
                let user_name = getUserFullName(nomManager.user_ref);
                let emailHtml = await sails.renderView('pages/mail/new-progress-photo', {
                    title: subject,
                    user_name,
                    added_by,
                    project_name,
                    layout: false
                });
                let emailAdd = nomManager.user_ref && nomManager.user_ref.email;
                sails.log.info('Sending mail to', emailAdd);
                await sendMail(subject, [emailAdd], emailHtml);
                sails.log.info('Progress Photos notification has been sent');
            }

            //send mail to company project admins(Nominated Managers)
            if (projectInfo.project_category === 'company-project' && projectInfo.parent_company) {
                await sendMailToNominatedManagerCPA(projectInfo.parent_company, projectInfo, req.user, 'Progress Photo', 'submitted Progress Photo/s');
            }
            return ResponseService.successResponse(res, progressPhotos);
        }
        sails.log.info('Failed to add Progress Photos')
        return ResponseService.errorResponse(res, sails.__('Failed to add Progress Photos'));
    },

    getProgressPhotos: async (req, res) => {
        let ppId = +req.param('ppId');
        let progressPhotos;
        sails.log.info('Fetch progress photos, id:', ppId);

        progressPhotos = await  sails.models.progressphotos.findOne({id: ppId})
            .populate('user_ref')
            .populate('project_ref');

        if(progressPhotos) {
            progressPhotos = await expandProgressImages([progressPhotos]);
            if (!progressPhotos) {
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR);
            }
            return ResponseService.successResponse(res, {progressPhotos});
        }

        sails.log.info('Failed to fetch all Progress Photos');
        return ResponseService.errorResponse(res, 'Failed to fetch all Progress Photos');
    },

    updateProgressPhotos: async (req, res) => {
        let ppId = +req.param('ppId');
        let progressPhotos;
        sails.log.info('Updating progress photos, id:', ppId);

        if (!ppId) {
            return ResponseService.errorResponse(res, 'progress photos id is required');
        }

        let {validationError, payload} = createOrUpdatePhotoRecord(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'Validation Error: ', {validationError});
        }

        let updateRequest = _.pick((req.body || {}), [
            'pp_title',
            'pp_images',
            'pp_location',
            'pp_description',
            'tagged_owner',
            'location',
            'amendment_log',
            'album_ref',
        ]);
        updateRequest.album_ref = updateRequest.album_ref === 0 ? null: updateRequest.album_ref;
        sails.log.info('Updating progress photos with: ', updateRequest);

        progressPhotos = await sails.models.progressphotos.updateOne({id: ppId}).set(updateRequest);
        progressPhotos = await sails.models.progressphotos_reader.findOne({id: progressPhotos.id})
            .populate('user_ref')
            .populate('tagged_owner');
        progressPhotos = await expandProgressImages([progressPhotos]);
        progressPhotos =  progressPhotos[0] || {};

        sails.log.info('Updated progress photos successfully, id', (progressPhotos.id ? progressPhotos.id : null));
        return ResponseService.successResponse(res, {progress_photos: progressPhotos});
    },

    getAllProgressPhotos: async (req, res) => {
        let projectId = +req.param('projectId');
        let albumId   = +req.param('albumId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let search = req.param('search');
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let tagged_owner = req.param('tagged_owner') ? req.param('tagged_owner').split(',').map(a=>+a): [];
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'desc');

        let filter =  {};
        if (projectId) {
            filter = {project_ref:projectId};
        }
        if(tagged_owner.length){
            filter.tagged_owner = {in:tagged_owner}
        }

        if (albumId && albumId > 0) {
            filter.album_ref = albumId;
        }
        let ids = [];
        if(search && search !== 'null') {
            let photoSearch = await sails.models.progressphotos.find({
                where: filter,
                select: ['user_ref', 'pp_title']
            });
            photoSearch = await populateUserRefs(photoSearch, 'user_ref', []);

            let users_id = photoSearch.map(record => record.user_ref && record.user_ref.id);
            let usersEmpDetail = await sails.models.userempdetail.find({user_ref: _uniq(users_id)});
            if(usersEmpDetail  && usersEmpDetail.length) {
                photoSearch = photoSearch.map(record => {
                    if (record.user_ref && record.user_ref.id) {
                        record.user_employer = usersEmpDetail.find(ue => ue.user_ref === record.user_ref.id);
                    }

                    return record;
                });
            }
             photoSearch.map(d => {
               if(
                    (d.pp_title && d.pp_title.toLowerCase().indexOf(search) !== -1) ||
                    (d.user_ref && d.user_ref.first_name && d.user_ref.first_name.toLowerCase().indexOf(search) !== -1) ||
                    (d.user_ref && d.user_ref.middle_name && d.user_ref.middle_name.toLowerCase().indexOf(search) !== -1) ||
                    (d.user_ref && d.user_ref.last_name && d.user_ref.last_name.toLowerCase().indexOf(search) !== -1) ||
                    (d.user_employer && d.user_employer.employer && d.user_employer.employer.toLowerCase().indexOf(search) !== -1) || !search

                ){
                    ids.push(d.id)
                }
            });
            filter = {
                id: ids
            };
        }

        sails.log.info('get all progress-photos with filter:', filter, `is_inherited_project: ${is_inherited_project}`);
        let projectProgressPhotos = await sails.models.progressphotos.find(filter)
            .sort([
                {[sortKey]: sortDir}
            ])
            .limit(pageSize)
            .skip(pageNumber * pageSize)
            .populate('album_ref');
        projectProgressPhotos = await populateEmployerRefs(projectProgressPhotos, 'tagged_owner', ['id', 'name', 'country_code', 'company_initial', 'logo_file_id', 'projects_alias']);
        projectProgressPhotos = await populateProjectRefs(projectProgressPhotos, 'project_ref', []);
        projectProgressPhotos = await populateUserRefs(projectProgressPhotos, 'user_ref', []);
        let taggedOwnersList = [];
        if(pageNumber == 0 && search == 'null' && !tagged_owner.length){
            taggedOwnersList = await getTaggedOwnersList('progress_photos', projectId);
        }
        projectProgressPhotos = await attachProfilePicWithUserRefInfo(projectProgressPhotos);
        let total_record_count = await sails.models.progressphotos.count(filter)
        projectProgressPhotos = await expandProgressImages(projectProgressPhotos);
        if (!projectProgressPhotos) {
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR);
        }
        let progress_photos_count = null;
        if (pageNumber === 0) {
            let projectProgressPhotosCount =  await sails.models.progressphotos_reader.find(filter).select('pp_images')
            if (projectProgressPhotosCount) {
                progress_photos_count = await totalProgressImagesCount(projectProgressPhotosCount);
            }
        }
        return ResponseService.successResponse(res, {progress_photos: projectProgressPhotos, progress_photos_count, total_record_count, taggedOwnersList});
    },

    getUserProgressPhotos: async (req, res) => {
        let userId = +req.param('userId');
        let projectId = +req.param('projectId');
        let filter, userProgressPhotos;
        if (userId ) {
            filter = {user_ref:userId};
        } else {
            return ResponseService.errorResponse(res, 'user id is required');
        }

        if (projectId) {
            filter.project_ref = projectId;
        }

        try {
            userProgressPhotos = await sails.models.progressphotos.find(filter)
                .sort([
                    {id: 'DESC'}
                ]);
            userProgressPhotos = await populateEmployerRefs(userProgressPhotos, 'tagged_owner', ['id', 'name', 'country_code', 'company_initial', 'logo_file_id', 'projects_alias']);
            userProgressPhotos = await populateProjectRefs(userProgressPhotos, 'project_ref', []);
            userProgressPhotos = await populateUserRefs(userProgressPhotos, 'user_ref', []);
            if(userProgressPhotos) {
                userProgressPhotos = await expandProgressImages(userProgressPhotos);
                if (!userProgressPhotos) {
                    return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR);
                }
                return ResponseService.successResponse(res, {progress_photos: userProgressPhotos});
            }
        } catch (e) {
            sails.log.info('Failed to fetch all ProgressPhotos ?', e);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, e);
        }
    },

    downloadProgressPhotosXLSX: async (req, res) => {
        //req.setTimeout(240000);
        let projectId = (req.body.projectId || '');
        let fromDate = moment(req.body.fromDate).startOf('day').valueOf();
        let toDate = moment(req.body.toDate).endOf('day').valueOf();
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let album_id = +(req.param('album_id', 0));
        let tagged_owner = req.body.tagged_owner;
        let filter = {};
        if (!fromDate || !toDate) {
            return ResponseService.errorResponse(res, 'from date and to date are required.');
        }
        if (projectId) {
            filter = {project_ref:projectId};
        }

        if (album_id > 0) {
            filter.album_ref = album_id;
        }

        sails.log.info('Download xls of progress-photos with filter:', filter, fromDate, toDate, `is_inherited_project: ${is_inherited_project}`);
        let projectProgressPhotos = await sails.models.progressphotos.find({
            where: {
                createdAt: {'>=': fromDate, '<=': toDate},
                ...filter
            },
        }).sort([
            {id: 'DESC'}
        ]);
        projectProgressPhotos = await populateEmployerRefs(projectProgressPhotos, 'tagged_owner', ['id', 'name', 'country_code', 'company_initial', 'logo_file_id', 'projects_alias']);
        projectProgressPhotos = await populateProjectRefs(projectProgressPhotos, 'project_ref', []);
        projectProgressPhotos = await populateUserRefs(projectProgressPhotos, 'user_ref', []);
        if(tagged_owner) {
            projectProgressPhotos = projectProgressPhotos.filter(f=> f.tagged_owner && f.tagged_owner.id === tagged_owner);
        }
        projectProgressPhotos = await expandProgressImages(projectProgressPhotos);
        if (!projectProgressPhotos) {
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR);
        }
        let workbook = await progressPhotosReport(projectProgressPhotos);
        return streamExcelDownload(res, workbook);

    },

    // @todo: Satyam Hardia: deprecated as of Jun 15, 2021. API and all related code should be removed by Sept 11, 2021.
    downloadProgressPhotos: async (req, res) => {
        let ppId = +req.param('ppId');
        let updatedAt = +req.param('timestamp');

        let whereClause = {
            id: ppId,
            updatedAt: updatedAt
        };

        return await processPPDownload(req, res, whereClause);
    },

    downloadProgressPhotosV2: async (req, res) => {
        let ppId = +req.param('ppId');
        let createdAt = +req.body.createdAt || 0;

        let whereClause = {
            id: ppId,
            createdAt: createdAt
        };

        return await processPPDownload(req, res, whereClause);
    },

    fetchProgressPhotosReportRecords: async (req, res) => {
        let projectId = (req.body.projectId || '');
        let fromDate = +(req.body.fromDate);
        let toDate = +(req.body.toDate);

        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let album_id = (req.body.album_id);
        let filter = { project_ref: projectId };

        if (album_id && album_id > 0) {
            filter.album_ref = album_id;
        }

        if (req.body.companyId) {
            filter.tagged_owner = req.body.companyId;
        }

        if (!fromDate || !toDate) {
            return ResponseService.errorResponse(res, 'from date and to date are required.');
        }

        try {
            sails.log.info('fetch all progress-photos with filter:', filter, fromDate, toDate, `is_inherited_project: ${is_inherited_project}`);
            let allProgressPhotos =  await sails.models.progressphotos.find({
                where: {
                    createdAt: {'>=': fromDate, '<=': toDate},
                    ...filter
                },
                sort: ['createdAt DESC']
            });
            allProgressPhotos = await populateEmployerRefs(allProgressPhotos, 'tagged_owner', ['id', 'name']);

            allProgressPhotos = await populateUserRefs(allProgressPhotos, 'user_ref', []);

            let formattedProgressPhotos = [];
            let photosId = [];
            if (allProgressPhotos && allProgressPhotos.length) {
                let all_keys = Object.keys(allProgressPhotos);
                let allPhotosId = [];
                for(let i = 0, len = all_keys.length; i <  len; i++) {
                    let progressPhotos = allProgressPhotos[i];
                    if (progressPhotos.pp_images && progressPhotos.pp_images.length) {
                        allPhotosId.push(...progressPhotos.pp_images);
                    }
                }

                sails.log.info('photos', allPhotosId);

                if (allPhotosId.length) {
                    let allPhotos = await sails.models.userfile.find({id: allPhotosId}).select(['file_url', 'sm_url', 'img_translation']);
                    for(let i = 0, len = all_keys.length; i <  len; i++) {
                        let progressPhotos = allProgressPhotos[i];
                        if (progressPhotos.pp_images && progressPhotos.pp_images.length) {
                            let photos = allPhotos.filter(photo => {
                                return progressPhotos.pp_images.includes(photo.id);
                            });

                            formattedProgressPhotos.push(...(photos || []).map(p => ({
                                ...progressPhotos,
                                photo: p
                            })));
                            photosId.push(...(photos || []).map(p => p.id));
                        }
                    }
                }
            }
            return ResponseService.successResponse(res, {all_progress_photos: formattedProgressPhotos, photos_id: photosId});
        } catch(failure){
            sails.log.info('Failed to find progress photos', failure);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, failure);
        }
    },

    downloadProgressPhotosReport: async (req, res) => {
        let projectId = (req.body.projectId || '');
        let fromDate = moment(req.body.fromDate).startOf('day').valueOf();
        let toDate = moment(req.body.toDate).endOf('day').valueOf();
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let reportTitle = (req.body.reportTitle);
        let fileName = (req.body.fileName);
        let reportSubTitle = (req.body.reportSubTitle);
        let reportDateRange = (req.body.reportDateRange);
        let reportShowDateSubmitted = (req.body.reportShowDateSubmitted);
        let reportShowDescription = (req.body.reportShowDescription);
        let selectedProgressPhotos = (req.body.selectedProgressPhotos);
        let isSiteAdmin = req.body.isSiteAdmin;
        let isFolderDownload = req.body.isFolderDownload;
        if (!projectId) {
            return ResponseService.errorResponse(res, 'Project Id is required');
        }
        let filter = { project_ref: projectId };

        if (!fromDate || !toDate) {
            return ResponseService.errorResponse(res, 'from date and to date are required.');
        }

        try {
            sails.log.info('Download pdf of progress-photos with filter:', filter, fromDate, toDate, `is_inherited_project: ${is_inherited_project}`);
            let select = isFolderDownload ? ["pp_images", "id"] : ["*"];
            let allProgressPhotos =  await sails.models.progressphotos_reader.find({
                select: select,
                where: {
                    createdAt: {'>=': fromDate, '<=': toDate},
                    ...filter
                },
                sort: ['createdAt ASC']
            });
            allProgressPhotos = await populateUserRefs(allProgressPhotos, 'user_ref', []);

            sails.log.info("progress photos length ", allProgressPhotos.length);
            if (allProgressPhotos && allProgressPhotos.length) {
                let projectInfo = await sails.models.project_reader.findOne({
                    select: ['name', 'project_number', 'project_category', 'parent_company', 'client', 'contractor', 'custom_field'],
                    where: {
                        id: projectId
                    }
                });

                let { project_logo_file, companyName } = await getCompanyInfo(projectInfo);

                let totalPhotos = 0;
                let allphotoUrl = await sails.models.userfile_reader.find({id: selectedProgressPhotos}).select(['file_url', 'md_url', 'sm_url', 'file_mime', 'img_translation']);
                sails.log.info('Preparing Progress Photos with images.');
                let imgUrls = [];
                let sortedProgressPhotos = (selectedProgressPhotos || []).reduce((allPhotos, row, index) => {
                    let filesUrl = (allphotoUrl || []).find((photo) => photo.id === row);
                    let photosData = (allProgressPhotos || []).find(progressPhoto => {
                        return progressPhoto.pp_images.includes(row);
                    });
                    if(photosData) {
                        if(filesUrl.file_mime == 'application/pdf') {
                            for(let i = 0, len = filesUrl.img_translation.length; i <  len; i++) {
                                let clonedData = {...photosData};
                                clonedData.pp_images = {};
                                clonedData.pp_images[`${index+1}.${i+1}`] = filesUrl.img_translation[i];
                                imgUrls.push(filesUrl.img_translation[i]);
                                allPhotos.push(clonedData);
                                totalPhotos++;
                            }
                        } else {
                            let clonedData = {...photosData};
                            clonedData.pp_images = {};
                            clonedData.pp_images[`${index+1}.0`] = filesUrl.md_url || filesUrl.file_url;
                            imgUrls.push(filesUrl.file_url);
                            allPhotos.push(clonedData);
                            totalPhotos++;
                        }
                    }
                    return allPhotos;
                }, []);

                if(isFolderDownload) {
                    return await downloadZippedImages(req, res, imgUrls, fileName);
                }

                let totalPages = 0;
                if (totalPhotos) {
                    totalPages = Math.round(totalPhotos / 2);
                }

                if (!reportTitle) {
                    reportTitle = "Progress Report"
                }

                if (!reportSubTitle) {
                    let companyString = (companyName) ? `${companyName} - ` : '';
                    reportSubTitle = (projectInfo.project_number != null) ? companyString.concat(`${projectInfo.project_number} - ${projectInfo.name}`) : companyString.concat(`${projectInfo.name}`);
                }

                if (!reportDateRange) {
                    reportDateRange = `Date: ${moment(req.body.fromDate).format('DD-MM-YYYY')} - ${moment(req.body.toDate).format('DD-MM-YYYY')}`
                }

                let form_template = `pages/progress-photos-report-page`;
                let html = await sails.renderView(form_template, {
                    title: reportTitle,
                    subtitle: reportSubTitle,
                    date_range: reportDateRange,
                    reportShowDateSubmitted,
                    reportShowDescription,
                    allProgressPhotos: sortedProgressPhotos,
                    projectInfo,
                    companyName,
                    project_logo_file,
                    totalPages,
                    totalPhotos,
                    getPhoto: (pp_image) => {
                        if (pp_image) {
                            let imageKey = Object.keys(pp_image);
                            if (imageKey && imageKey[0] in pp_image) {
                                return [imageKey[0], pp_image[imageKey[0]]];
                            }
                            return [];
                        }
                    },
                    replaceAll(str, find, replace) {
                        const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
                        return (str || '').replace(new RegExp(escapedFind, 'g'), replace);
                    },
                    unix(n, format) {
                        return moment(n).format(format);
                    },
                    layout: false
                });

                return await instantPdfGenerator(req, res, html, 'progress-photos-report', fileName, req.headers['user-agent'], {format: 'A4'}, 'url');
            }

            return ResponseService.errorResponse(res, 'No progress photos available during the dates.');
        } catch(failure){
            sails.log.info('Failed to find progress photos', failure);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, failure);
        }
    },

    createProgressPhotosAlbum: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let title = req.param('title');
        if (!title) {
            return ResponseService.errorResponse(res, 'album title is required');
        }
        let albumAddRequest = {
            creator_ref	: req.user.id,
            title: title,
            project_ref: projectId
        };
        sails.log.info('albumAddRequest', albumAddRequest);

        let inserted = await sails.models.album.create(albumAddRequest);
        return ResponseService.successResponse(res, {album: inserted, created: true});
    },

    updateProgressPhotosAlbum: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let id = +req.param('id');
        let title = req.param('title');
        if (!title) {
            return ResponseService.errorResponse(res, 'album title is required');
        }
        let albumUpdateRequest = {
            creator_ref	: req.user.id,
            title: title
        };
        sails.log.info('albumUpdateRequest', albumUpdateRequest);
        let updated = await sails.models.album.updateOne({id: id, project_ref: projectId}).set(albumUpdateRequest);
        return ResponseService.successResponse(res, {album: updated});

    },

    deleteProgressPhotosAlbum: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let albumId = +req.param('id');

        let progress_photos_album = await sails.models.progressphotos.find({album_ref: albumId, project_ref: projectId}).select(['id']);
        let ids = progress_photos_album.map(d => d.id);
        sails.log.info('delete album progress photos ids >>>', ids);
        if (ids.length > 0) {
            await sails.models.progressphotos.update({id: ids}).set({album_ref:null});
        }
        let deleted = await sails.models.album.destroy({id: albumId, project_ref: projectId});
        await sails.models.useralbumpref.destroy({album_ref: albumId});
        return ResponseService.successResponse(res, {album: deleted});

    },

    getAllProgressPhotosAlbums: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let pp_album = (req.param('pp_album', false).toString() === 'true');
        let user_album = await sails.models.album_reader.find({project_ref: projectId}).select(['id','title','createdAt']).sort([{ createdAt: 'DESC' }]);
        sails.log.info('Total user album found', user_album);
        let allAlbumObj = {"createdAt" : null, "id" :0, "title" : "All"};
        user_album.push(allAlbumObj);

        let project_progress_photos_album = null;
        if (pp_album) {
            let selectAlbum = await sails.sendNativeQuery(`SELECT a.id, a.title, sq.*, pp.pp_images as recent_pp_images
            FROM (
                SELECT max(pp.id) as recent_pp_id, sum(json_array_length(pp.pp_images)) as total_pp_count, a.id as album_id
                FROM progress_photos pp
                LEFT JOIN album a ON pp.album_ref = a.id
                WHERE pp.project_ref = $1
                GROUP BY album_id
                UNION
                    SELECT NULL, 0, a.id as album_id
                    FROM album a
                    WHERE project_ref=$1 and NOT EXISTS (
                        SELECT 1
                        FROM progress_photos pp
                        WHERE pp.album_ref = a.id
                        AND pp.project_ref = $1
                    )
            ) as sq
            LEFT JOIN album as a ON sq.album_id = a.id
            LEFT JOIN progress_photos pp on pp.id = sq.recent_pp_id
            LEFT JOIN (select * from user_album_pref where user_ref = $2 and project_ref = $1) uar ON uar.album_ref = a.id

            ORDER BY COALESCE(uar.sort_order, 999);`,
                 [projectId, req.user.id]);

            let almubsWithImages = selectAlbum.rows.map(r=> {
                if(!r.recent_pp_images) {
                    r.recent_pp_images = [];
                };
                return r;
            });
            sails.log.info(`selectAlbum rowCount:`, selectAlbum.rowCount);
            project_progress_photos_album = await groupByAlbumProgressImages(almubsWithImages);
            project_progress_photos_album.unshift(project_progress_photos_album.pop());

        }
        return ResponseService.successResponse(res, {
            user_album,project_progress_photos_album
        });
    },

    savePhotosAlbumsUserPreference: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let album_pref = req.param('album_pref');
        sails.log.info('Add Progress Photos for project, by', req.user.id, projectId);
        let albumIds = album_pref.map(ar => ar.album_ref);
        var existingRecords  = await sails.models.useralbumpref.find({where:{album_ref: albumIds, and: [{user_ref:req.user.id}, {project_ref:projectId }]}});
        let recordsToCreate = [];
        if(existingRecords.length === 0) {
            recordsToCreate = album_pref.map(ar => {
                ar.user_ref = req.user.id;
                ar.project_ref = projectId;
                return ar;
            })
        } else {
            for(let i=0; i<album_pref.length; i++) {
                let record = album_pref[i];
                let pref = existingRecords.find(e => e.album_ref === record.album_ref);
                if (!pref) {
                    record.user_ref = req.user.id;
                    record.project_ref = projectId;
                    recordsToCreate.push(record);
                }
                else {
                    await sails.models.useralbumpref.update({id: pref.id}).set({sort_order: record.sort_order});
                }
            }
        }
        if(recordsToCreate.length) {
            await sails.models.useralbumpref.createEach(recordsToCreate);
        }

        return ResponseService.successResponse(res, {});
    },

    getProgressPhotosList: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let pageSize = +req.param('pageSize', 50);
        let pageNumber = +req.param('pageNumber', 0);
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'desc');
        let owner = req.param('owner', null);
        let albumId = req.param('albumId', null);
        let raisedBy = +req.param('raisedBy', 0);
        let searchTerm = (req.param('q', '')).toString().trim();

        let defaultResponse = {
            progress_photos: [],
            totalCount: 0,
            projectId,
            q: searchTerm,
            pageSize,
            pageNumber,
            sortKey,
            sortDir,
            owner,
            albumId,
            raisedBy,
        };

        if(raisedBy !== 0 && raisedBy !== req.user.id) {
            sails.log.info(`Returning 0 CoW record, for projectId: ${projectId}, raisedBy: ${raisedBy}. ERR_mismatch_raisedBy.`);
            return ResponseService.successResponse(res, defaultResponse);
        }

        if(raisedBy == 0 && !req.is_verified_ca_cpa && !req.is_verified_sa){
            sails.log.error('Restricted SITE_ADMIN/CA/CPA API call, User:', (req.user && req.user.id), req.path, `Time: ${moment().format()} Origin HOST:`, req.headers['host'], 'UA:', req.headers['user-agent']);
            return ResponseService.sendResponse(res, ResponseService.authErrorObject(sails.__('user_does_not_have_permission')));
        }

        let {
            total: totalCount,
            records: progress_photos
        } = await progressPhotoFn.getProgressPhotosPage(projectId, pageSize, (pageSize * pageNumber), sortKey, sortDir, {
            searchTerm, albumId, owner, ...(raisedBy && {userId: raisedBy}),
        });

        if (!progress_photos.length) {
            sails.log.info(`No CoW record found, for projectId: ${projectId}, raisedBy: ${raisedBy}, q: "${searchTerm}" pageNumber: ${pageNumber} pageSize: ${pageSize} totalCount: ${totalCount}`);
            return ResponseService.successResponse(res, {...defaultResponse, totalCount});
        }

        let {pp_images} = await progressPhotoFn.expandProgressPhotoFiles(progress_photos.map(pp => pp.id));

        for (let i = 0; i < progress_photos.length; i++) {
            let pp_image_row = (pp_images || []).find(row => row.id === progress_photos[i].id) || {};
            progress_photos[i].pp_images = pp_image_row.pp_images;
        }

        return ResponseService.successResponse(res, {
            ...defaultResponse,
            progress_photos,
            totalCount,
        });
    },
};
