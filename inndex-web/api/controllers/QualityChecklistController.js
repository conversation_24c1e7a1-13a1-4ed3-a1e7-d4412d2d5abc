/**
 * Created by <PERSON><PERSON><PERSON> on 12/05/21.
 */

const {
    UserRevisionService: {getLatestUserRevision},
    TokenUtil: {
        getCompanyInfo,
        allProjectAdminsByOneOfDesignations,
        filterProjectUsersEmailEligibility,
        ROLES, 
        resourceIdentifier
    },
    ResponseService: {
        errorResponse,
        successResponse
    },
    DataProcessingService: {
        buildRecordRef,
        getProjectTimezone,
        getUserFullName,
        populateUserRefs,
        populateProjectRefs,
        shareReportViaEmail,
        capitalizeFirstLetter: capitalize
    },
    HttpService: { typeOf },
    NotificationService: {
        queuePushNotifications,
        NOTIFICATION_CATEGORY,
        sendNotification
    },
    EmailService: {
        queueEmailNotifications,
    },
    SharedService: {
        downloadPdfViaGenerator
    }
} = require('./../services');
const {
    QCValidator: {
        createOrUpdateChecklistRecord,
        createChecklistInspectionRecord,
        validateSignoffRequest
    }
} = require('./../validators');
const { DEFAULT_PAGE_SIZE, EMAIL_NOTIFICATION_FEATURE_CODES } = sails.config.constants;
const _uniq = require('lodash/uniq');
const moment = require('moment');
const momentTz = require('moment-timezone');
const { qualitychecklistFn:{ getITPChecklistInsp, getAssignedToInspectionList }, inductionFn: {getUserInductionDetailsFromUserIds}} = require('../sql.fn');
const dbDateFormat = 'DD-MM-YYYY';
const dayjs = require('dayjs');
const dateTimeFormat = 'DD-MM-YYYY HH:mm:ss';
const ITP_TYPE = {
    project: 'project',
    company: 'company'
}
const putCorrectIDValue = (itemArr) => {
    return (itemArr || []).reduce((arr, value) => {
        if (value && typeOf(value, 'number')) {
            arr.push(value)
        } else if (value && typeOf(value, 'object') && value.id) {
            arr.push(value.id);
        }
        return arr;
    }, []);
}

const cleanItem = (item) => {
    if (item.photos) {
        item.photos = putCorrectIDValue(item.photos);
    }

    if (item.close_out && item.close_out.images) {
        item.close_out.images = putCorrectIDValue(item.close_out.images);
    }
    return item;
};
const extractImageIdsAndUserRefs = (items) => {
    let imageIds = [];
    let userRefs = [];

    items.forEach(item => {
        if (item.photos && item.photos.length) {
            imageIds.push(...item.photos);
        }
        if (item.close_out && item.close_out.images && item.close_out.images.length) {
            imageIds.push(...item.close_out.images);
        }
        if (item.tagged_user_ref && !isNaN(item.tagged_user_ref)) {
            userRefs.push(item.tagged_user_ref);
        }
    });

    return { imageIds, userRefs };
};
const populateImageIdsAndUserRefs = (items, itemImages, usersInfoMap, projectId) => {
    return items.map(item => {
        if (item.photos && item.photos.length) {
            item.photos = itemImages.filter(img => item.photos.includes(img.id));
        }
        if (item.close_out && item.close_out.images && item.close_out.images.length) {
            item.close_out.images = itemImages.filter(img => item.close_out.images.includes(img.id));
        }

        if (item.tagged_user_ref && !isNaN(item.tagged_user_ref)) {
            const key = `${item.tagged_user_ref}_${projectId}`;
            item.tagged_user_ref = usersInfoMap[key] || item.tagged_user_ref;
        }

        return item;
    });
};


const viewOrDownloadQualityChecklistTemplate = async ( req, res, type, qclId, projectInfo ) => {
    let qualityChecklist = await sails.models.qualitychecklist_reader.findOne({id: qclId});
    let qclDocRows = 0;
    let qclDocuments = [];
    if(qualityChecklist && qualityChecklist.id) {

        if(qualityChecklist.qc_doc && qualityChecklist.qc_doc.length) {
        sails.log.info(`Expanding quality checklist docs...`);
        qclDocRows += qualityChecklist.qc_doc.length;
        qclDocuments = await sails.models.userfile_reader.find({
            where: {id: _uniq(qualityChecklist.qc_doc)},
            select: ['id', 'file_url', 'name', 'user_id', 'createdAt', 'img_translation']
        });
        qclDocuments = await populateUserRefs(qclDocuments, 'user_id', []);
        sails.log.info(`Total docs, ${qclDocuments.length}`);
    }
    let { project_logo_file, companyName } = await getCompanyInfo(projectInfo);
    let form_template = `pages/quality-checklist-form-page`;
    let html =  await sails.renderView(form_template, {
        type: 'pdf',
        title: (qualityChecklist.qc_title) ? qualityChecklist.qc_title : `ITP Inspection`,  
        project: projectInfo,
        qualityChecklist,
        companyName,
        qclDocuments,
        project_logo_file,
        layout: false,
        showTemplate: true,
        momentTz(n, format) {
            return projectInfo.custom_field.timezone ? momentTz(+n).tz(projectInfo.custom_field.timezone).format(format) : moment(n).format(format);
        },
        getSequenceNum(question) {
            return  Math.ceil(((question || '').length / 66));
        },
        toFixed(number) {
            return (+number) ? (+number).toFixed(4) : 0;
        }
    });
    if (type === 'pdf') {
        sails.log.info('Generating pdf');
        let tz = getProjectTimezone(projectInfo);
        let qclPhrase = projectInfo.custom_field.qcl_phrase_singlr || 'ITP';
        let file_name = `${qclPhrase}-${qualityChecklist.qc_title}-${moment().format(dbDateFormat)}`;
        let project_line = `${(projectInfo.project_number != null) ? projectInfo.project_number + ' - ' + projectInfo.name : projectInfo.name} (#${projectInfo.id}): ${projectInfo.contractor}`;
        
        return await downloadPdfViaGenerator({
            req,
            res,
            html,
            tool: 'quality-checklist',
            file_name,
            heading_line: (qualityChecklist.qc_title) ? qualityChecklist.qc_title : `ITP Inspection`,
            project_line,
            date_line: '',
            logo_file: project_logo_file,
            has_cover: true,
        });
    }
    sails.log.info('Rendering html view');
    return res.send(html);
    }
}
const viewORDownloadChecklistReport = async ( req, res, id, type, updatedAt ) => {

        sails.log.info('Fetch project quality checklist inspection:', id);

        let { checklistInspection: [qclInspection] }= await getChecklistInspection({id:id}, 'DESC', true, true, false, true, null, null, true);

        if (qclInspection && qclInspection.id) {
            sails.log.info('got record, id', qclInspection ? qclInspection.id : undefined);
            if (+qclInspection.updatedAt !== +updatedAt) {
                sails.log.info('Unauthorized  request.');
                return errorResponse(res, 'Unauthorized request.');

            }
            let qualityChecklist = qclInspection.checklist_ref;
            let html = await getITPReportHtml(qclInspection);
            if (type === 'pdf') {
                sails.log.info('Generating pdf');
                let project = qclInspection.project_ref;
                let tz = getProjectTimezone(qclInspection.project_ref);
                let report_datetime = (+qclInspection.report_datetime) ? +qclInspection.report_datetime : +qclInspection.createdAt;
                let { project_logo_file, companyName } = await getCompanyInfo(qclInspection.project_ref);
                let qclPhrase = qclInspection.project_ref.custom_field.qcl_phrase_singlr || 'ITP';
                let recordRef = buildRecordRef(qclInspection);
                let preparedBy = `${qclInspection.user_ref.first_name}${' ' + qclInspection.user_ref.last_name}`.trim();
                let file_name = `${qclPhrase}-${recordRef}-${qualityChecklist.qc_title}-${moment().format(dbDateFormat)}-${preparedBy}`;
                let project_line = `${(project.project_number != null) ? project.project_number + ' - ' + project.name : project.name} (#${project.id}): ${project.contractor}`;
                let date_line = `Report Date: ${momentTz(report_datetime).tz(tz).format('DD-MM-YY HH:mm:ss')}`;

                return await downloadPdfViaGenerator({
                    req,
                    res,
                    html,
                    tool: 'quality-checklist',
                    file_name,
                    heading_line: (qualityChecklist.qc_title) ? qualityChecklist.qc_title : `ITP Inspection`,
                    project_line,
                    date_line,
                    logo_file: project_logo_file,
                    has_cover: true,
                });
            }
            sails.log.info('Rendering html view');
            return res.send(html);
        }

        sails.log.info('Failed to find Quality Checklist Inspec.');
        return errorResponse(res, sails.__('internal server error'));
}


const expandQualityChecklists = async (quality_checklists = []) => {
    // reduce array to get doc Ids
    let qclDocIds = quality_checklists.reduce((ids, record) => {
        if (record.qc_doc && record.qc_doc.length) {
            ids.push(...record.qc_doc);
        }
        return ids;
    }, []);

    let _qclDocIds = _.uniq(qclDocIds);
    sails.log.info('Fetching Quality Checklists Docs', _qclDocIds);
    _qclDocIds = _qclDocIds.filter(docId => docId != null);
    if (_qclDocIds.length) {
        let docFiles = await sails.models.userfile.find({
            where: {id: _qclDocIds},
            select: ['id', 'file_url', 'name']
        });

        if  (docFiles && docFiles.length) {
            quality_checklists = quality_checklists.map(record => {
                if (record.qc_doc) {
                    record.qc_doc = record.qc_doc.map(doc_id => {
                        return docFiles.find(doc => doc.id === doc_id);
                    });
                }
                return record;
            });
        }
    }

    return quality_checklists;
};


const cleanPayloadForImages = (record) => {
    if(record.has_subheadings) {
        (record.items || []).forEach(heading => {
            heading.subheadings.forEach(item => {
                cleanItem(item);
            })
        })
    } else {
        (record.items || []).forEach(item => {
            cleanItem(item);
        })
    }
    return record;
}

const fetchAndAssignInspectionData = async (where) => {
    let { checklistInspection: [itpInspection] } = await getChecklistInspection(where, 'DESC', true, false);
    let projectInfo = itpInspection.project_ref;
    let tz = getProjectTimezone(projectInfo);
    return {itpInspection, projectInfo, tz}
}

const getChecklistInspection = async (filter, OrderBy, expandItems = false, useReadReplica = true, limitedData = false, excludeFinalised = false, pageSize= null, pageNumber= null, allReports = false) => {

    let whereFilter = {
        ...filter,
        sortDir:OrderBy,
        sortKey: 'id'
    }
    if(pageSize !== null && pageNumber !== null){
        whereFilter = {
            ...filter,
            limit: pageSize,
            offset: pageSize * pageNumber,
            sortDir: OrderBy,
            sortKey: 'id'
        }
    }

    let { records: checklistInspection, total } = await getITPChecklistInsp(whereFilter, useReadReplica, limitedData, excludeFinalised, allReports);

    checklistInspection = checklistInspection.map(qc => ({
        ...qc,
        record_ref: buildRecordRef(qc),
        createdAt:  +qc.createdAt,
        updatedAt: +qc.updatedAt,
      }));
    checklistInspection = await populateProjectRefs(checklistInspection, 'project_ref', limitedData ? ['id','name'] : []);


    checklistInspection = (checklistInspection || []).filter(it => it.project_ref != null);
    sails.log.info('Fetched project checklist inspections.', checklistInspection.length);

    if (expandItems) {
        checklistInspection = await expandImageAndUserRefs(checklistInspection);
    }
    return { checklistInspection, total };
};

const getQualityChecklists = async (filter, OrderBy, expandItems = false, itp_type, projectId = 0, enabledOnly = false) => {

    let qualityChecklists = await sails.models.qualitychecklist.find({where: filter})
        .sort([
            {id: OrderBy}
        ]);
    if(itp_type === ITP_TYPE.project){
        qualityChecklists = (qualityChecklists || []).filter(it => it.project_ref != null);
        qualityChecklists = await populateProjectRefs(qualityChecklists, 'project_ref', []);
    }
    if(itp_type === ITP_TYPE.company && projectId && enabledOnly){
        qualityChecklists = qualityChecklists.filter(checklist => 
            checklist.activate_on_projects.some(project => 
              project.project_id === projectId && project.enabled === true
            )
          );
    }
    qualityChecklists = await populateUserRefs(qualityChecklists, 'user_ref', []);
    sails.log.info('Fetched project checklists.', qualityChecklists.length);

    if (expandItems) {
        qualityChecklists = await expandQualityChecklists(qualityChecklists);
    }
    return qualityChecklists;
};

const sendMailToTaggedUser = async (item, projectInfo, reportNumber, reportDateTime, userName, itp_title) => {
    let imagesSrc = [];
    if(item.photos && item.photos.length) {
        (item.photos || []).map(imageObj => {
            imagesSrc.push((imageObj.sm_url || imageObj.file_url));
        })
    }

    let taggedUserInfo = item.tagged_user_ref;
    let item_title = `${item.question}`;
    sails.log.info(`Send email to ${taggedUserInfo.email} for item ${item_title}`);
    let taggedUserFname = taggedUserInfo.first_name;
    let subject = `Defect Identified: Project - ${projectInfo.name}`;
    let html = await sails.renderView('pages/mail/mail-content', {
        mail_body: 'itp-tagged-in-item-notify',
        title: subject,
        responsible_user_fname: taggedUserFname,
        project_name: projectInfo.name,
        inspection_number: reportNumber,
        itp_title,
        inspection_date_time: reportDateTime,
        inspection_carried_out_by: userName,
        item_title,
        summary: (item.summary) ? item.summary : 'N/A',
        action: (item.corrective_action_required) ? item.corrective_action_required : 'N/A',
        images: imagesSrc,
        layout: false
    });
    await EmailService.sendMail(subject, [taggedUserInfo.email], html);
};

function extractPlatformVersion(platform) {
    if (!platform) return false; // Handle missing values

    // Extract version from the string
    const versionMatch = platform.match(/\b(\d+\.\d+\.\d+)\b/);
    if (!versionMatch) return false; // Return false if no version is found

    const version = versionMatch[1]; // Extracted version string
    console.log("Extracted Version:", version);

    // Regex to validate versions between 4.0.0 and 4.0.08 inclusively
    const versionPattern = /^4\.0\.0[0-8]?$/; 
    return versionPattern.test(version);
}

const sendPushNotification = async({message, messageTitle, itp_id, itp_inspection_id, item_info, category, projectInfo, recipientUserInfo, submittedByUserInfo}) => {
    sails.log.info(`Sending push notification for ITP item to user: ${recipientUserInfo.id}`);
    let profile_pic = null;
    if(submittedByUserInfo.profile_pic_ref) {
        if (submittedByUserInfo.profile_pic_ref && (typeof submittedByUserInfo.profile_pic_ref === "number")) {
            submittedByUserInfo.profile_pic_ref = await sails.models.userfile_reader.findOne({
                where: {id: submittedByUserInfo.profile_pic_ref},
                select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation', 'file_mime']
            });
        }
        profile_pic = submittedByUserInfo.profile_pic_ref.file_url;
    }

    let notificationData = {
        user_ref: recipientUserInfo.id,
        category,
        message,
        title: messageTitle,
        data: {
            category: category,
            profile_pic: profile_pic,
            project_ref: projectInfo.id,
            itp_id,
            itp_inspection_id,
            item_info: JSON.stringify(item_info)
        },
    };
    let notification = await sails.models.usernotifications.create(notificationData);
    let deviceInfo = await sails.models.registereddevices_reader.find({user_ref: recipientUserInfo.id});
    sails.log.info(`Found ${deviceInfo.length} devices for user: #${recipientUserInfo.id}`);
    let messageData = {};
    for(const device of deviceInfo) {
        messageData = {
            notification: {
                title: messageTitle,
                body: message
            },
            android: {
                notification: {
                    icon: 'ic_icon_inndex',
                    color: '#14152D'
                }
            },
            data: {
                category: category,
                project_ref: projectInfo.id.toString(),
                notification_ref: notification.id.toString(),
                profile_pic: profile_pic || '',
                itp_id: JSON.stringify(itp_id),
                itp_inspection_id: itp_inspection_id.toString(),
                item_info: JSON.stringify(item_info)
            },
            token: device.token
        };
        await sendNotification(messageData);
    }
};
async function processDefectItem(item, itpInspection, checklistRef, projectInfo, reportNumber, reportDateTime, submittedByUserName, ITPChecklist, itpPhrase, profile_pic) {
    if (item && item.is_defect && item.tagged_user_ref) {
        await sendMailToTaggedUser(item, projectInfo, reportNumber, reportDateTime, submittedByUserName, ITPChecklist.qc_title);

        const taggedUserInfo = item.tagged_user_ref;
        const itemTitle = `${item.question}`;
        const message = `${submittedByUserName} has tagged you in ${itemTitle} item and marked it as defect on ${projectInfo.name}`;
        
        await queuePushNotifications(
            { title: `Tagged in ${itpPhrase} item`, body: message },
            {
                category: NOTIFICATION_CATEGORY.ITP_INSPECTION,
                project_ref: projectInfo.id.toString(),
                profile_pic: profile_pic || '',
                itp_id: JSON.stringify(checklistRef),
                itp_inspection_id: itpInspection.id.toString(),
                item_info: JSON.stringify(item),
            },
            [taggedUserInfo.id]
        );
    }
}


const getITPReportHtml = async (qclInspection, type = 'pdf') => {
    let totalPages = 2;
    let { project_logo_file, companyName } = await getCompanyInfo(qclInspection.project_ref);
    let qualityChecklist = qclInspection.checklist_ref;

    let qclDocRows = 0;
    let qclDocuments = [];
    if(qualityChecklist.qc_doc && qualityChecklist.qc_doc.length) {
        sails.log.info(`Expanding quality checklist docs...`);
        qclDocRows += qualityChecklist.qc_doc.length;
        qclDocuments = await sails.models.userfile_reader.find({
            where: {id: _uniq(qualityChecklist.qc_doc)},
            select: ['id', 'file_url', 'name', 'user_id', 'createdAt', 'img_translation']
        });
        qclDocuments = await populateUserRefs(qclDocuments, 'user_id', []);
        sails.log.info(`Total docs, ${qclDocuments.length}`);
    }

    //considering two columns here
    let checklistTableMaxRowsParColumn = Math.ceil((qclInspection.items.length + 2)/2);

    sails.log.info(`Checklist row per column: ${checklistTableMaxRowsParColumn}`, `& Total checklist row:  ${qclInspection.items.length}`);
    let itemsInfoPage = [];
    if(qclInspection.has_subheadings){
        qclInspection.items.forEach((heading,i) => {
            heading.subheadings.forEach(function(item, j) {
                let itemInfo =  populateITPItemsHTMLData(item, i,  type, j, heading);
                if(itemInfo){
                    itemsInfoPage.push(itemInfo);
                } 
            })
        })
    }else{
        qclInspection.items.forEach(function(item, i) {
            let itemInfo = populateITPItemsHTMLData(item, i, type);
            if(itemInfo){
                itemsInfoPage.push(itemInfo);
            } 
        });
    }
    qclInspection.custom_field_data =  qclInspection.custom_field_data.filter(a =>  a.field_name && a.field_name !== 'updated_by')
    let report_datetime = (+qclInspection.report_datetime) ? +qclInspection.report_datetime : +qclInspection.createdAt;
    let form_template = `pages/quality-checklist-form-page`;
    return await sails.renderView(form_template, {
        type,
        title: (qualityChecklist.qc_title) ? qualityChecklist.qc_title : `ITP Inspection`,
        report_datetime,
        project: qclInspection.project_ref,
        checklistTableMaxRowsParColumn,
        companyName,
        qclInspection,
        qualityChecklist,
        qclDocuments,
        itemsInfoPage,
        totalPages,
        moment,
        dateFormat: 'DD/MM/YYYY',
        project_logo_file,
        layout: false,
        showTemplate: false,
        unix(n, format) {
            return moment.unix(n).format(format);
        },
        momentTz(n, format) {
            return qclInspection.project_ref.custom_field.timezone ? momentTz(+n).tz(qclInspection.project_ref.custom_field.timezone).format(format) : moment(n).format(format);
        },
        capitalizeFirstLetter(string) {
            return capitalize(string);
        },     
        getSequenceNum(question) {
            return  Math.ceil(((question || '').length / 66));
        },
        toFixed(number) {
            return (+number) ? (+number).toFixed(4) : 0;
        }
    });
};

const populateITPItemsHTMLData = (item, i, type, j = 0, heading = null) => {
    sails.log.info(`[getITPReportHtml]: item : ${JSON.stringify(item)}, type: ${type}, heading:${JSON.stringify(heading)} headingIndex:${i}, subheadingIndex:${j}`);
    let itemInfo = {};
    if (item.summary || (item.photos && item.photos.length) || (item.is_defect && item.is_defect == true)) {
        
        itemInfo.title = heading ? `${i+1}.${j+1} ${item.question}` : `${i+1}. ${item.question}` ;
        itemInfo.answer = item.answer;
        itemInfo.summary = (item.summary) ? item.summary : 'N/A';
        itemInfo.is_defect = item.is_defect;
        itemInfo.corrective_action = (item.corrective_action_required) ? item.corrective_action_required : '';
        itemInfo.assigned_to = (item.tagged_user_ref && item.tagged_user_ref.id) ?
            `${getUserFullName(item.tagged_user_ref)} (${item.tagged_user_ref.email})` : '';

        if (item.photos && item.photos.length) {
            itemInfo.images = [];
            itemInfo.images.push(...item.photos);
        }

        if (item.close_out && Object.keys(item.close_out).length) {
            itemInfo.item_closeout_reviewd_by = (item.close_out.reviewed_by) ? item.close_out.reviewed_by : 'N/A';
            itemInfo.item_closeout_at = +item.close_out.close_out_at ? moment(+item.close_out.close_out_at).format('DD-MM-YYYY HH:mm:ss') : 'N/A';
            itemInfo.item_closeout_detail = item.close_out.details;

            if (item.close_out.images && item.close_out.images.length) {
                itemInfo.item_closeout_images = [];
                item.close_out.images.forEach(function(image, i) {
                    if (image.img_translation && image.img_translation.length) {
                        itemInfo.item_closeout_images.push(...image.img_translation);
                    } else if (image.file_url) {
                        if(type == 'pdf') {
                            itemInfo.item_closeout_images.push((image.sm_url || image.md_url || image.file_url));
                        } else {
                            itemInfo.item_closeout_images.push(image.file_url);
                        }
                    }
                });

            }
        }
        return itemInfo;
    }
}

const expandImageAndUserRefs = async (checklistInspection) => {
    let itemImageIds = [], userProjectPairs = [];

    for (let clInspection of checklistInspection) {
        const projectId = clInspection.project_ref?.id;

        if (clInspection.has_subheadings) {
            (clInspection.items || []).forEach(heading => {
                const { imageIds: headingImageIds, userRefs: headingUserRefs } = extractImageIdsAndUserRefs(heading.subheadings || []);
                itemImageIds.push(...headingImageIds);
                userProjectPairs.push(...headingUserRefs.map(userId => ({ userId, projectId })));

                if (heading.assigned_to) {
                    userProjectPairs.push({ userId: heading.assigned_to, projectId });
                }
                if (heading.updated_by && heading.updated_by.id) {
                    userProjectPairs.push({ userId: +heading.updated_by.id, projectId });
                }
            });
        } else {
            const { imageIds: itemsImageIds, userRefs: itemsUserRefs } = extractImageIdsAndUserRefs(clInspection.items || []);
            itemImageIds.push(...itemsImageIds);
            userProjectPairs.push(...itemsUserRefs.map(userId => ({ userId, projectId })));
        }

        (clInspection.sign_off_data || []).forEach(data => {
            if (data.user_ref) {
                userProjectPairs.push({ userId: data.user_ref, projectId });
            }
        });

        if (clInspection.user_ref) {
            userProjectPairs.push({ userId: clInspection.user_ref, projectId });
        }
    }

    sails.log.info('Checklist item & closeout images, ', _uniq(itemImageIds));

    const itemImages = await sails.models.userfile_reader.find({
        where: { id: _uniq(itemImageIds) },
        select: ['id', 'file_url', 'sm_url']
    });

    const seen = new Set();
    const uniquePairs = userProjectPairs.filter(({ userId, projectId }) => {
        const key = `${userId}_${projectId}`;
        if (seen.has(key)) return false;
        seen.add(key);
        return true;
    });

    const usersInfoMap = {};
    for (const { userId, projectId } of uniquePairs) {
        const userInfo = await getUserInductionDetailsFromUserIds([userId], projectId);
        if (userInfo && userInfo.length) {
            usersInfoMap[`${userId}_${projectId}`] = userInfo[0]; 
        }
    }

    checklistInspection.map(clInspection => {
        const projectId = clInspection.project_ref?.id;

        if (clInspection.user_ref) {
            clInspection.user_ref = usersInfoMap[`${clInspection.user_ref}_${projectId}`];
        }

        if (clInspection.has_subheadings) {
            (clInspection.items || []).forEach(heading => {
                heading.subheadings = populateImageIdsAndUserRefs(heading.subheadings || [], itemImages, usersInfoMap, projectId);
                if (heading.assigned_to) {
                    heading.assigned_to = usersInfoMap[`${heading.assigned_to}_${projectId}`];
                }
                if (heading.updated_by && heading.updated_by.id) {
                    heading.updated_by = usersInfoMap[`${+heading.updated_by.id}_${projectId}`] || heading.updated_by;
                }
            });
        } else {
            clInspection.items = populateImageIdsAndUserRefs(clInspection.items || [], itemImages, usersInfoMap, projectId);
        }

        (clInspection.sign_off_data || []).forEach(item => {
            if (item.user_ref) {
                item.user_ref = usersInfoMap[`${item.user_ref}_${projectId}`];
            }
        });

        clInspection.record_ref = buildRecordRef(clInspection);
        return clInspection;
    });

    return checklistInspection;
};



const proccessITPItemCloseout = async (res, itpInsection, itemIdx, itemInfo, subItemIdx = 0) => {
    if (itpInsection && itpInsection.id) {
        let has_subheadings = itpInsection.has_subheadings;
        let checklist;
        if(has_subheadings){
            if(itpInsection["items"][itemIdx] && itpInsection["items"][itemIdx].subheadings) {
                checklist = itpInsection["items"][itemIdx].subheadings;
            } else {
                return errorResponse(res, `no checklist found with related item index`);
            }
        } else {
            checklist = itpInsection["items"];
        }
 
        checklist = (checklist || []).map((item, index) => {
            if (index ===  (has_subheadings ? subItemIdx : itemIdx)) {
                item = {...item,...itemInfo};
            }
            return item;
        });

        sails.log.info('Updating ITP inspection to close out an item.');
        let updateRequest = {};
        if (has_subheadings) {
            updateRequest["items"] = [...itpInsection.items];
            updateRequest["items"][itemIdx] = {
                ...updateRequest["items"][itemIdx],
                subheadings: checklist
            };
        } else {
            updateRequest["items"] = checklist;
        }
        let updatedITPInspection = await sails.models.checklistinspection.updateOne({id: itpInsection.id}).set(updateRequest);

        sails.log.info('Item Closeout: Updated ITP inspection successfully.');
        return successResponse(res, {project_itp_inspection: updatedITPInspection});
    }

    sails.log.info('Failed to update ITP inspection to close out an item.');
    return errorResponse(res, sails.__('Failed to update ITP inspection to close out an item.'));
};

const createUpdateChecklistReport = async (user, createUpdateRequest, checklistRef, reportId) => {
    createUpdateRequest.user_ref = user.id;
    let ITPChecklist = await sails.models.qualitychecklist_reader.findOne({id: checklistRef});
    createUpdateRequest.status =  await createStatusTypeForChecklistRecord(createUpdateRequest, ITPChecklist)
    let revision = await getLatestUserRevision(createUpdateRequest.user_ref);
    createUpdateRequest.user_revision_ref = revision.id;

    sails.log.info('Adding quality checklist.');

    let qualityChecklistReport;
    if (reportId) {
        qualityChecklistReport = await sails.models.checklistinspection.updateOne({ id: reportId }).set(createUpdateRequest);
        sails.log.info('Updated quality checklist successfully.');
    } else {
        qualityChecklistReport = await sails.models.checklistinspection.create(createUpdateRequest);
        sails.log.info('Created quality checklist successfully.');
    }
    let {itpInspection, projectInfo, tz} = await fetchAndAssignInspectionData({ id: qualityChecklistReport.id, finalised: qualityChecklistReport.finalised });
   
    let subject = `${ITPChecklist.qc_title}: ${projectInfo.name}`;
    if(itpInspection.status === 1){
        let pendingSignoff = (itpInspection.items || []).find(cl => cl.assigned_to && !cl.sign_off);
        if(pendingSignoff){
            let users = await sails.models.inductionrequest_reader.find({where: {user_ref: [ pendingSignoff.request.user_id, pendingSignoff.assigned_to], project_ref: qualityChecklistReport.project_ref, status_code:[2,6] },
                select: ['additional_data']});
            let requestor = users.find(u=> u.additional_data.user_info.id === pendingSignoff.request.user_id);
            let assignee = users.find(u=> u.additional_data.user_info.id === pendingSignoff.assigned_to );
            let assignee_user_info = assignee && assignee.additional_data && assignee.additional_data.user_info;
            let emailHtml = await sails.renderView('pages/mail/notify-section-assignee', {
                user: {first_name: assignee_user_info && assignee_user_info.name},
                itp: {section_title: pendingSignoff.heading, title: ITPChecklist.qc_title},
                author: {name: requestor.additional_data.user_info.name, induction_company: requestor.additional_data.employment_detail.employer},
                completion_date: dayjs(pendingSignoff.last_updated).tz(tz).format(dbDateFormat),
                completion_time:dayjs(pendingSignoff.last_updated).tz(tz).format('HH:mm'),
                layout: false
            });
                  
            await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.PLAIN_MESSAGE_ALERT, [{name: assignee_user_info.name, email:assignee_user_info.email, id: assignee_user_info.id}], {
                messageInfo: {
                    mail_title: subject,
                    html_message: emailHtml,
                    origin_action: `itp`
                },
            });
        }
    }      
    if (itpInspection.finalised) {
        ({ itpInspection, projectInfo, tz } = await fetchAndAssignInspectionData({ id: itpInspection.id }));
        let itpPhrase = projectInfo.custom_field.qcl_phrase_singlr;
        let submittedByUserName = getUserFullName(user);
        let reportNumber = buildRecordRef(itpInspection);
        let report_datetime = (+itpInspection.report_datetime) ? +itpInspection.report_datetime : +itpInspection.createdAt;
        let reportDateTime = tz ? momentTz(report_datetime).tz(tz).format('DD-MM-YY HH:mm:ss') : moment(report_datetime).format('DD-MM-YY HH:mm:ss');
        let ITPChecklist = itpInspection.checklist_ref;
        let profile_pic = '';
        if(user.profile_pic_ref) {
            if (user.profile_pic_ref && (typeof user.profile_pic_ref === "number")) {
                user.profile_pic_ref = await sails.models.userfile_reader.findOne({
                    where: {id: user.profile_pic_ref},
                    select: ['id', 'file_url']
                });
            }
            profile_pic = user.profile_pic_ref.file_url;
        }
        if(itpInspection.has_subheadings){
            (itpInspection.items || []).map(async(heading)=>{
                (heading.subheadings || []).forEach(async (item) => {
                    await processDefectItem(item, itpInspection, checklistRef, projectInfo, reportNumber, reportDateTime, submittedByUserName, ITPChecklist, itpPhrase, profile_pic);
                });
            })
        }else{
            (itpInspection.items || []).forEach(async (item) => {
                await processDefectItem(item, itpInspection, checklistRef, projectInfo, reportNumber, reportDateTime, submittedByUserName, ITPChecklist, itpPhrase, profile_pic);
            });
        }
        let project_name = projectInfo.name;
        let projUsrResult = await allProjectAdminsByOneOfDesignations(projectInfo.id, ['nominated', 'custom']);
        projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, 'quality_checklist');
        sails.log.info('Send email to nom managers, count:', projUsrResult.length);
        let emailHtml = await sails.renderView('pages/mail/new-itp-inspection', {
            title: subject,
            added_by: submittedByUserName,
            itp_title: ITPChecklist.qc_title,
            itpInspection,
            project_name,
            layout: false
        });
        let nodManagerRecipients = []
        for (let j = 0, len = projUsrResult.length; j < len; j++) {
            let nomManager = projUsrResult[j];
            let user_name = getUserFullName(nomManager.user_ref);
            if (nomManager.user_ref && nomManager.user_ref.email) {
                nodManagerRecipients.push({id: nomManager.user_ref.id, name: user_name, email: nomManager.user_ref.email})
            }
        }
        sails.log.info(`Sending mail to ${nodManagerRecipients.length} recipients`);
        if (nodManagerRecipients.length) {
            await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.PLAIN_MESSAGE_ALERT, nodManagerRecipients, {
                messageInfo: {
                    mail_title: subject,
                    html_message: emailHtml,
                    origin_action: `itp`
                },
            });
        }
        sails.log.info('ITP email notification has been sent.');
    }
    return itpInspection;
}

const createChecklistFn = async (req, res) => {
    sails.log.info('Create quality checklist for project, by', req.user.id);

    let {validationError} = createOrUpdateChecklistRecord(req);
    if(validationError){
        return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
    }

    let createRequest = _.pick((req.body || {}), [
        'project_ref',
        'company_ref',
        'qc_title',
        'qc_ref',
        'qc_doc',
        'sign_off_parties',
        'qc_items',
        'itp_type',
        'enabled',
        'activate_on_projects',
        'section_approval_required',
        'custom_fields',
        'has_subheadings'
    ]);

    createRequest.user_ref = req.user.id;

    let revision = await getLatestUserRevision(createRequest.user_ref);
    createRequest.user_revision_ref = revision.id;

    sails.log.info('Adding quality checklist.');
    let qualityChecklist = await sails.models.qualitychecklist.create(createRequest);

    sails.log.info('Created quality check successfully.');
    return successResponse(res, qualityChecklist);
};

const updateChecklistFn = async (req, res) => {
    let qclId = +req.param('qclId');
    let deletePartialCLI = req.param('deletePartialCLI') === 'true';
    sails.log.info('Updating Quality Checklist, id:', qclId);

    if (!qclId) {
        return ResponseService.errorResponse(res, 'quality checklist id is required');
    }
    let {validationError} = createOrUpdateChecklistRecord(req);
    if(validationError){
        return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
    }

    let updateRequest = _.pick((req.body || {}), [
        'qc_title',
        'qc_ref',
        'qc_doc',
        'sign_off_parties',
        'qc_items',
        'itp_type',
        'project_ref',
        'company_ref',
        'custom_fields',
        'section_approval_required',
        'enabled',
        'activate_on_projects',
        'has_subheadings',
        'current_active_section'
    ]);

    sails.log.info('Updating quality checklist with: ', updateRequest);

    let qualityChecklist = await sails.models.qualitychecklist.updateOne({id: qclId}).set(updateRequest);
    if(deletePartialCLI){
        await sails.models.checklistinspection.update({ checklist_ref: qclId, or: [ {finalised: false}, {status:0} ] }).set({is_deleted: 1});
    }
    sails.log.info('Updated quality checklist successfully, id', (qualityChecklist.id ? qualityChecklist.id : null));
    return ResponseService.successResponse(res, {quality_checklist: qualityChecklist});
};

const createChecklistInspectionFn = async (req, res) => {
    sails.log.info('Create checklist inspection for project, by', req.user.id);

    let createRequest = _.pick((req.body || {}), [
        'project_ref',
        'checklist_ref',
        'location',
        'summary',
        'report_datetime',
        'location_ref',
        'supervisor',
        'sign',
        'items',
        'sign_off_data',
        'custom_field_data',
        'finalised',
        'has_subheadings',
        'section_approval_required',
        'current_active_section',
        'status'
    ]);

    if (createRequest.finalised === undefined) {
        createRequest.finalised = true;
    }

    let { validationError } = createChecklistInspectionRecord(req);
    if (validationError) {
        return ResponseService.errorResponse(res, 'Invalid Request.', { validationError });
    }

    const checklistReport = await createUpdateChecklistReport(req.user, createRequest, createRequest.checklist_ref)

    sails.log.info('Created quality checklist report successfully.');
    return successResponse(res, checklistReport);
};

const updateChecklistInspectionFn = async (req, res) => {
    let projectId = +req.param('projectId');
    let reportId = +req.param('reportId');

    sails.log.info(`Update checklist inspection projectId: ${projectId}, reportId: ${reportId}, userId: ${req.user.id}`);

    let updateRequest = _.pick((req.body || {}), [
        'location',
        'summary',
        'report_datetime',
        'location_ref',
        'supervisor',
        'sign',
        'items',
        'sign_off_data',
        'custom_field_data',
        'finalised',
        'has_subheadings',
        'updated_by',
        'section_approval_required',
        'status'
    ]);

    updateRequest = cleanPayloadForImages(updateRequest);

    let { validationError } = createChecklistInspectionRecord(req);
    if (validationError) {
        return ResponseService.errorResponse(res, 'Invalid Request.', { validationError });
    }

    // check checklist report is submitted or modified
    let checklistReport = await sails.models.checklistinspection_reader.findOne({
        where: {
            id: reportId,
            project_ref: projectId,
        },
        select: ['finalised', 'updatedAt', 'version','status']
    });
    if (!checklistReport) {
        return ResponseService.errorResponse(res, 'This report no longer exist');
    }
    if (checklistReport.status === 2 && checklistReport.finalised) {
        return ResponseService.errorResponse(res, 'This report already submitted', { submitted: true });
    }
    if (checklistReport.updatedAt != req.body.updatedAt) {
        sails.log.info(`Report ${checklistReport.id} is already modified`, {server: checklistReport.updatedAt, request: req.body});
        return ResponseService.errorResponse(res, 'This report has been modified', { modified: true });
    }
    updateRequest.version = checklistReport.version + 1
    updateRequest.updated_by = {id:req.user.id, name: getUserFullName(req.user)}
    const updatedChecklistReport = await createUpdateChecklistReport(req.user, updateRequest, req.body.checklist_ref, reportId)

    sails.log.info(`Update ITP checklist project: ${projectId} and report: ${reportId} successfully.`);
    return successResponse(res, updatedChecklistReport);
};

const deleteChecklistInspectionFn = async (req, res) => {
    let projectId = +req.param('projectId');
    let reportId = +req.param('reportId');
    sails.log.info(`Delete checklist report projectId: ${projectId}, reportId: ${reportId}, userId: ${req.user.id}`);

    let itpReport = await sails.models.checklistinspection_reader.findOne({
        where: {
            id: reportId,
            project_ref: projectId,
            user_ref: req.user.id,
        }
    });

    if (!itpReport) {
        return ResponseService.errorResponse(res, 'ITP Report not found');
    }

    if (itpReport.finalised) {
        return ResponseService.errorResponse(res, 'Only draft ITP reports can be deleted');
    }
         
    let deletedRecord = await sails.models.checklistinspection.destroyOne({
        id: reportId,
        project_ref: projectId,
        user_ref: req.user.id,
        finalised: false,
    });
    return ResponseService.successResponse(res, {
        deleted: deletedRecord
    });
};

const getChecklistInspectionReportFn = async (req, res) => {
    let projectId = +req.param('projectId');
    let reportId = +req.param('reportId');

    sails.log.info(`Fetch Project: ${projectId} report id: ${reportId}`);
    let ibReport = await sails.models.checklistinspection_reader.findOne({
        where: {
            id: reportId,
            project_ref: projectId,
        }
    });

    return ResponseService.successResponse(res, ibReport);
};

const searchChecklistFn = async (req, res) => {
    let q = (req.query.q || '').toString().trim();
    let projectId = (req.query.projectId || '').toString().trim();
    if (!projectId || !projectId.length) {
        return ResponseService.errorResponse(res, 'projectId is required');
    }
    sails.log.info(`Search Quality Checklist., q: '${q}'`);
    sails.log.info("User", req.user.id);

    let filter = {};
    if(q) {
        if (q.length && !isNaN(q)) {
            filter = {id: q};
        } else {
            filter = {qc_title: {contains: q}};
        }
    }
    let searcFilter;
    if(Object.keys(filter).length) {
        searcFilter = {...filter, project_ref:projectId};
    } else {
        searcFilter = {project_ref:projectId};
    }

    let qualityChecklist = await sails.models.qualitychecklist.find().where(searcFilter)
        .sort([
            {qc_title: 'ASC'},
            {id: 'ASC'},
        ]).limit(50);

    qualityChecklist = await expandQualityChecklists(qualityChecklist);

    sails.log.info('got quality checklist, total', qualityChecklist.length);
    return ResponseService.successResponse(res, {qualityChecklist});
};

const ITP_REPORT_STATUS = {
    // DRAFT: 1, - deprecated
    COMPLETED: 2,
    PARTIALLY_COMPLETE: 3,
    IN_PROGRESS: 4
};

  const createStatusTypeForChecklistRecord = async (record, checklistRef) => {
    record = { ...record, checklist_ref: checklistRef };
    // Initial status based on whether the record is finalised
    let status = !record.finalised ? ITP_REPORT_STATUS.IN_PROGRESS : ITP_REPORT_STATUS.COMPLETED;
    const isCompleted = status === ITP_REPORT_STATUS.COMPLETED;
    const isFinalSignOffCompleted = (record.sign_off_data && record.sign_off_data.length > 0) || false;
  
    let totalSection =
      record.checklist_ref &&
      record.checklist_ref.sign_off_parties &&
      record.checklist_ref.sign_off_parties.length > 0
        ? 1
        : 0;
    let completedSection = 0;
  
    // If already finalized, mark as completed.
    if (isCompleted) {
      return status;
    }
  
    // Custom field handling
    const detailTotalLength =
      (record.checklist_ref &&
        record.checklist_ref.custom_fields &&
        record.checklist_ref.custom_fields.length) ||
      0;
    const filledDetailLength =
      (record.custom_field_data && record.custom_field_data.length) || 0;
    const sectionApproveRequired =
      (record.checklist_ref &&
        record.checklist_ref.section_approval_required) ||
      false;
    const filledItems = record.items || [];
    const itemFilled = filledItems.length > 0;
  
    //  Added extra check to match Appside behavior
    if (detailTotalLength > 0 && filledDetailLength === 0 && !itemFilled) {
      status = ITP_REPORT_STATUS.IN_PROGRESS;
      return status;
    }
  
    // Early return based on partial/full custom field completion
    // +1 because a  object contains last updated at details(getting used in App)
    if (
      filledDetailLength > 0 &&
      detailTotalLength > 0 &&
      filledDetailLength <= detailTotalLength + 1 &&
      !itemFilled
    ) {
      status = ITP_REPORT_STATUS.IN_PROGRESS;
      return status;
    }
  
    if (!sectionApproveRequired && itemFilled) {
      status = ITP_REPORT_STATUS.IN_PROGRESS;
      return status;
    }
  
    const hasSubheadings = record.has_subheadings || false;
    const qcItems = (record.checklist_ref && record.checklist_ref.qc_items) || [];
    totalSection += hasSubheadings ? qcItems.length : 1;
  
    // Calculate completed items count
    let completedItems = 0;
    if (hasSubheadings) {
      completedItems = (record.items || []).reduce(
        (sum, item) => sum + ((item.subheadings && item.subheadings.length) || 0),
        0
      );
    } else {
      completedItems = (record.items || []).length;
    }
  
    // Calculate total items count based on qcItems
    let totalItem = 0;
    if (hasSubheadings) {
      totalItem = qcItems.reduce(
        (sum, item) => sum + ((item.subheadings && item.subheadings.length) || 0),
        0
      );
    } else {
      totalItem = qcItems.length;
    }
  
    // Calculate sign-off counts
    let completedSignOff = 0;
    let requiredSignOff = hasSubheadings ? qcItems.length : 1;
  
    if (hasSubheadings) {
      (record.items || []).forEach(item => {
        requiredSignOff++;
        if (item.sign_off) {
          completedSection++;
          completedSignOff++;
        }
      });
    } else {
      const allCompleted =
        qcItems.length === (record.items ? record.items.length : 0);
      if (sectionApproveRequired) {
        if (record.items && record.items.length > 0 && record.items[0].sign_off != null) {
          completedSection++;
        }
      } else if (allCompleted) {
        completedSection++;
      }
    }
  
    // Determine final status based on computed counts
    if (totalItem === completedItems && requiredSignOff === completedSignOff) {
      status = isFinalSignOffCompleted ? ITP_REPORT_STATUS.COMPLETED : ITP_REPORT_STATUS.PARTIALLY_COMPLETE;
    } else if (totalItem > 0) {
      if (completedSection === 1) {
        const totalItemForCurrentSection = hasSubheadings
          ? ((qcItems[0] && qcItems[0].subheadings && qcItems[0].subheadings.length) || 0)
          : 0;
        const completedItemForCurrentSection = hasSubheadings
          ? ((record.items[0] && record.items[0].subheadings && record.items[0].subheadings.length) || 0)
          : 0;
        const signOffFirstSection = hasSubheadings
          ? Boolean(record.items[0] && record.items[0].sign_off)
          : false;
        if (
          completedItemForCurrentSection < totalItemForCurrentSection ||
          (completedItemForCurrentSection === totalItemForCurrentSection &&
            !signOffFirstSection)
        ) {
          status = ITP_REPORT_STATUS.IN_PROGRESS;
        } else {
          status = ITP_REPORT_STATUS.PARTIALLY_COMPLETE;
        }
      } else {
        status = completedSection === 0 ? ITP_REPORT_STATUS.IN_PROGRESS : ITP_REPORT_STATUS.PARTIALLY_COMPLETE;
        }
    }
    return status;
  };
  

const increaseDefectCount = (items, count, hasfullOrRestrictedAccess, userId, includeRecord) =>{
    if (!items.length) return includeRecord ? false : count;

    const isDefectiveItem = (item) => 
        item.is_defect &&
        (!item.close_out || !item.close_out.close_out_at) &&
        (hasfullOrRestrictedAccess || (item.tagged_user_ref && (typeof item.tagged_user_ref === 'number' ? item.tagged_user_ref === userId : item.tagged_user_ref.id === userId)));

    if (includeRecord) {
        return items.some(item => isDefectiveItem(item));
    } else {
        items.forEach(item => {
            if (isDefectiveItem(item)) {
                count++;
            }
        });
        return count;
    }
};

const getPartialCompletedReportCount = async ( req, res ) => {
    let qclId = +req.param('qclId');
    let totalPartiallyCompletedReports = await sails.models.checklistinspection.count({ where: { checklist_ref: qclId, or: [ {finalised: false}, {status:0} ], is_deleted: 0 }});
    successResponse( res, {totalPartiallyCompletedReports});
};

module.exports = {
    createChecklist: createChecklistFn,

    createChecklistV2: createChecklistFn,
    
    createCompanyChecklist: createChecklistFn,

    updateChecklist: updateChecklistFn,

    updateCompanyChecklist: updateChecklistFn,

    updateChecklistV2: updateChecklistFn,

    createChecklistInspection: createChecklistInspectionFn,

    createChecklistInspectionV2: createChecklistInspectionFn,

    updateChecklistInspection: updateChecklistInspectionFn,

    deleteChecklistInspection: deleteChecklistInspectionFn,

    getChecklistInspectionReport: getChecklistInspectionReportFn,

    searchChecklist: searchChecklistFn,

    searchChecklistV2: searchChecklistFn,

    getChecklistInspections: async (req, res) => {
        let projectId = +req.param('projectId');
        let where = {
            project_ref: projectId
        };

        sails.log.info('Fetch checklist inspections filter.', where);
        let { checklistInspection: projectCLInspection } = await getChecklistInspection(where, 'DESC', true);

        return ResponseService.successResponse(res, {project_cl_inspection: projectCLInspection});
    },

    signOffInspectionSection: async (req, res) => {
        let clId = +req.param('clId');
        let { heading_id, sign_off } = req.body;
        let {validationError} = validateSignoffRequest(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
        }
        let checklistInspection = await sails.models.checklistinspection_reader.findOne({ where: { id: clId } });
        let section = checklistInspection.items.find(a => a.id === heading_id);
        if (section.sign_off) {
          sails.log.info('section already signed off');
          return errorResponse(res, 'section already signed off');
        }
        section.sign_off = sign_off;
        const updatedItems = checklistInspection.items.map(item =>
          item.id === heading_id ? section : item
        );
        checklistInspection = await sails.models.checklistinspection.updateOne({ id: checklistInspection.id }).set({ items: updatedItems });
        successResponse(res, { checklistInspection });
    },
      
    getAssignedSignOffList: async (req, res) => {
        let userId = +req.param('assignedTo');
        let projectId = +req.param('projectId');
        let assignedITPReportList = await getAssignedToInspectionList(userId, projectId);
        assignedITPReportList = await expandImageAndUserRefs(assignedITPReportList);
        return successResponse(res, { signoffList: assignedITPReportList });
    },

    getUserCLInspections: async (req, res) => {
        let userId = +req.param('userId');
        let projectId = +req.param('projectId');
        let status = req.param('status') ? req.param('status') : null;
        let allReports = req.param('allReports', 'false') === 'true';
        let finalised = req.param('finalised') ? req.param('finalised') === 'true': null;
        let searchTerm = req.param('q') ? req.param('q') : '';

        let where = {
            user_ref: userId,
        };

        if (projectId) {
            where.project_ref = projectId;
        }

        if(finalised !== null) {
            where.finalised = finalised;
        }

        if(searchTerm){
            where.searchTerm = searchTerm;
        }

        if(status !== null){
            where.additionalFilter = {
                status
            }
        }

        sails.log.info('Fetch user checklist inspections filter.', {...where, allReports});
        let { checklistInspection: userCLInspection } = await getChecklistInspection(where, 'DESC', true, true, false, false, null, null, allReports);

        return ResponseService.successResponse(res, {user_cl_inspection: userCLInspection});
    },

    getUserCLInspectionsV2: async (req, res) => {
        let userId = +req.param('userId', 0);
        let projectId = +req.param('projectId', 0);
        let qclId = +req.param('qclId', 0);
        let finalised = req.param('finalised') ? req.param('finalised') === 'true' : true;
        let excludeFinalised = req.param('excludeFinalised', 'true') === 'true';
        let allReports = req.param('allReports', 'false') === 'true';
        let status = req.param('status', null);
        let searchTerm = req.param('q') ? req.param('q') : '';

        if(!userId || !projectId){
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid request.');
        }

        let defaultResponse = {
            user_ref: userId,
            checklist_ref: qclId,
            project_ref: projectId,
            finalised,
            status,
            user_cl_inspection: [],
            total_record_count: 0,
            allReports
        };
 
        let where = {
            user_ref: userId,
            checklist_ref: qclId,
            project_ref: projectId,
            finalised
        };

        if(status !== null){
            where.additionalFilter = {
                status: +status
            }
        }

        if(searchTerm){
            where.searchTerm = searchTerm;
        };

        sails.log.info('Fetch user checklist inspections filter.', {...where, allReports});
        let { checklistInspection: user_cl_inspection, total: total_record_count } = await getChecklistInspection(where, 'DESC', true, true, false,  excludeFinalised, null, null, allReports);

        return ResponseService.successResponse(res, {...defaultResponse, user_cl_inspection, total_record_count});
    },

    getProjectChecklists: async (req, res) => {
        let projectId = +req.param('projectId');
        let enabledOnly = req.param('enabled') === "true";

        let where = {
            project_ref : projectId,
            itp_type: ITP_TYPE.project
        };
        if(enabledOnly){
            where.enabled = true;
        }
    
        sails.log.info('Fetch checklists filter.', where);
        let projectQuaChecklists = await getQualityChecklists(where, 'DESC', true, ITP_TYPE.project);
        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['id', 'name', 'contractor', 'parent_company', 'custom_field']
        });
    
        let { employer } = await getCompanyInfo(projectInfo, null, [], false);
        where = {
            company_ref: employer.id,
            itp_type: ITP_TYPE.company,
            enabled: true
        };
        let companyQcChecklists = await getQualityChecklists(where, 'DESC', true, ITP_TYPE.company, projectId, enabledOnly);
        return ResponseService.successResponse(res, {project_checklists: [...projectQuaChecklists, ...companyQcChecklists]});
    },
    getCompanyChecklists : async(req,res)=>{
        let employerId = +req.param('employerId', 0);
        let where = {
            company_ref:employerId,
            itp_type: ITP_TYPE.company
        }
        let companyQcChecklists = await getQualityChecklists(where, 'DESC', true, 'company');
        return ResponseService.successResponse(res, {company_checklists: companyQcChecklists});
    },

    getCLInspectionsByQclId: async (req, res) => {
        let qclId = +req.param('qclId');
        let projectId = +req.param('projectId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let finalised = req.param('finalised', 'true') === 'true';
        let searchTerm = req.param('q') ? req.param('q') : '';
        let excludeFinalised = req.param('excludeFinalised', 'true') === 'true';
        let status = req.param('status') ? req.param('status') : null;
        let allReports = req.param('allReports', 'false') === 'true';

        if((!qclId && !allReports) || !projectId){
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid request.');
        }

        let where = {
            checklist_ref: qclId,
            project_ref: projectId,
            finalised: finalised,
        };
        if(searchTerm){
            where.searchTerm = searchTerm;
        }
        if(status !== null){
            where.additionalFilter = {
                status
            }
        }

        sails.log.info('Fetch inspections for checklist filter.', {...where, allReports});
        let { checklistInspection: qclInspections, total: total_record_count } = await getChecklistInspection(where, 'DESC', true, true, false, excludeFinalised, pageSize, pageNumber, allReports);

        let platform = req.headers['platform'];
        if(platform && extractPlatformVersion(platform)) {
            (qclInspections || []).map((cli) => {
                if([3,4].includes(cli.status)) {
                    cli.status = 2;
                }
                return cli;
            })
        }

        return ResponseService.successResponse(res, {cl_inspections: qclInspections, total_record_count});
    },

    reviewAndCloseoutList: async (req, res) => {
        let projectId = +req.param('projectId');
        let hasfullOrRestrictedAccess = req.user.user_roles.some(a => a.role === ROLES.SITE_ADMIN && a.resource == resourceIdentifier.PROJECT(projectId) && (a.designation.includes('other') || a.designation.includes('restricted')));
        let where = {
            project_ref: projectId
        };
        
        let { checklistInspection: ClInspections } = await getChecklistInspection(where, 'DESC', true, true, false, true);
        let inspectionsWithDefects = new Set();

        const hasDefectsInItems = (items) => increaseDefectCount(items, 0, hasfullOrRestrictedAccess, req.user.id, true);

        ClInspections.forEach(inspection => {
                if (inspection.has_subheadings) {
                    inspection.items.forEach((heading, i) => {
                        if (hasDefectsInItems(heading.subheadings)) {
                            inspectionsWithDefects.add(inspection);
                        }
                    });
                } else {
                    if (hasDefectsInItems(inspection.items)) {
                        inspectionsWithDefects.add(inspection);
                    }
                }
        });
        inspectionsWithDefects =  Array.from(inspectionsWithDefects);
        successResponse(res, {inspectionsWithDefects})
    },

    getItpOpenDefectsCount: async (req, res) => {
        let projectId = +req.param('projectId');
        let hasfullOrRestrictedAccess = req.user.user_roles.some(a => a.role === ROLES.SITE_ADMIN && a.resource == resourceIdentifier.PROJECT(projectId) && (a.designation.includes('other') || a.designation.includes('restricted')));
        let ClInspections = await sails.models.checklistinspection.find({
            where: { project_ref: projectId },
            select: ['items','has_subheadings']
        });
        let openDefects = 0
        ClInspections.forEach(inspection => {
            if(inspection.has_subheadings){
                inspection.items.forEach((heading,i) => {
                    openDefects = increaseDefectCount(heading.subheadings, openDefects, hasfullOrRestrictedAccess, req.user.id, false);
                })
            }else{
                openDefects = increaseDefectCount(inspection.items, openDefects, hasfullOrRestrictedAccess, req.user.id, false);
            }
        })
        successResponse(res, {openDefects})
    },

    updateITPToCloseOutItem: async (req, res) => {
        let id = +req.param('itpId');
        let itemInfo = _.pick((req.body.item_info || {}), [
            "question",
            "answer",
            "summary",
            "photos",
            "is_defect",
            "close_out",
        ]);
        itemInfo = cleanItem(itemInfo);
        let itemIdx = +req.body.item_idx;
        let subItemIdx = +req.body.sub_item_idx || 0;

        sails.log.info('Fetch project inspection tour: ', id);
        let itpInsection = await sails.models.checklistinspection_reader.findOne({
            where: {id: id},
            select: ['items', 'has_subheadings']
        })
        .populate('project_ref')
        .populate('user_ref');

        return proccessITPItemCloseout(res, itpInsection, itemIdx, itemInfo, subItemIdx);
    },

    closeoutITPItemAction: async (req, res) => {
        let id = +req.param('itpId');
        let itemInfo = _.pick((req.body.item_info || {}), [
            "question",
            "answer",
            "summary",
            "photos",
            "is_defect",
            "close_out",
        ]);
        itemInfo = cleanItem(itemInfo);
        let itemIdx = +req.body.item_idx;
        let subItemIdx = +req.body.sub_item_idx || 0;
        let user = req.user;

        sails.log.info('Fetch project inspection tour: ', id);
        let itpInsection = await sails.models.checklistinspection_reader.findOne({
            where: {id: id},
            select: ['items', 'has_subheadings']
        })
        .populate('project_ref')
        .populate('user_ref');

        let has_subheadings = itpInsection.has_subheadings;

        let checklist = has_subheadings ? itpInsection["items"][itemIdx]['subheadings'] : itpInsection["items"];
        let isAllowedToCloseOut = (checklist || []).findIndex((item, index) => index === ( has_subheadings ? subItemIdx : itemIdx ) && (
            item.tagged_user_ref === user.id
        )) !== -1;

        if(!isAllowedToCloseOut) {
            sails.log.info('User not allowed to closed out checklist report item.', user.id);
            return successResponse(res, {message: 'Unable to closeout, Access denied.'});
        }

        return proccessITPItemCloseout(res, itpInsection, itemIdx, itemInfo, subItemIdx);
    },

    downloadQclReportV2: async( req, res ) => {
        let projectId = +req.param('projectId');
        let type = req.param('type',);
        let qclId = +req.param('qclId');
        let reportId = +req.param('reportId');
        let updatedAt = +req.param('updatedAt', 0);
        
        // incase of fresh report the id will not be present, handling by sending 0.
        if(!reportId) {
            let project = await sails.models.project_reader.findOne({id: projectId})
            return viewOrDownloadQualityChecklistTemplate(req, res, type, qclId, project)
        }else {
            return viewORDownloadChecklistReport(req, res, reportId, type, updatedAt )
        }
    },

    downloadQclReport: async (req, res) => {
        let id = +req.param('id');
        let type = req.param('type');
        let updatedAt = +req.param('updatedAt');
        return viewORDownloadChecklistReport(req, res, id, type, updatedAt)
    },

    getPartialCompletedReportCountSA: getPartialCompletedReportCount,
    getPartialCompletedReportCountCA: getPartialCompletedReportCount,

    shareItpReport: async(req, res) => {
        let id = +req.param('id');
        let email = req.body.email;
        let projectId = req.body.projectId;
        let byUser = req.user;
        sails.log.info('Share ITP Report request by user', byUser.id, ' for project ', projectId);
        let { checklistInspection:[qclInspection] } = await getChecklistInspection({id:id}, 'DESC', true);
        if(qclInspection && qclInspection.id) {
            let qualityChecklist = qclInspection.checklist_ref;
            let html = await getITPReportHtml(qclInspection);
            let qclPhrase = qclInspection.project_ref.custom_field.qcl_phrase_singlr || 'ITP';
            let recordRef = buildRecordRef(qclInspection);
            let preparedBy = `${qclInspection.user_ref.first_name}${' ' + qclInspection.user_ref.last_name}`.trim();
            let attachmentName = `${qclPhrase}-${recordRef}-${qualityChecklist.qc_title}-${moment().format(dbDateFormat)}-${preparedBy}`;

            ResponseService.successResponse(res, {message: `The ${qclPhrase} report is being prepared and will be shared shortly.`})
            await shareReportViaEmail(req, res, html, 'quality-checklist', attachmentName, qualityChecklist.qc_title, byUser, email, qclInspection.project_ref.name);
            return;
        }
    },
}
