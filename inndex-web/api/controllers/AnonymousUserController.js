/**
 * Created by spatel on 10/11/19.
 */

const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const AccessLogService = require('./../services/AccessLogService');
const HttpService = require('./../services/HttpService');
const ResponseService = require('./../services/ResponseService');
const TokenUtil = require('./../services/TokenUtil');
const moment = require('moment');
const auth = require('http-auth');
const {
    PUBLIC_URL,
} = sails.config.custom;

const authorizeProjectUserByPin = async (record_id, pin) => {
    let rawResult = await sails.sendNativeQuery(`SELECT id, name, is_active, project_initial, postcode, contractor,
                (UPPER(coalesce(project_initial, '')) || id::text) as record_id,
                custom_field->>'country_code' as country_code,
                custom_field->>'timezone' as timezone
                FROM project
                WHERE
                  (
                    (UPPER(coalesce(project_initial, '')) || id::text) = $1 AND
                    pin = $2
                  )`,
        [record_id.toString().toUpperCase().trim(), pin]
    );
    if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
        sails.log.info(`login by project pin query results: ${rawResult.rows.length}`);
        return rawResult.rows.shift();
    }
    sails.log.info(`invalid project / pin, record count: ${rawResult.rows.length}`);
    return {};
};

const create_auth_response = async (project) => {
    sails.log.info(`preparing project auth response, project: ${project.id}`);
    project.project_logo_file = {};
    let country_code = (project.custom_field && project.custom_field.country_code);
    project.country_code = country_code;
    project.timezone = (project.custom_field && project.custom_field.timezone);
    if (project.contractor) {
        project.project_logo_file = await TokenUtil.getProjectLogo('contractor', project.contractor, country_code);
    }
    project.setting = await sails.models.optimasetting_reader.findOne({
        where: {
            project_ref: project.id,
            geo_fence_locations:  {'!=': null}
        },
        select: ['id', 'geo_fence_locations', 'has_bulk_clocking', 'selfie_required', 'has_vehicle_clocking',
            'force_onsite_for_an_out', 'has_fr', 'kiosk_mode', 'clock_in_mode',
            'liveness_check',
        ]
    });

    return project;
};

module.exports = {
    authorizeInTimeUser: async (req, res) => {
        let record_id = req.body.record_id;
        let pin = req.body.pin;

        if(!record_id || !pin || isNaN(pin)){
            sails.log.info('authorizeInTimeUser: Invalid request');
            return ResponseService.errorResponse(res, 'Invalid request');
        }

        try {
            let project = await authorizeProjectUserByPin(record_id, pin);
            if (!project || !project.id) {
                return ResponseService.errorResponse(res, sails.__('invalid_credentials_error_for_tvlogin'));
            }
            project = await create_auth_response(project);
            if(!project.setting){
                sails.log.info(`given project doesn't have geo-fence settings`);
                return ResponseService.errorResponse(res, "Invalid credentials.");
            }
            let platform = req.headers['platform'] || '-';
            let payload = {
                id: project.id,
                type: TokenUtil.tokenType.ANONYMOUS_PROJECT_TOKEN,
                platform,
            };
            TokenUtil.generateUserToken(payload, (tokenErr, tokenInfo, refreshToken) => {
                if (tokenErr) {
                    return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, tokenErr);
                }
                sails.log.info(`Created new ANONYMOUS token project: ${project.id}, now: ${moment().format()} expiresOn:`, tokenInfo.expiresOn);
                AccessLogService.logRequest({
                    path: `${req.method}: ${req.path}`,
                    action: 'anonymoususer/authorizeintimeuser',
                    user_agent: req.headers['user-agent'],
                    platform: platform,
                    auth_token: tokenInfo.token,
                    ip: (req.headers['x-forwarded-for'] || req.connection.remoteAddress),
                    project_ref: project.id,
                }).catch(sails.log.error);
                // Assigning refresh token in http-only cookie
                res.cookie('refresh', refreshToken, {
                    // domain: '*.inndex.co.uk',
                    httpOnly: true,
                    sameSite: 'None',
                    secure: true,
                    signed: true,
                    maxAge: 15 * 24 * 60 * 60 * 1000    // 15 days validity.
                });
                return ResponseService.successResponse(res, {project, tokenInfo});
            });
        } catch (e) {
            sails.log.info('Failed to authorize project', e);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, {e});
        }

    },

    authorizeRaspberryDeviceLogin: async (req, res) => {
        const basic = auth.basic({
                realm: "innDex Auth"
            },
            (username, password, callback) => {
                sails.log.info('Checker got', username, password);
                if(!username || !password || isNaN(password)){
                    sails.log.info('authorizeRaspberryDeviceLogin: Invalid request');
                    return ResponseService.errorResponse(res, 'Invalid request');
                }
                authorizeProjectUserByPin(username, password).then(project => {
                    callback((project && project.id));
                });
            }
        );
        basic.check((req, res, err) => {
            if (err) {
                // next(err);
                res.end(`ERROR`);
            } else {
                // need way to avoid string in project ID
                let projectId = (req.user).toString().replace(/[^0-9]/g,'');
                sails.log.info(`Details are valid, redirecting to private area - ${projectId}`);
                let platform = req.headers['platform'] || 'raspberry-device';
                TokenUtil.generateUserToken({
                    id: projectId,
                    type: TokenUtil.tokenType.ANONYMOUS_PROJECT_TOKEN,
                    platform,
                }, (tokenErr, tokenInfo) => {
                    if (tokenErr) {
                        return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, tokenErr);
                    }
                    sails.log.info(`Created new short ANONYMOUS token project: ${projectId}, now: ${moment().format()} expiresOn:`, tokenInfo.expiresOn);
                    AccessLogService.logRequest({
                        path: `${req.method}: ${req.path}`,
                        action: 'anonymoususer/authorizeraspberrydevicelogin',
                        user_agent: req.headers['user-agent'],
                        platform: req.headers['platform'] || '-',
                        auth_token: tokenInfo.token,
                        ip: (req.headers['x-forwarded-for'] || req.connection.remoteAddress),
                        project_ref: projectId,
                    }).catch(sails.log.error);
                    let resultRoute = `${PUBLIC_URL}rest/auth/callback`;
                    return res.redirect(302, resultRoute + '?status=success&pi=true&value='+tokenInfo.token);
                }, 2, 'm');

            }
        })(req, res);
    },

    getProjectInfoByToken: async (req, res) => {
        let project = await create_auth_response(req.project);
        return ResponseService.successResponse(res, {project});
    },

    reFetchAnonymousTokenInfo: async (req, res) => {
        let project = await create_auth_response(req.project);
        if(!project.setting){
            sails.log.info(`given project doesn't have geo-fence settings`);
            return ResponseService.errorResponse(res, `given project doesn't have geo-fence settings`);
        }
        let platform = req.headers['platform'] || '-';
        TokenUtil.generateUserToken({
            id: project.id,
            type: TokenUtil.tokenType.ANONYMOUS_PROJECT_TOKEN,
            platform,
        }, (tokenErr, tokenInfo) => {
            if (tokenErr) {
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, tokenErr);
            }
            sails.log.info(`Refreshed ANONYMOUS token project: ${project.id}, now: ${moment().format()} expiresOn:`, tokenInfo.expiresOn);
            return ResponseService.successResponse(res, {project, tokenInfo});
        });
    },

    refresh: async (req, res) => {
        let token = req.signedCookies.refresh || null;
        if(!token){
            return ResponseService.sendResponse(res, ResponseService.authErrorObject(sails.__('user_logged_in_session_expired'), true));
        }
        sails.log.info('refreshing project token, refresh token');
        let payload = await TokenUtil.authenticateRefreshToken(token);
        if(payload.type !== TokenUtil.tokenType.ANONYMOUS_PROJECT_TOKEN){
            sails.log.info(`Invalid API got called to refresh, ${payload.id}`);
            return ResponseService.sendResponse(res, ResponseService.authErrorObject(sails.__('user_logged_in_session_expired'), true));
        }
        sails.log.info('refresh token payload', payload);
        let deleted = await sails.models.userrefreshtoken.destroy({
            where:  {
                token: token,
                project_ref: payload.id
            }
        });
        if(!deleted || !deleted.length){
            sails.log.error('Valid refresh token token not found into DB', token);
            return ResponseService.sendResponse(res, ResponseService.authErrorObject(sails.__('user_logged_in_session_expired'), true));
        }
        // sails.log.info('get user info', payload.id);
        let project = await sails.models.project_reader.findOne({
            id: payload.id,
            is_active: 1,
        });
        if(!project){
            sails.log.warn('Project got deactivated, aborting refresh token call', payload.id);
            return ResponseService.sendResponse(res, ResponseService.authErrorObject(sails.__('user_logged_in_session_expired'), true));
        }
        project = await create_auth_response(project);
        if(!project.setting){
            sails.log.warn('Project geo-fence setting deactivated, aborting refresh token call', payload.id);
            return ResponseService.sendResponse(res, ResponseService.authErrorObject(sails.__('user_logged_in_session_expired'), true));
        }
        let platform = (req.headers['platform'] || '-');
        sails.log.info('get new token for project', payload.id);
        TokenUtil.generateUserToken({
            id: payload.id,
            email: payload.email,
            type: payload.type,
            platform: platform,
        }, (err, tokenInfo, refreshToken) => {
            if (err) {
                return ResponseService.errorResponse(res, sails.__('internal server error'), err);
            }
            AccessLogService.logRequest({
                path: `${req.method}: ${req.path}`,
                action: (req.options && req.options.action) || 'Unknown',
                user_agent: req.headers['user-agent'],
                platform: platform,
                auth_token: tokenInfo.token,
                ip: (req.headers['x-forwarded-for'] || req.connection.remoteAddress),
                project_ref: project.id,
                user_ref: req.param('userId') || req.param('user_ref')
            }).catch(sails.log.error);

            // Assigning refresh token in http-only cookie
            res.cookie('refresh', refreshToken, {
                // domain: '*.inndex.co.uk',
                httpOnly: true,
                sameSite: 'None',
                secure: true,
                signed: true,
                maxAge: 15 * 24 * 60 * 60 * 1000    // 15 days validity.
            });
            sails.log.info(`Refreshed ANONYMOUS token project: ${project.id}, now: ${moment().format()} expiresOn:`, tokenInfo.expiresOn);
            return ResponseService.successResponse(res, {project, tokenInfo});
        });
    },
};
