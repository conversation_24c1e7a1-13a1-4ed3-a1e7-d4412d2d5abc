/**
 * ResourceController
 *
 * @description :: Server-side actions for handling incoming requests.
 * @help        :: See https://sailsjs.com/docs/concepts/actions
 */
const fs = require('fs');
const path = require('path');
const momentTz = require("moment-timezone");
const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const {TokenUtil, ResponseService, DataProcessingService} = require('./../services');
const _uniq = require('lodash/uniq');
const {companyFn} = require('./../sql.fn');
const { DATA_STORES, COMPANY_SETTING_KEY, PROJECT_SETTING_KEY, INNDEX_SETTING_KEY} = sails.config.constants;

const getSettings = async (req, res) => {
    let names = (req.body.names) || [];
    sails.log.info('get inndex settings, by names', names);
    const PRIVATE_SETTING_NAMEs = ['test_project_ids'];
    let got_private_setting_name = (names.findIndex(n => PRIVATE_SETTING_NAMEs.includes(n)) !== -1);
    if (!names.length || got_private_setting_name) {
        return ResponseService.successResponse(res, {names});
    }
    let records = await sails.models.inndexsetting_reader.find({
        where: {name: names},
        select: ['name', 'value'],
    });
    let categories = [
        INNDEX_SETTING_KEY.CLOSE_CALL_CATEGORY_EN_GB, INNDEX_SETTING_KEY.CLOSE_CALL_CATEGORY_EN_US, INNDEX_SETTING_KEY.CLOSE_CALL_CATEGORY_DE_DE,
        INNDEX_SETTING_KEY.META_LIGHTING_CONDITIONS_EN_GB, INNDEX_SETTING_KEY.META_LIGHTING_CONDITIONS_EN_US, INNDEX_SETTING_KEY.META_LIGHTING_CONDITIONS_DE_DE
    ]
    sails.log.info(`got total ${records.length} inndex settings`);
    let settings = records.reduce((list, r) => {
        list[r.name] = categories.includes(r.name) ? (r.value || []).sort((a, b) => a.name.localeCompare(b.name)) : r.value;
        return list;
    }, {});
    return ResponseService.successResponse(res, {settings, names});
};

module.exports = {
    // Super admin only.
    saveCompanySettingFromAdmin: async (req, res) => {
        let companyId = +req.param('companyId', 0);
        const {setting_name, setting_value, enabled_on} = req.body || {};
        if (!(Object.values(COMPANY_SETTING_KEY)).includes(setting_name)) {
            sails.log.info(`Invalid company setting key supplied: ${setting_name} for company ${companyId}.`);
            return ResponseService.errorResponse(res, `Invalid company setting key supplied: ${setting_name}`);
        }
        const filter = {
            name: setting_name,
            company_ref: companyId
        };

        sails.log.info(`[ADMIN] Updating setting ${setting_name} for company ${companyId}`);
        let existingSetting = await sails.models.companysetting_reader.findOne(filter);

        let result = {};
        if (existingSetting && existingSetting.id) {
            result.updated = true;
            result.record = await sails.models.companysetting.updateOne(filter).set({value: setting_value, enabled_on});
            sails.log.info(`[ADMIN] Updated ${setting_name} for company ${companyId}.`);

        } else {
            result.created = true;
            result.record = await sails.models.companysetting.create({
                company_ref: companyId,
                name: setting_name,
                enabled_on,
                value: setting_value
            });
            sails.log.info(`[ADMIN] Added ${setting_name} for company ${companyId}.`);
        }

        // Below logic will be moved to a standalone fn.
        if(setting_name === COMPANY_SETTING_KEY.FACIAL_RECOGNITION_CONFIG){
            sails.log.info(`[ADMIN] [FR]: update projects of company: ${companyId} with`, setting_value);
            // get IDs for projects of company
            let projects = await companyFn.getCompanyAccessibleProjects(companyId);
            if(projects.length){
                let excluded_projects = setting_value.excluded_projects || [];
                sails.log.info(`[ADMIN] [FR]: company: ${companyId} have total ${projects.length} projects where excluded projects are: ${excluded_projects}`);
                let projectIds = projects.reduce((list, project) => {
                    if(!excluded_projects.includes(project.id)) {
                        list.push(project.id);
                    }
                    return list;
                }, []);
                sails.log.info(`[ADMIN] [FR]: Company: ${companyId}, apply status: ${setting_value.status}, to following projects: ${projectIds}`);
                if(setting_value.status){
                    // Enabled,
                    let settings = await sails.models.optimasetting.update({project_ref: projectIds}).set({
                        has_fr: true,
                    });
                    if(settings.length && setting_value.fr_clock_in_mode){
                        await sails.models.optimasetting.update({project_ref: projectIds, clock_in_mode: 1}).set({
                            clock_in_mode: setting_value.fr_clock_in_mode
                        });
                    }
                    sails.log.info(`[ADMIN] [FR]: Enabled FR for ${settings.length} projects of company: ${companyId}`);
                }else{
                    // Disabled, update all optima setting
                    let settings = await sails.models.optimasetting.update({project_ref: projectIds}).set({
                        has_fr: false,
                        kiosk_mode: false,
                        clock_in_mode: 1
                    });
                    sails.log.info(`[ADMIN] [FR]: disabled FR for ${settings.length} projects of company: ${companyId}`);
                }
            }
        }

        // This logic used for update project section access tools
        if(setting_name === COMPANY_SETTING_KEY.PROJECT_FEATURE_PERMISSION){
            let updatedTools = result.record.value.tools;
            let projectList = await sails.models.project_reader.find({parent_company: companyId, is_active:1});
            if(!projectList.length){
                sails.log.info(`[ADMIN] Project list not found for this company: ${companyId}`);
                return ResponseService.successResponse(res, result);
            }
            sails.log.info(`[ADMIN] Total ${projectList.length} projects found for this company: ${companyId}`);
            let setEnableTools = Object.keys(updatedTools)
                .filter((ele) => updatedTools[ele] === 'lock-on')
                .reduce((ele, key) => {
                    ele[key] = true;
                    return ele;
                }, {});

            let setDisableTools = Object.keys(updatedTools)
                .filter((ele) => updatedTools[ele] === 'lock-off')
                .reduce((ele, key) => {
                    ele[key] = false;
                    return ele;
                }, {});

            let updatedCompanyTools = Object.assign({}, setEnableTools, setDisableTools);
            let fatigue_management_status = updatedCompanyTools.fatigue_management_status;
            let delivery_management_status = updatedCompanyTools.delivery_management_status;
            delete updatedCompanyTools.fatigue_management_status;
            delete updatedCompanyTools.delivery_management_status;
            if(!Object.keys(updatedCompanyTools).length && updatedTools.delivery_management_status && updatedTools.fatigue_management_status){
                sails.log.info(`[ADMIN] Tools with false value not found for: ${companyId}`);
                return ResponseService.successResponse(res, result);
            }

            for(let i=0; i<projectList.length; i++){
                sails.log.info(`[ADMIN] Proceed to update project access tools for project: ${projectList[i].id}`);
                const project_section_access = projectList[i].project_section_access ? projectList[i].project_section_access : {};
                const company_additional_project_section_access = projectList[i].company_additional_project_section_access ? projectList[i].company_additional_project_section_access : {};
                delete updatedCompanyTools.asset_management;
                updatedCompanyTools.inspection_tour = updatedCompanyTools.ib_checklist === undefined ? project_section_access.inspection_tour : updatedCompanyTools.ib_checklist;
                let projectSectionAccess = Object.assign(project_section_access , updatedCompanyTools);
                let companyProjectSectionAccess = Object.assign(company_additional_project_section_access , updatedCompanyTools);
                // sails.log.info(`projectSectionAccess ${projectList[i].id} inspection_tour`, (projectSectionAccess.inspection_tour), 'ib_checklist',(projectSectionAccess.ib_checklist))
                let updatedProject = await sails.models.project.updateOne({id: projectList[i].id}).set({
                    project_section_access: projectSectionAccess,
                    company_additional_project_section_access: companyProjectSectionAccess,
                    fatigue_management_status: fatigue_management_status,
                    delivery_management_status: delivery_management_status
                });
                sails.log.info(`[ADMIN] Updated section access tools for project ${projectList[i].id}`);
            }
        }
        return ResponseService.successResponse(res, result);
    },

    addCompetency: async (req, res) => {
        let request_record = _.pick((req.body || {}), [
            'name',
            'min_upload_required',
            'sort_order',
            'country_code',
            'message',
            'scheme_id',
            'category',
            'is_master',
            'tags',
        ]);
        request_record.name = (request_record.name || '').toString().trim();
        request_record.scheme_id = (request_record.scheme_id || '').toString().trim();
        if(!request_record.scheme_id){
            request_record.scheme_id = null;
        }
        sails.log.info('new competency request', JSON.stringify(request_record));
        if(!request_record.name){
            return ResponseService.errorResponse(res, "Invalid request, name is required");
        }

        let records = await sails.models.competencies.find({
            where: {
                name: request_record.name,
                country_code: request_record.country_code,
            }
        });
        if(records && records.length){
            return ResponseService.errorResponse(res, 'Record already exist', {already_exist: true, records});
        }
        let newRecord = await sails.models.competencies.create(request_record);
        sails.log.info('new competency record created', JSON.stringify(newRecord));
        return ResponseService.successResponse(res, {
            message: 'Successfully created new record'
        });
    },

    updateMetaCompetency: async (req, res) => {
        const competencyId = +req.param('competencyId', 0);
        let request_record = _.pick((req.body || {}), [
            // 'name',
            // 'country_code',
            'min_upload_required',
            'sort_order',
            'message',
            'scheme_id',
            'category',
            'is_master',
            'tags',
        ]);
        request_record.scheme_id = (request_record.scheme_id || '').toString().trim();
        if(!request_record.scheme_id){
            request_record.scheme_id = null;
        }
        sails.log.info(`[ADMIN] update competency ${competencyId}, with request`, JSON.stringify(request_record));
        let updated = await sails.models.competencies.update({id: competencyId}).set(request_record);
        sails.log.info('[ADMIN] updated competency', JSON.stringify(updated));
        return ResponseService.successResponse(res, {message: 'Successfully updated record', updated});
    },

    getCompetencies: async (req,res) =>{
        let country_code = ((req.param('country_code') == 'undefined') || !req.param('country_code')) ? 'GB' : req.param('country_code');
        let competencies = await sails.models.competencies_reader.find({
            select: ['name', 'sort_order', 'message', 'min_upload_required', 'tags', 'scheme_id', 'category', 'is_master'],
            where: {country_code}
        }).sort([{ sort_order: 'ASC' }, { name: 'ASC' }]);
        sails.log.info(`fetched "${country_code}" competencies list records`, competencies.length);
        return ResponseService.successResponse(res, {competencieslist:competencies});
    },

    deleteCompetency: async (req,res) =>{
        sails.log.info('competencyId...........', req.param('competencyId'));

        if(req.param('competencyId')) {
            let deletedOneRecords = await sails.models.competencies.destroy({
                id: req.param('competencyId')
            });

            if(deletedOneRecords){
                return ResponseService.successResponse(res, sails.__('Record has been deleted.'));
            }
        }
    },

    addWork: async (req, res) => {
        let work = {name: req.query.work_name};

        let records = await sails.models.typeofworks.find(work);

        if (records.length > 0) {
            return res.status(200).send({ success: 'Already Exist' });
        } else {
            sails.models.typeofworks.create(work).exec(function (err, result) {
                if (err) {
                    sails.log('Some error occured ' + err.raw);
                    console.error(JSON.stringify(err));
                    return res.status(500).send({ error: 'Some error occured' });
                }

                sails.log.debug('Success', JSON.stringify(result));
                return res.status(200).send({ success: 'Success' });

            });
        }
    },

    getTypeOfWorks: async (req,res) =>{
        sails.models.typeofworks.find({ select: ['name', 'sort_order'] }).sort([{ sort_order: 'ASC' }, { name: 'ASC' }]).exec(function fetchRecords(error, typeOfWorklist) {
            if (error) {
                sails.log.info('Error while fetching', error);
                return ResponseService.errorResponse(res, sails.__('internal server error'), error);
            }
            sails.log.info('fetched records', typeOfWorklist.name);
            return ResponseService.successResponse(res, {typeOfWorklist:typeOfWorklist});
        });
    },

    deleteWork: async (req,res) =>{
        sails.log.info('workId...........', req.param('workId'));

        if(!+req.param('workId')) {
            return ResponseService.errorResponse(res, 'Invalid request');
        }

        let deletedOneRecords = await sails.models.typeofworks.destroy({
            id: +req.param('workId')
        });

        return ResponseService.successResponse(res, {deletedOneRecords});
    },

    uploadQRImage: async(req, res) => {
        sails.log.info('saving QRImage');
        var inndexSetting = {name: 'qrcode_image'};
        var data = {name: 'qrcode_image', value: req.body}

        let records = await sails.models.inndexsetting.find(inndexSetting);

        if (records.length > 0) {
            sails.models.inndexsetting.updateOne(inndexSetting).set(data).exec(function updateCallback(updateError, inndexsetting){
                if(updateError){
                    sails.log.info('Failed to update image');
                    return ResponseService.errorResponse(res, sails.__('internal server error'), {updateError});
                }
                sails.log.info('update successful', inndexsetting ? inndexsetting.id : null);
                return ResponseService.successResponse(res, {inndexsetting: inndexsetting});
            });
        } else {
            sails.models.inndexsetting.create(data).exec(function (err, result) {
                if (err) {
                    sails.log('Some error occured ' + err.raw);
                    console.error(JSON.stringify(err));
                    return res.status(500).send({ error: 'Some error occured' });
                }

                sails.log.debug('Success', JSON.stringify(result));
                return res.status(200).send({ success: 'Success' });

            });
        }
    },

    getQRImage: async(req, res) => {
        sails.log.info('Fetch QR code image');
        var inndexSetting = {name: 'qrcode_image'};
        let records = await sails.models.inndexsetting.find(inndexSetting).exec(function fetchRecords(error, qrcodeImage) {
            if (error) {
                sails.log.info('Error while fetching', error);
                return ResponseService.errorResponse(res, sails.__('internal server error'), error);
            }
            return ResponseService.successResponse(res, {qrcodeImage:qrcodeImage});
        });
    },

    getLightingConditions: async (req,res) =>{
        sails.models.lightingconditions.find({ select: ['name', 'sort_order'] }).exec(function fetchRecords(error, lightingconditionslist) {
            if (error) {
                sails.log.info('Error while fetching', error);
                return ResponseService.errorResponse(res, sails.__('internal server error'), error);
            }
            sails.log.info('fetched records', lightingconditionslist.name);
            return ResponseService.successResponse(res, {lightingconditionslist:lightingconditionslist});
        });
    },

    getConversationCategory: async (req,res) =>{
        sails.models.conversationcategory.find({ select: ['name', 'sort_order'] }).sort('name ASC').exec(function fetchRecords(error, conversationcategorylist) {
            if (error) {
                sails.log.info('Error while fetching', error);
                return ResponseService.errorResponse(res, sails.__('internal server error'), error);
            }
            sails.log.info('fetched records', conversationcategorylist);
            return ResponseService.successResponse(res, {conversationcategorylist:conversationcategorylist});
        });
    },

    timezones: async (req, res) => {
        return ResponseService.successResponse(res, {timezones: momentTz.tz.names()}, ResponseService.CACHE_POLICY.VERY_LONG);
    },

    getMatesInMind: async (req, res) => {
        let pageNumber = +(req.query.pageNumber || 1);
        let limit = +(req.query.limit || 10);

        if(isNaN(pageNumber) || isNaN(limit)){
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid request required.');
        }

        let skip = ((pageNumber - 1) * limit);
        sails.log.info(`fetch mates in mind page: ${pageNumber} skip: ${skip}`);
        try {

            let total = await sails.models.matesinmind.count({
                where: {
                    published: true,
                }
            });
            // query all MiM records
            let mates_in_mind = await sails.models.matesinmind.find({
                where: {
                    published: true,
                },
                skip,
                limit,
                sort: 'updatedAt DESC'
            }).populate('media');

            sails.log.info(`responding with MiM, count:${mates_in_mind.length}`);
            return ResponseService.successResponse(res, {
                mates_in_mind,
                pageNumber,
                limit,
                total,
            });
        } catch (fetchError) {
            sails.log.error('Failed to fetch mates in mind', fetchError);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
        }
    },

    getCountries: async (req, res) => {
        let sendNationalities = req.param('outcome', 'nationalities') === 'nationalities';
        let setting_name = "countries_meta";

        sails.log.info('Loading countries list: ', 'sendNationalities?', sendNationalities);
        if (sendNationalities) {
            setting_name = "nationalities_meta";
        }
        const record = await sails.models.inndexsetting_reader.findOne({
            where: { name: setting_name },
            select: ['value'],
        });
        res.send(record.value);
    },

    getSettingByName: async (req, res) => {
        let payload = _.pick((req.body || {}), [
            'name',
            'project_id'
        ]);
        sails.log.info('get inndex setting, name', payload.name);

        let record = await sails.models.inndexsetting_reader.findOne({ name: payload.name });

        if (+payload.project_id && (payload.name).includes('observation_category')) {
            sails.log.info("Fetching project level observation categories.");
            let projectSetting = await sails.models.projectsetting_reader.findOne({
                where:  {
                    name: PROJECT_SETTING_KEY.OBSERVATION_SETTING,
                    project_ref: +payload.project_id
                }
            });

            let projectLevelCategories = ((projectSetting && projectSetting.value && projectSetting.value.categories) || []).filter(r => r.is_active);
            sails.log.info(`Found ${projectLevelCategories.length} project level observation categories.`);
            if (projectLevelCategories && projectLevelCategories.length) {
                record.value = [...record.value, ...projectLevelCategories].sort((a, b) => a.name.localeCompare(b.name));
            }
        }
        let categories = [INNDEX_SETTING_KEY.GOOD_CALL_CATEGORY_EN_US, INNDEX_SETTING_KEY.GOOD_CALL_CATEGORY_EN_US, INNDEX_SETTING_KEY.GOOD_CALL_CATEGORY_DE_DE,
            INNDEX_SETTING_KEY.OBSERVATION_CATEGORY_EN_GB, INNDEX_SETTING_KEY.OBSERVATION_CATEGORY_EN_US, INNDEX_SETTING_KEY.OBSERVATION_CATEGORY_DE_DE]
        if(categories.includes(payload.name)) {
            record.value = (record.value || []).sort((a, b) => a.name.localeCompare(b.name));
        }

        return ResponseService.successResponse(res,{record: (record && record.id) ? record : null});
    },

    getSettings: getSettings,
    getSettingsInnTime: getSettings,

    getForceUpdateConfig: async (req, res) => {
        sails.log.info('get inndex setting, name `mobile_app_force_update_config`');
        let record = await sails.models.inndexsetting_reader.findOne({name: 'mobile_app_force_update_config'});
        return ResponseService.successResponse(res,{record: (record && record.id) ? record : null});
    },

    updateForceUpdateConfig: async(req, res) => {
        sails.log.info('Updating App Force Update Config.');
        var inndexSetting = {name: 'mobile_app_force_update_config'};
        var data = {name: 'mobile_app_force_update_config', value: req.body}

        let record = await sails.models.inndexsetting_reader.findOne(inndexSetting);

        if (record && record.id) {
            updatedConf = await sails.models.inndexsetting.updateOne(inndexSetting).set(data);
            sails.log.info('update successful', updatedConf ? updatedConf.id : null);
            return ResponseService.successResponse(res, {record: updatedConf});
        } else {
            sails.log.info('Creating new record.');
            let record = await sails.models.inndexsetting.create(data);
            return ResponseService.successResponse(res, {record: (record && record.id) ? record : null});
        }
    },

    updateUsefulInfoFilesData: async(req, res) => {
        sails.log.info('Updating Useful Info Files Data.');
        var inndexSetting = {name: 'useful_info_files_data'};
        var data = {name: 'useful_info_files_data', value: req.body}

        let record = await sails.models.inndexsetting.findOne(inndexSetting);

        if (record && record.id) {
            updatedConf = await sails.models.inndexsetting.updateOne(inndexSetting).set(data);
            sails.log.info('update successful', updatedConf ? updatedConf.id : null);
            return ResponseService.successResponse(res, {record: updatedConf});
        } else {
            sails.log.info('Creating new record.');
            let record = await sails.models.inndexsetting.create(data);
            return ResponseService.successResponse(res, {record: (record && record.id) ? record : null});
        }
    },

    saveIncidentActionCategories: async(req, res) => {
        let companyId = req.param('companyId');
        let filter = {
            name: COMPANY_SETTING_KEY.INCIDENT_REPORT_ACTION_CATEGORY_CONFIG,
            company_ref: companyId
        };

        sails.log.info('Updating Action Category Config for Incident Reports feature.');
        let categoryConfig = await sails.models.companysetting_reader.findOne(filter);

        let data = {
            company_ref: companyId,
            name: COMPANY_SETTING_KEY.INCIDENT_REPORT_ACTION_CATEGORY_CONFIG,
            value: (req.body || []).sort((a, b) => (a.label.toLowerCase() || "").toString().localeCompare((b.label.toLowerCase() || "").toString()))
        };
        if (categoryConfig && categoryConfig.id) {
            updatedConf = await sails.models.companysetting.updateOne(filter).set(data);
            sails.log.info('update successful', updatedConf ? updatedConf.id : null);
            return ResponseService.successResponse(res, {record: updatedConf});
        } else {
            sails.log.info('Creating new record.');
            let record = await sails.models.companysetting.create(data);
            return ResponseService.successResponse(res, {record: (record && record.id) ? record : null});
        }
    },

    getCompanySettingByName: async (req, res) => {
        let companyId = +req.param('companyId', 0);
        let filter = {
            name: req.body.name,
            company_ref: companyId
        };

        let excludedNames = [COMPANY_SETTING_KEY.OKTA_SSO_SETTING, COMPANY_SETTING_KEY.CSCS_CONFIG];
        if(req.is_super_admin){
            sails.log.info(`[ADMIN] Allowing super-admin ${req.user.id} to access ${filter.name}`);
            excludedNames = [];
        }
        if(excludedNames.includes(filter.name)){
            sails.log.warn('Preventing read of company setting, name ', filter.name);
            return ResponseService.successResponse(res,{record: null});
        }

        sails.log.info('Fetching company setting, name ', filter.name);
        let record = await sails.models.companysetting_reader.findOne(filter);
        return ResponseService.successResponse(res,{record: (record && record.id) ? record : null});
    },

    saveCompanySetting: async(req, res) => {
        let companyId = req.param('companyId');
        let filter = {
            name: req.body.setting_name,
            company_ref: companyId
        };

        let excludedNames = [COMPANY_SETTING_KEY.OKTA_SSO_SETTING, COMPANY_SETTING_KEY.CSCS_CONFIG];
        if(req.is_super_admin){
            sails.log.info(`[ADMIN] Allowing super-admin ${req.user.id} to save ${filter.name}`);
            excludedNames = [];
        }
        if(excludedNames.includes(filter.name)){
            sails.log.warn('Preventing save of company setting, name ', filter.name);
            return ResponseService.successResponse(res,{record: null});
        }
        sails.log.info(`Updating ${req.body.setting_name} for company ${companyId}.`);
        let existingSetting  = await sails.models.companysetting_reader.findOne(filter);

        let data = {
            company_ref: companyId,
            name: req.body.setting_name,
            value: req.body.setting_value
        };
        if (existingSetting && existingSetting.id) {
            let updatedSetting = await sails.models.companysetting.updateOne(filter).set(data);
            if(data.name == 'company_supply_chain_config'/* && data.value.pre_select*/) {
                //process all projects, merge current project supply chain with company supply chain.
                DataProcessingService.updateCompanyProjectsSupplyChain(companyId, data.value.supply_chain);
            }
            sails.log.info(`Updated ${req.body.setting_name} for company ${companyId}.`);
            return ResponseService.successResponse(res, {record: updatedSetting});
        } else {
            let record = await sails.models.companysetting.create(data);
            if(data.name == 'company_supply_chain_config'/* && data.value.pre_select*/) {
                //process all projects, merge current project supply chain with company supply chain.
                DataProcessingService.updateCompanyProjectsSupplyChain(companyId, data.value.supply_chain);
            }
            sails.log.info(`Added ${req.body.setting_name} for company ${companyId}.`);
            return ResponseService.successResponse(res, {record: (record && record.id) ? record : null});
        }
    },

    getProjectSettingsByName: async (req, res) => {
        let projectId = req.param('projectId');

        sails.log.info('Fetching project settings, name ', req.body.settings_name);
        if ([PROJECT_SETTING_KEY.SMART_SHEET_APP_INFO, PROJECT_SETTING_KEY.PROCORE_INTEGRATION_PROJECT_INFO].findIndex(key => (req.body.settings_name || []).includes(key)) > -1) {
            sails.log.info(`Preventing reading ${req.body.settings_name} for project ${projectId}.`);
            return ResponseService.successResponse(res, {project_settings: {}});
        }
        let project_settings = await sails.models.projectsetting_reader.find({
            where:  {
                name: { in: req.body.settings_name },
                project_ref: projectId
            }
        });
        project_settings = (project_settings || []).reduce((object, item) => {
            if (item.name) {
                object[item.name] = item.value;
            }
            return object;
        }, {});
        if ((req.body.settings_name).includes('rams_assessment_form') && project_settings.rams_assessment_form &&
            project_settings.rams_assessment_form.rams_assessment_ref) {
            project_settings.rams_assessment_form.rams_assessment_ref = await sails.models.userfile_reader.findOne({
                where: {id: project_settings.rams_assessment_form.rams_assessment_ref}
            });
        }

        let asiteConfigKey = PROJECT_SETTING_KEY.A_SITE_PROJECT_CONFIG;
        if ((req.body.settings_name).includes(asiteConfigKey) && project_settings[asiteConfigKey] && project_settings[asiteConfigKey].tools_mapping.length) {
            project_settings[asiteConfigKey].tools_mapping = await DataProcessingService.populateToolsMappingCompany(project_settings[asiteConfigKey].tools_mapping);
        }
        return ResponseService.successResponse(res,{project_settings});
    },

    deleteProjectSetting: async (req, res) => {
        let projectId = req.param('projectId');

        sails.log.info('Deleting project setting, name ', req.body.setting_name);
        let deleted_record = await sails.models.projectsetting.destroyOne({
            where:  {
                name: req.body.setting_name,
                project_ref: projectId
            }
        });

        sails.log.info('project setting has been deleted, name ', deleted_record.name);

        return ResponseService.successResponse(res,{deleted_project_setting:deleted_record});
    },

    getToolListData: async (req, res) => {
        sails.log.info('Fetching toolList');
        return ResponseService.successResponse(res,{
            tool_list: TokenUtil.toolList
        });
    },

    getAccessLogs: async (req, res) => {
        sails.log.info('Fetching access logs');

        let pageSize = +req.query.pageSize || 20;
        let pageNumber = +req.query.pageNumber || 0;
        let searchTerm = req.query.searchTerm;
        let result;

        let datastore = await sails.getDatastore(DATA_STORES.storage);

        if (searchTerm) {
            result = await datastore.sendNativeQuery(`
                SELECT "createdAt", path, action, user_agent, platform, ip, user_ref, project_ref
                FROM api_access_log
                WHERE user_ref::text ILIKE $1 OR path ILIKE $1 OR action ILIKE $1
                ORDER BY id DESC
                LIMIT $2 OFFSET $3`,
                [`%${searchTerm}%`, pageSize, (pageNumber * pageSize)]
            );
        } else {
            result = await datastore.sendNativeQuery(`
                SELECT "createdAt", path, action, user_agent, platform, ip, user_ref, project_ref
                FROM api_access_log
                ORDER BY id DESC
                LIMIT $1 OFFSET $2`,
                [pageSize, (pageNumber * pageSize)]
            );
        }

        return ResponseService.successResponse(res, { logs: result.rows } );
    },

    getMetaDistrict: async (req, res) => {
        sails.log.info("fetching meta district");
        let meta_districts = await sails.models.metadistrict_reader.find().sort([{ id: 'ASC' }]);
        return ResponseService.successResponse(res, { meta_districts } );
    },

    getInteractionLogs: async (req, res) => {
        sails.log.info('Fetching interaction logs');

        let pageSize = +req.query.pageSize || 20;
        let pageNumber = +req.query.pageNumber || 0;
        let searchTerm = req.query.searchTerm;
        let result;

        let datastore = await sails.getDatastore(DATA_STORES.storage);

        if (searchTerm) {
            result = await datastore.sendNativeQuery(`
                SELECT *
                FROM interaction_log
                WHERE service_id::text ILIKE $1 OR resource_ref::text ILIKE $1 OR url::text ILIKE $1 OR payload::text ILIKE $1
                ORDER BY id DESC
                LIMIT $2 OFFSET $3`,
                [`%${searchTerm}%`, pageSize, (pageNumber * pageSize)]
            );
        } else {
            result = await datastore.sendNativeQuery(`
                SELECT *
                FROM interaction_log
                ORDER BY id DESC
                LIMIT $1 OFFSET $2`,
                [pageSize, (pageNumber * pageSize)]
            );
        }

        if (result.rows.length) {
            result.rows.forEach(el => {
                delete el.headers.authorization
                delete el.headers.cookie
                delete el.headers.secretKey
                delete el.headers.optimaToken
            })
        }

        return ResponseService.successResponse(res, { logs: result.rows } );
    },
};
