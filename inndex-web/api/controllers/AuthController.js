/**
 * Created by spatel on 17/11/18.
 */

const { DATA_STORES } = sails.config.constants;
const {
    AuthValidator: {
        changeEmailAddress
    }
} = require('./../validators');
const {AccessLogService} = require('./../services/index');
const ResponseService = require('./../services/ResponseService');
const TokenUtil = require('./../services/TokenUtil');
const EmailService = require('./../services/EmailService');
const bcrypt = require("bcryptjs");
const moment = require('moment');
const POSSIBLE_DEVICES_TYPE = ['mobile', 'tablet'];
const POSSIBLE_OS_TYPE = ['Android', 'iOS'];
const { COMPANY_SETTING_KEY } = sails.config.constants;
const EMAIL_REGEX = /^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/;
const { baseUrl } = sails.config.custom;
const USER_ALREADY_EXISTS = 'User with given details already exists';

const sendForgotPassEmailNotification = async (user = {}) => {
    sails.log.info('Sending Reset password Email Alert to:', user.id);
    try{
        let subject = `innDex - password reset`;
        let html = await sails.renderView('pages/mail/forgot-password-email', {
            title: subject,
            user,
            layout: false
        });
        return await EmailService.sendMail(subject, [user.email], html);
    }catch (e) {
        sails.log.info('Failed to send email', e);
        return false;
    }
};

const view = async (res = {}) => {
    return res.ok({now: (new Date()).getTime()});
    // return res.view('pages/homepage', {title: ''});
};

const generateSSOUrl = (key, email) => {
    if (key === 'okta') {
        return `${baseUrl}okta/authorize`
    }
    if (key === 'microsoft') {
        return `${baseUrl}azure/authorize?login_hint=${email}`
    }
    return ''
}

const doesUsernameExist = async (email) => {
    let user = await sails.models.user.findOne({where: {email}, select: ['id']});
    return !!user;
};


module.exports = {

    index: async (req, res) => {
        // Only for production
        sails.log.info('Request got req.baseUrl & req.path', req.baseUrl, ' : ',req.path);
        return await view(res);
    },

    /**
     * API server health check API route fn.
     *  With optionally checking for default database connection as well.
     * @param req
     * @param res
     * @returns {Promise<{}>}
     */
    healthCheck: async (req, res) => {
        let all = req.param('all','') === 'all';
        if(all){
            try{
                await sails.getDatastore(DATA_STORES.default).sendNativeQuery(`SELECT 1+1 as result`);
            }catch (e) {
                return res.status(503).send(ResponseService.errorObject(sails.__('internal server error')));
            }
        }
        return ResponseService.successResponse(res, {now: (new Date()).getTime()});
    },

    logout: async (req, res) => {
        let refreshToken = req.signedCookies.refresh || null;
        if(refreshToken){
            // sails.log.info('Logout user, after removing refresh token', refreshToken)
            let deleted = await sails.models.userrefreshtoken.destroy({
                where:  {
                    token: refreshToken,
                    // user_ref: {'!=': null}   // As same call is being used by innTime Auth.
                }
            });
            // sails.log.info('removed refresh tokens')
        }
        res.clearCookie('refresh');
        return ResponseService.successResponse(res, {now: (new Date()).getTime()});
    },

    refresh: async (req, res) => {
        let token = req.signedCookies.refresh || null;
        if(!token){
            return ResponseService.sendResponse(res, ResponseService.authErrorObject('Your Logged in Session is expired, please login again', true));
        }
        sails.log.info('refreshing user token, refresh token');
        let payload = await TokenUtil.authenticateRefreshToken(token);
        if(payload.type !== TokenUtil.tokenType.AUTH_TOKEN){
            sails.log.info(`Invalid API got called to refresh, ${payload.id}`);
            return ResponseService.sendResponse(res, ResponseService.authErrorObject(sails.__('user_logged_in_session_expired'), true));
        }
        sails.log.info('refresh token payload', payload);
        let deleted = await sails.models.userrefreshtoken.destroy({
            where:  {
                token: token,
                user_ref: payload.id
            }
        });
        if (!deleted || !deleted.length) {
            sails.log.error('Valid refresh token token not found into DB', token);
            return ResponseService.sendResponse(res, ResponseService.authErrorObject('Your Logged in Session is expired, please login again', true));
        }
        // sails.log.info('get user info', payload.id);
        let user = await sails.models.user.findOne({
            id: payload.id,
            is_active: 1,
        }).populate('profile_pic_ref').populate('parent_company').populate('user_roles');
        if(!user){
            sails.log.warn('User got deactivated, aborting refresh token call', payload.id);
            return ResponseService.sendResponse(res, ResponseService.authErrorObject('Your Logged in Session is expired, please login again', true));
        }
        user._authorized_companies = await TokenUtil.getAuthorizedCompaniesCount(user.user_roles);
        let platform = req.headers['platform'] || ((req.headers['accept-language'] && (req.headers['accept-language']).indexOf('mobile') !== -1) ? req.headers['accept-language'] : '-');
        sails.log.info('get new token for user', payload.id);
        TokenUtil.generateUserToken({
            id: payload.id,
            email: payload.email,
            type: payload.type,
            platform: platform,
        }, (err, tokenInfo, refreshToken) => {
            if (err) {
                return ResponseService.errorResponse(res, sails.__('internal server error'), err);
            }
            sails.log.info(`refresh successful, id: ${user.id}, email: ${user.email} now:`, moment().format());
            AccessLogService.logRequest({
                path: `${req.method}: ${req.path}`,
                action: (req.options && req.options.action) || 'Unknown',
                user_agent: req.headers['user-agent'],
                platform: platform,
                auth_token: tokenInfo.token,
                ip: (req.headers['x-forwarded-for'] || req.connection.remoteAddress),
                user_ref: user.id
            }).catch(sails.log.error);

            // Assigning refresh token in http-only cookie
            res.cookie('refresh', refreshToken, {
                // domain: '*.inndex.co.uk',
                httpOnly: true,
                sameSite: 'None',
                secure: true,
                signed: true,
                maxAge: 15 * 24 * 60 * 60 * 1000    // 15 days validity.
            });
            return ResponseService.successResponse(res, {user, tokenInfo});
        });
    },

    verifyEmailToken: async (req, res) => {
        let token = req.param('token');
        let user_id = +req.param('user_id', 0);
        if(!token || !user_id){
            sails.log.info('Invalid request for verify email', {token, user_id});
            return ResponseService.errorResponse(res, 'Invalid request');
        }

        try{
            let user = await sails.models.user.findOne({
                id: user_id,
                verify_email_token: token
            })
            if(!user){
                sails.log.info('email token not found');
                return ResponseService.errorResponse(res, 'Sorry, this link has been expired. Please check your inbox if email address is not verified yet');
            }
            const emailToChange = user.email_to_change

            if(!emailToChange){
                TokenUtil.storeUACRecordsOrError([{
                    user_ref: user_id,
                    role: TokenUtil.ROLES.USER,
                    resource: '',
                    sub_resource: '',
                    designation: null,
                    flags: {}
                }]).catch(sails.log.error);
            }
            sails.log.info('Token is valid, updating user on', moment().valueOf());

            sails.models.user.updateOne({id: user_id}).set({
                email: emailToChange ? emailToChange : user.email,
                email_to_change: null,
                verify_email_token: null,
                email_verified_on: moment().valueOf(),
            }).exec(function updateCallback(updateError, user){
                if(updateError){
                    sails.log.info('Failed to update user');
                    return ResponseService.errorResponse(res, sails.__('internal server error'), {updateError});
                }
                sails.log.info('update successful', user ? user.id : null);
                return ResponseService.successResponse(res, {user: {id: user.id}});
            });
            if(emailToChange){
                let updatedTicket = await sails.models.supportticket.update({email:user.email}).set({
                    email: emailToChange
                });
            }
        }catch (e) {
            sails.log.info('Failed to verify email', e);
            return ResponseService.errorResponse(res, sails.__('internal server error'), {e});
        }
    },

    forgotPassword: async (req, res) => {
        let userEmail = (req.body.email || '').trim();
        if(!userEmail.length){
            return ResponseService.errorResponse(res, 'All fields required.');
        }

        try {
            let user = await sails.models.user.findOne({
                email: userEmail.toLowerCase(),
                is_active: 1,
                email_verified_on: {'!=': null}
            });
            if (!user) {
                sails.log.info('Active user not found for', userEmail);
                return ResponseService.errorResponse(res, 'Sorry, No active account with given email address exists.');
            }

            // Extract domain from email
            const domain = user.email.split('@')[1]

            // Check if the domain is managed by an SSO provider
            const ssoConfiguration = await sails.models.companyssoconfiguration_reader.findOne({
                where: {
                    domain,
                    enabled: true,
                },
            });

            // If the domain is managed by an SSO provider, return an error
            if (ssoConfiguration && ssoConfiguration.providers) {
                sails.log.info(`Password reset not possible as your account is managed by an SSO provider ${ssoConfiguration.providers[0].key}, please contact your company administrator for a new password`);

                return ResponseService.errorResponse(res, `Password reset not possible as your account is managed by an SSO provider ${ssoConfiguration.providers[0].key}, please contact your company administrator for a new password`);
            }

            let now = moment();
            sails.log.info(`Email is valid, Checking existing reset request, expiry ? ${user.reset_password_expires} > ${now.valueOf()}`);
            if((user.reset_password_expires && user.reset_password_expires > now.valueOf()) && user.reset_password_token){
                // reset flow is already initiated for given user
                return ResponseService.successResponse(res, {message: `An email has already been sent to given address with further instructions.`});
            }

            if(user.reset_password_token && user.reset_count >= 5){
                // too many forgot password request for user
                sails.log.info('Account is locked, count is :', user.reset_count);
                return ResponseService.errorResponse(res, `Too many requests, this account has been locked. Please contact support team to continue.`);
            }

            let token = (Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15));
            sails.log.info(`Existing token doesn't exists or expired, sending new email `, token);
            user = await sails.models.user.updateOne({id: user.id}).set({
                reset_password_token: token,
                reset_password_expires: moment().add(3, 'h').valueOf(),
                reset_count: (user.reset_count + 1)
            });

            // update user with reset token & expiry & send an Email
            await sendForgotPassEmailNotification(user);
            return ResponseService.successResponse(res, {message: `Please check your email for password reset instructions, you may also need to check your junk email.`});
        }catch (e) {
            sails.log.info('Failed to initiate forgot password', e);
            return ResponseService.errorResponse(res, sails.__('internal server error'), {e});
        }
    },

    resetPassword: async (req, res) => {
        let resetToken = (req.body.reset_token || '').trim();
        let newPass = (req.body.password || '');
        if(!resetToken.length || !newPass.length){
            return ResponseService.errorResponse(res, 'All fields required.');
        }

        try {
            sails.log.info('Searching user by token', resetToken);
            let user = await sails.models.user.findOne({
                reset_password_token: resetToken,
                reset_password_expires: {'>':moment().valueOf()}
            });
            if(!user){
                return ResponseService.errorResponse(res, `Sorry, this link has been expired. Please request another link using from forgot password screen.`, {expired: true});
            }

            sails.log.info('Reset token is valid, changing password');
            let updatedUser = await sails.models.user.updateOne({id: user.id}).set({
                password: newPass,
                reset_password_token: null,
                reset_password_expires: null,
                reset_count: 0
            });
            sails.log.info('update successful', updatedUser ? updatedUser.id : null);
            // todo: send mail alert to user
            return ResponseService.successResponse(res, {message: `Password changed successfully.`});
        }catch (e) {
            sails.log.info('Failed to reset password', e);
            return ResponseService.errorResponse(res, sails.__('internal server error'), {e});
        }
    },
    changeEmail: async(req, res) => {
        //@todo: This needs to be fixed, We should verify email on Change.
        let {validationError, payload} = changeEmailAddress(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'All fields required.', {validationError});
        }
        let currentEmail = (req.body.current_email || '').trim().toLowerCase();
        let newEmail = (req.body.email || '').trim().toLowerCase();

        const alreadyExistUser = await sails.models.user.findOne({
            email: newEmail
        });
        if(alreadyExistUser){
            return ResponseService.errorResponse(res, 'User already exist with this email');
        }
        sails.log.info(`Change email request`, req.user.id, currentEmail);
        if(req.user.email !== currentEmail){
            sails.log.info('Failed to update user email');
            return ResponseService.errorResponse(res, `Email update failed: The provided current email is incorrect.`, {wrongEmail:true});
        }

        try {
            let updatedUser = await sails.models.user.updateOne({id: req.user.id, email:currentEmail}).set({
                email_to_change: newEmail,
                verify_email_token: (Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)),
                email_verified_on: null
            });

            if (!updatedUser) {
                sails.log.info('Failed to update user email');
                return ResponseService.errorResponse(res, sails.__('internal server error'), updatedUser);
            }

            // let updatedTicket = await sails.models.supportticket.update({email:currentEmail}).set({
            //     email: newEmail
            // })
            // if (!updatedTicket) {
            //     sails.log.info('Failed to update support_ticket email');
            // }
            sails.log.info('update successful', updatedUser ? updatedUser.id : null);

            if (updatedUser) {
                let subject = `You're almost there! Just confirm your email address`;
                let html = await sails.renderView('pages/mail/verify-email', {
                    title: subject,
                    user: updatedUser,
                    layout: false
                });
            await EmailService.sendMail(subject, [updatedUser.email_to_change], html);
            }
        return ResponseService.successResponse(res, {updatedUser});
        } catch (e) {
            sails.log.info('Failed to reset email', e.message);
            return ResponseService.errorResponse(res, sails.__('internal server error'), {e});
        }
    },

    changePassword: async (req, res) => {
        let currentPass = req.body.current_password || '';
        let newPass = req.body.password || '';
        sails.log.info(`Change password request`);
        //let confirmPass = req.body.confirm_password || '';
        if(!currentPass || !newPass || !newPass.length){
            return ResponseService.errorResponse(res, 'All fields required.');
        }
        bcrypt.compare(currentPass, req.user.password, function (matchError, matched) {
            if (matchError) {
                return ResponseService.errorResponse(res, sails.__('internal server error'), matchError);
            }

            if (!matched) {
                return ResponseService.errorResponse(res, "Incorrect current password.");
            }

            sails.models.user.updateOne({id: req.user.id}).set({
                password: newPass
            }).exec(function updateCallback(updateError, user) {
                if (updateError) {
                    sails.log.info('Failed to update user password');
                    return ResponseService.errorResponse(res, sails.__('internal server error'), updateError);
                }
                sails.log.info('update successful', user ? user.id : null);
                return ResponseService.successResponse(res, {user});
            });
        });
    },

    registerDevice: async (req, res) => {
        sails.log.info("register request for register device token by user ", req.body.user_ref);
        let createRequest = _.pick((req.body || {}), [
            'token',
            'user_ref',
            'platform'
        ]);
        let registeredEntry = await sails.models.registereddevices.find({user_ref: createRequest.user_ref, platform: createRequest.platform}).sort([{id: 'ASC'}]).limit(1);
        if(registeredEntry.length) {
            let deviceEntry = await sails.models.registereddevices.updateOne({id: registeredEntry[0].id}).set(createRequest);
            return ResponseService.successResponse(res, {data: deviceEntry});
        } else {
            let deviceEntry = await sails.models.registereddevices.create(createRequest);
            return ResponseService.successResponse(res, {data: deviceEntry});
        }
    },

    ssoCheck: async (req, res) => {
        sails.log.info("sso configuration check request for email domain ", req.body.email);
        const { login } = req.query;
        const is_login = login === 'true';

        if (!_.has(req.body, 'email')) {
            return ResponseService.errorResponse(res, "Invalid request");
        }
        let email = req.body.email.toString().toLowerCase();

        if (!email.match(EMAIL_REGEX)) {
            sails.log.info('Invalid email address provided', req.body.email);
            return ResponseService.errorResponse(res, "Invalid email address");
        }

        let exists = !is_login ? await doesUsernameExist(email) : undefined;

        const domain = email.split('@')[1]

        const ssoConfiguration = await sails.models.companyssoconfiguration_reader.findOne({
            where: {
                domain: domain,
                enabled: true,
            },
        });

        if (ssoConfiguration && ssoConfiguration.providers) {
            sails.log.info('Sso is enabled');

            const providers = ssoConfiguration.providers.map(el => ({
                ...el,
                url: generateSSOUrl(el.key, email)
            }));

            return ResponseService.successResponse(res, {
                sso_enabled: true,
                providers,
                support_email: ssoConfiguration.support_email,
                ...!is_login ? { exists } : {}
            });
        }
        sails.log.info('Sso is not enabled');
        return ResponseService.successResponse(res, { sso_enabled: false, ...!is_login ? { exists } : {} });
    }
};
