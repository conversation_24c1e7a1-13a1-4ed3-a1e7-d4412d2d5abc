/*
 * RAMS = Risk Assessment & Method Statements.
 */
const moment = require("moment");
const momentTz = require('moment-timezone');
const {
    DEFAULT_PAGE_SIZE,
    PROJECT_SETTING_KEY: {
        RAMS_ASSESSMENT_FORM,
    },
    displayDateFormat_DD_MM_YYYY,
    displayDateFormat_DD_MM_YYYY_HH_mm_ss,
    displayTimeFormat_HH_mm_ss,
} = sails.config.constants;
const {
    UserRevisionService: {getLatestUserRevision},
    DataProcessingService: {
        getUserFullName,
        getProjectTimezone,
        expandToolboxTalks,
        sendMailToNM,
        sendToolboxTalksInvitation,
        populateUserRefs,
        populateProjectRefs,
        containsNonLatinCodepoints,
        buildBriefingToolStatusMessage,
    },
    EmailService: {
        sendMail,
        sendRawEmail,
    },
    ResponseService: {
        errorResponse,
        successResponse,
        sendResponse,
        errorObject,
    },
    ExcelService: {
        streamExcelDownload,
        workPackagePlanReport,
        downloadToolboxTalkXLSX,
        downloadRegisterXLSX,
    },
    TokenUtil: {
        getCompanyInfo,
        hasOnlySiteManagementAccess,
        buildRamsStatusMessage,
    },
    SharedService: {
        getCountryCodeOfProject,
        downloadPdfViaGenerator,
        mergingPdfs,
        mergePdfsViaUrls,
    },
    HttpService: {
        fetchUrlAs,
        decodeURIParam
    },
    NotificationService: {
        NOTIFICATION_CATEGORY,
        sendInviteNotification
    },
    PdfUtil: {
        saveAndUploadPdfObject
    }
} = require('./../services');

const {
    ramsFn: {
        getPaginateRams,
    },
    inductionFn: {
        getUserInductionEmployer
    },
    companyFn:{
        getTaggedOwnersList
    },
    coreFn: {
        runReadQuery
    }
} = require('../sql.fn');

const {
    RamsValidator: {
        validateCreateRams
    }
} = require('../validators')

const {
    PDFDocument,
    degrees,
    drawImage
} = require("pdf-lib");

const {allProjectAdminsByOneOfDesignations} = require("../services/TokenUtil");
const fontkit = require("@pdf-lib/fontkit");
const dayjs = require("dayjs");
const {s3UploaderWithExpiry, uploadToS3} = require("../services/SharedService");
const path = require("path");
const {writeFileSync} = require("fs");
const { v4: uuid4 } = require('uuid');

const getRams = async (filter, pageNumber, pageSize, download, briefed_by_user_ref=0) => {
    let total_record_count = 0
    let countFilter = filter;
    if(pageSize && !isNaN(pageNumber) && download != true) {
        filter = {
            where: filter,
            skip : pageNumber * pageSize,
            limit: pageSize
        }
    }
    let projectRamsRecords = await sails.models.projectrams_reader.find(filter)
        .sort([
            {id: 'DESC'}
        ])
        .populate('briefing_file_ref')
        .populate('tagged_owner');
    projectRamsRecords = await populateProjectRefs(projectRamsRecords, 'project_ref', []);
    projectRamsRecords = await populateUserRefs(projectRamsRecords, 'user_ref', []);

    if(pageSize && download != true) {
        total_record_count = await sails.models.projectrams.count(countFilter)
    }
    //check what id done here
    return await expandToolboxTalks(filter.project_ref, {}, projectRamsRecords, 'rams', briefed_by_user_ref, total_record_count);
};

const expandRamsBriefings = async (projectId, records, totalCount) => {
    let {expanded_briefings, total_record_count, received_briefings } = await expandToolboxTalks(projectId, {}, records, 'rams', 0, totalCount);
    // Converting briefing.register structure from [{user_ref: 2}] to [2].
    expanded_briefings.map(rams => {
        rams.register.map(briefing => {
            briefing.register = briefing.register.map(r => r.user_ref);
            return briefing;
        });
        return rams;
    });

    return {expanded_briefings, total_record_count, received_briefings };
}

const lastRecordGroupId = async (projectId) => {
    let baseValue = 0;
    let [ramsRecord] = await sails.models.projectrams_reader.find({
        where: {project_ref: projectId},
        select: ['id', 'group_id'],
        limit: 1,
        sort: ['group_id DESC']
    });

    let next_group_id = parseInt((ramsRecord && ramsRecord.group_id) ? ramsRecord.group_id : baseValue) + 1;
    sails.log.info('RAMS next_group_id will be', next_group_id);
    return next_group_id;
}

const PROJECT_DATA_FIELDS = {
    PROJECT_NAME: "project_name",
    PROJECT_NUMBER: "project_number",
    PROJECT_POSTCODE: "project_postcode"
}

const RAMS_DATA_FIELDS = {
    DATE_SUBMITTED: "rams_submitted_at",
    DATETIME_SUBMITTED: "datetime_of_submit",
    RAMS_TITLE: "rams_title",
    REFERENCE_NUMBER: "reference_number",
    REVISION: "rams_revision",
    STATUS: "rams_status",
    SUBMITTED_BY: "rams_submitted_by"
}

const fillFormField  = (form, fieldName, fieldValue, fontSize) => {
    let field = form.getTextField(fieldName);
    let da = field.acroField.getDefaultAppearance() || '';
    if (field && fieldValue) {
        if (fontSize && da) {
            field.setFontSize(fontSize)
        }
        field.setText(`${fieldValue}`);
    }

    return form;
}

const fillPdfForm = async (req, ramsRecord, project, tempUpload=false) => {
    let tz = getProjectTimezone(project);

    let ramsAssessmentForm = await sails.models.projectsetting_reader.findOne({
        select: ['value'],
        where: {'name': 'rams_assessment_form', 'project_ref': +project.id}
    });

    let fontSize = ramsAssessmentForm.value.font_size || 10;
    let files = await sails.models.userfile_reader.find({
        where: {id: [+ramsAssessmentForm.value.rams_assessment_ref, ramsRecord.briefing_file_ref]},
        select: ['file_url', 'name']
    });

    let ramsAssessmentFormFields = ramsRecord.custom_field.assessment_form_fields;
    let assessmentFormFile = (files).find(file => file.id === +ramsAssessmentForm.value.rams_assessment_ref);
    let briefingFile = (files).find(file => file.id === ramsRecord.briefing_file_ref);

    //Filling PDF START
    sails.log.info(`RAMS: filling assessment form start, fontSize: ${fontSize}.`);
    const formUrl = assessmentFormFile.file_url; //'https://pdf-lib.js.org/assets/dod_character.pdf';
    let response = await fetchUrlAs(formUrl, 'arraybuffer');
    let formBytes = '';
    if (!response.success) {
        sails.log.info('Failed to update RAMS status, Assessment form not found.');
        return errorObject('Failed to update RAMS status, Assessment form not found.');
    }
    formBytes = Buffer.from(response.data, 'binary').toString('base64');

    // Load the PDF with form fields
    const pdfDoc = await PDFDocument.load(formBytes);

    // get two text fields from the form
    let form = pdfDoc.getForm();

    let hasNonLatinChar = false;
    for(let formField of ramsAssessmentFormFields) {
        if (!formField.answer && formField.field_type !== 'data_field') {
            continue;
        }
        if(formField.field_type === 'signature') {
            sails.log.info("Adding Signature.");
            const emblemImage = await pdfDoc.embedPng(formField.answer)
            const factionImageField = form.getButton(formField.field_name)
            // Fill the faction image field with our emblem image
            factionImageField.setImage(emblemImage)
        } else if(formField.field_type === 'esignature') {
            sails.log.info("Adding E-Signature.");
            const emblemImage = await pdfDoc.embedPng(formField.answer);
            const imageField = form.getSignature(formField.field_name);
            imageField.acroField.getWidgets().forEach((widget) => {
                const { context } = widget.dict;
                const { width, height } = widget.getRectangle();
                const appearance = [
                    ...drawImage(formField.field_name, {
                        x: 5,
                        y: 5,
                        width: width - 10,
                        height: height - 10,
                        rotate: degrees(0),
                        xSkew: degrees(0),
                        ySkew: degrees(0),
                    }),
                ];

                const stream = context.formXObject(appearance, {
                    Resources: { XObject: { [formField.field_name]: emblemImage.ref } },
                    BBox: context.obj([0, 0, width, height]),
                    Matrix: context.obj([1, 0, 0, 1, 0, 0]),
                });
                const streamRef = context.register(stream);

                widget.setNormalAppearance(streamRef);
            });
        } else if(formField.field_type === 'data_field') {
            sails.log.info(`Filling data field through: ${formField.data_field}`);
            let fieldValue = null;

            if ([PROJECT_DATA_FIELDS.PROJECT_NAME, PROJECT_DATA_FIELDS.PROJECT_NUMBER, PROJECT_DATA_FIELDS.PROJECT_POSTCODE].includes(formField.data_field)) {
                fieldValue = (PROJECT_DATA_FIELDS.PROJECT_NAME === formField.data_field) ? project.name : ((PROJECT_DATA_FIELDS.PROJECT_NUMBER === formField.data_field) ? project.project_number : project.postcode);
            } else if ([RAMS_DATA_FIELDS.DATE_SUBMITTED, RAMS_DATA_FIELDS.DATETIME_SUBMITTED].includes(formField.data_field) && ramsRecord.createdAt) {
                fieldValue = dayjs(ramsRecord.createdAt).tz(tz).format((formField.data_field === RAMS_DATA_FIELDS.DATE_SUBMITTED) ? displayDateFormat_DD_MM_YYYY : displayDateFormat_DD_MM_YYYY_HH_mm_ss);
            } else if ([RAMS_DATA_FIELDS.RAMS_TITLE, RAMS_DATA_FIELDS.REFERENCE_NUMBER, RAMS_DATA_FIELDS.REVISION].includes(formField.data_field)) {
                fieldValue = (RAMS_DATA_FIELDS.RAMS_TITLE === formField.data_field) ? ramsRecord.briefing_title : (RAMS_DATA_FIELDS.REFERENCE_NUMBER === formField.data_field) ? ramsRecord.reference_number :  ramsRecord.revision_number;
            } else if (RAMS_DATA_FIELDS.STATUS === formField.data_field) {
                fieldValue = buildBriefingToolStatusMessage(ramsRecord.status);
            }

            form = fillFormField(form, formField.field_name, fieldValue, fontSize);
        } else if(formField.field_type === 'datetime_selector') {
            let fieldValue = dayjs(formField.answer).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss);
            form = fillFormField(form, formField.field_name, fieldValue, fontSize);
        }  else if(formField.field_type === 'date') {
            let fieldValue = dayjs(formField.answer).format(displayDateFormat_DD_MM_YYYY);
            form = fillFormField(form, formField.field_name, fieldValue, fontSize);
        } else if(formField.field_type === 'time_selector') {
            let duration = dayjs.duration(formField.answer);
            let fieldValue = dayjs().hour(duration.hours()).minute(duration.minutes()).format("HH:mm");
            form = fillFormField(form, formField.field_name, fieldValue, fontSize);
        } else {
            if (!hasNonLatinChar) {
                hasNonLatinChar = containsNonLatinCodepoints(formField.answer);
            }
            let fieldValue = formField.answer;
            form = fillFormField(form, formField.field_name, fieldValue, fontSize);
        }
    }

    if (hasNonLatinChar) {
        sails.log.info('Some values contains non latin or special characters.');
        // Fetch the Ubuntu font
        const fontUrl = 'https://pdf-lib.js.org/assets/ubuntu/Ubuntu-R.ttf';
        const fontBytes = await fetch(fontUrl).then((res) => res.arrayBuffer());

        // Embed the Ubuntu font
        pdfDoc.registerFontkit(fontkit);
        const ubuntuFont = await pdfDoc.embedFont(fontBytes);

        // **Key Step:** Update the field appearances with the Ubuntu font
        form.updateFieldAppearances(ubuntuFont);
    }

    // Flatten the form's fields
    try {
        form.flatten();
    } catch (e) {
        sails.log.info("Unable to flatten, ", e);
    }

    let filledFileUrl = '';
    if (tempUpload) {
        const pdfBytes = await pdfDoc.save();
        let fileAsBuffer = new Buffer.from(pdfBytes);

        const mergedPdf = await PDFDocument.create();
        const pdfA = await PDFDocument.load(fileAsBuffer);
        const copiedPagesA = await mergedPdf.copyPages(pdfA, pdfA.getPageIndices());
        copiedPagesA.forEach((page) => mergedPdf.addPage(page));

        let briefingFileBuffer = await fetchUrlAs(briefingFile.file_url, 'arraybuffer');
        const pdfB = await PDFDocument.load(briefingFileBuffer.data);
        const copiedPagesB = await mergedPdf.copyPages(pdfB, pdfB.getPageIndices());
        copiedPagesB.forEach((page) => mergedPdf.addPage(page));

        const mergedPdfFile = await mergedPdf.save();
        fileAsBuffer = new Buffer.from(mergedPdfFile);

        let filledFileName = `${uuid4()}-${assessmentFormFile.name}`;

        let s3UploadResponse = await s3UploaderWithExpiry(`rams-assessment/${filledFileName}`, fileAsBuffer, 'application/pdf', 'public-read', 1);
        if (s3UploadResponse.Location || s3UploadResponse.location) {
            sails.log.info("got preview pdf.")
            return {
                file_url: s3UploadResponse.public_url || s3UploadResponse.Location,
                name: filledFileName
            };
        }
    } else {
        let filled_file = await saveAndUploadPdfObject(req.user.id, pdfDoc, assessmentFormFile.name, 'rams-assessment');
        filledFileUrl = filled_file.file_url;

        //Merge PDFs START
        sails.log.info('RAMS: merging start.', JSON.stringify(files));
        let request = {
            user: req.user,
            body: {
                pdf_files: [filledFileUrl, briefingFile.file_url],
                file_category: 'rams-upload',
                merged_file_name: `Reviewed-${moment().unix()}-${briefingFile.name}`
            }
        }

        return await mergingPdfs(request);
        //Merge PDFs END
    }
}

module.exports = {

    createRams: async (req, res) => {
        sails.log.info('Create risk assessment & method statements(RAMs) for project, by', req.user.id);
        let createRequest = _.pick((req.body || {}), [
            'briefing_title',
            'briefing_file_ref',
            'is_available',
            'tagged_owner',
            'include_during_induction',
            'revision_number',
            'reference_number',
            'is_rams_revision_request',
            'ref_id'
        ]);

        //convert multiple uploads into single by merging
        if (req.body.pdf_files && req.body.pdf_files.length) {
            let user_file = await mergingPdfs(req);
            if (!user_file.id) {
                return sendResponse(res, user_file);
            }
            createRequest.briefing_file_ref = (user_file && user_file.id) ? user_file.id : createRequest.briefing_file_ref;
        }

        //if user who is adding has 'Full Access' or 'Restricted Access' access levels
        if(req.body.is_auto_approve) {
            createRequest.auto_approved = true;
            createRequest.approved_by = req.user.id;
            createRequest.approved_at = moment().valueOf();
            createRequest.status = 2;
        }

        createRequest.user_ref = req.user.id;
        createRequest.project_ref = +req.param('projectId');

        if(createRequest.user_ref){
            let revision = await getLatestUserRevision(createRequest.user_ref);
            createRequest.user_revision_ref = revision.id;
        }

        let ramsExistingRecord = {};
        createRequest.revision_status = true;
        if (createRequest.is_rams_revision_request && createRequest.ref_id) {
            ramsExistingRecord = await sails.models.projectrams_reader.findOne({
                select: ['group_id', 'project_ref', 'record_id'],
                where: {
                    id: +createRequest.ref_id
                }
            });
            createRequest.group_id = ramsExistingRecord.group_id;
            createRequest.record_id = ramsExistingRecord.record_id;
            createRequest.status = (req.body.is_auto_approve) ? 2 : 1;
        } else {
            createRequest.group_id = await lastRecordGroupId(createRequest.project_ref);
        }

        sails.log.info('creating risk assessment & method statements(RAMs) with', createRequest);
        let {validationError, payload} = validateCreateRams(createRequest);
        if(validationError) {
            return errorResponse(res, 'Request failed due to invalid data, Please try again.', {validationError});
        }

        if (ramsExistingRecord.id && ramsExistingRecord.group_id) {
            sails.log.info(`Disable all existing revision of group id ${ramsExistingRecord.group_id} on project ${ramsExistingRecord.project_ref}.`);
            await sails.models.projectrams.update({project_ref: ramsExistingRecord.project_ref, group_id: ramsExistingRecord.group_id}).set({
                revision_status: false,
                is_available: false
            });
        }
        let ramsRecord = await sails.models.projectrams.create(createRequest);
        if(ramsRecord && ramsRecord.id)  {
            let userEmpDetail = await sails.models.userempdetail.findOne({
                where: {user_ref: req.user.id},
                select: ['employer']
            });

            let projectInfo = await sails.models.project.findOne({where: {id: ramsRecord.project_ref}, select: ['name', 'custom_field']});
            let project_name = projectInfo.name;
            let ramsPhrase = projectInfo.custom_field.rams_phrase_singlr || '';
            let typeTitle = 'rams';
            let mailSubject = `${ramsPhrase} Approval Pending - Project: ${project_name}`;
            let mailBodyText = `${getUserFullName(req.user)} (${userEmpDetail.employer}) has added ${ramsPhrase} ${ramsRecord.briefing_title} onto ${project_name} project.`;
            await sendMailToNM(ramsRecord, typeTitle, mailSubject, mailBodyText, 'notify-to-nm-on-add-rams');

            sails.log.info('Failed to create risk assessment & method statements(RAMs).')
            return successResponse(res,  {project_rams: ramsRecord});
        }
        sails.log.info('Failed to create risk assessment & method statements(RAMs).')
        return errorResponse(res, sails.__('Failed to create risk assessment & method statements(RAMs).'));
    },

    updateRams: async (req, res) => {
        let id = +req.param('id');
        sails.log.info('Update risk assessment & method statements(RAMs) request.', id);

        let updateRequest = _.pick((req.body || {}), [
            'briefing_title',
            'is_available',
            'reject_reason',
            'tagged_owner',
            'include_during_induction',
        ]);

        sails.log.info(`[updateRams]: record id ${id}, body:`, req.body);

        if (req.body.is_approving) {
            updateRequest.approved_by = req.user.id;
            updateRequest.approved_at = moment().valueOf();
            updateRequest.status = 2;
        } else if (req.body.is_declining) {
            updateRequest.status = 0;
        }
        let ramsRecord = await sails.models.projectrams.updateOne({id: id}).set(updateRequest);

        if (req.body.is_declining || req.body.is_approving) {
            let userInfo = await sails.models.user_reader.findOne({
                where: {'id': ramsRecord.user_ref},
                select: ['email', 'first_name']
            });

            let projectInfo = await sails.models.project.findOne({
                select: ['name', 'custom_field'],
                where: {id: ramsRecord.project_ref}
            });

            let ramsPhrase = (projectInfo.custom_field.rams_phrase_singlr || 'RAMS');
            let mailSubject = (req.body.is_declining) ? `${ramsPhrase} Rejected: Project - ${projectInfo.name}` : `${ramsPhrase} Accepted: Project - ${projectInfo.name}`;

            let by_user = getUserFullName(req.user);

            let emailHtml = null;
            if (req.body.is_declining) {
                emailHtml = await sails.renderView('pages/mail/mail-content', {
                    mail_body: 'project-rams-declined',
                    title: mailSubject,
                    project_name: projectInfo.name,
                    rams_phrase: ramsPhrase,
                    user_fname: userInfo.first_name,
                    ramsRecord,
                    briefing_file_url: '',
                    briefing_file_name: '',
                    by_user,
                    layout: false
                });
            } else {
                emailHtml = await sails.renderView('pages/mail/mail-content', {
                    mail_body: 'project-rams-approved',
                    title: mailSubject,
                    project_name: projectInfo.name,
                    rams_phrase: ramsPhrase,
                    user_fname: userInfo.first_name,
                    ramsRecord,
                    briefing_file_url: '',
                    briefing_file_name: '',
                    by_user,
                    layout: false
                });
            }

            sails.log.info('Sending mail to', userInfo.email);
            await sendMail(mailSubject, [userInfo.email], emailHtml);
        }

        sails.log.info('Updated risk assessment & method statements(RAMs) successfully, id', ramsRecord.id);
        return successResponse(res, {project_rams: ramsRecord});
    },

    getProjectRamsById: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let ramsId = +req.param('ramsId', 0);
        sails.log.info(`get RAMS Id: ${ramsId} of project ${projectId}`);
        let rams = await sails.models.projectrams_reader.findOne({
            where: {id: ramsId, project_ref: projectId}
        })
            .populate('briefing_file_ref')
            .populate('tagged_owner');

        return successResponse(res, {rams});
    },

    /**
     * @deprecated As it is processing all register data.
     * Use taskbriefings.getprojectbriefingtoolrecords
     * @param req
     * @param res
     * @returns {Promise<*>}
     */
    getProjectRams: async (req, res) => {
        let projectId = +req.param('projectId');
        let pageNumber = +req.param('pageNumber', 0);
        let tagged_owner = req.param('tagged_owner') ? req.param('tagged_owner').split(',').map(a=>+a): [];
        let searchTerm = (req.param('search') == 'null') ? null : req.param('search');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'desc');
        //check if user has only site management access on project
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
        sails.log.info("hasOnlySiteManagement: ", hasOnlySiteManagement);

        //Site management access level will only be able to access the RAMS that are specific to their company. So if users company = RAMS company
        let taggedOwnersList = [];
        if (hasOnlySiteManagement) {
            let userEmployer = await getUserInductionEmployer(req.user, projectId);
            tagged_owner[0] = (userEmployer || 0);
        }
        if(pageNumber == 0 && !searchTerm && !tagged_owner.length){
            taggedOwnersList = await getTaggedOwnersList('project_rams', projectId);
        }
        if(searchTerm){
            searchTerm = decodeURIParam(searchTerm);
        }
        let defaultResponse = {
            project_rams_records: [],
            total_record_count: 0,
            q: searchTerm,
            pageSize,
            pageNumber,
            sortKey,
            sortDir,
        };
        let {
            total: total_record,
            records: ramsRecords
        } = await getPaginateRams(projectId, tagged_owner, pageSize, (pageSize * pageNumber), false, sortKey, sortDir, {
            searchTerm
        });
        let userEmployer = {};
        let userEmployerId = await getUserInductionEmployer(req.user, projectId);
        if(userEmployerId){
            userEmployer = await sails.models.createemployer_reader.findOne({
                where: {id: userEmployerId},
                select: ['id', 'name', 'country_code']
            });
        }
        let active_rams =  await sails.models.projectrams_reader.find({
            select: ['id', 'status', 'tagged_owner'],
            where: {project_ref: projectId, is_available: true, status: [2, 3], include_during_induction: true, is_archived: false}
        });

        sails.log.info(`Got ${active_rams.length} active rams on the project ${projectId}.`);

        if (!ramsRecords.length) {
            sails.log.info(`No record found, for projectId: ${projectId}, q: "${searchTerm}" pageNumber: ${pageNumber} pageSize: ${pageSize} totalCount: ${total_record}`);
            return successResponse(res, {...defaultResponse, active_rams, total_record_count:total_record, employerList: [], userEmployer});
        }

        let {expanded_briefings: projectRamsRecords, total_record_count, received_briefings } = await expandRamsBriefings(projectId, ramsRecords, total_record);

        return successResponse(res, {...defaultResponse, project_rams_records: projectRamsRecords, active_rams, total_record_count, employerList: [], userEmployer,taggedOwnersList});
    },

    getProjectRamsV2: async (req, res) => {
        let projectId = +req.param('projectId');
        let pageNumber = +req.param('pageNumber', 0);
        let tagged_owner = (req.param('tagged_owner') && req.param('tagged_owner') !== 'null') ? req.param('tagged_owner').split(',').map(a=>+a): [];
        let searchTerm = (req.param('search') == 'null') ? null : req.param('search');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'desc');
        //check if user has only site management access on project
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
        sails.log.info("hasOnlySiteManagement: ", hasOnlySiteManagement);

        //Site management access level will only be able to access the RAMS that are specific to their company. So if users company = RAMS company
        if (hasOnlySiteManagement) {
            let userEmployer = await getUserInductionEmployer(req.user, projectId);
            tagged_owner[0] = (userEmployer || 0);
        }

        if(searchTerm){
            searchTerm = decodeURIParam(searchTerm);
        }

        let defaultResponse = {
            project_rams_records: [],
            totalCount: 0,
            q: searchTerm,
            pageSize,
            pageNumber,
            sortKey,
            sortDir,
        };
        let {
            total: totalCount,
            records: project_rams_records
        } = await getPaginateRams(projectId, tagged_owner, pageSize, (pageSize * pageNumber), false, sortKey, sortDir, {
            searchTerm,
            select: ['id', 'status', 'briefing_title', 'record_id', 'briefing_file_ref', 'is_available', 'tagged_owner', `"createdAt"`]
        });

        if (!project_rams_records.length) {
            sails.log.info(`No record found, for projectId: ${projectId}, q: "${searchTerm}" pageNumber: ${pageNumber} pageSize: ${pageSize} totalCount: ${totalCount}`);
            return successResponse(res, {...defaultResponse, totalCount});
        }

        return successResponse(res, {
            ...defaultResponse,
            project_rams_records,
            totalCount,
        });
    },

    getProjectRamsList: async (req, res) => {
        let projectId = +req.param('projectId');
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'desc');
        let activeOnly = (req.param('activeOnly') === 'true');
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
        sails.log.info("hasOnlySiteManagement: ", hasOnlySiteManagement);

        //Site management access level will only be able to access the RAMS that are specific to their company. So if users company = RAMS company
        let tagged_owner = [];
        if (hasOnlySiteManagement) {
            let userEmployerId = await getUserInductionEmployer(req.user, projectId);
            tagged_owner = (userEmployerId) ? [userEmployerId] : [];
        }

        let filter = {
            project_ref: projectId,
            revision_status: true
        };

        if (activeOnly) {
            filter.status = [2];
            filter.is_available = true;
            filter.is_archived = false;
        }

        if (tagged_owner.length) {
            filter.tagged_owner = {'in': tagged_owner}
        } else if (hasOnlySiteManagement) {
            filter.user_ref = req.user.id;
        }

        let sorting = [{id: 'ASC'}];
        if (sortKey && sortDir) {
            sorting = [];
            sorting.push({[sortKey]: sortDir})
        }

        sails.log.info('fetching project rams list with filters ', filter, ' and sort ', sorting);
        let project_rams = await sails.models.projectrams_reader.find({
            select: ['id', 'briefing_title', 'revision_number', 'status'],
            where: filter
        }).sort(sorting);

        sails.log.info(`Found ${project_rams.length} project rams.`);
        return successResponse(res, {project_rams});
    },

    getProjectRamsByUser: async (req, res) => {
        let userId = +req.param('userId');
        let projectId = +req.param('projectId');
        let pageNumber = +req.param('pageNumber', 0);
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        if(isNaN(pageSize) || pageSize == undefined){
            pageSize = false
        }
        let filter = {
            project_ref: projectId,
            is_available: true,
            briefed_count: { '!=' : null }
        };

        let {expanded_briefings: projectRamsRecords, received_briefings} = await getRams(filter, pageNumber,pageSize, false, userId);
        return successResponse(res, {project_rams_records: projectRamsRecords, received_briefings});
    },

    searchRams: async (req, res) => {
        let q = (req.query.q || '').toString().trim();
        let projectId = +req.param('projectId');

        sails.log.info(`Search risk assessment & method statements(RAMs)., q: '${q}'`);

        let filter = { is_available:true, revision_status:true, project_ref:projectId, or : [{briefing_title: {contains: q}}], is_archived: false};

        let projectRamsRecords = await sails.models.projectrams_reader.find(filter)
            .sort([
                {briefing_title: 'ASC'},
                {id: 'ASC'},
            ]).limit(50);

        sails.log.info('got , total', projectRamsRecords.length);
        return successResponse(res, {project_rams_records: projectRamsRecords});
    },

    inviteToRams: async(req, res) => {
        let projectId = +req.param('projectId');
        sails.log.info(`invite to risk assessment & method statements(RAMs) for project: ${projectId}, by`, req.user.first_name);

        let inviteRequest = _.pick((req.body || {}), [
            'usersToInvite'
        ]);
        inviteRequest.items = req.body.project_rams;
        let user_name = getUserFullName(req.user);
        let project = await sails.models.project_reader.findOne({
            select: ['name', 'custom_field'],
            where: {id: projectId}
        });
        inviteRequest.items = await sails.models.projectrams_reader.find({ where: {id: inviteRequest.items}, select: ['id', 'briefing_title']});
        let ramsPhrase = project.custom_field.rams_phrase_singlr;
        const category = NOTIFICATION_CATEGORY.RAMS_INVITATION;
        let data = {
            category: category,
            project_ref: project.id,
            invitedBy: user_name
        };
        let firebaseMsgData = Object.assign({}, data);
        firebaseMsgData.project_ref = project.id.toString();
        for(let i=0; i<inviteRequest.usersToInvite.length; i++) {
            let { user_ref: userToInvite } = inviteRequest.usersToInvite[i];
            for(let j=0; j<inviteRequest.items.length; j++) {
                const item = inviteRequest.items[j];
                let { briefing_title, id: briefingId} = item;
                data.ramsId = briefingId;
                firebaseMsgData.ramsId = briefingId.toString();
                sails.log.info('Sending push notification for rams invite for user ', userToInvite);
                await sendInviteNotification(ramsPhrase, briefing_title, project, userToInvite, req.user, category, data, firebaseMsgData);
            }
        }
        let subject = `${ramsPhrase} Invite - Project ${project.name}`;
        await sendToolboxTalksInvitation(inviteRequest, project, subject, user_name, ramsPhrase);
        return successResponse(res, {status: 'Invites sent successfully!'});
    },

    downloadRams: async (req, res) => {
        let id = +req.param('id');
        let type = req.param('type','html');
        let projectId = +req.param('projectId');

        sails.log.info('Render risk assessment & method statements(RAMs):', id, `type: ${type}`);

        let ramsRecord =  await sails.models.projectrams_reader.findOne({id: id})
            .populate('briefing_file_ref')
            .populate('user_ref');
        if (!ramsRecord || !ramsRecord.id) {
            return errorResponse(res, sails.__('Failed to get risk assessment & method statements(RAMs).'));
        }

        let project = await sails.models.project.findOne({where: {id: projectId}});
        sails.log.info('got record, id', ramsRecord.id);
        let { project_logo_file, companyName } = await getCompanyInfo(project);
        let ramsPhrase = project.custom_field.rams_phrase_singlr;

        let tz = getProjectTimezone(project);
        ramsRecord.project_ref = project;
        let {expanded_briefings, received_briefings} = await expandToolboxTalks(projectId, {}, [ramsRecord], 'rams');
        let registers = expanded_briefings[0].register;
        const docName = ramsRecord.briefing_file_ref && ramsRecord.briefing_file_ref.name;
        const docLink = ramsRecord.briefing_file_ref && ramsRecord.briefing_file_ref.file_url;

        let form_template = `pages/toolboxtalk-form-page`;
        let html = await sails.renderView(form_template, {
            title: ramsPhrase,
            item_number: ramsRecord.record_id,
            item_title: `${ramsRecord.reference_number} - ${ramsRecord.briefing_title}`,
            revision_number: ramsRecord.revision_number,
            report_number: `${ramsPhrase} No. ${ramsRecord.record_id}`,
            doc_name: docName,
            doc_link: docLink,
            project_logo_file: project_logo_file,
            project: project,
            registers: registers,
            momentTz(n, format) {
                return momentTz(+n).tz(tz).format(format);
            },
            layout: false
        });

        if(type === 'html') {
            sails.log.info('Rendering html view');
            return res.send(html);
        }

        let file_name = `${ramsPhrase} Report ${ramsRecord.id} ${moment().format('YYYY-MM-DD')}`;
        let project_line = `${project.name}`;
        let date_line = `${ramsPhrase} No. ${ramsRecord.record_id}`;

        const ramsPdf = await downloadPdfViaGenerator({
            req,
            res,
            html,
            tool: 'project-rams',
            file_name,
            heading_line: ramsPhrase,
            project_line,
            date_line,
            logo_file: project_logo_file,
            has_cover: true,
            has_one_page: true,
            responseType: 'path',
        });

        const pdfFiles =  [docLink, ramsPdf.location];
        const mergedFile = await mergePdfsViaUrls(req, res, pdfFiles, file_name);
        return ResponseService.successResponse(res, mergedFile)
    },

    downloadRamsReport: async (req, res) => {
        let projectId = +req.param('projectId');
        let fromDate = +(req.body.fromDate);
        let toDate = +(req.body.toDate);
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let filter = { project_ref: projectId };

        if (!fromDate || !toDate) {
            return errorResponse(res, 'from date and to date are required.');
        }
        let {expanded_briefings: projectRamsRecords, received_briefings} = await getRams(filter, pageNumber, pageSize, true);
        let project = await sails.models.project.findOne({
            select: ['name', 'custom_field'],
            where: {id: projectId}
        });
        let ramsPhrase = project.custom_field.rams_phrase_singlr;
        let workbook = await workPackagePlanReport(projectRamsRecords, fromDate, toDate, ramsPhrase);
        return streamExcelDownload(res, workbook);
    },

    downloadRamsXLSX: async (req, res) => {
        let id = +req.param('id');
        let projectId = +req.body.projectId;

        let record =  await sails.models.projectrams_reader.findOne({id: id})
            .populate('briefing_file_ref')
            .populate('user_ref');
        if (record && record.id) {
            sails.log.info('got record, id', record.id);
            let project = await sails.models.project.findOne({
                select: ['name', 'custom_field'],
                where: {id: projectId}
            });
            let tz = getProjectTimezone(project);
            let ramsPhrase = project.custom_field.rams_phrase_singlr;
            record.project_ref = project;
            let {expanded_briefings, received_briefings} = await expandToolboxTalks(projectId, {}, [record], 'rams');
            [record] = expanded_briefings;
            let workbook = await downloadToolboxTalkXLSX(record, 'rams', ramsPhrase, tz);
            return streamExcelDownload(res, workbook);
        }
        sails.log.info('risk assessment & method statements(RAMs) not found, id', id);
        return errorResponse(res, 'risk assessment & method statements(RAMs) not found.');
    },

    downloadRamsRegister: async (req, res) => {
        let projectId = +req.param('projectId');

        //check if user has only site management access on project
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
        sails.log.info("hasOnlySiteManagement: ", hasOnlySiteManagement);
        //Site management access level will only be able to access the RAMS that are specific to their company. So if users company = RAMS company
        let tagged_owner = [];
        if (hasOnlySiteManagement) {
            let userEmployer = await getUserInductionEmployer(req.user, projectId);
            tagged_owner[0] = (userEmployer || 0);
        }

        let filter = {
            project_ref: projectId
        };

        if (tagged_owner.length) {
            filter.tagged_owner = {'in': tagged_owner}
        }
        let records = await sails.models.projectrams_reader.find(filter)
            .sort([
                {status: 'DESC'},
                {id: 'DESC'}
            ]);

        let availableRecords = [];
        let notAvailableRecords = [];
        (records || []).map(record => {
            if (record.status === 1 || (record.status === 2 && record.is_available === true)) {
                availableRecords.push(record);
            } else {
                notAvailableRecords.push(record);
            }

            return record;
        });
        records = [...availableRecords, ...notAvailableRecords];
        sails.log.info(`Found ${records.length} RAMS for project ${projectId}`);
        let {workbook, fileName} = await downloadRegisterXLSX(projectId, 'rams', records, 'rams');
        return streamExcelDownload(res, workbook, fileName);
    },

    approveDeclineRams: async (req, res) => {
        let id = +req.param('id');
        let projectId = +req.param('projectId');
        let ramsAssessmentFormFields = req.body.form_fields;
        sails.log.info(`[approveDeclineRams]: updating rams record ${id} with ${req.body.action_type}`);

        let ramsRecord = await sails.models.projectrams.findOne({
            select: ['briefing_title', 'briefing_file_ref', 'createdAt', 'custom_field', 'reference_number', 'revision_number', 'status', 'user_ref', 'project_ref'],
            where: {id: id, project_ref:+projectId}
        });

        if (!ramsRecord || !ramsRecord.id) {
            return errorResponse(res, 'Request failed due to invalid request, Please try again.');
        }

        if (ramsRecord.status === 0 || ramsRecord.status === 2) {
            let message = `Oops! Looks like someone already ${buildRamsStatusMessage(ramsRecord.status)} this record. The status can't be changed anymore`;
            sails.log.info(`[approveDeclineRams]: ${message} of rams ${id}, `, ramsAssessmentFormFields);
            return errorResponse(res, message);
        }

        ramsRecord.custom_field = (!ramsRecord.custom_field) ? {assessment_form_fields: ramsAssessmentFormFields}: {assessment_form_fields: ramsRecord.custom_field.assessment_form_fields};
        let project = await sails.models.project_reader.findOne({
            where: {id: +ramsRecord.project_ref},
            select: ['name', 'project_number', 'postcode', 'custom_field']
        });

        let merged_file = await fillPdfForm(req, ramsRecord, project);
        if (!merged_file || !merged_file.id) {
            return sendResponse(res, merged_file);
        }
        let updateRequest = {
            reject_reason: req.body.reject_reason,
            status: (req.body.action_type === 'decline') ? 0 : 2,
            briefing_file_ref: merged_file.id,
            custom_field: {
                original_briefing_file_ref: ramsRecord.briefing_file_ref,
                assessment_form_fields: ramsAssessmentFormFields
            }
        };
        if (req.body.action_type === 'approve') {
            updateRequest.approved_by = req.user.id;
            updateRequest.approved_at = moment().valueOf();
        }
        ramsRecord = await sails.models.projectrams.updateOne({id: id}).set(updateRequest);

        let userInfo = await sails.models.user_reader.findOne({
            where: {'id': ramsRecord.user_ref},
            select: ['email', 'first_name']
        });

        let projectInfo = await sails.models.project.findOne({
            select: ['name', 'custom_field'],
            where: {id: ramsRecord.project_ref}
        });

        let ramsPhrase = (projectInfo.custom_field.rams_phrase_singlr || 'RAMS');
        let mailSubject = (req.body.action_type === 'decline') ? `${ramsPhrase} Rejected: Project - ${projectInfo.name}` : `${ramsPhrase} Accepted: Project - ${projectInfo.name}`;

        let by_user = getUserFullName(req.user);

        let emailHtml = await sails.renderView('pages/mail/mail-content', {
            mail_body: (req.body.action_type == 'decline') ? 'project-rams-declined' : 'project-rams-approved',
            title: (req.body.action_type == 'decline') ? `${ramsPhrase} Rejected: Project - ${projectInfo.name}` : `${ramsPhrase} Accepted: Project - ${projectInfo.name}`,
            project_name: projectInfo.name,
            rams_phrase: ramsPhrase,
            user_fname: userInfo.first_name,
            ramsRecord,
            briefing_file_url: (merged_file.file_url) ? merged_file.file_url : '',
            briefing_file_name: (merged_file.name) ? merged_file.name : '',
            by_user,
            layout: false
        });

        sails.log.info('[approveDeclineRams]: Sending mail to', userInfo.email);
        await sendMail(mailSubject, [userInfo.email], emailHtml);

        sails.log.info('[approveDeclineRams]: RAMS status has been update successfully', ramsRecord.id);
        return successResponse(res, {project_rams: ramsRecord});
    },

    partiallySaveAssessmentForm: async (req, res) => {
        let id = +req.param('id');
        let ramsAssessmentFormFields = req.body.form_fields;

        let updateRequest = {
            status: 3, //in review
            custom_field: {
                assessment_form_fields: ramsAssessmentFormFields
            }
        };
        let existingRecord = await sails.models.projectrams.findOne({
            where: {id: id},
            select: ['id', 'status'],
        });
        sails.log.info(`[partiallySaveAssessmentForm]: partially saving assessment form of rams ${id}.`);

        if (existingRecord.status !== 1 && existingRecord.status !== 3) {
            let message = `Oops! Looks like someone already ${buildRamsStatusMessage(existingRecord.status)} this record. The status can't be changed anymore`;
            sails.log.info(`[partiallySaveAssessmentForm]: ${message} of rams ${id}`, ramsAssessmentFormFields);
            return errorResponse(res, message);
        }
        let ramsRecord = await sails.models.projectrams.updateOne({id: id}).set(updateRequest);

        sails.log.info('[partiallySaveAssessmentForm]: Saved assessment form partially.');
        return successResponse(res, {project_rams: ramsRecord});
    },

    getRamsForInduction: async (req, res) => {
        let userEmployer = +(req.user.parent_company);
        if (!userEmployer) {
            sails.log.info('Failed to get project RAMS, Please complete your profile by selecting employer.')
            return errorResponse(res, 'Failed to get project RAMS, Please complete your profile by selecting employer.');
        }
        let projectId = +req.param('projectId');
        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['custom_field', 'project_section_access']
        });

        let induction_rams = [];
        if (projectInfo && projectInfo.id && projectInfo.project_section_access.rams && projectInfo.custom_field.has_rams_in_induction) {
            induction_rams = await sails.models.projectrams_reader.find({
                where: {
                    project_ref: projectId,
                    include_during_induction: true,
                    is_available: true,
                    status: [2],
                    tagged_owner: userEmployer,
                    is_archived: false,
                    revision_status: true
                },
                select: ['briefing_title', 'tagged_owner', 'briefing_file_ref']
            }).populate('briefing_file_ref');

            let taggedCompanyIds = induction_rams.map(rams => rams.tagged_owner);
            sails.log.info(`Found ${taggedCompanyIds} tagged companies.`);
            let taggedCompanies = await sails.models.createemployer_reader.find({
                where: {id: _.uniq(taggedCompanyIds)},
                select: ['name']
            });

            induction_rams = induction_rams.map(item => {
                item.tagged_owner = (taggedCompanies || []).find(obj => item.tagged_owner === obj.id);
                return item;
            });
        }

        sails.log.info(`Found ${induction_rams.length} to include during induction process.`);
        return successResponse(res, {induction_rams, rams_phrase: (projectInfo?.custom_field.rams_phrase_singlr || 'RAMS')});
    },

    archiveUnarchiveRams: async (req, res) => {
        let id = +req.param('id');
        let payload = { 'is_archived': req.body.is_archived};
        if (typeof payload.is_archived != "boolean") {
            return errorResponse(res, 'Request failed due to invalid data, Please try again.');
        }
        let statusText = (payload.is_archived) ? 'Archive' : 'Unarchive';
        sails.log.info(`${statusText} RAMS record, id: ${id}`);
        let ramsRecord = await sails.models.projectrams.updateOne({id: id}).set(payload);

        sails.log.info(`Successfully ${statusText} RAMS record, id: ${ramsRecord.id}.`);
        return successResponse(res, {project_rams: ramsRecord});
    },

    getArchivedRams: async (req, res) => {
        let projectId = +req.param('projectId');
        let pageNumber = +req.param('pageNumber', 0);
        let tagged_owner = req.param('tagged_owner') ? req.param('tagged_owner').split(',').map(a=>+a): [];
        let searchTerm = (req.param('search') == 'null') ? null : req.param('search');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        //check if user has only site management access on project
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
        sails.log.info("hasOnlySiteManagement: ", hasOnlySiteManagement);
        //Site management access level will only be able to access the RAMS that are specific to their company. So if users company = RAMS company
        if (hasOnlySiteManagement) {
            let userEmployer = await getUserInductionEmployer(req.user, projectId);
            tagged_owner[0] = (userEmployer || 0);
        }

        if(searchTerm){
            searchTerm = decodeURIParam(searchTerm);
        }
        let defaultResponse = {
            archived_rams: [],
            total_record_count: 0,
            q: searchTerm,
            pageSize,
            pageNumber,
            sortKey: 'id',
            sortDir: 'asc'
        };
        let {
            total: total_record,
            records: ramsRecords
        } = await getPaginateRams(projectId, tagged_owner, pageSize, (pageSize * pageNumber),true, 'id', 'asc', {
            searchTerm
        });

        if (!ramsRecords.length) {
            sails.log.info(`No archived rams found, for projectId: ${projectId}.`);
            return successResponse(res, {...defaultResponse, archived_rams: ramsRecords, total_record_count: total_record});
        }

        let {expanded_briefings: archived_rams, total_record_count, received_briefings } = await expandRamsBriefings(ramsRecords, ramsRecords.length);

        sails.log.info(`Got ${archived_rams.length} archived rams for projectId ${projectId}.`);
        return successResponse(res, {...defaultResponse, archived_rams: ramsRecords, total_record_count: total_record});
    },

    copyAssessmentRamsForm: async (req, res) => {
        let fromProjectId = +req.param('fromProjectId', 0);
        let toProjectId = +req.param('toProjectId', 0);
        let overrideExistingRams = req.param('overrideExistingRams', false);

        sails.log.info(`[ADMIN] Copy RAMS-form, request from ${fromProjectId} => ${toProjectId}`);
        let existing_rams_assessment_form = await sails.models.projectsetting.findOne({
            where: {project_ref: toProjectId, name: RAMS_ASSESSMENT_FORM},
            select: ['id'],
        });

        if(existing_rams_assessment_form && !overrideExistingRams){
            sails.log.info(`[ADMIN] RAMS-form id: ${existing_rams_assessment_form.id} already exists for ${toProjectId}`);
            return errorResponse(res, `RAMS assessment form already exists for project: ${toProjectId}`, {existing_rams_assessment_form});
        }
        let from_rams_assessment_form = await sails.models.projectsetting.findOne({
            where: {project_ref: fromProjectId, name: RAMS_ASSESSMENT_FORM},
        });

        if(!from_rams_assessment_form){
            sails.log.info(`[ADMIN] RAMS-form not found for ${fromProjectId}`);
            return errorResponse(res, `RAMS assessment form not found for project: ${fromProjectId}`);
        }

        if(existing_rams_assessment_form && overrideExistingRams){
            let update_record = await sails.models.projectsetting.update({project_ref: toProjectId, name: RAMS_ASSESSMENT_FORM}).set({
                name: RAMS_ASSESSMENT_FORM,
                value: from_rams_assessment_form.value,
            });
            sails.log.info(`[ADMIN] RAMS-form setting record updated, id: ${update_record[0].id} for project ${toProjectId}`);
            return successResponse(res, {updated: true, update_record});
        }

        let new_record = await sails.models.projectsetting.create({
            project_ref: toProjectId,
            name: RAMS_ASSESSMENT_FORM,
            value: from_rams_assessment_form.value,
        });
        sails.log.info(`[ADMIN] RAMS-form setting record created, id: ${new_record.id} for project ${toProjectId}`);
        return successResponse(res, {created: true, new_record});
    },

    ramsRevisionList: async (req, res) => {
        let ramsId = +req.param('id');
        let projectId = +req.param('projectId');
        let briefing_count = req.param('briefing_count') ? !(req.param('briefing_count', 'false') === 'false') : true;
        let rams = await sails.models.projectrams_reader.findOne({
            select: ['briefing_title', 'group_id'],
            where: {
                id: ramsId,
                project_ref: projectId
            }
        });

        if (!rams || !rams.id) {
            sails.log.error(`No RAMS found with given id ${ramsId} on project ${projectId}.`);
            return errorResponse(res, `No RAMS found with given id ${ramsId} on project ${projectId}.`);
        }
        sails.log.info(`Fetching RAMS by groupId: ${rams.group_id}.`);
        let ramsRecords = await sails.models.projectrams_reader.find({
            select: ['record_id','briefing_title', 'revision_number', 'reference_number', 'group_id'],
            where: {
                group_id: rams.group_id,
                project_ref: projectId
            }
        }).sort([
            {id: 'DESC'}
        ]);
        sails.log.info(`Found ${ramsRecords.length} RAMS by groupId: ${rams.group_id}.`);

        if (briefing_count) {
            let ramsRecordIds = ramsRecords.map(record => record.id);
            sails.log.info(`RAMS revision ids ${ramsRecordIds.join(',')} on project ${projectId} of group ${rams.group_id}.`);

            let tool_briefings_sql = `SELECT tool_record_ref, count(tool_record_ref)
                             FROM tool_briefings
                             WHERE tool_key='rams' AND tool_record_ref IN (${ramsRecordIds.map((r, index) => `${ramsRecordIds[index]}`).join(',')})
                             GROUP BY tool_record_ref`;

            let briefingsCountByRams = await runReadQuery(tool_briefings_sql);
            if (!briefingsCountByRams.length) {
                sails.log.info("No briefing found on any RAMS revision.");
                return successResponse(res, {rams_title: rams.briefing_title, ramsRecords});
            }

            let ramsWithBriefings = briefingsCountByRams.map(r => r.tool_record_ref);
            ramsRecords = ramsRecords.filter(r => ramsWithBriefings.includes(r.id));
        }

        sails.log.info(`Found ${ramsRecords.length} revisions with briefings for rams revision ${ramsId}.`);
        return successResponse(res, {rams_title: rams.briefing_title, ramsRecords});
    },

    searchRamsByRevision: async (req, res) => {
        let revisionNumber = decodeURIParam(req.query.revisionNumber);
        let groupId = +req.param('groupId');
        let projectId = +req.param('projectId');

        if (!revisionNumber) {
            return errorResponse(res, 'Invalid Request.');
        }

        let ramsRecords = await sails.models.projectrams_reader.find({
            select: ['id'],
            where: {
                or: [
                    {revision_number:  [revisionNumber, revisionNumber.toLowerCase(), revisionNumber.toUpperCase()]}
                ],
                project_ref: projectId,
                group_id: groupId
            }
        });

        sails.log.info(`Found ${ramsRecords.length} rams by revision number ${revisionNumber}.`);
        return successResponse(res, {rams_count: ramsRecords.length});
    },

    ramsRecentRevision: async (req, res) => {
        let ramsId = +req.param('id');
        let projectId = +req.param('projectId');
        let rams = await sails.models.projectrams_reader.findOne({
            select: ['briefing_title', 'group_id'],
            where: {
                id: ramsId,
                project_ref: projectId
            }
        });

        if (!rams || !rams.id) {
            sails.log.error(`No RAMS found with given id ${ramsId} on project ${projectId}.`);
            return errorResponse(res, `No RAMS found with given id ${ramsId} on project ${projectId}.`);
        }

        sails.log.info(`Fetching RAMS by groupId: ${rams.group_id}.`);
        let ramsRecords = await sails.models.projectrams_reader.find({
            select: ['id', 'custom_field'],
            where: {
                group_id: rams.group_id,
                project_ref: projectId,
                status: {'in': [0,2,3]},
                revision_status: false
            }
        }).sort([
            {id: 'DESC'}
        ]);
        sails.log.info(`Found ${ramsRecords.length} RAMS by groupId: ${rams.group_id}.`);

        let ramsRecentRevision = (ramsRecords.length) ? ramsRecords[0] : {};
        sails.log.info(`Found ${ramsRecords.length} revisions with briefings for rams revision ${ramsId}.`);
        return successResponse(res, {rams_recent_revision: ramsRecentRevision});
    },

    downloadDocumentPreview: async (req, res) => {
        let id = +req.param('id');
        let projectId = +req.param('projectId');
        let ramsAssessmentFormFields = req.body.assessment_form_fields;
        let ramsRecord = await sails.models.projectrams_reader.findOne({
            select: ['briefing_title', 'briefing_file_ref', 'createdAt', 'custom_field', 'reference_number', 'revision_number', 'status', 'user_ref', 'project_ref'],
            where: {id: id, project_ref:+projectId}
        });

        if (!ramsRecord) {
            return errorResponse(res, 'Request failed due to invalid data, Please try again.');
        }

        let project = await sails.models.project_reader.findOne({
            where: {id: +ramsRecord.project_ref},
            select: ['name', 'project_number', 'postcode', 'custom_field']
        });

        ramsRecord.custom_field = {assessment_form_fields: ramsAssessmentFormFields};
        let merged_file = await fillPdfForm(req, ramsRecord, project, true);
        if (!merged_file || !merged_file.file_url) {
            return sendResponse(res, merged_file);
        }

        sails.log.info(`Got preview file ${merged_file.file_url} for rams revision ${id}.`);
        return successResponse(res, {preview_file: merged_file});
    },
};
