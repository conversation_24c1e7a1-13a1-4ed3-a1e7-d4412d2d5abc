/*
 * @Author: sandeep.patel
 * @Date: 2018-09-17 18:40:47
 * @Last Modified by: sandeep.patel
 * @Last Modified time: 2018-09-19 20:30:25
 *
 *
 */
'use strict';

const displayDateFormat = 'DD-MM-YYYY';
const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const moment = require('moment');
const {
    CSCSService: {
        getCardsInfo,
    },
    EmailService,
    TokenUtil: {resourceIdentifier, ROLES,},
    RightToWorkService,
    ResponseService,
    SharedService,
    UserRevisionService,
} = require('./../services');
const {
    dbDateFormat_YYYY_MM_DD,
} = sails.config.constants;
const {
    UserDocumentValidator: {
        createUpdateUserDocumentRequest,
        getCardsInfoRequest,
    }
} = require('./../validators');
const {
    expandUserDocFiles,
    getUserFullName,
    getUserVerifiableUserDocuments,
    bulkValidateUserDocuments,
    getUpdatedOnboardStatus,
    populateDocumentChildren,
} = require('./../services/DataProcessingService');

const notifyUserOnUpload = async (user_document, fromUser, toUser, companyName) => {
    try{
        let subject = `New document/competency added`;
        let html = await sails.renderView('pages/mail/document-added', {
            title: subject,
            uploader_name: getUserFullName(fromUser),
            toUser: toUser,
            user_document,
            company_name: companyName,
            getExpiryDate: (expiry_date) => {
                return moment(+expiry_date).format(displayDateFormat);
            },
            layout: false
        });

        sails.log.info('Sending new document mail to', toUser.email);
        return await EmailService.sendMail(subject, [toUser.email], html);
    }catch(failure){
        sails.log.info('Failed to send mail', failure);
        return false;
    }
};

const _getUserDocumentsList = async (userId, linear, validOnly, doc_owner_id) => {
    let filter = {
        doc_owner_id: userId, // Changed filter. As `doc_owner_id` is the identification for document's owner.
        user_id: userId, // user_id is identification, who created this document.
        // parent_doc_ref: null, // get only parent docs
        is_deleted_manually: {'!=': 1}
    };
    let includeChildren = false;
    if (validOnly) {
        // call from induction screen, so no need send children
        filter.expiry_date = {'>': moment().valueOf()};
    } else if (!linear) {
        includeChildren = true;
        filter.parent_doc_ref = null; // get only parent docs
    }
    if (doc_owner_id) {
        filter.doc_owner_id = doc_owner_id;
        filter.user_id = undefined;
    }
    sails.log.info('get User Docs for filter', filter, ', linear:', linear);

    let user_documents = await sails.models.userdoc.find(filter).sort('id');
    if (includeChildren) {
        user_documents = await populateDocumentChildren(user_documents, filter);
    }
    user_documents = await (expandUserDocFiles(user_documents, (expandErr, expandedList) => expandedList || user_documents));
    sails.log.info('fetched successfully, total records', user_documents.length);
    return user_documents;
};

const getUserDocuments = async (req, res, userIdOverride) => {
    let userId = userIdOverride || req.user.id;
    let linear = ((req.query.linear || '-').toString().trim()) === 'true'; // for call from employer screen have 'true'
    let validOnly = (req.query.validOnly || '').toString().trim() === 'true';
    let doc_owner_id = (req.query.doc_owner_id || '').toString().trim();

    sails.log.info(`caller: ${req.user.id}, get User ${userId} Docs for filter linear:`, linear, `userIdOverride: ${userIdOverride} doc_owner_id: ${doc_owner_id}`);
    let user_documents = await _getUserDocumentsList(userId, linear, validOnly, doc_owner_id);
    return ResponseService.successResponse(res, {user_documents});
};

module.exports = {

    saveDocument: async (req, res) => {
        sails.log.info('Save Document Request has user id:', req.user.id);
        let {validationError, payload} = createUpdateUserDocumentRequest(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'Request failed due to invalid data, Please try again.', {validationError});
        }
        // Notify user about upload
        let notifyUser = ((req.query.notifyUser || '').toString().trim() === 'true');
        let user_document = _.pick(payload, [
            'name', 'description', 'doc_number', 'expiry_date', 'user_file_id', 'user_files', 'doc_owner_id', 'is_verified',
            'verification_info',
            'parent_doc_ref',
        ]);
        user_document.user_id = req.user.id;
        // Populate if not supplied
        if(!user_document.user_files && user_document.user_file_id){
            user_document.user_files = [user_document.user_file_id];
        }

        sails.log.info('Create user document with', user_document);

        user_document = await sails.models.userdoc.create(user_document);
        sails.log.info('created successful, id', user_document.id);
        if(notifyUser){
            sails.log.info('Notify user on document upload');
            let documentOwner = await sails.models.user.findOne({id: user_document.doc_owner_id || user_document.user_id});
            await notifyUserOnUpload(user_document, req.user, documentOwner, '');
        }

        if(req.user.user_onboard_status && !req.user.user_onboard_status.competencies){
            let user = await sails.models.user.updateOne({ id: req.user.id }).set({
                user_onboard_status: getUpdatedOnboardStatus(req.user, 'competencies')
            });
            UserRevisionService.createUserRevision(req.user.id, {personal: user}).catch(sails.log.error);
            req.user = user;
        }

        expandUserDocFiles([user_document], (expandErr, user_document) => {
            if (expandErr) {
                return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, expandErr);
            }
            user_document = user_document[0];
            return ResponseService.successResponse(res, {user_document});
        });

    },

    updateDocument: async (req, res) => {
        let user_doc_id = req.param('userDocId');
        sails.log.info('Update Document Request id:', user_doc_id);
        let {validationError, payload} = createUpdateUserDocumentRequest(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'Request failed due to invalid data, Please try again.', {validationError});
        }
        // Notify user about upload
        let notifyUser = ((req.query.notifyUser || '').toString().trim() === 'true');
        let user_document = _.pick(payload, [
            'name', 'description', 'doc_number', 'expiry_date', 'user_file_id', 'user_files', 'doc_owner_id', 'is_verified',
            'verification_info',
            'parent_doc_ref',
        ]);
        user_document.user_id = req.user.id;
        // resetting state of competency on user update.
        user_document.is_deleted_manually = 0;
        user_document.is_notified = 0;
        // Populate if not supplied
        if(!user_document.user_files && user_document.user_file_id){
            user_document.user_files = [user_document.user_file_id];
        }
        sails.log.info('Update user document with', user_document);
        let updated_doc = await sails.models.userdoc.updateOne({
            id: user_doc_id,
            or: [
                {user_id: req.user.id},
                {doc_owner_id: req.user.id}
            ]
        }).set(user_document);
        if(!updated_doc || !updated_doc.id){
            sails.log.info('Failed to update user_document')
            return ResponseService.errorResponse(res, 'Failed to update given record');
        }
        sails.log.info('update successful, id', updated_doc.id);
        if(notifyUser){
            sails.log.info('Notify user on document upload');
            let documentOwner = await sails.models.user.findOne({id: updated_doc.doc_owner_id || updated_doc.user_id});
            await notifyUserOnUpload(updated_doc, req.user, documentOwner, '');
        }

        let [first_doc] = await (expandUserDocFiles([updated_doc], (expandErr, expandedList) =>  expandedList || [updated_doc]));
        // let first_doc = expanded_docs[0];
        sails.log.info('Expanded document is', first_doc.id);
        return ResponseService.successResponse(res, {user_document: first_doc});
    },

    deleteDocument: async (req, res) => {
        let user_doc_id = req.body.user_doc_id;
        sails.log.info('Delete Document Request has id', user_doc_id);

        if (!_.has(req.body, 'user_doc_id')) {
            sails.log.info('Invalid Request')
            return res.ok({error: true, message: "document id is required."});
        }
        // no need to filter for parent docs, as we are fetching by ids only!
        let record = await UserDoc.findOne({
            id: user_doc_id,
            or: [
                {user_id: req.user.id},
                {doc_owner_id: req.user.id}
            ]
        });
        if (!record) {
            sails.log.info('Failed to find user_document')
            return ResponseService.errorResponse(res, 'Record not found');
        }
        sails.log.info('found record');
        /*if (record.user_file_id) {
            let deleted = await SharedService.deleteFileRecord(record.user_file_id, req.user.id);
            if (!deleted) {
                return ResponseService.errorResponse(res, 'Failed to delete file');
            }
        }*/

        try {
            sails.log.info('Deleting User Doc record', record.id);
            let data = {is_deleted: 1, is_deleted_manually: 1};
            let updated_doc = await sails.models.userdoc.updateOne({
                id: record.id,
            }).set(data);
            // mark child deleted as well
            await sails.models.userdoc.update({parent_doc_ref: record.id}).set(data);
            sails.log.info('update successful', updated_doc ? updated_doc.id : null);
        } catch (e) {
            sails.log.info('Failed to delete User Doc record', e);
            return ResponseService.errorResponse(res, 'Failed to delete record');
        }
        sails.log.info('Deleted User Doc record..');
        return ResponseService.successResponse(res, {message: "Deleted record successfully"});
    },

    getMyDocuments: async (req, res) => {
        return getUserDocuments(req, res);
    },

    getCompanyUserDocuments: async (req, res) => {
        let userId = +(req.param('userId', 0));
        return getUserDocuments(req, res, userId);
    },

    getAdditionalOwnedDocuments: async (req, res) => {
        try {
            let filter = {
                doc_owner_id: req.param('docOwnerId', 0),
                // user_id: req.user.id,
                parent_doc_ref: null, // get only parent docs
                is_deleted_manually : { '!=': 1 }
            };
            let validOnly = (req.query.validOnly || '').toString().trim();
            if(validOnly === 'true'){
                filter.expiry_date = { '>': moment().valueOf() };
            }
            sails.log.info('get User Additional Docs for filter', filter);

            let user_documents = await sails.models.userdoc.find(filter).sort('id');
            user_documents = await populateDocumentChildren(user_documents, filter);

            expandUserDocFiles(user_documents, (expandErr, user_documents) => {
                if (expandErr) {
                    return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, expandErr);
                }

                sails.log.info('fetched successfully, total records', user_documents.length);
                return ResponseService.successResponse(res, {user_documents});
            });
        } catch (getError) {
            sails.log.info('Failed to get additional user_documents', getError);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, getError);
        }
    },

    searchCitbAchievement: async(req, res) => {
        let payload = {};
        if (_.has(req.query, 'BirthDate')) {
            payload = _.pick((req.query || {}), [
                'UID', 'Surname', 'BirthDate', 'ULN',
            ]);
        }
        if (_.has(req.query, 'NINumber')) {
            payload = _.pick((req.query || {}), [
                'UID', 'Surname', 'NINumber', 'ULN',
            ]);
        }
        const CITB_API_SERVER = sails.config.custom.CITB_API_SERVER || '';
        if(!isNaN(+payload.UID)){
            payload.UID = +payload.UID;
        }
        sails.log.info('Search request has', payload);

        HttpService.makeGET(`${CITB_API_SERVER}/cardschemes`, payload, {
            'Ocp-Apim-Subscription-Key': (sails.config.custom.CITB_API_P_KEY || '')
        }, true, 20000).then(api_response => {
            sails.log.info('Response from n/w call', api_response.status);

            api_response.status = 200;
            HttpService.proxyHTTPResponse(res, api_response);
        });
    },

    getCardInfoFromCSCS: async (req, res) => {
        const companyId = +req.param('companyId', 0);
        const bulk_mode = (req.param('bulk_mode', 'false').toString()) === 'true';

        let {validationError, payload} = getCardsInfoRequest(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'Request failed due to invalid data, Please try again.', {validationError});
        }

        if(payload.competencies && payload.competencies.length > 15){
            ResponseService.errorResponse(res, 'Request payload is too large', payload);
        }
        sails.log.info(`looking card info for company: ${companyId} documentInfo:`, payload);
        let results = await getCardsInfo(companyId, payload.competencies, payload.surname);
        if(!bulk_mode){
            return ResponseService.sendResponse(res, results.pop());
        }
        return ResponseService.sendResponse(res, results);
    },

    autoVerifyUserDocuments: async (req, res) => {
        const companyId = +req.param('companyId', 0);
        const projectId = +req.param('project_id', 0);

        sails.log.info(`Auto verifying user: ${req.user.id} competencies using company: ${companyId} CSCS info for project: ${projectId}`);
        let user_docs = await _getUserDocumentsList(req.user.id, false, false, null);

        let company = await sails.models.createemployer_reader.findOne({where: {id: companyId}, select: ['id', 'name', 'country_code']});
        if(company && user_docs.length){
            // Only verifying Non-verified documents on user request
            let verifiable_user_documents = await getUserVerifiableUserDocuments(user_docs, req.user.id, company.country_code, true);
            if (verifiable_user_documents.length) {
                user_docs = await bulkValidateUserDocuments(user_docs, {id: req.user.id, last_name: req.user.last_name}, verifiable_user_documents, [companyId], true);
            }
        }
        return ResponseService.sendResponse(res, {user_documents: user_docs});
    },

    getPPACIdStatus: async (req, res) => {
        const companyId = +req.param('companyId', 0);
        const projectId = +req.param('project_ref', 0);
        const inductionId = +req.param('induction_id', 0);
        const update_code = req.param('update_code', 'false') === 'true';
        const payload = _.pick((req.body || {}), [
            'ppac_code',
            'surname',
            'dob',
            ]);

        if (projectId) {
            // if site-admin or owner of induction record.
            let resource = resourceIdentifier.PROJECT(projectId);
            let uac_row = (req.user.raw_uac || []).find(uac => uac.role === ROLES.SITE_ADMIN && uac.resource === resource);
            if (!uac_row) {
                sails.log.info(`[RTW] user is not site-admin of project: ${projectId}`);
                return ResponseService.sendResponse(res, ResponseService.authErrorObject(sails.__('user_does_not_have_permission')));
            }
        }

        const ir = await sails.models.inductionrequest_reader.findOne({
            where: {id: inductionId, project_ref: projectId},
            select: ['id', 'comments', 'rtw_doc_code', 'rtw_check_result'],
        });
        sails.log.info(`[RTW] validating ppac code for ir: ${ir?.id || ''} project: ${projectId}, code: "${payload.ppac_code}"`);
        if (!update_code && ir?.id && ir.rtw_doc_code !== `${payload.ppac_code}`) {
            sails.log.info(`[RTW] ppac code NOT matching for ir: ${ir?.id || ''} PPAC code: "${ir.rtw_doc_code}" => "${payload.ppac_code}"`);
            return ResponseService.errorResponse(res, sails.__('ppac_code_not_matching_with_induction'), {
                induction_data: ir,
            });
        }
        let outcome = {};
        let comment_message = ``;
        let ppac_code_changed = (ir && ir.rtw_doc_code !== `${payload.ppac_code}`);
        if(ppac_code_changed){
            sails.log.info(`[RTW] subscribing ppac code: ${payload.ppac_code}, status change`);
            const subscription_result = await RightToWorkService.subscribeToRtwStatusChangeForInduction(companyId, ir.id, payload).catch(ex => {
                sails.log.info(`[RTW] error while subscribing for status change of id: ${ir.id}, ppac_code: ${payload.ppac_code}`);
                sails.log.info(`[RTW] exception:`, ex);
                return {};
            });
            outcome.success = subscription_result.success;
            outcome.rtw_doc_code = payload.ppac_code;
            outcome.rtw_check_result = subscription_result.rtw_check_result || {};
            sails.log.info(`[RTW] updating ir: ${ir?.id || ''} PPAC code: "${ir.rtw_doc_code}" => "${payload.ppac_code}"`);
            comment_message = `PPAC Code changed from ${ir.rtw_doc_code || 'Not Found'} to ${payload.ppac_code}, with status ${outcome.rtw_check_result?.status || 'Not Found'}`
        }else {
            outcome = await RightToWorkService.getRtwStatusInfo(companyId, payload);
            if(outcome.success && ir?.id) {
                // accept induction record id and update comment, with new status change if any
                const previous_status = ir.rtw_check_result?.status || 'Not Found';
                const current_status = outcome.rtw_check_result?.status || 'Not Found';
                const status_has_changed = (previous_status !== current_status);

                sails.log.info(`[RTW] updating ir: ${ir?.id || ''} PPAC code: "${ir.rtw_doc_code}" => "${payload.ppac_code}" status: "${previous_status}" => "${current_status}" change:${status_has_changed}`);
                if(status_has_changed){
                    comment_message = `PPAC Code ${payload.ppac_code} status updated from ${previous_status} to ${current_status}`;
                }
            }
        }
        let ir_update = false;
        if(comment_message && ir?.id){
            const comments = ir.comments || [];
            comments.push({
                timestamp: moment().valueOf(),
                user_id: req.user.id,
                name: req.user.name,
                origin: "admin",
                module: "ppac-verification",
                note: comment_message,
            });
            const updated = await sails.models.inductionrequest.updateOne({
                id: ir.id
            }).set({
                comments,
                rtw_doc_code: outcome.rtw_doc_code,
                rtw_check_result: outcome.rtw_check_result,
            });
            ir_update = true;
        }
        return ResponseService.sendResponse(res, {
            ...outcome,
            ...(inductionId ? {ir_update} : {}),
        });
    },
};
