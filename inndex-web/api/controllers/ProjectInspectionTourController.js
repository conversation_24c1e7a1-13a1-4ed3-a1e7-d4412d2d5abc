const moment = require('moment');
const momentTz = require('moment-timezone');
const {
    HttpService: {makePOST},
    UserRevisionService: {getLatestUserRevision},
    TokenUtil: {
        getCompanyInfo
    },
    ResponseService: {
        errorResponse,
        successResponse
    },
    DataProcessingService: {
        attachProfilePicWithUserRefInfo,
        sendMailToNominatedManagerCPA,
        getUserFullName,
        getUserFirstName,
        getProjectTimezone,
        buildRecordRef,
        getInspectionTourMetaChecklist,
        getChecklistItemsReferences,
        getInspectionTours,
        sendInspectionMailToNomMngr,
        getTimezone,
        populateUserRefs,
        populateProjectRefs,
        shareReportViaEmail,
        capitalizeFirstLetter: capitalize,
    },
    ChartService: {
        getDonutChart,
        getOpenClosedBarChart,
        getStackedBarChart,
        getScatterPlot,
        getPercentageGauge<PERSON><PERSON>,
        getVerLoll<PERSON>op<PERSON><PERSON>,
        getHoriLollipop<PERSON>hart
    },
    ExcelService: {
        getInspectionParticipants,
        streamExcelDownload,
    },
    NotificationService: {
        NOTIFICATION_CATEGORY,
        sendNotification
    },
    SharedService: {
        instantPdfGenerator,
        downloadPdfViaGenerator,
        extractPlatformVersion,
    },
    ASiteService : { checkIfAsiteEnabled, getAsiteProjectToolMapping, uploadDocOnAsite }
} = require('./../services');
const {
    inductionFn: { getUserInductionEmployer },
} = require('../sql.fn');

const {
    DEFAULT_PAGE_SIZE,
    fall_back_timezone,
} = sails.config.constants;
const axios = require('axios');
const {
    InspectionTourValidator: { createOrUpdateITRecord, closeOutRequestValidate }
} = require('./../validators');
const dbDateFormat = 'DD-MM-YYYY';
const _uniq = require('lodash/uniq');
const goodRatingColor = "#1FA61B"; // green
const poorRatingColor = "#D60707"; // red
const fairRatingColor = "#EDB531"; // yellow

const colorRed = "#D60707";
const colorGreen = '#1FA61B';
const colorYellow = "#EDB531";

const getResponsibleUserInfo = (checklistItemsResponsibleUsers, user_id) => {
    sails.log.info(`Responsible User Id ${user_id}`);
    let userInfo = (checklistItemsResponsibleUsers || []).find(user => user.id == user_id);
    return (userInfo && userInfo.id) ? `${getUserFullName(userInfo)} (${userInfo.email})` : '';
};

const getTaggedCompanyInfo = (checklistItemsTaggedCompanies, company_id) => {
    sails.log.info(`Tagged Company Id ${company_id}`);
    let itemTaggedCompanies = checklistItemsTaggedCompanies.filter(company => company_id.includes(company.id));
    let taggedCompanyNames = (itemTaggedCompanies || []).map(company => company.name);
    return taggedCompanyNames.join(', ');
};

const getProjectsFromRecords = (inspectionTourRecords) => {
    let projects = [];
    (inspectionTourRecords || []).map(record => {
        let project = {'id': record.project_ref.id, 'name': record.project_ref.name};
        if (!projects.find(pro => pro.id === record.project_ref.id)) {
            projects.push(project);
        }
    });
    sails.log.info(`Found ${projects.length} projects in inspection tour records.`);

    return projects;
}

const cleanChecklist = (checklist) => {
    (checklist || []).map(item => {
        return cleanItem(item);
    });
    return checklist;
}

const cleanItem = (item) => {
    if (item.appendix) {
        item.appendix = (item.appendix || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
    }

    if (item.responsible_user_ref) {
        item.responsible_user_ref = (item.responsible_user_ref && item.responsible_user_ref.id) ? item.responsible_user_ref.id : item.responsible_user_ref;
    }

    if (item.tagged_company_ref) {
        item.tagged_company_ref = (item.tagged_company_ref || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
    }
    return item;
};

const prepareWeeksData = (fromEpoch, toEpoch) => {
    let a = moment(fromEpoch).startOf('isoWeek');
    let b = moment(toEpoch).endOf('isoWeek');

    let daysBetweenDates = b.diff(a, 'days');
    let startOfWeeksArr = [a.valueOf()];

    if (daysBetweenDates > 6) {
        for (let i = 0; i < (daysBetweenDates/6); i++) {
            let nextVal = moment(startOfWeeksArr[i]).add(7, 'days').valueOf();
            if (nextVal > b.valueOf()) {
                break;
            }
            startOfWeeksArr.push(nextVal);
        }
    }

    let data = [];
    for (let i in startOfWeeksArr) {
        let item = {
            'week': +i + 1,
            'unsatisfactory_items': 0,
            'total_items': 0,
            'start_of_week': startOfWeeksArr[i],
            'end_of_week': moment(startOfWeeksArr[i]).add(6, 'days').valueOf()
        };
        data.push(item);
    }

    return data;
};

const mathMinMax = (data, itemKey, type) => {
    if (type == 'max') {
        return Math.max.apply(Math, (data || []).map(item => { return item[itemKey] }));
    } else {
        return Math.min.apply(Math, (data || []).map(item => { return item[itemKey] }));
    }
};

const sleep = (ms) => {
    sails.log.info('Sleep for', ms, 'ms');
    return new Promise(resolve => setTimeout(resolve, ms));
};

const putImagesToClItems = (checklist, allItmImgs) => {
    (checklist || []).map(item => {
        if (item.appendix.length) {
            item.appendix = allItmImgs.filter(img => item.appendix.includes(img.id));
        }
        return item;
    });
    return checklist;
};

const fillParticipants = (inspectionTour, participants, totalItems, goodItems)  => {
    let percent = (totalItems && goodItems) ? parseInt(100/totalItems * goodItems) : 0;
    let inspection_rating = {percent, project_name: inspectionTour.project_ref.name};
    participants = participants.map(participant => {
        if ((inspectionTour.participants || []).includes(participant.id)) {
            participant.inspection_date.push(inspectionTour.createdAt);
            participant.inspections_participated.push(inspectionTour.id);
            participant.projects.push(inspectionTour.project_ref.id);
            participant.inspection_rating.push(inspection_rating);
        }
        return participant;
    });

    return participants;
}

const prepareViewOrDownloadInspectionTour = async(req, res, whereClause, type, responseType='pdf') => {
    let projectInspectionTour =  await sails.models.projectinspectiontour.findOne(whereClause)
        .populate('project_ref')
        .populate('user_ref')
        .populate('responsible_manager_user_ref')
        .populate('supervisor_user_ref');

    if (projectInspectionTour && projectInspectionTour.id) {
        // Finding user's employer
        if (projectInspectionTour.user_ref && projectInspectionTour.user_ref.id) {
            const induction_requests = await sails.models.inductionrequest_reader.find({
                where:{ user_ref: projectInspectionTour.user_ref.id },
                select: ['additional_data'],
                sort:[
                    {id: 'DESC'},
                ],
                limit: 1
            });
            const employer = ((induction_requests[0] && induction_requests[0].additional_data && induction_requests[0].additional_data.employment_detail && induction_requests[0].additional_data.employment_detail.employer) || '').toString().trim();
            projectInspectionTour.user_employer = employer
        }

        let participants = [];
        if (projectInspectionTour.participants && projectInspectionTour.participants.length) {
            participants = await sails.models.user.find({
                where: {id: projectInspectionTour.participants},
                select: ['first_name', 'middle_name', 'last_name']
            });
        }
        let totalPages = 2;
        let isIndustrialProject = (projectInspectionTour.project_ref.project_type == 'industrial');
        sails.log.info('got record, id', projectInspectionTour ? projectInspectionTour.id : undefined);
        let { project_logo_file, companyName } = await getCompanyInfo(projectInspectionTour.project_ref);

        let tz = getProjectTimezone(projectInspectionTour.project_ref);
        let metalist = await getInspectionTourMetaChecklist('inspection_tour_common_checklist');
        let inspectionTourChecklist = projectInspectionTour.common_checklist;
        //get closeout images id from common checklist
        let closeOutImagesId = [];
        let closeOutImageRows = 0;
        projectInspectionTour.common_checklist.map(item => {
            if (item.close_out && item.close_out.images && item.close_out.images.length) {
                closeOutImagesId.push(...item.close_out.images);
                closeOutImageRows += Math.ceil(item.close_out.images.length/3);
            }
        });

        //expand appendix images
        let appendixImageIds = projectInspectionTour.common_checklist.reduce((arr, item) => {
            if (Array.isArray(item.appendix)) {
                arr.push(...(item.appendix));
            }
            return arr;
        }, []);

        let railChecklistImages = [];
        let industrialChecklistImages = [];
        let additionalChecklistImages = [];
        if (projectInspectionTour.project_ref.project_type === 'rail') {
            let railChecklist = await getInspectionTourMetaChecklist('inspection_tour_rail_checklist');
            railChecklistImages = projectInspectionTour.rail_checklist.reduce((arr, item) => {
                if (Array.isArray(item.appendix)) {
                    arr.push(...(item.appendix));
                }
                return arr;
            }, []);


            metalist = [...metalist, ...railChecklist];
            inspectionTourChecklist = [...projectInspectionTour.common_checklist, ...projectInspectionTour.rail_checklist];

            //get closeout images id from rail checklist
            projectInspectionTour.rail_checklist.map(item => {
                if (item.close_out && item.close_out.images && item.close_out.images.length) {
                    closeOutImagesId.push(...item.close_out.images);
                    closeOutImageRows += Math.ceil(item.close_out.images.length/3);
                }
            });
            appendixImageIds = [...appendixImageIds, ...railChecklistImages];
        } else if (isIndustrialProject) {
            closeOutImagesId = [];
            closeOutImageRows = 0;
            let industrialChecklist = await getInspectionTourMetaChecklist('inspection_tour_industrial_checklist');
            industrialChecklistImages = projectInspectionTour.industrial_checklist.reduce((arr, item) => {
                if (Array.isArray(item.appendix)) {
                    arr.push(...(item.appendix));
                }
                return arr;
            }, []);


            metalist = [...industrialChecklist, ...projectInspectionTour.additional_checklist];
            inspectionTourChecklist = [...projectInspectionTour.industrial_checklist, ...projectInspectionTour.additional_checklist];

            //get closeout images id from industrialchecklist
            projectInspectionTour.industrial_checklist.map(item => {
                if (item.close_out && item.close_out.images && item.close_out.images.length) {
                    closeOutImagesId.push(...item.close_out.images);
                    closeOutImageRows += Math.ceil(item.close_out.images.length/3);
                }
            });

            projectInspectionTour.additional_checklist.map(item => {
                if (Array.isArray(item.appendix)) {
                    additionalChecklistImages.push(...(item.appendix));
                }

                if (item.close_out && item.close_out.images && item.close_out.images.length) {
                    closeOutImagesId.push(...item.close_out.images);
                    closeOutImageRows += Math.ceil(item.close_out.images.length/3);
                }
            });

            appendixImageIds = [...industrialChecklistImages, ...additionalChecklistImages];
        }

        //get observation images
        let observationImagesId = [];
        let observationImageRows = 0;
        (projectInspectionTour.observations || []).map(observation => {
            if (observation && observation.images && observation.images.length) {
                observationImagesId.push(...observation.images);
                observationImageRows += Math.ceil(observation.images.length/3);
            }
        });

        imageIds = [...closeOutImagesId, ...appendixImageIds, ...observationImagesId];
        let imagesObj = await sails.models.userfile_reader.find({
            where: {id: _uniq(imageIds)},
            select: ['id', 'sm_url', 'md_url', 'file_url', 'img_translation']
        });

        let appendixImages = imagesObj.filter(image => appendixImageIds.includes(image.id));
        appendixImages = (appendixImages || []).reduce((arr, imgObj) => {
            if (type === 'pdf') {
                arr[imgObj.id] = (imgObj.img_translation.length) ? imgObj.img_translation : (imgObj.sm_url || imgObj.md_url || imgObj.file_url);
            } else {
                arr[imgObj.id] = (imgObj.img_translation.length) ? imgObj.img_translation : imgObj.file_url;
            }
            return arr;
        }, {});

        sails.log.info(`appendix images count, ${Object.keys(appendixImageIds).length}`);

        //expanding observation images
        if (observationImagesId.length) {
            projectInspectionTour.observations.map(observation => {
                if (observation && observation.images && observation.images.length) {
                    observation.images = imagesObj.filter(image => observation.images.includes(image.id));
                }
                return observation
            });
        }

        //expanding closeout images
        if (closeOutImagesId.length) {
            projectInspectionTour.common_checklist.map(item => {
                if (item.close_out && item.close_out.images && item.close_out.images.length) {
                    item.close_out.images = imagesObj.filter(image => item.close_out.images.includes(image.id));
                }
                return item;
            });

            if (projectInspectionTour.project_ref.project_type === 'rail') {
                projectInspectionTour.rail_checklist.map(item => {
                    if (item.close_out && item.close_out.images && item.close_out.images.length) {
                        item.close_out.images = imagesObj.filter(image => item.close_out.images.includes(image.id));
                    }
                    return item;
                });
            } else if (isIndustrialProject) {
                projectInspectionTour.industrial_checklist.map(item => {
                    if (item.close_out && item.close_out.images && item.close_out.images.length) {
                        item.close_out.images = imagesObj.filter(image => item.close_out.images.includes(image.id));
                    }
                    return item;
                });

                projectInspectionTour.additional_checklist.map(item => {
                    if (item.close_out && item.close_out.images && item.close_out.images.length) {
                        item.close_out.images = imagesObj.filter(image => item.close_out.images.includes(image.id));
                    }
                    return item;
                });
            }
        }

        inspectionTourChecklistWithKey = inspectionTourChecklist.reduce((obj, ans) => {
            obj[ans.question_id] = ans;
            return obj;
        }, {});

        metaChecklist = metalist.reduce((obj, que) => {
            obj[que.question_id] = que;
            return obj;
        }, {});

        //Group meta items by category
        let metaChecklistCategoriesData = [];
        let metaChecklistCategories = [];
        let sequenceNum = 0;
        let sequenceNumArr = {};
        let checklistTableRowsCount = 0;
        for (let item of metalist) {
            if (item.category) {
                if (!metaChecklistCategoriesData[item.category]) {
                    metaChecklistCategoriesData[item.category] = [];
                    sequenceNumArr[item.category] = sequenceNum += 1;
                    checklistTableRowsCount += 1;
                }

                metaChecklist[item.question_id].display_number = `${sequenceNumArr[item.category]}.${metaChecklistCategoriesData[item.category].length+1}`;
                metaChecklistCategoriesData[item.category].push(item);
                checklistTableRowsCount += 1;
                if (!metaChecklistCategories.includes(item.category)) {
                    metaChecklistCategories.push(item.category);
                }
            }
        }

        //rating labels
        let goodRatingLabel = `Yes`;
        let fairRatingLabel = `Fair`;
        let poorRatingLabel = `No`;

        if (isIndustrialProject) {
            goodRatingLabel = `Good`;
            fairRatingLabel = `Fair`;
            poorRatingLabel = `Poor`;
        }

        let ratingsByGroupChartData = [];
        let ratingsByGroupChartColumns = ['group', goodRatingLabel, fairRatingLabel, poorRatingLabel];
        let totalRatingPerCategory = {};
        inspectionTourChecklist.map(item => {
            if ([goodRatingLabel.toLowerCase(),poorRatingLabel.toLowerCase(),'fair'].includes(item.answer) && metaChecklist[item.question_id]  && metaChecklist[item.question_id].category) {
                let category = metaChecklist[item.question_id].category;
                let dataItem = ratingsByGroupChartData.find(dItem => dItem.group === category) || {};
                if (Object.keys(dataItem).length) {
                    dataItem[capitalize(item.answer)] += 1;
                    totalRatingPerCategory[category] += 1;
                    let dataItemIndex = ratingsByGroupChartData.findIndex(dItem => dItem.group === category);
                    ratingsByGroupChartData[dataItemIndex] = dataItem;
                } else {
                    totalRatingPerCategory[category] = 0;
                    totalRatingPerCategory[category] += 1;
                    dataItem = {
                        'group': category
                    };
                    dataItem[goodRatingLabel] = 0;
                    dataItem[fairRatingLabel] = 0;
                    dataItem[poorRatingLabel] = 0;

                    dataItem[capitalize(item.answer)] += 1;
                    ratingsByGroupChartData.push(dataItem);
                }
            }

            item.display_number = metaChecklist[item.question_id].display_number;
            return item;
        });

        let maxRating = 0;
        if (Object.keys(totalRatingPerCategory).length) {
            let keyWithMaxRating = Object.keys(totalRatingPerCategory).reduce((a, b) => totalRatingPerCategory[a] > totalRatingPerCategory[b] ? a : b);
            maxRating = totalRatingPerCategory[keyWithMaxRating];
        }

        inspectionTourChecklist.sort((a, b) => parseFloat(a.display_number) - parseFloat(b.display_number));

        let observationItems = 0;
        if (projectInspectionTour.observations.length) {
            projectInspectionTour.observations.map(observation => {
                if (observation.images && observation.images.length) {
                    observationItems += 15 * Math.ceil(observation.images.length/3);
                }
                observationItems += 1;
            })
        }

        let {checklistItemsResponsibleUsers, checklistItemsTaggedCompanies} = await getChecklistItemsReferences(projectInspectionTour.project_ref, projectInspectionTour);

        //42 observation items per page(15 items === 1 image row)
        totalPages += Math.ceil(observationItems / 42);

        sails.log.info(`observationItems Pages: ${Math.ceil(observationItems / 42)}`);

        sails.log.info('generating inspection tour form content.');

        //considering two columns here
        let checklistTableMaxRowsParColumn = Math.ceil((checklistTableRowsCount + 2)/2);

        sails.log.info(`Checklist row per column: ${checklistTableMaxRowsParColumn}`, `& Total checklist row:  ${checklistTableRowsCount}`);
        let itemsInfo = [];
        let pageNumber = 0;
        let itemInfo = {};
        let goodRatingItemsCount = 0;
        let poorRatingItemsCount = 0;
        let fairRatingItemsCount = 0;
        let totalClosedOutItems = 0;
        let totalFairToCloseOut = 0;
        let totalPoorToCloseOut = 0;
        inspectionTourChecklist.forEach(function(item, i) {
            goodRatingItemsCount += (item.answer === 'good' || item.answer === 'yes') ? 1 : 0;
            poorRatingItemsCount += (item.answer === 'poor' || item.answer === 'no') ? 1 : 0;
            fairRatingItemsCount += (item.answer === 'fair') ? 1 : 0;
            itemInfo = {};

            if ((item.answer === 'no' || item.answer === 'poor' || item.answer === 'fair') && item.close_out && Object.keys(item.close_out).length) {
                itemInfo.item_closeout_reviewd_by = (item.close_out.reviewed_by) ? item.close_out.reviewed_by : 'N/A';
                let closeoutAt = +item.close_out.close_out_at;
                itemInfo.item_closeout_at = closeoutAt ? (tz ? momentTz(closeoutAt).tz(tz).format('DD-MM-YYYY HH:mm:ss') : moment(closeoutAt).format('DD-MM-YYYY HH:mm:ss')) : 'N/A';
                itemInfo.item_closeout_detail = item.close_out.details;

                if (item.close_out.images && item.close_out.images.length) {
                    itemInfo.item_closeout_images = [];
                    item.close_out.images.forEach(function(image, i) {
                        if (image.img_translation && image.img_translation.length) {
                            itemInfo.item_closeout_images.push(...image.img_translation);
                        } else if (image.file_url) {
                            if(type == 'pdf') {
                                itemInfo.item_closeout_images.push((image.sm_url || image.md_url || image.file_url));
                            } else {
                                itemInfo.item_closeout_images.push(image.file_url);
                            }
                        }
                    });
                }
                totalClosedOutItems += (item.answer === 'no' || item.answer === 'poor' || item.answer === 'fair') ? 1 : 0;
            } else {
                totalFairToCloseOut += (item.answer === 'fair') ? 1 : 0;
                totalPoorToCloseOut += (item.answer === 'no' || item.answer === 'poor') ? 1 : 0;
            }

            if (item.summary || item.corrective_action_required || (item.appendix && item.appendix.length) || (item.close_out && Object.keys(item.close_out).length) || item.responsible_user_ref || (item.tagged_company_ref&&item.tagged_company_ref.length)||(item.location_tag && Object.keys(item.location_tag).length)) {
                itemInfo.item_title = `Item: ${metaChecklist[item.question_id].display_number} – ${metaChecklist[item.question_id].question}`;
                itemInfo.item_answer = item.answer;
                itemInfo.item_summary = (item.summary) ? item.summary : 'N/A';
                itemInfo.item_action = (item.corrective_action_required) ? item.corrective_action_required : 'N/A';

                if (item.responsible_user_ref) {
                    itemInfo.item_assigned_to = getResponsibleUserInfo(checklistItemsResponsibleUsers, item.responsible_user_ref);
                }

                if (item.tagged_company_ref&&item.tagged_company_ref.length) {
                    itemInfo.item_tagged_company = getTaggedCompanyInfo(checklistItemsTaggedCompanies, item.tagged_company_ref);
                }

                if (item.closeout_due_on) {
                    itemInfo.item_closeout_due_on = tz ? momentTz(item.closeout_due_on).tz(tz).format('DD-MM-YYYY') : moment(item.closeout_due_on).format('DD-MM-YYYY');
                }

                if (item.appendix && item.appendix.length) {
                    itemInfo.item_images = [];
                    item.appendix.forEach(function(imgId, i) {
                        if (Array.isArray(appendixImages[imgId])) {
                            itemInfo.item_images.push(...appendixImages[imgId]);
                        } else if (appendixImages[imgId]) {
                            itemInfo.item_images.push(appendixImages[imgId]);
                        }
                    });
                }
                if (item.location_tag && Object.keys(item.location_tag).length) {
                    itemInfo.location = item.location_tag;
                }
                itemsInfo.push(itemInfo);
            }
        });

        sails.log.info(`Total ClosedOut Items: ${totalClosedOutItems}`, `& Total Fair Items to Closeout: ${totalFairToCloseOut}`, `& Total Poor/No Items to Closeout: ${totalPoorToCloseOut}`)

        let totalRatingCount = +(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
        let goodRatingPercentage = parseInt(100/(totalRatingCount)*(goodRatingItemsCount));
        let fairRatingPercentage = parseInt(100/(totalRatingCount)*(fairRatingItemsCount));
        let poorRatingPercentage = parseInt(100/(totalRatingCount)*(poorRatingItemsCount));

        let donutChartData = [
            {name: `${goodRatingLabel} (${goodRatingItemsCount})`, count: goodRatingItemsCount, percentage: goodRatingPercentage, color: `${goodRatingColor}`, label: goodRatingLabel},
            {name: `${fairRatingLabel} (${fairRatingItemsCount})`, count: fairRatingItemsCount, percentage: fairRatingPercentage, color: `${fairRatingColor}`, label: fairRatingLabel},
            {name: `${poorRatingLabel} (${poorRatingItemsCount})`, count: poorRatingItemsCount, percentage: poorRatingPercentage, color: `${poorRatingColor}`, label: poorRatingLabel}
        ];
        donutChartData = donutChartData.filter(item => item.label != undefined); //filter undefined

        let openClosedBarChartData = [];
        if(isIndustrialProject) {
            openClosedBarChartData.push({name: `Open (${poorRatingLabel})`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`});
        } else {
            openClosedBarChartData.push({name: `Open`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`});
        }
        openClosedBarChartData.push({name: `Open (${fairRatingLabel})`, value: totalFairToCloseOut, type: `Rating: ${fairRatingLabel}`});
        openClosedBarChartData.push({name: "Closed", value: totalClosedOutItems, type: ''});

        let openClosedBarChartLegends = [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}];
        if (!isIndustrialProject) {
            openClosedBarChartLegends = [{ name: poorRatingLabel, color: `${colorRed}`}];
        }

        let hasAllClosed = (!totalPoorToCloseOut && !totalFairToCloseOut && totalClosedOutItems) ? `All ${totalClosedOutItems} items closed out` : '';

        let responsibleManager = '';
        let supervisor = '';
        if (projectInspectionTour.responsible_manager_user_ref) {
            responsibleManager = getUserFullName(projectInspectionTour.responsible_manager_user_ref);
        } else if (projectInspectionTour.custom_field.responsible_manager && projectInspectionTour.custom_field.responsible_manager.name) {
            responsibleManager = `${projectInspectionTour.custom_field.responsible_manager.name}(${projectInspectionTour.custom_field.responsible_manager.job_role})`
        }

        if (projectInspectionTour.supervisor_user_ref) {
            supervisor = getUserFullName(projectInspectionTour.supervisor_user_ref);
        } else if (projectInspectionTour.custom_field.supervisor && projectInspectionTour.custom_field.supervisor.name) {
            supervisor = `${projectInspectionTour.custom_field.supervisor.name}(${projectInspectionTour.custom_field.supervisor.job_role})`
        }

        let detailsSectionInnerHeight = (responsibleManager || supervisor) ? 88 : 44;
        if (((projectInspectionTour.inspection_summary || '').split("<br>")).length > 2) {
            detailsSectionInnerHeight += ((projectInspectionTour.inspection_summary || '').split("<br>")).length * 22;
        } else {
            detailsSectionInnerHeight += 44;
        }

        sails.log.info(`chart height is ${detailsSectionInnerHeight}`);

        let donutChartWidth = 522;
        let donutChartHeight = 250;
        let openClosedBarChartWidth = 492;
        let openClosedBarChartHeight = 16;
        let openClosedBarChartFont = '.7em';
        let openChartMarginTop = '0px';
        let adjustments = {
            adj1: -2,
            adj2: 16,
            adj3: -23.5,
            adj4: 7.5,
            adj5: -1.5,
            adj6: 3.5,
            adj7: 25.5,
            adj8: (hasAllClosed) ? 3.2 : 4.7,
            adj9: 28,
            adj10: -1,
            adj11: 1,
            adj12: 1,
            adj13: 3,
        };

        let ratingsByGroupChartWidth = 981;
        let ratingsByGroupChartHeight = 200;
        let donutChartPercentageTxtAdj = -10;
        if(type == 'pdf') {
            donutChartWidth = 374;
            donutChartHeight = 219;
            openClosedBarChartWidth = 360;
            openClosedBarChartHeight = 19;
            openChartMarginTop = '0px';
            adjustments = {
                adj1: -2,
                adj2: 20,
                adj3: -27,
                adj4: 8.5,
                adj5: -1.5,
                adj6: 4.5,
                adj7: 26,
                adj8: 2,
                adj9: 37,
                adj10: 4,
                adj11: 2,
                adj12: 2,
                adj13: 3,
            }
            openClosedBarChartFont = '.8em';
            ratingsByGroupChartWidth = 680;
            ratingsByGroupChartHeight = 200;
            donutChartPercentageTxtAdj = -5;
        }

        //removing data with 0 values
        for (let category in totalRatingPerCategory) {
            let totalCount = totalRatingPerCategory[category];
            if (!totalCount) {
                let itemIndex = ratingsByGroupChartData.findIndex(item => item.group == category);
                ratingsByGroupChartData.splice(itemIndex, 1);
            }
        }

        totalPages += pageNumber + 1;
        let project = projectInspectionTour.project_ref;
        let checklistTitle = (projectInspectionTour.report_title) ? projectInspectionTour.report_title : `Inspection Tour`;
        let report_datetime = Number((projectInspectionTour.report_datetime) ? projectInspectionTour.report_datetime : projectInspectionTour.createdAt);
        let form_template = `pages/inspection-tour-form-page`;
        let html = await sails.renderView(form_template, {
            type,
            title: checklistTitle,
            report_datetime,
            project,
            companyName,
            projectInspectionTour,
            metaChecklistCategories,
            metaChecklistCategoriesData,
            metaChecklist,
            inspectionTourChecklist,
            inspectionTourChecklistWithKey,
            isIndustrialProject,
            itemsInfo,
            totalPages,
            responsibleManager,
            supervisor,
            totalInspectedItems: (goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount),
            totalUnsatisfactoryItems: (fairRatingItemsCount + poorRatingItemsCount),
            participants,
            project_logo_file,
            checklistTableMaxRowsParColumn,
            checklistTableRowsCount,
            layout: false,
            donutChart: await getDonutChart(donutChartData, donutChartWidth, donutChartHeight, donutChartPercentageTxtAdj, 100, 60, "30 -13 260 270", `${goodRatingLabel} (${goodRatingItemsCount})`, `${fairRatingLabel} (${fairRatingItemsCount})`, `${poorRatingLabel} (${poorRatingItemsCount})`, true, isIndustrialProject, 0, 0),
            openClosedBarChart: await getOpenClosedBarChart(openClosedBarChartData, hasAllClosed, openClosedBarChartWidth, openClosedBarChartHeight, openClosedBarChartFont, openChartMarginTop, adjustments, [`${colorRed}`, `${colorYellow}`, "#2c961b"], openClosedBarChartLegends),
            ratingsByGroupChart: await getStackedBarChart(ratingsByGroupChartData, ratingsByGroupChartColumns, maxRating, ratingsByGroupChartWidth, ratingsByGroupChartHeight, [`${colorGreen}`, `${colorYellow}`, `${colorRed}`], '6.5px', '', isIndustrialProject, false, 25),
            momentTz(n, format) {
                return tz ? momentTz(+n).tz(tz).format(format) : moment(+n).format(format);
            },
            capitalizeFirstLetter(string) {
                return capitalize(string);
            },
            toFixed(number) {
                return (+number) ? (+number).toFixed(4) : 0;
            }
        });

        if (type === 'pdf') {
            sails.log.info('Generating pdf');
            let file_name = `${checklistTitle}-#${projectInspectionTour.project_ref.id}-${projectInspectionTour.record_id}-${moment().format(dbDateFormat)}-${projectInspectionTour.project_ref.name}`;
            let project_line = `${(project.project_number != null) ? project.project_number + ' - ' + project.name : project.name} (#${project.id}): ${project.contractor}`;
            let date_line = `Report Date: ${momentTz(+report_datetime).tz(tz).format('DD-MM-YY HH:mm:ss')}`;
            return await downloadPdfViaGenerator({
                req,
                res,
                html,
                tool: 'inspection-tour',
                file_name,
                heading_line: checklistTitle,
                project_line,
                date_line,
                logo_file: project_logo_file,
                has_cover: true,
                responseType
            });
        }
        return html;
    }

    sails.log.info('Failed to find Inspection Tour');
    return errorResponse(res, sails.__('internal server error'));
}

const sendCreatedNotification = async(projectInspectionTour) => {
    let projectInfo = await sails.models.project.findOne({
        where: {id: projectInspectionTour.project_ref},
        select: ['name', 'project_category', 'parent_company', 'project_type', 'custom_field']
    });

    let isIndustrialProject = (projectInfo.project_type === 'industrial');
    let tz = getProjectTimezone(projectInfo);

    let responsibleUserIds = [projectInspectionTour.user_ref];
    let taggedCompanyIds = [];
    let metaChecklist = [];
    let inspectionTourChecklist = [];
    let itemAppendixImageIds = [];
    if (isIndustrialProject) {
        let industrialResUserIds = (projectInspectionTour.industrial_checklist || []).reduce((userIds, item) => {
            if (item.responsible_user_ref && !isNaN(item.responsible_user_ref)) {
                userIds.push(item.responsible_user_ref);
            }
            itemAppendixImageIds.push(...item.appendix);

            if (item.tagged_company_ref && item.tagged_company_ref.length) {
                taggedCompanyIds.push(...item.tagged_company_ref);
            }
            return userIds;
        }, []);
        responsibleUserIds.push(...industrialResUserIds);
        let industrialChecklist = await getInspectionTourMetaChecklist('inspection_tour_industrial_checklist');

        let additionalResUserIds = (projectInspectionTour.additional_checklist || []).reduce((userIds, item) => {
            if (item.responsible_user_ref && !isNaN(item.responsible_user_ref)) {
                userIds.push(item.responsible_user_ref);
            }
            itemAppendixImageIds.push(...item.appendix);

            if (item.tagged_company_ref && item.tagged_company_ref.length) {
                taggedCompanyIds.push(...item.tagged_company_ref);
            }
            return userIds;
        }, []);
        responsibleUserIds.push(...additionalResUserIds);
        metaChecklist = [...industrialChecklist, ...projectInspectionTour.additional_checklist];
        inspectionTourChecklist = [...projectInspectionTour.industrial_checklist, ...projectInspectionTour.additional_checklist];
    } else {
        let commonResUserIds = (projectInspectionTour.common_checklist || []).reduce((userIds, item) => {
            if (item.responsible_user_ref && !isNaN(item.responsible_user_ref)) {
                userIds.push(item.responsible_user_ref);
            }
            itemAppendixImageIds.push(...item.appendix);

            if (item.tagged_company_ref && item.tagged_company_ref.length) {
                taggedCompanyIds.push(...item.tagged_company_ref);
            }
            return userIds;
        }, []);
        responsibleUserIds.push(...commonResUserIds);
        let commonChecklist = await getInspectionTourMetaChecklist('inspection_tour_common_checklist');
        metaChecklist = [...commonChecklist];
        inspectionTourChecklist = [...projectInspectionTour.common_checklist];

        if (projectInfo.project_type === 'rail') {
            let railResUserIds = (projectInspectionTour.rail_checklist || []).reduce((userIds, item) => {
                if (item.responsible_user_ref && !isNaN(item.responsible_user_ref)) {
                    userIds.push(item.responsible_user_ref);
                }
                itemAppendixImageIds.push(...item.appendix);

                if (item.tagged_company_ref && item.tagged_company_ref) {
                    taggedCompanyIds.push(...item.tagged_company_ref);
                }
                return userIds;
            }, []);
            responsibleUserIds.push(...railResUserIds);
            let railChecklist = await getInspectionTourMetaChecklist('inspection_tour_rail_checklist');
            metaChecklist.push(...railChecklist);
            inspectionTourChecklist.push(...projectInspectionTour.rail_checklist);
        }
    }

    inspectionTourChecklist = inspectionTourChecklist.reduce((obj, ans) => {
        obj[ans.question_id] = ans;
        return obj;
    }, {});

    metaChecklist = metaChecklist.reduce((obj, que) => {
        obj[que.question_id] = que;
        return obj;
    }, {});

    let usersInfo = await sails.models.user.find({
        where: {id: _uniq(responsibleUserIds)},
        select: ['first_name', 'middle_name', 'last_name', 'email', 'timezone']
    });

    sails.log.info('Tagged Company Ids of Items, ', taggedCompanyIds);
    let taggedCompaniesInfo = await sails.models.createemployer.find({
        where: {id: _uniq(taggedCompanyIds)},
        select: ['name']
    });

    let itemAppendixImages = await sails.models.userfile.find({
        where: {id: _uniq(itemAppendixImageIds)},
        select: ['id', 'file_url', 'sm_url']
    });

    let midRatedItemsCount = 0;
    let lowRatedItemsCount = 0;
    let highRatedItemsCount = 0;
    let submittedByUserInfo = usersInfo.find(user => user.id == projectInspectionTour.user_ref);
    let submittedByUserName = getUserFullName(submittedByUserInfo, true);
    let inspectionNumber = buildRecordRef(projectInspectionTour);
    let inspectionDateTime = tz ? momentTz(+projectInspectionTour.createdAt).tz(tz).format('DD-MM-YY HH:mm:ss') : moment(projectInspectionTour.createdAt).format('DD-MM-YY HH:mm:ss');
    for ( let i in metaChecklist) {
        let metaItem = metaChecklist[i];

        let rating_color = '#000';
        if (inspectionTourChecklist[metaItem.question_id] && ['good', 'yes'].includes(inspectionTourChecklist[metaItem.question_id].answer)) {
            rating_color = goodRatingColor;
            highRatedItemsCount += 1;
        } else if (inspectionTourChecklist[metaItem.question_id] &&  ['poor', 'no'].includes(inspectionTourChecklist[metaItem.question_id].answer)) {
            rating_color = poorRatingColor;
            lowRatedItemsCount += 1;
        } else if (inspectionTourChecklist[metaItem.question_id] && inspectionTourChecklist[metaItem.question_id].answer === 'fair') {
            rating_color = fairRatingColor;
            midRatedItemsCount += 1;
        }

        if (inspectionTourChecklist[metaItem.question_id] && inspectionTourChecklist[metaItem.question_id].responsible_user_ref) {
            let imagesSrc = [];
            if(inspectionTourChecklist[metaItem.question_id].appendix && inspectionTourChecklist[metaItem.question_id].appendix.length) {
                (itemAppendixImages || []).map(imageObj => {
                    if (imageObj.id && inspectionTourChecklist[metaItem.question_id].appendix.includes(imageObj.id)) {
                        imagesSrc.push((imageObj.sm_url || imageObj.file_url));
                    }
                })
            }

            let responsibleUserInfo = usersInfo.find(user => user.id ==  inspectionTourChecklist[metaItem.question_id].responsible_user_ref);
            let taggedCompanyNames = getTaggedCompanyInfo(taggedCompaniesInfo, inspectionTourChecklist[metaItem.question_id].tagged_company_ref);

            if (responsibleUserInfo) {
                sails.log.info(`Send email to ${responsibleUserInfo.email} for item ${metaItem.category} - ${metaItem.question}`);
                let responsibleUserFname = responsibleUserInfo.first_name;
                let subject = `Inspection: ${projectInfo.name}`;
                let item_title = `${metaItem.category} - ${metaItem.question}`;
                let closeoutDue = inspectionTourChecklist[metaItem.question_id].closeout_due_on;
                let html = await sails.renderView('pages/mail/mail-content', {
                    mail_body: 'inspection-tour-item-notify',
                    title: subject,
                    responsible_user_fname: responsibleUserFname,
                    project_name: projectInfo.name,
                    inspection_number: inspectionNumber,
                    inspection_date_time: inspectionDateTime,
                    inspection_carried_out_by: submittedByUserName,
                    rating_label: (isIndustrialProject) ? 'Rating' : 'Satisfactory',
                    item_title,
                    item_responsible_company: taggedCompanyNames,
                    closeout_due_date: (closeoutDue) ? (tz ? momentTz(closeoutDue).tz(tz).format('DD-MM-YYYY') : moment(closeoutDue).format('DD-MM-YYYY')) : '',
                    rating: (inspectionTourChecklist[metaItem.question_id].answer) ? inspectionTourChecklist[metaItem.question_id].answer.toUpperCase() : 'N/A',
                    rating_color,
                    summary: (inspectionTourChecklist[metaItem.question_id].summary) ? inspectionTourChecklist[metaItem.question_id].summary : 'N/A',
                    action: (inspectionTourChecklist[metaItem.question_id].corrective_action_required) ? inspectionTourChecklist[metaItem.question_id].corrective_action_required : 'N/A',
                    appendix_images: imagesSrc,
                    time_to_closeout: '',
                    root_cause: '',
                    layout: false
                });
                await EmailService.sendMail(subject, [responsibleUserInfo.email], html);
                let message = `${submittedByUserName} has tagged you in ${item_title} item on ${projectInfo.name}`;
                await sendPushNotification({
                    message,
                    messageTitle: 'Tagged in inspection tour Item',
                    record_id: projectInspectionTour.id,
                    item_info: inspectionTourChecklist[metaItem.question_id],
                    category: NOTIFICATION_CATEGORY.INSPECTION_TOUR,
                    projectInfo,
                    recipientUserInfo: responsibleUserInfo,
                    submittedByUserInfo});
            }
        }
    }

    let unsatisfactoryItems = (midRatedItemsCount + lowRatedItemsCount);
    let mailData = {
        title: 'Inspection Tour',
        report_num: inspectionNumber,
        total_items: (unsatisfactoryItems + highRatedItemsCount),
        unsatisfactory_items: unsatisfactoryItems
    };
    //Send inspection mail to Project's Nominated Manager.
    await sendInspectionMailToNomMngr(projectInfo, submittedByUserInfo, 'Inspection Tour Submitted', mailData);

    //send mail to company project admins(Nominated Managers)
    if (projectInfo.project_category === 'company-project' && projectInfo.parent_company) {
        await sendMailToNominatedManagerCPA(
            projectInfo.parent_company,
            projectInfo,
            submittedByUserInfo,
            'Inspection Tour Submitted',
            '',
            false,
            'inspection-mail-to-company-nm',
            mailData
        );
    }
}

const sendPushNotification = async({message, messageTitle, record_id, item_info, category, projectInfo, recipientUserInfo, submittedByUserInfo}) => {
    let profile_pic = null;
    if(submittedByUserInfo.profile_pic_ref) {
        if (submittedByUserInfo.profile_pic_ref && (typeof submittedByUserInfo.profile_pic_ref === "number")) {
            submittedByUserInfo.profile_pic_ref = await sails.models.userfile.findOne({
                where: {id: submittedByUserInfo.profile_pic_ref},
                select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation', 'file_mime']
            });
        }
        profile_pic = submittedByUserInfo.profile_pic_ref.file_url;
    }

    let notificationData = {
        user_ref: recipientUserInfo.id,
        category,
        message,
        title: messageTitle,
        data: {
            category: category,
            profile_pic: profile_pic,
            project_ref: projectInfo.id,
            record_id: record_id,
            item_info: JSON.stringify(item_info)
        },
    };
    let notification = await sails.models.usernotifications.create(notificationData);
    let deviceInfo = await sails.models.registereddevices.find({user_ref: recipientUserInfo.id});
    sails.log.info(`Found ${deviceInfo.length} devices for user: #${recipientUserInfo.id}`);
    let messageData = {};
    for(const device of deviceInfo) {
        messageData = {
            notification: {
                title: messageTitle,
                body: message
            },
            android: {
                notification: {
                    icon: 'ic_icon_inndex',
                    color: '#14152D'
                }
            },
            data: {
                category: category,
                project_ref: projectInfo.id.toString(),
                notification_ref: notification.id.toString(),
                profile_pic: profile_pic || '',
                record_id: record_id.toString(),
                item_info: JSON.stringify(item_info)
            },
            token: device.token
        };
        await sendNotification(messageData);
    }
};

const createITRecord = async(req) =>{

    let createRequest = _.pick((req.body || {}), [
        'project_ref',
        'location',
        'type_of_work',
        'responsible_manager_user_ref',
        'supervisor_user_ref',
        'common_checklist',
        'rail_checklist',
        'industrial_checklist',
        'additional_checklist',
        'observations',
        'inspection_summary',
        'report_title',
        'report_datetime',
        'custom_field',
        'participants',
        'finalised',
        'rating_percentage',
        'sign',
    ]);

    createRequest.user_ref = req.user.id;

    let revision = await getLatestUserRevision(createRequest.user_ref);
    createRequest.user_revision_ref = revision.id;

    sails.log.info('Adding inspection tour.');

    return await sails.models.projectinspectiontour.create(createRequest);
}

const updateITRecord = async(req, id, updateRequest) =>{
    if (req.body.project_ref.project_type === 'industrial') {
        updateRequest.industrial_checklist = cleanChecklist(updateRequest.industrial_checklist);
        updateRequest.additional_checklist = cleanChecklist(updateRequest.additional_checklist);
    } else {
        updateRequest.common_checklist = cleanChecklist(updateRequest.common_checklist);

        if (req.body.project_ref.project_type === 'rail') {
            updateRequest.rail_checklist = cleanChecklist(updateRequest.rail_checklist);
        }
    }

    sails.log.info('Updating inspection tour.');

    return await sails.models.projectinspectiontour.updateOne({id: id}).set(updateRequest);
}

const getITRecord = async(where) =>{
    let projectInspectionTour =  await sails.models.projectinspectiontour.findOne(where)
        .populate('project_ref')
        .populate('user_ref')
        .populate('responsible_manager_user_ref')
        .populate('supervisor_user_ref');
    sails.log.info('got inspection tour record, id', projectInspectionTour ? projectInspectionTour.id : undefined);

    let commonChecklist = await getInspectionTourMetaChecklist('inspection_tour_common_checklist');
    let railChecklist = await getInspectionTourMetaChecklist('inspection_tour_rail_checklist');
    let industrialChecklist = await getInspectionTourMetaChecklist('inspection_tour_industrial_checklist');

    return {projectInspectionTour, commonChecklist, railChecklist, industrialChecklist};
}

const prepareRawItems = (inspectionTour, inspectionTourChecklist, metaChecklist, selected_responsible_companies, companiesWithCompanyPortal) => {
    let rawItems = [];
    let companiesId = [];
    (inspectionTourChecklist || []).forEach(function (item, j) {
        if(item.tagged_company_ref && item.tagged_company_ref.length) {
            let itemCategory = metaChecklist[item.question_id].category;
            (item.tagged_company_ref || []).map(id => {
                let isCompanyHasPortal = (companiesWithCompanyPortal || []).find(company => company.id == id);
                //skip item if tagged company own company portal
                if (!isCompanyHasPortal) {
                    companiesId.push(id);
                    if(!selected_responsible_companies.length || selected_responsible_companies.includes(id)) {
                        rawItems.push({
                            ...item,
                            it_id: inspectionTour.id,
                            it_createdAt: inspectionTour.createdAt,
                            project_id: inspectionTour.project_ref.id,
                            itemCategory,
                            tagged_company_ref: id
                        });
                    }
                }
            })
        }
    });

    return { rawItems, companiesId };
}

const prepareRawItemsByTaggedOwner = async (inspectionTour, inspectionTourChecklist, metaChecklist, selectedTaggedCompany, isIndustrialProject, ratingFilter) => {
    //rating labels
    let goodRatingLabel = (ratingFilter.yes) ? `Yes` : undefined;
    let fairRatingLabel = undefined;
    let poorRatingLabel = (ratingFilter.no) ? `No` : undefined;

    if(isIndustrialProject) {
        goodRatingLabel = (ratingFilter.good) ? `Good` : undefined;
        fairRatingLabel = (ratingFilter.fair) ? `Fair` : undefined;
        poorRatingLabel = (ratingFilter.poor) ? `Poor` : undefined;
    }

    let goodRatingLabelLC = (goodRatingLabel) ? goodRatingLabel.toLowerCase() : goodRatingLabel;
    let fairRatingLabelLC = (fairRatingLabel) ? fairRatingLabel.toLowerCase() : fairRatingLabel;
    let poorRatingLabelLC = (poorRatingLabel) ? poorRatingLabel.toLowerCase() : poorRatingLabel;

    let rawItems = [];
    (inspectionTourChecklist || []).forEach(function (item, j) {
        let itemAnswerLC = (item.answer || '').toLowerCase();
        if([goodRatingLabelLC, fairRatingLabelLC, poorRatingLabelLC].includes(itemAnswerLC) && item.tagged_company_ref && item.tagged_company_ref.length && (item.tagged_company_ref || []).includes(selectedTaggedCompany)) {
            rawItems.push({
                ...item,
                question: metaChecklist[item.question_id].question,
                it_id: inspectionTour.id,
                it_createdAt: inspectionTour.createdAt,
                it_user_ref: inspectionTour.user_ref,
                project_id: inspectionTour.project_ref.id,
                project_name: inspectionTour.project_ref.name,
                item_category: metaChecklist[item.question_id].category,
                display_number: metaChecklist[item.question_id].display_number,
            });
        }
    });

    //rawItems.sort((a, b) => parseFloat(a.display_number) - parseFloat(b.display_number));

    return { rawItems };
}

const prepareSubcontractorDashboardCharts = async(rawItems, taggedCompaniesIdNamePair, top20TaggedCompanies, industrialProjects, returnType)  => {
    let stackedChartWidth = 472;
    let stackedChartHeight = 193;
    let stackChartLegendAdj = 85;
    let horiLolipopChartWidth = 1120;
    let horiLolipopChartHeight = 400;
    let avgCloseoutLegendAdj = 10;

    if (returnType === 'pdf') {
        stackedChartWidth = 690;
        stackedChartHeight = 275;
        stackChartLegendAdj = 110;

        horiLolipopChartWidth = 1400;
        horiLolipopChartHeight = 478;
        avgCloseoutLegendAdj = 10;
    }

    //rating labels
    let goodRatingLabel = `Yes`;
    let fairRatingLabel = ``;
    let poorRatingLabel = `No`;
    let allRatingsArr = [goodRatingLabel.toLowerCase(), poorRatingLabel.toLowerCase()];
    let ratingsToCloseoutArr = [poorRatingLabel.toLowerCase()];
    let hasIndustrialDashboard = false;
    let hasNormalDashboard = false;

    if(industrialProjects) {
        goodRatingLabel = `Good`;
        fairRatingLabel = `Fair`;
        poorRatingLabel = `Poor`;
        allRatingsArr = [goodRatingLabel.toLowerCase(), poorRatingLabel.toLowerCase(), fairRatingLabel.toLowerCase()];
        ratingsToCloseoutArr = [poorRatingLabel.toLowerCase(), fairRatingLabel.toLowerCase()];
    }

    let goodRatingItemsCount = 0;
    let poorRatingItemsCount = 0;
    let fairRatingItemsCount = 0;

    let issuesRaisedChartData = [];
    let issuesRaisedChartColumns = ['group', poorRatingLabel];

    let positiveItemsChartData = [];
    let positiveItemsChartColumns = ['group', goodRatingLabel];

    let numberOfProjectsChartData = [];
    let numberOfProjectsChartColumns = ['group', 'Project'];

    let avgCloseoutTimeChartData = [];
    let avgCloseoutTimeChartGroups = [];
    let poorRatingData = [];
    let fairRatingData = [];

    let biggestProblemAreasChartData = [];
    let biggestProblemAreasChartColumns = ['group', poorRatingLabel];

    if(industrialProjects) {
        issuesRaisedChartColumns = ['group', fairRatingLabel, poorRatingLabel];
        biggestProblemAreasChartColumns = ['group', fairRatingLabel, poorRatingLabel];
    }

    if (rawItems.length) {
        if (industrialProjects) {
            hasIndustrialDashboard = true;
        } else {
            hasNormalDashboard = true;
        }

        (rawItems || []).forEach(function (item) {
            let isCompanyAmongTop20 = (top20TaggedCompanies).find(c => c.id === item.tagged_company_ref);
            if (!isCompanyAmongTop20) {
                return;
            }
            goodRatingItemsCount += (item.answer === goodRatingLabel.toLowerCase()) ? 1 : 0;
            poorRatingItemsCount += (item.answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
            if(industrialProjects) {
                fairRatingItemsCount += (fairRatingLabel && item.answer === fairRatingLabel.toLowerCase()) ? 1 : 0;
            }

            if (allRatingsArr.includes(item.answer)) {
                //Stacked Chart: Data of issues raised chart
                if (ratingsToCloseoutArr.includes(item.answer)) {
                    let existingRecord = issuesRaisedChartData.find(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                    if (existingRecord) {
                        existingRecord[capitalize(item.answer)] += 1;
                        existingRecord['total'] += 1;
                        let existingRecordIndex = issuesRaisedChartData.findIndex(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                        issuesRaisedChartData[existingRecordIndex] = existingRecord;
                    } else {
                        let dataItem = {
                            'group': taggedCompaniesIdNamePair[item.tagged_company_ref]
                        };
                        if(industrialProjects) {
                            dataItem[fairRatingLabel] = 0;
                        }
                        dataItem[poorRatingLabel] = 0;
                        dataItem['total'] = 1;
                        dataItem[capitalize(item.answer)] += 1;
                        issuesRaisedChartData.push(dataItem);
                    }

                    if (item.close_out && Object.keys(item.close_out).length) {
                        let existingRecord = avgCloseoutTimeChartData.find(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                        let hours = (+item.close_out.close_out_at - +item.it_createdAt) / (60 * 60 * 1000);
                        let day = 0;
                        if ([poorRatingLabel.toLowerCase()].includes(item.answer)) {
                            day = hours/24;
                        } else if(industrialProjects && [fairRatingLabel.toLowerCase()].includes(item.answer)) {
                            day = hours/24;
                        }

                        if (existingRecord && day) {
                            let existingRecordIndex = avgCloseoutTimeChartData.findIndex(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                            existingRecord[capitalize(item.answer)].push(day);
                            avgCloseoutTimeChartData[existingRecordIndex] = existingRecord;
                        } else {
                            let dataItem = {
                                'group': taggedCompaniesIdNamePair[item.tagged_company_ref]
                            };
                            if(industrialProjects) {
                                dataItem[fairRatingLabel] = [];
                            }
                            dataItem[poorRatingLabel] = [];
                            dataItem[capitalize(item.answer)].push(day);
                            if(day) {
                                avgCloseoutTimeChartData.push(dataItem);
                            }
                        }
                    }

                    //Stacked Chart: Data of Biggest Problem Areas chart
                    let dataItem = biggestProblemAreasChartData.find(dItem => dItem.group === item.itemCategory) || {};
                    if (Object.keys(dataItem).length) {
                        dataItem[capitalize(item.answer)] += 1;
                        dataItem['total'] += 1;
                        let dataItemIndex = biggestProblemAreasChartData.findIndex(dItem => dItem.group === item.itemCategory);
                        biggestProblemAreasChartData[dataItemIndex] = dataItem;
                    } else {
                        dataItem = {
                            'group': item.itemCategory
                        };
                        if(industrialProjects) {
                            dataItem[fairRatingLabel] = 0;
                        }
                        dataItem[poorRatingLabel] = 0;
                        dataItem['total'] = 1;
                        dataItem[capitalize(item.answer)] += 1;
                        biggestProblemAreasChartData.push(dataItem);
                    }
                }

                //Stacked Chart: Data of positive items chart
                if (goodRatingLabel.toLowerCase() == item.answer) {
                    let existingRecord = positiveItemsChartData.find(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                    if (existingRecord) {
                        existingRecord[capitalize(item.answer)] += 1;
                        existingRecord['total'] += 1;
                        let existingRecordIndex = positiveItemsChartData.findIndex(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                        positiveItemsChartData[existingRecordIndex] = existingRecord;
                    } else {
                        let dataItem = {
                            'group': taggedCompaniesIdNamePair[item.tagged_company_ref]
                        };
                        dataItem[goodRatingLabel] = 0;
                        dataItem['total'] = 1;
                        dataItem[capitalize(item.answer)] += 1;
                        positiveItemsChartData.push(dataItem);
                    }
                }

                //Stacked Chart: Data of number of Projects chart
                let existingRecord = numberOfProjectsChartData.find(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                if (existingRecord) {
                    if(!existingRecord.projectIds.includes(item.project_id)) {
                        existingRecord.projectIds.push(item.project_id);
                        existingRecord['Project'] += 1;
                        let existingRecordIndex = numberOfProjectsChartData.findIndex(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                        numberOfProjectsChartData[existingRecordIndex] = existingRecord;
                    }
                } else {
                    let dataItem = {
                        'group': taggedCompaniesIdNamePair[item.tagged_company_ref]
                    };
                    dataItem['Project'] = 1;
                    dataItem['projectIds'] = [item.project_id];
                    numberOfProjectsChartData.push(dataItem);
                }
            }
        });

        (issuesRaisedChartData || []).sort((a,b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0));
        (positiveItemsChartData || []).sort((a,b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0));
        (numberOfProjectsChartData || []).sort((a,b) => (a.Project < b.Project) ? 1 : ((b.Project < a.Project) ? -1 : 0));
        (biggestProblemAreasChartData || []).sort((a,b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0));

        //Horizontal Chart: Data of Average Issue Closeout Time
        (avgCloseoutTimeChartData || []).map(item => {
            let poorAvgDays = (item[poorRatingLabel].length) ? (item[poorRatingLabel].reduce(function (a, b) {
                return a + b;
            }, 0)) / item[poorRatingLabel].length : 0;

            if(poorAvgDays) {
                poorRatingData.push({
                    "group": item.group,
                    "rating": poorAvgDays
                });
            }

            let fairAvgDays = 0;
            if(industrialProjects) {
                fairAvgDays = (item[fairRatingLabel].length) ? (item[fairRatingLabel].reduce(function (a, b) {
                    return a + b;
                }, 0)) / item[fairRatingLabel].length : 0;

                if(fairAvgDays) {
                    fairRatingData.push({
                        "group": item.group,
                        "rating": fairAvgDays
                    });
                }
            }

            if(poorAvgDays || fairAvgDays) {
                avgCloseoutTimeChartGroups.push(item.group);
            }
        });
    }

    let issuesRaisedChartMax = (issuesRaisedChartData.length) ? issuesRaisedChartData[0].total : 0;
    let positiveItemsChartMax = (positiveItemsChartData.length) ? positiveItemsChartData[0].total : 0;
    let numberOfProjectsChartMax = (numberOfProjectsChartData.length) ? numberOfProjectsChartData[0].Project : 0;
    let biggestProblemAreasChartMax = (biggestProblemAreasChartData.length) ? biggestProblemAreasChartData[0].total : 0;

    if(industrialProjects) {
        let ind_issuesRaisedChart = await getStackedBarChart(issuesRaisedChartData, issuesRaisedChartColumns, issuesRaisedChartMax, stackedChartWidth, stackedChartHeight, [`${colorYellow}`, `${colorRed}`], '7px', 'Number', true, true, stackChartLegendAdj);

        let ind_positiveItemsChart = await getStackedBarChart(positiveItemsChartData, positiveItemsChartColumns, positiveItemsChartMax, stackedChartWidth, stackedChartHeight, [`${colorGreen}`], '7px', 'Number', true, true, stackChartLegendAdj);

        let ind_numberOfProjectsChart = await getStackedBarChart(numberOfProjectsChartData, numberOfProjectsChartColumns, numberOfProjectsChartMax, stackedChartWidth, stackedChartHeight, ["#6171a9"], '7px', 'Number', true, true, stackChartLegendAdj);

        let maxCloseoutTimePoor = mathMinMax(poorRatingData, 'rating', 'max');
        let maxCloseoutTimeFair = mathMinMax(fairRatingData, 'rating', 'max');
        let avgCloseoutTimeChartMax = Math.max(maxCloseoutTimePoor, maxCloseoutTimeFair);
        let ind_avgCloseoutTimeChart = await getHoriLollipopChart(avgCloseoutTimeChartGroups, poorRatingData, fairRatingData, avgCloseoutTimeChartMax, horiLolipopChartWidth, horiLolipopChartHeight,"Days to closeout", '7px', [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}], avgCloseoutLegendAdj);

        let ind_biggestProblemAreasChart = await getStackedBarChart(biggestProblemAreasChartData, biggestProblemAreasChartColumns, biggestProblemAreasChartMax, stackedChartWidth, stackedChartHeight, [`${colorYellow}`, `${colorRed}`], '7px', 'Number', true, true, stackChartLegendAdj);
        return { hasIndustrialDashboard, ind_issuesRaisedChart, ind_positiveItemsChart, ind_numberOfProjectsChart, ind_avgCloseoutTimeChart, ind_biggestProblemAreasChart};
    } else {
        let nor_issuesRaisedChart = await getStackedBarChart(issuesRaisedChartData, issuesRaisedChartColumns, issuesRaisedChartMax, stackedChartWidth, stackedChartHeight, [`${colorRed}`], '7px', 'Number', false, true, stackChartLegendAdj);

        let nor_positiveItemsChart = await getStackedBarChart(positiveItemsChartData, positiveItemsChartColumns, positiveItemsChartMax, stackedChartWidth, stackedChartHeight, [`${colorGreen}`], '7px', 'Number', false, true, stackChartLegendAdj);

        let nor_numberOfProjectsChart = await getStackedBarChart(numberOfProjectsChartData, numberOfProjectsChartColumns, numberOfProjectsChartMax, stackedChartWidth, stackedChartHeight, ["#6171a9"], '7px', 'Number', false, true, stackChartLegendAdj);

        let avgCloseoutTimeChartMax = mathMinMax(poorRatingData, 'rating', 'max');
        let nor_avgCloseoutTimeChart = await getHoriLollipopChart(avgCloseoutTimeChartGroups, poorRatingData, [], avgCloseoutTimeChartMax, horiLolipopChartWidth, horiLolipopChartHeight,"Days to closeout", '7px', [{ name: poorRatingLabel, color: `${colorRed}`}], avgCloseoutLegendAdj);

        let nor_biggestProblemAreasChart = await getStackedBarChart(biggestProblemAreasChartData, biggestProblemAreasChartColumns, biggestProblemAreasChartMax, stackedChartWidth, stackedChartHeight, [`${colorRed}`], '7px', 'Number', false, true, stackChartLegendAdj);

        return { hasNormalDashboard, nor_issuesRaisedChart, nor_positiveItemsChart, nor_numberOfProjectsChart, nor_avgCloseoutTimeChart, nor_biggestProblemAreasChart};
    }
};

const prepareTaggedOwnerDashboardCharts = async (rawItems, isIndustrialProject, ratingFilter) => {
    //rating labels
    let goodRatingLabel = (ratingFilter.yes) ? `Yes` : undefined;
    let fairRatingLabel = undefined;
    let poorRatingLabel = (ratingFilter.no) ? `No` : undefined;

    if(isIndustrialProject) {
        goodRatingLabel = (ratingFilter.good) ? `Good` : undefined;
        fairRatingLabel = (ratingFilter.fair) ? `Fair` : undefined;
        poorRatingLabel = (ratingFilter.poor) ? `Poor` : undefined;
    }

    let goodRatingLabelLC = (goodRatingLabel) ? goodRatingLabel.toLowerCase() : goodRatingLabel;
    let fairRatingLabelLC = (fairRatingLabel) ? fairRatingLabel.toLowerCase() : fairRatingLabel;
    let poorRatingLabelLC = (poorRatingLabel) ? poorRatingLabel.toLowerCase() : poorRatingLabel;

    let goodRatingItemsCount = 0;
    let poorRatingItemsCount = 0;
    let fairRatingItemsCount = 0;
    let totalClosedOutItems = 0;
    let totalFairToCloseOut = 0;
    let totalPoorToCloseOut = 0;
    let ratingsByGroupChartData = [];
    let ratingsByGroupChartColumns = ['group', goodRatingLabel, fairRatingLabel, poorRatingLabel];
    ratingsByGroupChartColumns = ratingsByGroupChartColumns.filter(element => element !== undefined); //filter undefineds
    let totalRatingPerCategory = {};
    let itemsInfo = [];
    let participatedProjectIds = [];
    rawItems.forEach(function(item, i) {
        let itemAnswerLC = (item.answer || '').toLowerCase();
        let itemInfo = {};
        if ([goodRatingLabelLC, fairRatingLabelLC, poorRatingLabelLC].includes(itemAnswerLC)) {
            itemInfo.inspected_by = item.it_user_ref; //expand needed
            itemInfo.inspected_at = item.it_createdAt;
            goodRatingItemsCount += (goodRatingLabelLC && goodRatingLabelLC == itemAnswerLC) ? 1 : 0;
            fairRatingItemsCount += (fairRatingLabelLC && fairRatingLabelLC === itemAnswerLC) ? 1 : 0;
            poorRatingItemsCount += (poorRatingLabelLC && poorRatingLabelLC === itemAnswerLC) ? 1 : 0;

            if ([fairRatingLabelLC, poorRatingLabelLC].includes(itemAnswerLC) && item.close_out && Object.keys(item.close_out).length) {
                totalClosedOutItems += ([fairRatingLabelLC, poorRatingLabelLC].includes(itemAnswerLC)) ? 1 : 0;
                itemInfo.item_closeout_reviewd_by = (item.close_out.reviewed_by) ? item.close_out.reviewed_by : 'N/A';
                itemInfo.item_closeout_at = +item.close_out.close_out_at ? moment(+item.close_out.close_out_at).format('DD-MM-YYYY HH:mm:ss') : 'N/A';
                itemInfo.item_closeout_detail = item.close_out.details;
                itemInfo.item_closeout_images = (item.close_out.images && item.close_out.images.length) ? item.close_out.images : []; //expand needed
            } else {
                totalFairToCloseOut += (fairRatingLabelLC === item.answer) ? 1 : 0;
                totalPoorToCloseOut += (poorRatingLabelLC === item.answer) ? 1 : 0;
            }

            if (item.summary || item.corrective_action_required || (item.appendix && item.appendix.length) || (item.close_out && Object.keys(item.close_out).length) || item.responsible_user_ref || (item.tagged_company_ref && item.tagged_company_ref.length) || (item.location_tag && Object.keys(item.location_tag).length)) {
                itemInfo.item_title = `Item: ${item.display_number} – ${item.question}`;
                itemInfo.item_answer = item.answer;
                itemInfo.item_summary = (item.summary) ? item.summary : 'N/A';
                itemInfo.item_action = (item.corrective_action_required) ? item.corrective_action_required : 'N/A';
                itemInfo.item_assigned_to = (item.responsible_user_ref) ? item.responsible_user_ref : null; //expand needed
                itemInfo.item_closeout_due_on = (item.closeout_due_on) ? moment(item.closeout_due_on).format('DD-MM-YYYY') : null;
                itemInfo.item_images = (item.appendix && item.appendix.length) ? item.appendix : []; //expand needed
                itemInfo.location = (item.location_tag && Object.keys(item.location_tag).length) ? item.location_tag : null;
                itemInfo.project_name = item.project_name;

                itemsInfo.push(itemInfo);
            }

            let dataItem = ratingsByGroupChartData.find(dItem => dItem.group === item.item_category) || {};
            if (Object.keys(dataItem).length) {
                dataItem[capitalize(item.answer)] += 1;
                totalRatingPerCategory[item.item_category] += 1;
                let dataItemIndex = ratingsByGroupChartData.findIndex(dItem => dItem.group === item.item_category);
                ratingsByGroupChartData[dataItemIndex] = dataItem;
            } else {
                totalRatingPerCategory[item.item_category] = 0;
                totalRatingPerCategory[item.item_category] += 1;
                dataItem = {
                    'group': item.item_category
                };
                dataItem[goodRatingLabel] = 0;
                dataItem[fairRatingLabel] = 0;
                dataItem[poorRatingLabel] = 0;
                Object.keys(dataItem).forEach(key => key === 'undefined' ? delete dataItem[key] : {}); //filter undefined

                dataItem[capitalize(item.answer)] += 1;
                ratingsByGroupChartData.push(dataItem);
            }

            if (item.project_id) {
                participatedProjectIds.push(item.project_id);
            }
        }
    });
    let totalRatingCount = +(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
    let goodRatingPercentage = parseInt(100/(totalRatingCount)*(goodRatingItemsCount));
    let fairRatingPercentage = parseInt(100/(totalRatingCount)*(fairRatingItemsCount));
    let poorRatingPercentage = parseInt(100/(totalRatingCount)*(poorRatingItemsCount));
    let donutChartData = [
        {name: `${goodRatingLabel} (${goodRatingItemsCount})`, count: goodRatingItemsCount, percentage: goodRatingPercentage, color: `${goodRatingColor}`, label: goodRatingLabel},
        {name: `${fairRatingLabel} (${fairRatingItemsCount})`, count: fairRatingItemsCount, percentage: fairRatingPercentage, color: `${fairRatingColor}`, label: fairRatingLabel},
        {name: `${poorRatingLabel} (${poorRatingItemsCount})`, count: poorRatingItemsCount, percentage: poorRatingPercentage, color: `${poorRatingColor}`, label: poorRatingLabel}
    ];
    donutChartData = donutChartData.filter(item => item.label != undefined); //filter undefined

    let donutChartWidth = 374;
    let donutChartHeight = 219;
    let donutChartPercentageTxtAdj = -5;

    let openClosedBarChartData = [];
    if(isIndustrialProject) {
        openClosedBarChartData.push({name: `Open (${poorRatingLabel})`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`, label: poorRatingLabel});
        openClosedBarChartData.push({name: `Open (${fairRatingLabel})`, value: totalFairToCloseOut, type: `Rating: ${fairRatingLabel}`, label: fairRatingLabel});
    } else {
        openClosedBarChartData.push({name: `Open`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`, label: poorRatingLabel});
    }
    openClosedBarChartData.push({name: "Closed", value: totalClosedOutItems, type: '', label: "Closed"});
    openClosedBarChartData = openClosedBarChartData.filter(item => item.label != undefined); //filter undefined

    let hasAllClosed = (!totalPoorToCloseOut && !totalFairToCloseOut && totalClosedOutItems) ? `All ${totalClosedOutItems} items closed out` : '';
    let openClosedBarChartLegends = [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}];
    if (!isIndustrialProject) {
        openClosedBarChartLegends = [{ name: poorRatingLabel, color: `${colorRed}`}];
    }
    openClosedBarChartLegends = openClosedBarChartLegends.filter(item => item.name != undefined); //filter undefined

    let openClosedBarChartWidth = 360;
    let openClosedBarChartHeight = 19;
    let openChartMarginTop = '0px';
    let adjustments = {
        adj1: -2,
        adj2: 20,
        adj3: -27,
        adj4: 8.5,
        adj5: -1.5,
        adj6: 4.5,
        adj7: 26,
        adj8: 2,
        adj9: 37,
        adj10: 4,
        adj11: 2,
        adj12: 2,
        adj13: 3,
    }
    let openClosedBarChartFont = '.8em';

    let ratingsByGroupChartWidth = 680;
    let ratingsByGroupChartHeight = 200;
    let maxRating = 0;
    if (Object.keys(totalRatingPerCategory).length) {
        let keyWithMaxRating = Object.keys(totalRatingPerCategory).reduce((a, b) => totalRatingPerCategory[a] > totalRatingPerCategory[b] ? a : b);
        maxRating = totalRatingPerCategory[keyWithMaxRating];
    }

    let donutGoodLabel = (goodRatingLabel) ? `${goodRatingLabel} (${goodRatingItemsCount})` : '';
    let donutFairLabel = (fairRatingLabel) ? `${fairRatingLabel} (${fairRatingItemsCount})` : '';
    let donutPoorLabel = (poorRatingLabel) ? `${poorRatingLabel} (${poorRatingItemsCount})` : '';
    let openClosedChartColor = [];
    let stackedBarChartColor = [];
    if (goodRatingLabel) {
        stackedBarChartColor.push(colorGreen);
    }

    if (fairRatingLabel) {
        openClosedChartColor.push(colorYellow);
        stackedBarChartColor.push(colorYellow);
    }

    if (poorRatingLabel) {
        openClosedChartColor.push(colorRed);
        stackedBarChartColor.push(colorRed);
    }
    openClosedChartColor.push("#2c961b");

    if (isIndustrialProject) {
        let ind_donutChart = await getDonutChart(donutChartData, donutChartWidth, donutChartHeight, donutChartPercentageTxtAdj, 100, 60, "30 -13 260 270", donutGoodLabel, donutFairLabel, donutPoorLabel, true, isIndustrialProject, 0, 0);
        let ind_openClosedBarChart = await getOpenClosedBarChart(openClosedBarChartData, hasAllClosed, openClosedBarChartWidth, openClosedBarChartHeight, openClosedBarChartFont, openChartMarginTop, adjustments, openClosedChartColor, openClosedBarChartLegends);
        let ind_ratingsByGroupChart = await getStackedBarChart(ratingsByGroupChartData, ratingsByGroupChartColumns, maxRating, ratingsByGroupChartWidth, ratingsByGroupChartHeight, stackedBarChartColor, '6.5px', '', isIndustrialProject, false, 25);

        return {
            donutChart: ind_donutChart,
            openClosedBarChart: ind_openClosedBarChart,
            ratingsByGroupChart: ind_ratingsByGroupChart,
            no_of_projects: _uniq(participatedProjectIds),
            no_of_items_tagged: (goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount),
            no_of_positive_items: goodRatingItemsCount,
            no_of_issues_raised: (fairRatingItemsCount + poorRatingItemsCount),
            itemsInfo
        };
    }

    let nor_donutChart = await getDonutChart(donutChartData, donutChartWidth, donutChartHeight, donutChartPercentageTxtAdj, 100, 60, "30 -13 260 270", donutGoodLabel, donutFairLabel, donutPoorLabel, true, isIndustrialProject, 0, 0);
    let nor_openClosedBarChart = await getOpenClosedBarChart(openClosedBarChartData, hasAllClosed, openClosedBarChartWidth, openClosedBarChartHeight, openClosedBarChartFont, openChartMarginTop, adjustments, openClosedChartColor, openClosedBarChartLegends);
    let nor_ratingsByGroupChart = await getStackedBarChart(ratingsByGroupChartData, ratingsByGroupChartColumns, maxRating, ratingsByGroupChartWidth, ratingsByGroupChartHeight, stackedBarChartColor, '6.5px', '', isIndustrialProject, false, 25);

    return {
        donutChart: nor_donutChart,
        openClosedBarChart: nor_openClosedBarChart,
        ratingsByGroupChart: nor_ratingsByGroupChart,
        no_of_projects: _uniq(participatedProjectIds),
        no_of_items_tagged: (goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount),
        no_of_positive_items: goodRatingItemsCount,
        no_of_issues_raised: (fairRatingItemsCount + poorRatingItemsCount),
        itemsInfo
    };
}

const calculateRatingPercentage = async (inspectionTourReq) => {
    let projectInfo =  await sails.models.project_reader.findOne({
        where: {id: inspectionTourReq.project_ref},
        select: ['project_type']
    });
    let isIndustrialProject = (projectInfo.project_type === 'industrial');
    //rating labels
    let goodRatingLabel = `yes`;
    let fairRatingLabel = ``;
    let poorRatingLabel = `no`;
    let inspectionTourChecklist = [];
    if (isIndustrialProject) {
        goodRatingLabel = `good`;
        fairRatingLabel = `fair`;
        poorRatingLabel = `poor`;
        inspectionTourChecklist = [...inspectionTourReq.industrial_checklist, ...inspectionTourReq.additional_checklist];
    } else {
        inspectionTourChecklist = [...inspectionTourReq.common_checklist];
        if (projectInfo.project_type === 'rail') {
            inspectionTourChecklist.push(...inspectionTourReq.rail_checklist);
        }
    }
    let goodRatingItemsCount = 0;
    let poorRatingItemsCount = 0;
    let fairRatingItemsCount = 0;
    inspectionTourChecklist.forEach(function(item, j) {
        let answer = (item.answer || '').toLowerCase();
        goodRatingItemsCount += (answer === goodRatingLabel) ? 1 : 0;
        poorRatingItemsCount += (answer === poorRatingLabel) ? 1 : 0;
        fairRatingItemsCount += (fairRatingLabel && answer === fairRatingLabel) ? 1 : 0;
    });

    let ratingPercent = parseInt(100/(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount)*(goodRatingItemsCount));
    return (isNaN(ratingPercent)) ? 0 : ratingPercent;
}

const processInspectionTourItemCloseOut = async (req, res, projectInspectionTour, checklistType, itemInfo) => {
    if (projectInspectionTour && projectInspectionTour.id) {
        let itemMetaInfo = {};
        let metaChecklist = [];
        let status = +req.body.closeout_status;

        if (checklistType !== "additional_checklist") {
            metaChecklist = await getInspectionTourMetaChecklist(`inspection_tour_${checklistType}`);
            itemMetaInfo = (metaChecklist || []).find(metaItem => metaItem.question_id == itemInfo.question_id);
        } else {
            itemMetaInfo = itemInfo;
        }

        let checklist = projectInspectionTour[checklistType];
        checklist = (checklist || []).map(item => {
            if (item.question_id == itemInfo.question_id) {
                item = itemInfo;
            }

            return item;
        });

        sails.log.info('Updating inspection tour to close out an item.');
        let updateRequest = {};
        updateRequest[checklistType] = checklist;
        updateRequest["status"] = status;
        let updatedInspectionTour = await sails.models.projectinspectiontour.updateOne({id: projectInspectionTour.id}).set(updateRequest);

        sails.log.info('Updated inspection tour successfully.');
        successResponse(res, {project_inspection_tour: updatedInspectionTour});

        // @todo: Added a sleep here to wait for completing pdf(close out images) to images translation
        await sleep(30000);

        sails.log.info('Sending close out item mail to inspection tour owner.')
        let closeOutImagesSrc = [];
        if(Object.keys(itemInfo.close_out).length && itemInfo.close_out.images && itemInfo.close_out.images.length) {
            let files = await sails.models.userfile.find({
                where: {id: _uniq(itemInfo.close_out.images)},
                select: ['id', 'file_url', 'img_translation']
            });
            closeOutImagesSrc = (files || []).reduce((arr, file) => {
                if (file.file_url && !file.img_translation.length) {
                    arr.push(file.file_url);
                } else if (file.img_translation.length) {
                    arr.push(...file.img_translation);
                }
                return arr;
            }, []);
        }

        sails.log.info(`Send email to ${projectInspectionTour.user_ref.email} for close out item ${itemMetaInfo.category} - ${itemMetaInfo.question}`);

        let tz = getProjectTimezone(projectInspectionTour.project_ref);
        let inspectionDateTime = tz ? momentTz(+projectInspectionTour.createdAt).tz(tz).format('DD-MM-YY HH:mm:ss') : moment(projectInspectionTour.createdAt).format('DD-MM-YY HH:mm:ss');
        let subject = `Inspection Closeout: ${projectInspectionTour.project_ref.name}`;
        let html = await sails.renderView('pages/mail/mail-content', {
            mail_body: 'inspection-tour-notify-on-item-close-out',
            title: subject,
            completed_by_name: getUserFirstName(projectInspectionTour.user_ref),
            project_name: projectInspectionTour.project_ref.name,
            closed_out_by: itemInfo.close_out.reviewed_by,
            inspection_number: buildRecordRef(projectInspectionTour),
            inspection_date_time: inspectionDateTime,
            item_title: `${itemMetaInfo.category} - ${itemMetaInfo.question}`,
            item_summary: itemInfo.summary,
            closeout_details: itemInfo.close_out.details,
            closeout_images: closeOutImagesSrc,
            layout: false
        });
        await EmailService.sendMail(subject, [projectInspectionTour.user_ref.email], html);

        await checkAndSyncToAsite(req, res, projectInspectionTour, updatedInspectionTour);
        return;
    }

    sails.log.info('Failed to update inspection tour to close out an item.');
    return errorResponse(res, sails.__('Failed to update inspection tour to close out an item.'));
};

const checkAndSyncToAsite = async (req, res, projectInspectionTour, updatedInspectionTour) => {
    let hasAsite = await checkIfAsiteEnabled(projectInspectionTour.project_ref.id);
    if(!hasAsite){
        sails.log.info(`Asite config on Project ${projectInspectionTour.project_ref.id} is disabled.`);
        return;
    }
    sails.log.info(`Project has asite enabled trying to sync Inspection Builder document for project ${projectInspectionTour.project_ref.id}`);

    let inspectionTourChecklist = [];
    let projectType = projectInspectionTour.project_ref.project_type;
    let isIndustrialProject = (projectType === 'industrial');

    if (isIndustrialProject) {
        inspectionTourChecklist = [...updatedInspectionTour.industrial_checklist, ...updatedInspectionTour.additional_checklist];
    } else {
        inspectionTourChecklist = [...updatedInspectionTour.common_checklist];
        if (projectType === 'rail') {
            inspectionTourChecklist.push(...updatedInspectionTour.rail_checklist);
        }
    }

    let totalItemsToCloseOut = 0;
    let poorRatingLabel = `No`;

    if (isIndustrialProject) {
        goodRatingLabel = `Good`;
        poorRatingLabel = `Poor`;
    }
    inspectionTourChecklist.forEach(function(item, j) {
        if ((poorRatingLabel.toLowerCase() == item.answer || item.answer === 'fair') && !(item.close_out && Object.keys(item.close_out).length)) {
            totalItemsToCloseOut += 1;
        }
    });
    sails.log.info(`Items remaining to closeout ${totalItemsToCloseOut}`);
    if(totalItemsToCloseOut === 0) {
        let userEmployer = await getUserInductionEmployer({id: projectInspectionTour.user_ref.id}, projectInspectionTour.project_ref.id);
        // Check if user's induction employer not there, Load employer from profile data.
        if(!userEmployer) {
            userEmployer = projectInspectionTour.user_ref.parent_company;
        }
        // Not awaiting syncInspectionTourToAsite fn, to run the PDF generation and asite upload in backend.
        let localsObj = Object.assign({}, res.locals); // Cloning the res.locals, as the object gets cleared after the API response sent.
        syncInspectionTourToAsite(req, res, projectInspectionTour.project_ref, projectInspectionTour.id, projectInspectionTour.record_id, localsObj, userEmployer).catch(sails.log.error);
    }
    return;
}

const syncInspectionTourToAsite = async (req, res, projectInfo, inspectionId, recordId, localsObj, userEmp = 0) => {
    sails.log.info(`[syncInspectionTourToAsite] Starting execution for inspection tour report ${inspectionId} employer ID ${userEmp}`);

    let {workspace_id, matched_tool} = await getAsiteProjectToolMapping(projectInfo.id, 'inspections', userEmp);
    if(!workspace_id || !matched_tool) {
        sails.log.info(`[syncInspectionTourToAsite] Aborting execution for inspection tour report ${inspectionId} workspace_id or matched_tool not found.`);
        return;
    }

    let { employer } = await getCompanyInfo(projectInfo, null, ['id', 'company_initial']);

    sails.log.info(`[syncInspectionTourToAsite] preparing PDF for inspection tour report ${inspectionId}`);
    res.locals = localsObj || res.locals;

    let fileData = await prepareViewOrDownloadInspectionTour(req, res, {id: inspectionId}, 'pdf', 'path');
    sails.log.info(`[syncInspectionTourToAsite] PDF prepared, starting asite upload for inspection tour report ${inspectionId}`);

    //File name format -> {PROJECT/CONTRACT NUMBER} ({INNDEX PROJECT ID NUMBER})-{COMPANY INITIALS}-{TOOL NAME}-{REPORT TYPE}-{REPORT #}
    fileData.name = `${projectInfo.project_number} (${projectInfo.id})-${employer.company_initial}-InspectionTour_Report-${recordId}.pdf`;
    sails.log.info(`[syncInspectionTourToAsite] Filename to be used on asite ${fileData.name}`);

    await uploadDocOnAsite(employer.id, workspace_id, matched_tool.folder_id, fileData);
};

const getInspectionTourMetaChecklistFn = async (req, res) => {
    let commonChecklist = await getInspectionTourMetaChecklist('inspection_tour_common_checklist');
    let railChecklist = await getInspectionTourMetaChecklist('inspection_tour_rail_checklist');
    let industrialChecklist = await getInspectionTourMetaChecklist('inspection_tour_industrial_checklist');
    return successResponse(res, {common_checklist: commonChecklist, rail_checklist: railChecklist, industrial_checklist: industrialChecklist});
};

/**
This function is being used to create map of inductionId: userId, for handling invalid payload from mobile app v4.0.11.
This function can be removed once there is no user using the app `v4.0.11`
**/
const getTaggedInductionRefs = async (reqBody, projectId) => {
    let taggedInductionIds = [];
    fillTaggedUserRefsArray([...reqBody.common_checklist, ...reqBody.additional_checklist, ...reqBody.industrial_checklist, ...reqBody.rail_checklist], taggedInductionIds);
    taggedInductionIds = _uniq(taggedInductionIds);

    sails.log.info('Tagged Induction Ids of Items, ', taggedInductionIds);
    let inductionRequests = await sails.models.inductionrequest_reader.find({
        where: {
            id: taggedInductionIds,
            project_ref: projectId
        },
        select: ['user_ref'],
    });

    let inductionIdToUserRef = {};
    inductionRequests.forEach(induction => {
        inductionIdToUserRef[induction.id] = induction.user_ref;
    });

    return inductionIdToUserRef;
};

const fillTaggedUserRefsArray = (checklists, taggedUserRefs = []) => {
    (checklists || []).forEach(function(item) {
        if (item.responsible_user_ref && !isNaN(item.responsible_user_ref)) {
            taggedUserRefs.push(item.responsible_user_ref);
        }
    });
};

module.exports = {
    /**
     * @deprecated Moved functionality to fn partialCreateOrUpdate() below.
     */
    createInspectionTour: async (req, res) => {
        sails.log.info('Create inspection tour for project, by', req.user.id);

        let projectInspectionTour = await createITRecord(req);
        if (projectInspectionTour) {
            await sendCreatedNotification(projectInspectionTour);

            sails.log.info('Created inspection tour successfully.');
            return successResponse(res, projectInspectionTour);
        }
        sails.log.info('Failed to create inspection tour');
        return ResponseService.errorResponse(res, sails.__('Failed to create inspection tour'));
    },
    /**
     * @deprecated Moved functionality to fn partialCreateOrUpdate() below.
     */
    updateInspectionTour: async (req, res) => {
        let id = +req.param('id');
        sails.log.info('update inspection tour request', id);

        let updateRequest = _.pick((req.body || {}), [
            'common_checklist',
            'rail_checklist',
            'industrial_checklist',
            'additional_checklist',
            'status',
            'report_title',
            'report_datetime',
            'custom_field',
            'participants',
        ]);

        let projectInspectionTour = await updateITRecord(req, id, updateRequest);

        if(projectInspectionTour && projectInspectionTour.id) {
            sails.log.info('Updated inspection tour successfully.');
            return successResponse(res, projectInspectionTour);
        }

        sails.log.info('Failed to update inspection tour.');
        return errorResponse(res, sails.__('Failed to update inspection tour.'), failure);
    },

    partialCreateOrUpdate: async (req, res) => {
        let id = +req.body.id || 0;
        let finalised = +req.body.finalised || false;
        let projectId = +req.param('projectId');

        sails.log.info('Partial create/update inspection tour for project, by', req.user.id, 'recordId: ', id);
        let projectInspectionTour = {};

        let {validationError} = createOrUpdateITRecord(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
        }

        //! Patch: For app version = 4.0.11 in the payload it's passing induction id in field of responsible_user_ref instead of user_ref. Below if block is handling to correct the id
        let platform = req.headers['platform'];
        let versionPattern = /^4\.0\.11$/
        if (platform && extractPlatformVersion(platform, versionPattern)) {
            sails.log.info("Request is from mobile app version: 4.0.11");
            let inductionIdToUserRef = {}
            let fetchedInspectionTour;
            if (id) {
                [fetchedInspectionTour, inductionIdToUserRef] = await Promise.all([
                    sails.models.projectinspectiontour_reader.findOne({ id: id, project_ref: projectId }),
                    getTaggedInductionRefs(req.body, projectId)
                ]);
            } else {
                inductionIdToUserRef = await getTaggedInductionRefs(req.body, projectId);
            }

            // Edge case when started from web or mobile app other then 4.0.11 version, to check if responsible_user_ref is same as saved copy then do not update; Creating checklist and AdditionalChecklist tagged UserRef map to use later.
            const commonChecklistResponsibleUserRefMap = {};
            const additionalChecklistResponsibleUserRefMap = {};
            const industrialChecklistResponsibleUserRefMap = {};
            const railChecklistResponsibleUserRefMap = {};

            if (fetchedInspectionTour) {
                (fetchedInspectionTour.common_checklist || []).forEach(checklist => {
                    const checklistQueId = checklist.question_id;
                    commonChecklistResponsibleUserRefMap[checklistQueId] = checklist.responsible_user_ref
                });
                (fetchedInspectionTour.additional_checklist || []).forEach(checklist => {
                    const checklistQueId = checklist.question_id;
                    additionalChecklistResponsibleUserRefMap[checklistQueId] = checklist.responsible_user_ref
                });
                (fetchedInspectionTour.industrial_checklist || []).forEach(checklist => {
                    const checklistQueId = checklist.question_id;
                    industrialChecklistResponsibleUserRefMap[checklistQueId] = checklist.responsible_user_ref
                });
                (fetchedInspectionTour.rail_checklist || []).forEach(checklist => {
                    const checklistQueId = checklist.question_id;
                    railChecklistResponsibleUserRefMap[checklistQueId] = checklist.responsible_user_ref
                });
            }

            sails.log.info(`Changing responsible_user_ref for inspectionTourId: ${fetchedInspectionTour && fetchedInspectionTour.id}`);
            req.body.common_checklist.forEach(checklist => {
                const originalId = checklist.responsible_user_ref;
                if (originalId && inductionIdToUserRef[originalId] && (!fetchedInspectionTour || originalId !== commonChecklistResponsibleUserRefMap[checklist.question_id])) {
                    sails.log.info(`Updating responsible_user_ref ${originalId} -> ${inductionIdToUserRef[originalId]} for inspectionTourId: ${fetchedInspectionTour && fetchedInspectionTour.id}`);
                    checklist.responsible_user_ref = inductionIdToUserRef[originalId];
                }
            });
            req.body.additional_checklist.forEach(checklist => {
                const originalId = checklist.responsible_user_ref;
                if (originalId && inductionIdToUserRef[originalId] && (!fetchedInspectionTour || originalId !== additionalChecklistResponsibleUserRefMap[checklist.question_id])) {
                    sails.log.info(`Updating responsible_user_ref ${originalId} -> ${inductionIdToUserRef[originalId]} for inspectionTourId: ${fetchedInspectionTour && fetchedInspectionTour.id}`);
                    checklist.responsible_user_ref = inductionIdToUserRef[originalId];
                }
            });
            req.body.industrial_checklist.forEach(checklist => {
                const originalId = checklist.responsible_user_ref;
                if (originalId && inductionIdToUserRef[originalId] && (!fetchedInspectionTour || originalId !== industrialChecklistResponsibleUserRefMap[checklist.question_id])) {
                    sails.log.info(`Updating responsible_user_ref ${originalId} -> ${inductionIdToUserRef[originalId]} for inspectionTourId: ${fetchedInspectionTour && fetchedInspectionTour.id}`);
                    checklist.responsible_user_ref = inductionIdToUserRef[originalId];
                }
            });
            req.body.rail_checklist.forEach(checklist => {
                const originalId = checklist.responsible_user_ref;
                if (originalId && inductionIdToUserRef[originalId] && (!fetchedInspectionTour || originalId !== railChecklistResponsibleUserRefMap[checklist.question_id])) {
                    sails.log.info(`Updating responsible_user_ref ${originalId} -> ${inductionIdToUserRef[originalId]} for inspectionTourId: ${fetchedInspectionTour && fetchedInspectionTour.id}`);
                    checklist.responsible_user_ref = inductionIdToUserRef[originalId];
                }
            });
        }

        if(id) {
            sails.log.info('Update partial inspection tour request', id);

            let pendingIncidentRecordCount = await sails.models.projectinspectiontour.count({id: id, finalised: false});
            if(pendingIncidentRecordCount == 0){
                sails.log.info('No pending inspection tour found with ID: ', id);
                return ResponseService.errorResponse(res, sails.__('No pending inspection tour found with ID: '+ id));
            }

            let updateRequest = _.pick((req.body || {}), [
                'location',
                'type_of_work',
                'responsible_manager_user_ref',
                'supervisor_user_ref',
                'common_checklist',
                'rail_checklist',
                'industrial_checklist',
                'additional_checklist',
                'observations',
                'inspection_summary',
                'report_title',
                'report_datetime',
                'custom_field',
                'participants',
                'finalised',
                'sign',
            ]);

            updateRequest.rating_percentage = await calculateRatingPercentage(req.body);
            projectInspectionTour = await updateITRecord(req, id, updateRequest);
        } else {
            sails.log.info('Create partial inspection tour request');
            req.body.rating_percentage = await calculateRatingPercentage(req.body);
            projectInspectionTour = await createITRecord(req);
        }

        if (projectInspectionTour) {
            if (finalised) {
                await sendCreatedNotification(projectInspectionTour);
                sails.log.info('Deleting all the pending inspections by user on a project, As an inspecion is finalized.');
                await sails.models.projectinspectiontour.destroy({
                    project_ref: projectId,
                    user_ref: req.user.id,
                    finalised: false
                });

                let it =  await sails.models.projectinspectiontour.findOne({
                    where: {id: projectInspectionTour.id},
                    select: ['user_ref', 'project_ref', 'record_id', 'createdAt']
                });
                [it] = await populateUserRefs([it], 'user_ref', ['id', 'first_name', 'last_name', 'email']);
                [it] = await populateProjectRefs([it], 'project_ref', ['id', 'name', 'custom_field', 'project_type', 'parent_company', 'project_number']);

                await checkAndSyncToAsite(req, res, it, projectInspectionTour);
            }
            sails.log.info('Created/updated partial inspection tour successfully, finalised:', finalised);
            return successResponse(res, projectInspectionTour);
        }
        sails.log.info('Failed to create/update inspection tour.');
        return ResponseService.errorResponse(res, sails.__('Failed to create/update inspection tour.'));
    },

    getPendingInspectionTour: async (req, res) => {
        let projectId = +req.param('projectId');
        let userId = +req.user.id;

        sails.log.info('Fetch pending project IT inspection, projectId:', projectId, 'userId', userId);

        let [projectInspectionTour] =  await sails.models.projectinspectiontour.find({
            where: {
                project_ref: projectId,
                user_ref: userId,
                finalised: false
            },
            limit: 1,
            sort: 'updatedAt DESC'
        })
            .populate('project_ref')
            .populate('user_ref')
            .populate('responsible_manager_user_ref')
            .populate('supervisor_user_ref');

        if(projectInspectionTour && projectInspectionTour.id) {
            let itemAppendixImageIds = [];
            ([
                ...projectInspectionTour.industrial_checklist,
                ...projectInspectionTour.additional_checklist,
                ...projectInspectionTour.common_checklist,
                ...projectInspectionTour.rail_checklist
            ] || []).map(item => {
                if (item.appendix.length) {
                    itemAppendixImageIds.push(...item.appendix);
                }
                return item;
            });

            sails.log.info('Appendix images of Items, ', _uniq(itemAppendixImageIds));
            let itemAppendixImages = await sails.models.userfile.find({
                where: {id: _uniq(itemAppendixImageIds)},
                select: ['file_url', 'sm_url']
            });

            projectInspectionTour.industrial_checklist = putImagesToClItems(projectInspectionTour.industrial_checklist, itemAppendixImages);
            projectInspectionTour.additional_checklist = putImagesToClItems(projectInspectionTour.additional_checklist, itemAppendixImages);
            projectInspectionTour.common_checklist = putImagesToClItems(projectInspectionTour.common_checklist, itemAppendixImages);
            projectInspectionTour.rail_checklist = putImagesToClItems(projectInspectionTour.rail_checklist, itemAppendixImages);
        }

        return successResponse(res, {
            project_inspection_tour: projectInspectionTour || null
        });
    },

    getInspectionTour: async (req, res) => {
        let id = +req.param('id');

        sails.log.info('Fetch inspection tour, id:', id);

        let {projectInspectionTour, commonChecklist, railChecklist, industrialChecklist} = await getITRecord({id: id});

        return successResponse(res, {
            project_inspection_tour: projectInspectionTour,
            common_checklist: commonChecklist,
            rail_checklist: railChecklist,
            industrial_checklist: industrialChecklist
        });
    },
    // todo: update mobile app to use getProjectInspectionToursList API which supports searching,
    getProjectInspectionTours: async (req, res) => {
        let projectId = +req.param('projectId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let searchTerm = req.param('q') ? req.param('q') : '';
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        sails.log.info('Fetch project inspection tours, projectId:', projectId, `is_inherited_project: ${is_inherited_project}, search :${searchTerm}`);
        let filter = {
            project_ref: [projectId],
            finalised: true
        };

        let countFilter = filter;
        filter = {
            where: filter,
            skip : pageNumber * pageSize,
            limit: pageSize
        }

        sails.log.info('Fetch inspection tour filter.', filter);

        let {projectInspectionTours, commonChecklist, railChecklist, industrialChecklist} = await getInspectionTours(filter, 'DESC', false, true);
        let total_record_count = await sails.models.projectinspectiontour.count(countFilter);
        projectInspectionTours = await attachProfilePicWithUserRefInfo(projectInspectionTours);

        return successResponse(res, {
            project_inspection_tours: projectInspectionTours,
            common_checklist: commonChecklist,
            rail_checklist: railChecklist,
            industrial_checklist: industrialChecklist,
            total_record_count
        });
    },

    // @todo: vshal: deprecated api, need to remove on 1st Dec
    downloadInspectionTour: async (req, res) => {
        let id = +req.param('id');
        let type = req.param('type');
        let updatedAt = +req.param('updatedAt');
        sails.log.info('Fetch project inspection tour :', id);

        let whereClause = {id: id, updatedAt: updatedAt};
        return await prepareViewOrDownloadInspectionTour(req, res, whereClause, type);
    },

    downloadInspectionTourV1: async (req, res) => {
        let id = +req.param('id');
        sails.log.info('inspection tour, id', id);

        let whereClause = {id: id};
        let response = await prepareViewOrDownloadInspectionTour(req, res, whereClause, req.body.type);

        if(req.body.type === 'html') {
            sails.log.info('Rendering html view');
            return res.send(response);
        }
        //pdf
        return response;
    },

    getInspectionTourMetaChecklist: getInspectionTourMetaChecklistFn,

    getInspectionTourMetaChecklistV2: getInspectionTourMetaChecklistFn,

    getProjectInspectionToursByUser: async (req, res) => {
        let projectId = +req.param('projectId');
        let userId = +req.param('userId');
        sails.log.info('Fetch project inspection tours, projectId:', projectId, 'userId', userId);
        let where = {
            project_ref: [projectId],
            user_ref: userId,
            finalised: true
        };

        sails.log.info('Fetch inspection tour filter.', where);

        let {projectInspectionTours, commonChecklist, railChecklist, industrialChecklist} = await getInspectionTours({where: where}, 'DESC');

        return successResponse(res, {
            project_inspection_tours: projectInspectionTours,
            common_checklist: commonChecklist,
            rail_checklist: railChecklist,
            industrial_checklist: industrialChecklist
        });
    },

    getProjectInspectionToursList : async (req,res) => {
        let projectId = +req.param('projectId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let searchTerm = req.param('q') ? req.param('q') : '';
        sails.log.info('Fetch project inspection tours List, projectId:', projectId, `search :${searchTerm}`);

        let filter = {
            project_ref: [projectId],
            finalised: true,
        };

        filter = {
            where: filter,
            skip : pageNumber * pageSize,
            limit: pageSize
        }
        if(searchTerm){
            filter.where.searchTerm = searchTerm;
        }

        let defaultResponse = {
            projectId,
            q: searchTerm,
            pageSize,
            pageNumber,
            project_inspection_tours: [],
            common_checklist: [],
            rail_checklist: [],
            industrial_checklist: [],
            total_record_count:0
        };

        let {projectInspectionTours, commonChecklist, railChecklist, industrialChecklist, total_record_count} = await getInspectionTours(filter, 'DESC', false, true);
        projectInspectionTours = await attachProfilePicWithUserRefInfo(projectInspectionTours);

        return successResponse(res, {...defaultResponse,
            project_inspection_tours: projectInspectionTours,
            common_checklist: commonChecklist,
            rail_checklist: railChecklist,
            industrial_checklist: industrialChecklist,
            total_record_count
        });
    },

    updateChecklistToCloseOutItem: async (req, res) => {
        let id = +req.param('id');
        let itemInfo = cleanItem(req.body.item_info);
        let checklistType = req.body.checklist_type;

        let {validationError} = closeOutRequestValidate(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
        }

        sails.log.info('Fetch project inspection tour: ', id);
        let projectInspectionTour =  await sails.models.projectinspectiontour.findOne({
            where: {id: id},
            select: ['industrial_checklist', 'additional_checklist', 'common_checklist', 'rail_checklist', 'user_ref', 'project_ref', 'record_id', 'createdAt']
        });
        [projectInspectionTour] = await populateUserRefs([projectInspectionTour], 'user_ref', ['id', 'first_name', 'last_name', 'email']);
        [projectInspectionTour] = await populateProjectRefs([projectInspectionTour], 'project_ref', ['id', 'name', 'custom_field', 'project_type', 'parent_company', 'project_number']);

        return processInspectionTourItemCloseOut(req, res, projectInspectionTour, checklistType, itemInfo);
    },

    closeOutChecklistItemAction: async (req, res) => {
        let id = +req.param('id');
        let itemInfo = cleanItem(req.body.item_info);
        let checklistType = req.body.checklist_type;
        let user = req.user;

        let {validationError} = closeOutRequestValidate(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
        }

        sails.log.info(`[closeOutChecklistItemAction] Fetch project IT checklist report ID: ${id}, checklistType: ${checklistType}`);

        let projectInspectionTour =  await sails.models.projectinspectiontour.findOne({
            where: {id: id},
            select: ['industrial_checklist', 'additional_checklist', 'common_checklist', 'rail_checklist', 'user_ref', 'project_ref', 'record_id', 'createdAt']
        });
        [projectInspectionTour] = await populateUserRefs([projectInspectionTour], 'user_ref', ['id', 'first_name', 'last_name', 'email']);
        [projectInspectionTour] = await populateProjectRefs([projectInspectionTour], 'project_ref', ['id', 'name', 'custom_field', 'project_type', 'parent_company', 'project_number']);

        let checklist = projectInspectionTour[checklistType];

        let isAllowedToCloseOut = (checklist || []).findIndex((item, index) => item.question_id == itemInfo.question_id && (
            item.responsible_user_ref === user.id
        )) !== -1;

        if(!isAllowedToCloseOut) {
            sails.log.info('User not allowed to closed out checklist report item.', user.id);
            return successResponse(res, {message: 'Unable to closeout, Access denied.'});
        }

        return processInspectionTourItemCloseOut(req, res, projectInspectionTour, checklistType, itemInfo);
    },

    dashboardOfInspectionTour: async (req, res) => {
        let projectId = +req.param('projectId');
        let type = req.param('type');
        let totalPages = 1;
        let where = {
            project_ref: [projectId],
            finalised: true
        };

        if(req.body.from_date && req.body.to_date) {
            where.createdAt = {'from': +req.body.from_date, 'to': +req.body.to_date};
        }

        sails.log.info('Fetch inspection tour filter.', where);
        let {projectInspectionTours, commonChecklist, railChecklist, industrialChecklist} = await getInspectionTours({where: where}, 'ASC');
        sails.log.info(`${projectInspectionTours.length} inspection tours found.`);

        if (projectInspectionTours.length) {
            let projectInfo = await sails.models.project.findOne({
                where: {id: projectId},
                select: ['name', 'project_number', 'project_category', 'parent_company', 'client', 'contractor', 'project_type', 'createdAt', 'custom_field']
            });
            let projectTimezone = await getTimezone(projectId, 'project', projectInfo);
            let isIndustrialProject = (projectInfo.project_type === 'industrial');
            //rating labels
            let goodRatingLabel = `Yes`;
            let fairRatingLabel = `Fair`;
            let poorRatingLabel = `No`;

            if (isIndustrialProject) {
                goodRatingLabel = `Good`;
                fairRatingLabel = `Fair`;
                poorRatingLabel = `Poor`;
            }

            let ratingsByGroupChartData = [];
            let ratingsByGroupChartColumns = ['group', goodRatingLabel, fairRatingLabel, poorRatingLabel];
            let totalRatingPerCategory = {};
            let goodRatingItemsCount = 0;
            let poorRatingItemsCount = 0;
            let fairRatingItemsCount = 0;
            let totalClosedOutItems = 0;
            let totalFairToCloseOut = 0;
            let totalPoorToCloseOut = 0;
            let scatteredChartData = [];
            let scatterPlotXAxisValues = [''];
            let daysArr = [];
            let metaChecklist = [];
            let groupChartDataOfWorseItems = [];
            let inspectionTourCreatedDate = '';
            let datePrefix = '';
            let previousInspectionTourCreatedDate = '';
            let progressIssuesChartData = [];
            let progressIssuesChartXAxisValues = [''];
            let progressIssuesCountArr = [];
            (projectInspectionTours || []).forEach(function(inspectionTour, i) {
                let inspectionTourChecklist = [];
                if (isIndustrialProject) {
                    inspectionTourChecklist = [...inspectionTour.industrial_checklist, ...inspectionTour.additional_checklist];
                    metaChecklist = [...industrialChecklist, ...inspectionTour.additional_checklist];
                } else {
                    inspectionTourChecklist = [...inspectionTour.common_checklist];
                    metaChecklist = [...commonChecklist];
                    if (projectInfo.project_type === 'rail') {
                        inspectionTourChecklist.push(...inspectionTour.rail_checklist);
                        metaChecklist = [...commonChecklist, ...railChecklist];
                    }
                }

                metaChecklist = metaChecklist.reduce((obj, que) => {
                    obj[que.question_id] = que;
                    return obj;
                }, {});

                let chartGroup = inspectionTour.createdAt;
                let poorClosedOutDays = [];
                let fairClosedOutDays = [];
                let scatteredChartItem = {};
                let progressIssuesChartItem = {};
                let totalPoorIssues = 0;
                let totalFairIssues = 0;
                inspectionTourChecklist.forEach(function(item, j) {
                    goodRatingItemsCount += (item.answer === goodRatingLabel.toLowerCase()) ? 1 : 0;
                    poorRatingItemsCount += (item.answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    fairRatingItemsCount += (item.answer === 'fair') ? 1 : 0;

                    if ((poorRatingLabel.toLowerCase() == item.answer || item.answer === 'fair') && item.close_out && Object.keys(item.close_out).length) {
                        let hours = (+item.close_out.close_out_at - +inspectionTour.createdAt) / (60*60*1000);
                        if ([poorRatingLabel.toLowerCase()].includes(item.answer)) {
                            let day = hours/24;
                            poorClosedOutDays.push(day);
                        } else {
                            let day = hours/24;
                            fairClosedOutDays.push(day);
                        }
                        totalClosedOutItems += (item.answer === poorRatingLabel.toLowerCase() || item.answer === 'fair') ? 1 : 0;
                    } else {
                        totalFairToCloseOut += (item.answer === 'fair') ? 1 : 0;
                        totalPoorToCloseOut += (item.answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    }

                    if (poorRatingLabel.toLowerCase() == item.answer || item.answer === 'fair') {
                        if (poorRatingLabel.toLowerCase() == item.answer) {
                            totalPoorIssues += 1;
                        } else {
                            totalFairIssues += 1;
                        }
                    }


                    if ([goodRatingLabel.toLowerCase(),poorRatingLabel.toLowerCase(),'fair'].includes(item.answer)) {
                        let dataItem = ratingsByGroupChartData.find(dItem => dItem.group === chartGroup) || {};
                        if (Object.keys(dataItem).length) {
                            dataItem[capitalize(item.answer)] += 1;
                            totalRatingPerCategory[chartGroup] += 1;
                            let dataItemIndex = ratingsByGroupChartData.findIndex(dItem => dItem.group === chartGroup);
                            ratingsByGroupChartData[dataItemIndex] = dataItem;
                        } else {
                            totalRatingPerCategory[chartGroup] = 0;
                            totalRatingPerCategory[chartGroup] += 1;
                            dataItem = {
                                'group': chartGroup
                            };
                            dataItem[goodRatingLabel] = 0;
                            dataItem[fairRatingLabel] = 0;
                            dataItem[poorRatingLabel] = 0;

                            dataItem[capitalize(item.answer)] += 1;
                            ratingsByGroupChartData.push(dataItem);
                        }

                        let itemCategory = metaChecklist[item.question_id].category;
                        if ([poorRatingLabel.toLowerCase(), 'fair'].includes(item.answer)) {
                            let dataItem = groupChartDataOfWorseItems.find(dItem => dItem.group === itemCategory) || {};
                            if (Object.keys(dataItem).length) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['total'] += 1;
                                let dataItemIndex = groupChartDataOfWorseItems.findIndex(dItem => dItem.group === itemCategory);
                                groupChartDataOfWorseItems[dataItemIndex] = dataItem;
                            } else {
                                dataItem = {
                                    'group': itemCategory
                                };
                                dataItem[fairRatingLabel] = 0;
                                dataItem[poorRatingLabel] = 0;
                                dataItem['total'] = 1;
                                dataItem[capitalize(item.answer)] += 1;
                                groupChartDataOfWorseItems.push(dataItem);
                            }
                        }
                    }
                });

                inspectionTourCreatedDate = moment(+inspectionTour.createdAt).format('Do MMM YY');
                datePrefix = (previousInspectionTourCreatedDate == inspectionTourCreatedDate) ? datePrefix+' ' : '';
                scatteredChartItem.date = scatteredChartData.length + 1;
                scatterPlotXAxisValues.push(datePrefix+inspectionTourCreatedDate);

                let poorAvgDays = (poorClosedOutDays.length) ? (poorClosedOutDays.reduce(function(a, b){return a + b;}, 0))/poorClosedOutDays.length : 0;
                daysArr.push(poorAvgDays);
                scatteredChartItem[poorRatingLabel] = poorAvgDays;

                let fairAvgDays = (fairClosedOutDays.length) ? (fairClosedOutDays.reduce(function(a, b){return a + b;}, 0))/fairClosedOutDays.length : 0;
                daysArr.push(fairAvgDays);
                scatteredChartItem[fairRatingLabel] = fairAvgDays;
                scatteredChartData.push(scatteredChartItem);

                //Progress of issues
                progressIssuesChartItem.date =  progressIssuesChartData.length + 1;
                progressIssuesChartXAxisValues.push(datePrefix+inspectionTourCreatedDate);
                progressIssuesChartItem[poorRatingLabel] = totalPoorIssues;
                progressIssuesChartItem[fairRatingLabel] = totalFairIssues;
                progressIssuesCountArr.push(totalPoorIssues + totalFairIssues);
                progressIssuesChartData.push(progressIssuesChartItem);

                previousInspectionTourCreatedDate = inspectionTourCreatedDate;
            });

            //get max total
            let progressIssuesCharMaxNumber = Math.max(...progressIssuesCountArr);

            //get top 10 items by total count
            groupChartDataOfWorseItems = (groupChartDataOfWorseItems.sort((a,b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0))).slice(0, 10);
            let maxRatingInWorse = (groupChartDataOfWorseItems.length) ? groupChartDataOfWorseItems[0].total : 0;

            let previousDate = '';
            let prefix = '';
            ratingsByGroupChartData = (ratingsByGroupChartData || []).map((data, i) => {
                if (data.group && +(data.group)) {
                    let date = moment(+data.group).format('Do MMM YY');
                    prefix = (previousDate == date) ? prefix+' ' : '';
                    data.group =  prefix+date;
                    previousDate = date;
                }
                return data;
            });
            let maxRating = 0;
            if (Object.keys(totalRatingPerCategory).length) {
                let keyWithMaxRating = Object.keys(totalRatingPerCategory).reduce((a, b) => totalRatingPerCategory[a] > totalRatingPerCategory[b] ? a : b);
                maxRating = totalRatingPerCategory[keyWithMaxRating];
            }

            let maxNumber = Math.max(...daysArr);
            let openClosedBarChartData = [];
            if(isIndustrialProject) {
                openClosedBarChartData.push({name: `Open (${poorRatingLabel})`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`});
            } else {
                openClosedBarChartData.push({name: `Open`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`});
            }
            openClosedBarChartData.push({name: `Open (${fairRatingLabel})`, value: totalFairToCloseOut, type: `Rating: ${fairRatingLabel}`});
            openClosedBarChartData.push({name: "Closed", value: totalClosedOutItems, type: ''});

            let hasAllClosed = (!totalPoorToCloseOut && !totalFairToCloseOut && totalClosedOutItems) ? `All ${totalClosedOutItems} items closed out` : '';

            let totalRatingCount = +(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
            let goodRatingPercentage = parseInt(100/(totalRatingCount)*(goodRatingItemsCount));
            let fairRatingPercentage = parseInt(100/(totalRatingCount)*(fairRatingItemsCount));
            let poorRatingPercentage = parseInt(100/(totalRatingCount)*(poorRatingItemsCount));

            let donutChartData = [
                {name: `${goodRatingLabel} (${goodRatingItemsCount})`, count: goodRatingItemsCount, percentage: goodRatingPercentage, color: `${goodRatingColor}`, label: goodRatingLabel},
                {name: `${fairRatingLabel} (${fairRatingItemsCount})`, count: fairRatingItemsCount, percentage: fairRatingPercentage, color: `${fairRatingColor}`, label: fairRatingLabel},
                {name: `${poorRatingLabel} (${poorRatingItemsCount})`, count: poorRatingItemsCount, percentage: poorRatingPercentage, color: `${poorRatingColor}`, label: poorRatingLabel}
            ];

            let { project_logo_file, companyName } = await getCompanyInfo(projectInfo);

            sails.log.info('generating inspection tours dashboard. Total Pages: ', totalPages);

            let openClosedBarChartWidth = '600';
            let donutChartWidth = 400;
            let donutChartHeight = 315;
            let donutLegendsAdj = -360;
            let donutLegendh = -240;
            let donutChartCentroidAdj = -40;
            let ratingsByGroupChartWidth = 537;
            let ratingsByGroupChartHeight = 200;
            let scatterPlotWidth = 537;
            let scatterPlotHeight = 235;
            let labelFontSize = '.4em';
            let marginTop = '0px';
            let stackHeight = 10;
            let adjustments = {
                adj1: -2,
                adj2: 10,
                adj3: -14,
                adj4: 6,
                adj5: -2,
                adj6: 1,
                adj7: 18,
                adj8: (hasAllClosed) ? 5.2 : 4.7,
                adj9: (hasAllClosed) ? 18 : 17,
                adj10: (hasAllClosed) ? -3 : 0,
                adj11: -6,
                adj12: -3.5,
                adj13: 3,
            };
            let problemAreasChartWidth = 300;
            let problemAreasChartHeight = 200;
            let stackChartLegendAdj = 20;
            let progressIssuesChartWidth = 331, progressIssuesChartHeight = 235;
            let scatterPlotLegendsAdj = 10;
            let progressIssuesChartLegendsAdj = 10;
            let donutViewbox = "10 -13 260 350";
            if (type === 'pdf') {
                openClosedBarChartWidth = '1000';
                donutChartWidth = 450;
                donutChartHeight = 360;
                donutViewbox = "50 -13 260 350";
                donutLegendsAdj = -480;
                donutLegendh = -280;
                donutChartCentroidAdj = -50;
                ratingsByGroupChartWidth = 700;
                ratingsByGroupChartHeight = 250;
                scatterPlotWidth = 700;
                scatterPlotHeight = 250;
                labelFontSize = '.4em';
                marginTop = '0px';
                stackHeight = 7;
                adjustments = {
                    adj1: -2,
                    adj2: 7,
                    adj3: -10,
                    adj4: 6,
                    adj5: -2.5,
                    adj6: .5,
                    adj7: 18,
                    adj8: 6,
                    adj9: 14,
                    adj10:0,
                    adj11: -6,
                    adj12: -3.5,
                    adj13: 3,
                }
                progressIssuesChartWidth = 450;
                progressIssuesChartHeight = 245;

                scatterPlotLegendsAdj = 5;
                progressIssuesChartLegendsAdj = 5;

                problemAreasChartWidth = 450;
                problemAreasChartHeight = 245;
                stackChartLegendAdj = 70;
            }

            let openClosedBarChartLegends = [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}];
            if (!isIndustrialProject) {
                adjustments.adj2 = 10;
                openClosedBarChartLegends = [{ name: poorRatingLabel, color: `${colorRed}`}];
            }

            let form_template = `pages/inspection-tour-dashboard-page`;
            let html = await sails.renderView(form_template, {
                title: `Inspection Dashboard`,
                report_from: (+req.body.from_date) ? momentTz(+req.body.from_date).tz(projectTimezone).format('DD/MM/YYYY') : '',
                report_to: (+req.body.to_date) ? momentTz(+req.body.to_date).tz(projectTimezone).format('DD/MM/YYYY') : '',
                company_name: companyName,
                project_number: projectInfo.project_number,
                project_name: projectInfo.name,
                project_logo_file,
                report_type: type,
                total_inspections: projectInspectionTours.length,
                totalRatedItems: goodRatingItemsCount + poorRatingItemsCount + fairRatingItemsCount,
                goodRatingItemsCount,
                poorRatingItemsCount,
                fairRatingItemsCount,
                totalClosedOutItems,
                totalFairToCloseOut,
                totalPoorToCloseOut,
                isIndustrialProject,
                totalPages,
                openClosedBarChart: await getOpenClosedBarChart(openClosedBarChartData, hasAllClosed, openClosedBarChartWidth, stackHeight, labelFontSize, marginTop, adjustments, [`${colorRed}`, `${colorYellow}`, "#2c961b"], openClosedBarChartLegends),
                donutChart: await getDonutChart(donutChartData, donutChartWidth, donutChartHeight, donutChartCentroidAdj, 100, 60, '0', `${goodRatingLabel} (${goodRatingItemsCount})`, `${fairRatingLabel} (${fairRatingItemsCount})`, `${poorRatingLabel} (${poorRatingItemsCount})`, false, isIndustrialProject, 0, donutLegendsAdj, donutLegendh),
                ratingsByGroupChart: await getStackedBarChart(ratingsByGroupChartData, ratingsByGroupChartColumns, maxRating, ratingsByGroupChartWidth, ratingsByGroupChartHeight, [`${colorGreen}`, `${colorYellow}`, `${colorRed}`], '7px','Items', isIndustrialProject, true, 25),
                scatterPlot: await getScatterPlot(scatteredChartData, 'date', scatterPlotXAxisValues, [fairRatingLabel, poorRatingLabel], maxNumber, scatterPlotWidth, scatterPlotHeight, '7px', '', 'Days to closeout', scatterPlotLegendsAdj, isIndustrialProject, [`${colorYellow}`, `${colorRed}`], true, "220 0 250 350"),
                percentageGaugeChart: await getPercentageGaugeChart(200, 90, (goodRatingItemsCount + poorRatingItemsCount + fairRatingItemsCount), goodRatingItemsCount, 'Project Rating'),
                groupChartDataOfWorseItems: await getStackedBarChart(groupChartDataOfWorseItems, ['group', fairRatingLabel, poorRatingLabel], maxRatingInWorse, problemAreasChartWidth, problemAreasChartHeight, [`${colorYellow}`, `${colorRed}`], '7px', 'Issues', isIndustrialProject, true, 88),
                progressIssuesChart: await getScatterPlot(progressIssuesChartData, 'date', progressIssuesChartXAxisValues,[fairRatingLabel, poorRatingLabel], progressIssuesCharMaxNumber, progressIssuesChartWidth, progressIssuesChartHeight, '7px', '', 'Issues', progressIssuesChartLegendsAdj, isIndustrialProject, [`${colorYellow}`, `${colorRed}`], false, "150 5 150 330"),
                layout: false
            });

            if (type === 'pdf') {
                let fileName = 'Inspections-Tour-Dashboard-' + moment().format('MM-DD-YYYY');
                return await instantPdfGenerator(req, res, html, 'inspection-tour-p-dashboard', fileName, req.headers['user-agent'], { format: 'A3', landscape: true }, 'url');
            }

            sails.log.info('Rendering html view');
            return res.send(html);
        }

        sails.log.info('No inspection records found between the selected duration on the project.');
        return res.send("<p style='text-align: center;margin-top: 30px;margin-bottom: auto;'>No inspection records found between the selected duration on the project.</p>");
    },

    companyDashboardOfInspectionTour: async (req, res) => {
        let companyId = +req.param('companyId');
        let type = req.param('type');
        let totalPages = 1;
        let where = {
            project_ref: (req.body.selected_projects || []),
            finalised: true
        };

        if (req.body.from_date && req.body.to_date) {
            where.createdAt = {'from': +req.body.from_date, 'to': +req.body.to_date};
        }

        sails.log.info('Fetch inspection tour filter.', where, "Requesting company:", companyId);
        let {projectInspectionTours, commonChecklist, railChecklist, industrialChecklist} = await getInspectionTours({where:where}, 'DESC');
        if (projectInspectionTours.length) {
            let companyTimezone = await getTimezone(companyId, 'company');
            //sort by project name
            projectInspectionTours = (projectInspectionTours || []).sort((a,b) => (a.project_ref.name > b.project_ref.name) ? 1 : ((b.project_ref.name > a.project_ref.name) ? -1 : 0));
            let industrialProjectInspectionTours = (projectInspectionTours || []).filter(it => (it.project_ref.project_type && it.project_ref.project_type === 'industrial'));
            sails.log.info(`get industrial projects from inspection tour records.`);
            let industrialProjects = getProjectsFromRecords(industrialProjectInspectionTours);
            let normalProjectInspectionTours = (projectInspectionTours || []).filter(it => (it.project_ref.project_type && it.project_ref.project_type != 'industrial'));
            sails.log.info(`get normal projects from inspection tour records.`);
            let normalProjects = getProjectsFromRecords(normalProjectInspectionTours);

            //rating labels
            let goodRatingLabel = `Good`;
            let fairRatingLabel = `Fair`;
            let poorRatingLabel = `Poor`;

            let totalRatingPerCategory = {};
            let goodRatingItemsCount = 0;
            let poorRatingItemsCount = 0;
            let fairRatingItemsCount = 0;
            let totalClosedOutItems = 0;
            let totalFairToCloseOut = 0;
            let totalPoorToCloseOut = 0;
            let metaChecklist = [];
            let groupChartDataOfWorseItems = [];
            let inspectionTourCreatedDate = '';
            let datePrefix = '';
            let previousInspectionTourCreatedDate = '';
            let avgCloseoutTimePerProjectPoor = [];
            let avgCloseoutTimePerProjectFair = [];
            let poorIssuesPerProject = [];
            let fairIssuesPerProject = [];
            let ratioPerProjectChartData = [];
            let maxCloseoutTimePerProject = 0;
            let maxIssuesPerProject = 0;
            let existingRecord = undefined;
            let existingRecordIndex = undefined;
            let weeklyUnsItemsChartData = [];
            if (industrialProjectInspectionTours.length) {
                weeklyUnsItemsChartData = prepareWeeksData(mathMinMax(industrialProjectInspectionTours, 'createdAt', 'min'), mathMinMax(industrialProjectInspectionTours, 'createdAt', 'max'));
            }
            let weeklyUnsItemsChartXAxisValues = [''];
            let weeklyUnsItemsMaxNumber = 0;
            let projectGroup = [];
            (industrialProjectInspectionTours || []).forEach(function (inspectionTour, i) {
                let inspectionTourChecklist = [...inspectionTour.industrial_checklist, ...inspectionTour.additional_checklist];
                metaChecklist = [...industrialChecklist, ...inspectionTour.additional_checklist];
                metaChecklist = metaChecklist.reduce((obj, que) => {
                    obj[que.question_id] = que;
                    return obj;
                }, {});

                let chartGroup = inspectionTour.createdAt;
                let poorClosedOutDays = [];
                let fairClosedOutDays = [];
                let totalGood = 0;
                let totalPoorIssues = 0;
                let totalFairIssues = 0;
                inspectionTourChecklist.forEach(function (item, j) {
                    goodRatingItemsCount += (item.answer === goodRatingLabel.toLowerCase()) ? 1 : 0;
                    poorRatingItemsCount += (item.answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    fairRatingItemsCount += (item.answer === 'fair') ? 1 : 0;

                    if ((poorRatingLabel.toLowerCase() == item.answer || item.answer === 'fair') && item.close_out && Object.keys(item.close_out).length) {
                        let hours = (+item.close_out.close_out_at - +inspectionTour.createdAt) / (60 * 60 * 1000);
                        if ([poorRatingLabel.toLowerCase()].includes(item.answer)) {
                            let day = hours/24;
                            poorClosedOutDays.push(day);
                        } else {
                            let day = hours/24;
                            fairClosedOutDays.push(day);
                        }

                        totalClosedOutItems += (item.answer === poorRatingLabel.toLowerCase() || item.answer === 'fair') ? 1 : 0;
                    } else {
                        totalFairToCloseOut += (item.answer === 'fair') ? 1 : 0;
                        totalPoorToCloseOut += (item.answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    }

                    if (poorRatingLabel.toLowerCase() == item.answer || item.answer === 'fair') {
                        if (poorRatingLabel.toLowerCase() == item.answer) {
                            totalPoorIssues += 1;
                        } else {
                            totalFairIssues += 1;
                        }
                    }

                    if ([goodRatingLabel.toLowerCase(), poorRatingLabel.toLowerCase(), 'fair'].includes(item.answer)) {
                        let itemCategory = metaChecklist[item.question_id].category;
                        if ([poorRatingLabel.toLowerCase(), 'fair'].includes(item.answer)) {
                            let dataItem = groupChartDataOfWorseItems.find(dItem => dItem.group === itemCategory) || {};
                            if (Object.keys(dataItem).length) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['total'] += 1;
                                let dataItemIndex = groupChartDataOfWorseItems.findIndex(dItem => dItem.group === itemCategory);
                                groupChartDataOfWorseItems[dataItemIndex] = dataItem;
                            } else {
                                dataItem = {
                                    'group': itemCategory
                                };
                                dataItem[fairRatingLabel] = 0;
                                dataItem[poorRatingLabel] = 0;
                                dataItem['total'] = 1;
                                dataItem[capitalize(item.answer)] += 1;
                                groupChartDataOfWorseItems.push(dataItem);
                            }
                        } else {
                            totalGood += 1;
                        }

                        let projectName = inspectionTour.project_ref.name;
                        dataItem = ratioPerProjectChartData.find(dItem => dItem.group === projectName) || {};
                        if (Object.keys(dataItem).length) {
                            if ([poorRatingLabel.toLowerCase(), 'fair'].includes(item.answer)) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['max_total'] += 1;
                            }
                            dataItem['total'] += 1;
                            let dataItemIndex = ratioPerProjectChartData.findIndex(dItem => dItem.group === projectName);
                            ratioPerProjectChartData[dataItemIndex] = dataItem;
                        } else {
                            dataItem = {
                                'group': projectName
                            };
                            dataItem[fairRatingLabel] = 0;
                            dataItem[poorRatingLabel] = 0;
                            dataItem['total'] = 1;
                            dataItem['max_total'] = 0;
                            if ([poorRatingLabel.toLowerCase(), 'fair'].includes(item.answer)) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['max_total'] = 1;
                            }
                            ratioPerProjectChartData.push(dataItem);
                        }
                    }
                });

                let weeklyUnsItem = weeklyUnsItemsChartData.find(item => (+inspectionTour.createdAt >= item.start_of_week && +inspectionTour.createdAt <= item.end_of_week));
                let weeklyUnsItemIndex = weeklyUnsItemsChartData.findIndex(item => (inspectionTour.createdAt >= item.start_of_week && inspectionTour.createdAt <= item.end_of_week ));
                if (weeklyUnsItem && weeklyUnsItemIndex != -1) {
                    weeklyUnsItem['unsatisfactory_items'] += (totalFairIssues + totalPoorIssues);
                    weeklyUnsItem['total_items'] += (totalGood + totalFairIssues + totalPoorIssues);
                    weeklyUnsItemsChartData[weeklyUnsItemIndex] = weeklyUnsItem;
                }

                //prepare variable poorIssuesPerProject
                existingRecord = poorIssuesPerProject.find(record => record.group === inspectionTour.project_ref.name);
                if (existingRecord) {
                    existingRecordIndex = poorIssuesPerProject.findIndex(record => record.group === inspectionTour.project_ref.name);
                    existingRecord["rating"].push(totalPoorIssues);
                    poorIssuesPerProject[existingRecordIndex] = existingRecord;
                } else {
                    poorIssuesPerProject.push({
                        "group": inspectionTour.project_ref.name,
                        "rating": [totalPoorIssues]
                    })
                }

                //prepare variable fairIssuesPerProject
                existingRecord = fairIssuesPerProject.find(record => record.group === inspectionTour.project_ref.name);
                if (existingRecord) {
                    existingRecordIndex = fairIssuesPerProject.findIndex(record => record.group === inspectionTour.project_ref.name);
                    existingRecord["rating"].push(totalFairIssues);
                    fairIssuesPerProject[existingRecordIndex] = existingRecord;
                } else {
                    fairIssuesPerProject.push({
                        "group": inspectionTour.project_ref.name,
                        "rating": [totalFairIssues]
                    })
                }

                inspectionTourCreatedDate = moment(+inspectionTour.createdAt).format('Do MMM YY');
                datePrefix = (previousInspectionTourCreatedDate == inspectionTourCreatedDate) ? datePrefix + ' ' : '';

                if (projectGroup.indexOf(inspectionTour.project_ref.name) == -1) {
                    projectGroup.push(inspectionTour.project_ref.name);
                }
                let poorAvgDays = (poorClosedOutDays.length) ? (poorClosedOutDays.reduce(function (a, b) {
                    return a + b;
                }, 0)) / poorClosedOutDays.length : 0;
                existingRecord = avgCloseoutTimePerProjectPoor.find(record => record.group === inspectionTour.project_ref.name);
                if (existingRecord) {
                    existingRecordIndex = avgCloseoutTimePerProjectPoor.findIndex(record => record.group === inspectionTour.project_ref.name);
                    existingRecord["rating"].push(poorAvgDays);
                    avgCloseoutTimePerProjectPoor[existingRecordIndex] = existingRecord;
                } else {
                    avgCloseoutTimePerProjectPoor.push({
                        "group": inspectionTour.project_ref.name,
                        "rating": [poorAvgDays]
                    })
                }

                let fairAvgDays = (fairClosedOutDays.length) ? (fairClosedOutDays.reduce(function (a, b) {
                    return a + b;
                }, 0)) / fairClosedOutDays.length : 0;
                existingRecord = avgCloseoutTimePerProjectFair.find(record => record.group === inspectionTour.project_ref.name);
                if (existingRecord) {
                    existingRecordIndex = avgCloseoutTimePerProjectFair.findIndex(record => record.group === inspectionTour.project_ref.name);
                    existingRecord["rating"].push(fairAvgDays);
                    avgCloseoutTimePerProjectFair[existingRecordIndex] = existingRecord;
                } else {
                    avgCloseoutTimePerProjectFair.push({
                        "group": inspectionTour.project_ref.name,
                        "rating": [fairAvgDays]
                    })
                }

                previousInspectionTourCreatedDate = inspectionTourCreatedDate;
            });

            weeklyUnsItemsChartData = weeklyUnsItemsChartData.reduce((arr, item) => {
                if (item.total_items) {
                    weeklyUnsItemsChartXAxisValues.push(item['week']);
                    item.week = arr.length + 1;
                    item.percent = 100/(item.total_items)*(item.unsatisfactory_items);
                    arr.push(item);
                    weeklyUnsItemsMaxNumber = (weeklyUnsItemsMaxNumber < item.percent) ? item.percent : weeklyUnsItemsMaxNumber;
                }
                return arr;
            }, []);

            poorIssuesPerProject = poorIssuesPerProject.map(item => {
                item['rating'] = parseInt(item['rating'].reduce((a, b) => a + b, 0)/item['rating'].length);
                maxIssuesPerProject = (maxIssuesPerProject < item['rating']) ? item['rating'] : maxIssuesPerProject;
                return item;
            });

            fairIssuesPerProject = fairIssuesPerProject.map(item => {
                item['rating'] = parseInt(item['rating'].reduce((a, b) => a + b, 0)/item['rating'].length);
                maxIssuesPerProject = (maxIssuesPerProject < item['rating']) ? item['rating'] : maxIssuesPerProject;
                return item;
            });

            avgCloseoutTimePerProjectPoor = (avgCloseoutTimePerProjectPoor || []).reduce((arr, item) => {
                let ratCount = item['rating'].reduce((a, b) => a + b, 0)/item['rating'].length;
                if (ratCount) {
                    item['rating'] = ratCount;
                    maxCloseoutTimePerProject = (maxCloseoutTimePerProject < item['rating']) ? item['rating'] : maxCloseoutTimePerProject;
                    arr.push(item);
                }
                return arr;
            }, []);
            let maxCloseoutTimePerProjectPoor = mathMinMax(avgCloseoutTimePerProjectPoor, 'rating', 'max');

            avgCloseoutTimePerProjectFair = (avgCloseoutTimePerProjectFair || []).reduce((arr, item) => {
                let ratCount = item['rating'].reduce((a, b) => a + b, 0) / item['rating'].length;
                if (ratCount) {
                    item['rating'] = ratCount;
                    maxCloseoutTimePerProject = (maxCloseoutTimePerProject < item['rating']) ? item['rating'] : maxCloseoutTimePerProject;
                    arr.push(item);
                }
                return arr;
            }, []);
            let maxCloseoutTimePerProjectFair = mathMinMax(avgCloseoutTimePerProjectFair, 'rating', 'max');

            maxCloseoutTimePerProject = Math.max(maxCloseoutTimePerProjectPoor, maxCloseoutTimePerProjectFair);

            //get top 10 items by total count
            groupChartDataOfWorseItems = (groupChartDataOfWorseItems.sort((a, b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0))).slice(0, 10);
            let maxRatingInWorse = (groupChartDataOfWorseItems.length) ? groupChartDataOfWorseItems[0].total : 0;

            let previousDate = '';
            let prefix = '';
            let openClosedBarChartData = [];
            openClosedBarChartData.push({
                name: `Open (${poorRatingLabel})`,
                value: totalPoorToCloseOut,
                type: `Rating: ${poorRatingLabel}`
            });
            openClosedBarChartData.push({
                name: `Open (${fairRatingLabel})`,
                value: totalFairToCloseOut,
                type: `Rating: ${fairRatingLabel}`
            });
            openClosedBarChartData.push({name: "Closed", value: totalClosedOutItems, type: ''});

            let hasAllClosed = (!totalPoorToCloseOut && !totalFairToCloseOut && totalClosedOutItems) ? `All ${totalClosedOutItems} items closed out` : '';

            let totalRatingCount = +(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
            let goodRatingPercentage = parseInt(100 / (totalRatingCount) * (goodRatingItemsCount));
            let fairRatingPercentage = parseInt(100 / (totalRatingCount) * (fairRatingItemsCount));
            let poorRatingPercentage = parseInt(100 / (totalRatingCount) * (poorRatingItemsCount));

            let donutChartData = [
                {
                    name: `${goodRatingLabel} (${goodRatingItemsCount})`,
                    count: goodRatingItemsCount,
                    percentage: goodRatingPercentage,
                    color: `${goodRatingColor}`,
                    label: goodRatingLabel
                },
                {
                    name: `${fairRatingLabel} (${fairRatingItemsCount})`,
                    count: fairRatingItemsCount,
                    percentage: fairRatingPercentage,
                    color: `${fairRatingColor}`,
                    label: fairRatingLabel
                },
                {
                    name: `${poorRatingLabel} (${poorRatingItemsCount})`,
                    count: poorRatingItemsCount,
                    percentage: poorRatingPercentage,
                    color: `${poorRatingColor}`,
                    label: poorRatingLabel
                }
            ];

            let openClosedBarChartWidth = '600';
            let donutViewBox = "70 10 280 310";
            let donutChartWidth = 400;
            let donutChartHeight = 280;
            let donutChartCentroidAdj = -28;
            let donutLegendsAdj = -360;
            let donutLegendh = -190;
            let lollipopChartWidth = 705;
            let lollipopChartHeight = 278;
            let ratioPerProjectChartWidth = 472;
            let ratioPerProjectChartHeight = 193;
            let problemAreasChartWidth = 472;
            let problemAreasChartHeight = 193;
            let stackChartLegendAdj = 85;
            let avgCloseoutTimePerProjectWidth = 1120;
            let avgCloseoutTimePerProjectHeight = 400;
            let weeklyUnsItemsChartWidth = 1040;
            let weeklyUnsItemsChartHeight = 300;
            let avgCloseoutLegendAdj = 10;
            let labelFontSize = '.4em';
            let marginTop = '0px';
            let stackHeight = 10;
            let adjustments = {
                adj1: -2,
                adj2: 10,
                adj3: -14,
                adj4: 6,
                adj5: -2,
                adj6: 1,
                adj7: 18,
                adj8: (hasAllClosed) ? 5.2 : 4.7,
                adj9: (hasAllClosed) ? 18 : 17,
                adj10: (hasAllClosed) ? 1 : 0,
                adj11: -6,
                adj12: -3.5,
                adj13: 3,
            };

            if (type === 'pdf') {
                openClosedBarChartWidth = '1000';
                donutChartWidth = 500;
                donutChartHeight = 350;
                donutChartCentroidAdj = -70;
                donutLegendsAdj = -510;
                donutLegendh = -280;
                donutViewBox = "120 50 280 310";
                lollipopChartWidth = 932;
                lollipopChartHeight = 348;
                ratioPerProjectChartWidth = 690;
                ratioPerProjectChartHeight = 280;
                problemAreasChartWidth = 690;
                problemAreasChartHeight = 265;
                stackChartLegendAdj = 110;
                avgCloseoutTimePerProjectWidth = 1400;
                avgCloseoutTimePerProjectHeight = 478;
                weeklyUnsItemsChartWidth = 1400;
                weeklyUnsItemsChartHeight = 350;
                avgCloseoutLegendAdj = 10;
                labelFontSize = '.4em';
                marginTop = '0px';
                stackHeight = 7;
                adjustments = {
                    adj1: -2,
                    adj2: 7,
                    adj3: -10,
                    adj4: 6,
                    adj5: -2.5,
                    adj6: .5,
                    adj7: 18,
                    adj8: 6,
                    adj9: 14,
                    adj10:0,
                    adj11: -6,
                    adj12: -3.5,
                    adj13: 3,
                }
            }

            let ratioPerProjectChartDataMax = 0;
            ratioPerProjectChartData = (ratioPerProjectChartData || []).map(item => {
                ratioPerProjectChartDataMax = (ratioPerProjectChartDataMax < item['max_total']) ? item['max_total'] : ratioPerProjectChartDataMax;
                return item;
            });

            let ind_totalRatedItems = goodRatingItemsCount + poorRatingItemsCount + fairRatingItemsCount;
            let ind_goodRatingItemsCount = goodRatingItemsCount;
            let ind_poorRatingItemsCount = poorRatingItemsCount;
            let ind_fairRatingItemsCount = fairRatingItemsCount;
            let ind_totalClosedOutItems = totalClosedOutItems;
            let ind_totalFairToCloseOut = totalFairToCloseOut;
            let ind_totalPoorToCloseOut = totalPoorToCloseOut;
            let ind_openClosedBarChart = await getOpenClosedBarChart(openClosedBarChartData, hasAllClosed, openClosedBarChartWidth, stackHeight, labelFontSize, marginTop, adjustments, [`${colorRed}`, `${colorYellow}`, "#2c961b"], [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}]);
            let ind_donutChart = await getDonutChart(donutChartData, donutChartWidth, donutChartHeight, donutChartCentroidAdj, 100, 60, donutViewBox, `${goodRatingLabel} (${goodRatingItemsCount})`, `${fairRatingLabel} (${fairRatingItemsCount})`, `${poorRatingLabel} (${poorRatingItemsCount})`, false, true, 0, donutLegendsAdj, donutLegendh);
            let ind_percentageGaugeChart = await getPercentageGaugeChart(200, 90, (goodRatingItemsCount + poorRatingItemsCount + fairRatingItemsCount), goodRatingItemsCount, 'Rating');
            let ind_groupChartDataOfWorseItemsChart = await getStackedBarChart(groupChartDataOfWorseItems, ['group', fairRatingLabel, poorRatingLabel], maxRatingInWorse, problemAreasChartWidth, problemAreasChartHeight, [`${colorYellow}`, `${colorRed}`], '7px', 'Issues', true, true, stackChartLegendAdj);
            let ind_avgCloseoutTimePerProject = await getHoriLollipopChart(projectGroup, avgCloseoutTimePerProjectPoor, avgCloseoutTimePerProjectFair, maxCloseoutTimePerProject, avgCloseoutTimePerProjectWidth, avgCloseoutTimePerProjectHeight,"Days to closeout", '7px', [{ name: 'Fair', color: `${colorYellow}`}, { name: 'Poor', color: `${colorRed}`}], avgCloseoutLegendAdj);
            let ind_issuesPerProjectChart = await getVerLollipopChart(projectGroup, poorIssuesPerProject, fairIssuesPerProject, maxIssuesPerProject, lollipopChartWidth, lollipopChartHeight,"Issues", '7px', [{ name: 'Fair', color: `${colorYellow}`},{ name: 'Poor', color: `${colorRed}`}], 10);
            let ind_ratioPerProjectChart = await getStackedBarChart(ratioPerProjectChartData, ['group', fairRatingLabel, poorRatingLabel], ratioPerProjectChartDataMax, ratioPerProjectChartWidth, ratioPerProjectChartHeight, [`${colorYellow}`, `${colorRed}`],"7px", "Number", true, true, stackChartLegendAdj);
            let ind_weeklyUnsatisfactoryItemsChart = await getScatterPlot(weeklyUnsItemsChartData, 'week', weeklyUnsItemsChartXAxisValues, ['percent'], weeklyUnsItemsMaxNumber, weeklyUnsItemsChartWidth, weeklyUnsItemsChartHeight, '7px', 'Week', '% Unsatisfactory Items', 10, true, [`${colorRed}`], false, "");

            //rating labels
            goodRatingLabel = `Yes`;
            poorRatingLabel = `No`;

            totalRatingPerCategory = {};
            goodRatingItemsCount = 0;
            poorRatingItemsCount = 0;
            totalClosedOutItems = 0;
            totalPoorToCloseOut = 0;
            metaChecklist = [];
            groupChartDataOfWorseItems = [];
            inspectionTourCreatedDate = '';
            datePrefix = '';
            previousInspectionTourCreatedDate = '';
            avgCloseoutTimePerProjectPoor = [];
            avgCloseoutTimePerProjectFair = [];
            maxCloseoutTimePerProject = 0;
            maxIssuesPerProject = 0;
            poorIssuesPerProject = [];
            fairIssuesPerProject = [];
            ratioPerProjectChartData = [];
            existingRecord = undefined;
            existingRecordIndex = undefined;
            weeklyUnsItemsChartData = (normalProjectInspectionTours.length) ? prepareWeeksData(normalProjectInspectionTours[0].createdAt, normalProjectInspectionTours[normalProjectInspectionTours.length - 1].createdAt) : [];
            if (normalProjectInspectionTours.length) {
                weeklyUnsItemsChartData = prepareWeeksData(mathMinMax(normalProjectInspectionTours, 'createdAt', 'min'), mathMinMax(normalProjectInspectionTours, 'createdAt', 'max'));
            }
            weeklyUnsItemsChartXAxisValues = [''];
            weeklyUnsItemsMaxNumber = 0;
            projectGroup = [];
            (normalProjectInspectionTours || []).forEach(function (inspectionTour, i) {
                inspectionTourChecklist = [...inspectionTour.common_checklist];
                metaChecklist = [...commonChecklist];
                if (inspectionTour.project_ref.project_type === 'rail') {
                    inspectionTourChecklist.push(...inspectionTour.rail_checklist);
                    metaChecklist = [...commonChecklist, ...railChecklist];
                }
                metaChecklist = metaChecklist.reduce((obj, que) => {
                    obj[que.question_id] = que;
                    return obj;
                }, {});

                let chartGroup = inspectionTour.createdAt;
                let poorClosedOutDays = [];
                let totalPoorIssues = 0;
                let totalGood = 0;
                inspectionTourChecklist.forEach(function (item, j) {
                    goodRatingItemsCount += (item.answer === goodRatingLabel.toLowerCase()) ? 1 : 0;
                    poorRatingItemsCount += (item.answer === poorRatingLabel.toLowerCase()) ? 1 : 0;

                    if (poorRatingLabel.toLowerCase() == item.answer && item.close_out && Object.keys(item.close_out).length) {
                        let hours = (+item.close_out.close_out_at - +inspectionTour.createdAt) / (60 * 60 * 1000);
                        let day = hours/24;
                        poorClosedOutDays.push(day);
                        totalClosedOutItems += (item.answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    } else {
                        totalPoorToCloseOut += (item.answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    }

                    if (poorRatingLabel.toLowerCase() == item.answer) {
                        totalPoorIssues += 1;
                    }

                    if ([goodRatingLabel.toLowerCase(), poorRatingLabel.toLowerCase()].includes(item.answer)) {
                        let itemCategory = metaChecklist[item.question_id].category;
                        if ([poorRatingLabel.toLowerCase()].includes(item.answer)) {
                            let dataItem = groupChartDataOfWorseItems.find(dItem => dItem.group === itemCategory) || {};
                            if (Object.keys(dataItem).length) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['total'] += 1;
                                let dataItemIndex = groupChartDataOfWorseItems.findIndex(dItem => dItem.group === itemCategory);
                                groupChartDataOfWorseItems[dataItemIndex] = dataItem;
                            } else {
                                dataItem = {
                                    'group': itemCategory
                                };
                                dataItem[poorRatingLabel] = 0;
                                dataItem['total'] = 1;
                                dataItem[capitalize(item.answer)] += 1;
                                groupChartDataOfWorseItems.push(dataItem);
                            }
                        } else {
                            totalGood += 1;
                        }

                        let projectName = inspectionTour.project_ref.name;
                        dataItem = ratioPerProjectChartData.find(dItem => dItem.group === projectName) || {};
                        if (Object.keys(dataItem).length) {
                            if ([poorRatingLabel.toLowerCase()].includes(item.answer)) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['max_total'] += 1;
                            }
                            dataItem['total'] += 1;
                            let dataItemIndex = ratioPerProjectChartData.findIndex(dItem => dItem.group === projectName);
                            ratioPerProjectChartData[dataItemIndex] = dataItem;
                        } else {
                            dataItem = {
                                'group': projectName
                            };
                            dataItem[poorRatingLabel] = 0;
                            dataItem['total'] = 1;
                            dataItem['max_total'] = 0;
                            if ([poorRatingLabel.toLowerCase()].includes(item.answer)) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['max_total'] = 1;
                            }
                            ratioPerProjectChartData.push(dataItem);
                        }
                    }
                });

                let weeklyUnsItem = weeklyUnsItemsChartData.find(item => (+inspectionTour.createdAt >= item.start_of_week && +inspectionTour.createdAt <= item.end_of_week));
                let weeklyUnsItemIndex = weeklyUnsItemsChartData.findIndex(item => (inspectionTour.createdAt >= item.start_of_week && inspectionTour.createdAt <= item.end_of_week ));
                if (weeklyUnsItem && weeklyUnsItemIndex != -1) {
                    weeklyUnsItem['unsatisfactory_items'] += totalPoorIssues;
                    weeklyUnsItem['total_items'] += (totalGood + totalPoorIssues);
                    weeklyUnsItemsChartData[weeklyUnsItemIndex] = weeklyUnsItem;
                }

                //prepare variable poorIssuesPerProject
                existingRecord = poorIssuesPerProject.find(record => record.group === inspectionTour.project_ref.name);
                if (existingRecord) {
                    existingRecordIndex = poorIssuesPerProject.findIndex(record => record.group === inspectionTour.project_ref.name);
                    existingRecord["rating"].push(totalPoorIssues);
                    poorIssuesPerProject[existingRecordIndex] = existingRecord;
                } else {
                    poorIssuesPerProject.push({
                        "group": inspectionTour.project_ref.name,
                        "rating": [totalPoorIssues]
                    })
                }

                inspectionTourCreatedDate = moment(inspectionTour.createdAt).format('Do MMM YY');
                datePrefix = (previousInspectionTourCreatedDate == inspectionTourCreatedDate) ? datePrefix + ' ' : '';

                if (projectGroup.indexOf(inspectionTour.project_ref.name) == -1) {
                    projectGroup.push(inspectionTour.project_ref.name);
                }

                let poorAvgDays = (poorClosedOutDays.length) ? (poorClosedOutDays.reduce(function (a, b) {
                    return a + b;
                }, 0)) / poorClosedOutDays.length : 0;
                existingRecord = avgCloseoutTimePerProjectPoor.find(record => record.group === inspectionTour.project_ref.name);
                if (existingRecord) {
                    existingRecordIndex = avgCloseoutTimePerProjectPoor.findIndex(record => record.group === inspectionTour.project_ref.name);
                    existingRecord["rating"].push(poorAvgDays);
                    avgCloseoutTimePerProjectPoor[existingRecordIndex] = existingRecord;
                } else {
                    avgCloseoutTimePerProjectPoor.push({
                        "group": inspectionTour.project_ref.name,
                        "rating": [poorAvgDays]
                    });
                }

                previousInspectionTourCreatedDate = inspectionTourCreatedDate;
            });

            weeklyUnsItemsChartData = weeklyUnsItemsChartData.reduce((arr, item) => {
                if (item.total_items) {
                    weeklyUnsItemsChartXAxisValues.push(item['week']);
                    item.week = arr.length + 1;
                    item.percent = 100/(item.total_items)*(item.unsatisfactory_items);
                    arr.push(item);
                    weeklyUnsItemsMaxNumber = (weeklyUnsItemsMaxNumber < item.percent) ? item.percent : weeklyUnsItemsMaxNumber;
                }
                return arr;
            }, []);

            poorIssuesPerProject = poorIssuesPerProject.map(item => {
                item['rating'] = parseInt(item['rating'].reduce((a, b) => a + b, 0)/item['rating'].length);
                maxIssuesPerProject = (maxIssuesPerProject < item['rating']) ? item['rating'] : maxIssuesPerProject;
                return item;
            });

            avgCloseoutTimePerProjectPoor = (avgCloseoutTimePerProjectPoor || []).reduce((arr, item) => {
                let ratCount = item['rating'].reduce((a, b) => a + b, 0)/item['rating'].length;
                if (ratCount) {
                    item['rating'] = ratCount;
                    arr.push(item);
                }
                return arr;
            }, []);
            maxCloseoutTimePerProject = mathMinMax(avgCloseoutTimePerProjectPoor, 'rating', 'max');

            //get top 10 items by total count
            groupChartDataOfWorseItems = (groupChartDataOfWorseItems.sort((a, b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0))).slice(0, 10);
            maxRatingInWorse = (groupChartDataOfWorseItems.length) ? groupChartDataOfWorseItems[0].total : 0;

            previousDate = '';
            prefix = '';
            openClosedBarChartData = [];
            openClosedBarChartData.push({name: `Open`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`});
            openClosedBarChartData.push({name: "Closed", value: totalClosedOutItems, type: ''});

            hasAllClosed = (!totalPoorToCloseOut && totalClosedOutItems) ? `All ${totalClosedOutItems} items closed out` : '';

            totalRatingCount = +(goodRatingItemsCount + poorRatingItemsCount);
            goodRatingPercentage = parseInt(100 / (totalRatingCount) * (goodRatingItemsCount));
            poorRatingPercentage = parseInt(100 / (totalRatingCount) * (poorRatingItemsCount));

            donutChartData = [
                {
                    name: `${goodRatingLabel} (${goodRatingItemsCount})`,
                    count: goodRatingItemsCount,
                    percentage: goodRatingPercentage,
                    color: `${goodRatingColor}`,
                    label: goodRatingLabel
                },
                {
                    name: `${poorRatingLabel} (${poorRatingItemsCount})`,
                    count: poorRatingItemsCount,
                    percentage: poorRatingPercentage,
                    color: `${poorRatingColor}`,
                    label: poorRatingLabel
                }
            ];

            openClosedBarChartWidth = '600';
            donutChartWidth = 400;
            donutChartHeight = 280;
            donutChartCentroidAdj = -28;
            donutLegendsAdj = -360;
            donutLegendh = -190;
            lollipopChartWidth = 705;
            lollipopChartHeight = 278;
            ratioPerProjectChartWidth = 472;
            ratioPerProjectChartHeight = 193;
            problemAreasChartWidth = 472;
            problemAreasChartHeight = 193;
            stackChartLegendAdj = 85;
            avgCloseoutTimePerProjectWidth = 1120;
            avgCloseoutTimePerProjectHeight = 400;
            weeklyUnsItemsChartWidth = 1040;
            weeklyUnsItemsChartHeight = 300;
            avgCloseoutLegendAdj = 10;
            labelFontSize = '.4em';
            marginTop = '0px';
            stackHeight = 10;
            adjustments = {
                adj1: -2,
                adj2: 10,
                adj3: -14,
                adj4: 6,
                adj5: -2,
                adj6: 1,
                adj7: 18,
                adj8: (hasAllClosed) ? 5.2 : 4.7,
                adj9: (hasAllClosed) ? 18 : 17,
                adj10: (hasAllClosed) ? -3 : 0,
                adj11: -6,
                adj12: -3.5,
                adj13: 3,
            };

            if (type === 'pdf') {
                openClosedBarChartWidth = '1000';
                donutChartWidth = 500;
                donutChartHeight = 350;
                donutChartCentroidAdj = -70;
                donutLegendsAdj = -510;
                donutLegendh = -280;
                donutViewBox = "120 50 280 310";
                lollipopChartWidth = 932;
                lollipopChartHeight = 348;
                ratioPerProjectChartWidth = 690;
                ratioPerProjectChartHeight = 290;
                problemAreasChartWidth = 690;
                problemAreasChartHeight = 275;
                stackChartLegendAdj = 110;
                avgCloseoutTimePerProjectWidth = 1400;
                avgCloseoutTimePerProjectHeight = 478;
                weeklyUnsItemsChartWidth = 1400;
                weeklyUnsItemsChartHeight = 350;
                avgCloseoutLegendAdj = 10;
                labelFontSize = '.4em';
                marginTop = '0px';
                stackHeight = 7;
                adjustments = {
                    adj1: -2,
                    adj2: 7,
                    adj3: -10,
                    adj4: 6,
                    adj5: -2.5,
                    adj6: .5,
                    adj7: 18,
                    adj8: 6,
                    adj9: 14,
                    adj10:0,
                    adj11: -6,
                    adj12: -3.5,
                    adj13: 3,
                }
            }

            ratioPerProjectChartDataMax = 0;
            ratioPerProjectChartData = (ratioPerProjectChartData || []).map(item => {
                ratioPerProjectChartDataMax = (ratioPerProjectChartDataMax < item['max_total']) ? item['max_total'] : ratioPerProjectChartDataMax;
                return item;
            });

            let nor_totalRatedItems = goodRatingItemsCount + poorRatingItemsCount;
            let nor_goodRatingItemsCount = goodRatingItemsCount;
            let nor_poorRatingItemsCount = poorRatingItemsCount;
            let nor_totalClosedOutItems = totalClosedOutItems;
            let nor_totalPoorToCloseOut = totalPoorToCloseOut;
            let nor_openClosedBarChart = await getOpenClosedBarChart(openClosedBarChartData, hasAllClosed, openClosedBarChartWidth, stackHeight, labelFontSize, marginTop, adjustments, [`${colorRed}`, "#2c961b"], [{ name: poorRatingLabel, color: `${colorRed}`}]);
            let nor_donutChart = await getDonutChart(donutChartData, donutChartWidth, donutChartHeight, donutChartCentroidAdj, 100, 60, donutViewBox, `${goodRatingLabel} (${goodRatingItemsCount})`, ``, `${poorRatingLabel} (${poorRatingItemsCount})`, false, false, 0, donutLegendsAdj, donutLegendh);
            let nor_percentageGaugeChart = await getPercentageGaugeChart(200, 90, (goodRatingItemsCount + poorRatingItemsCount), goodRatingItemsCount, 'Rating');
            let nor_groupChartDataOfWorseItemsChart = await getStackedBarChart(groupChartDataOfWorseItems, ['group', poorRatingLabel], maxRatingInWorse, problemAreasChartWidth, problemAreasChartHeight, [`${colorRed}`], '7px', 'Issues', false, true, stackChartLegendAdj);
            let nor_avgCloseoutTimePerProject = await getHoriLollipopChart(projectGroup, avgCloseoutTimePerProjectPoor, [], maxCloseoutTimePerProject, avgCloseoutTimePerProjectWidth, avgCloseoutTimePerProjectHeight,"Days to closeout", '7px', [{ name: 'Fair', color: `${colorYellow}`}, { name: 'Poor', color: `${colorRed}`}], avgCloseoutLegendAdj);
            let nor_issuesPerProjectChart = await getVerLollipopChart(projectGroup, poorIssuesPerProject, [], maxIssuesPerProject, lollipopChartWidth, lollipopChartHeight, "Issues", '7px', [{ name: 'Poor', color: `${colorRed}`}], 10);
            let nor_ratioPerProjectChart = await getStackedBarChart(ratioPerProjectChartData, ['group', poorRatingLabel], ratioPerProjectChartDataMax, ratioPerProjectChartWidth, ratioPerProjectChartHeight, [`${colorRed}`],"7px", "Number", false, false, stackChartLegendAdj);
            let nor_weeklyUnsatisfactoryItemsChart = await getScatterPlot(weeklyUnsItemsChartData, 'week', weeklyUnsItemsChartXAxisValues, ['percent'], weeklyUnsItemsMaxNumber, weeklyUnsItemsChartWidth, weeklyUnsItemsChartHeight, '7px', 'Week', '% Unsatisfactory Items', 10, false, [`${colorRed}`], false, "");

            sails.log.info('Industrial project inspection tours: ', industrialProjectInspectionTours.length);
            sails.log.info('Normal project inspection tours: ', normalProjectInspectionTours.length);

            sails.log.info(`${projectInspectionTours.length} inspection tours found.`);

            let {project_logo_file, companyName} = await getCompanyInfo({}, {id: companyId});

            let form_template = `pages/inspection-tour-company-dashboard-page`;
            let html = await sails.renderView(form_template, {
                title: `Inspection Dashboard`,
                layout: false,
                report_from: (+req.body.from_date) ? momentTz(+req.body.from_date).tz(companyTimezone).format('DD/MM/YYYY') : '',
                report_to: (+req.body.to_date) ? momentTz(+req.body.to_date).tz(companyTimezone).format('DD/MM/YYYY') : '',
                company_name: companyName,
                company_logo: project_logo_file,
                report_type: type,
                totalPages: (industrialProjectInspectionTours.length && normalProjectInspectionTours.length) ? 4 : 2,
                ind_total_inspections: industrialProjectInspectionTours.length,
                ind_totalRatedItems,
                ind_goodRatingItemsCount,
                ind_poorRatingItemsCount,
                ind_fairRatingItemsCount,
                ind_totalClosedOutItems,
                ind_totalFairToCloseOut,
                ind_totalPoorToCloseOut,
                ind_openClosedBarChart,
                ind_donutChart,
                ind_percentageGaugeChart,
                ind_groupChartDataOfWorseItemsChart,
                ind_issuesPerProjectChart,
                ind_avgCloseoutTimePerProject,
                ind_ratioPerProjectChart,
                ind_weeklyUnsatisfactoryItemsChart,
                ind_projectsCount: industrialProjects.length,

                nor_total_inspections: normalProjectInspectionTours.length,
                nor_totalRatedItems,
                nor_goodRatingItemsCount,
                nor_poorRatingItemsCount,
                nor_totalClosedOutItems,
                nor_totalPoorToCloseOut,
                nor_openClosedBarChart,
                nor_donutChart,
                nor_percentageGaugeChart,
                nor_groupChartDataOfWorseItemsChart,
                nor_issuesPerProjectChart,
                nor_avgCloseoutTimePerProject,
                nor_ratioPerProjectChart,
                nor_weeklyUnsatisfactoryItemsChart,
                nor_projectsCount: normalProjects.length,
            });

            if (type === 'pdf') {
                let fileName = 'Inspections-Tour-Dashboard-' + moment().format('MM-DD-YYYY');
                return await instantPdfGenerator(req, res, html, 'inspection-tour-c-dashboard', fileName, req.headers['user-agent'], { format: 'A3', landscape: true }, 'url');
            }

            sails.log.info('Rendering html view');
            return res.send(html);
        }
        sails.log.info('No inspection records found in selected criteria: ', where);
        return res.send("<p style='text-align: center;margin-top: 100px;margin-bottom: auto;'>No inspection records found in selected criteria.</p>");
    },

    downloadParticipantsList: async (req, res) => {
        let companyId = +req.param('companyId');

        sails.log.info('Fetch inspection tours', "Requesting company:", companyId);
        let query_variables = [+req.body.from_date, +req.body.to_date];
        let startingNoOfEscaped = 2;
        let project_ref_filter = `AND finalised = true AND project_ref IN (${(req.body.selected_projects || []).map((id) => {
            startingNoOfEscaped++;
            query_variables.push(id);
            return `$${startingNoOfEscaped}`;
        }).join(',')})`;

        sails.log.info(`project_ref_filter: `, project_ref_filter, "query_variables: ", query_variables);

        let rawResult = await sails.sendNativeQuery(
            `SELECT
                 id, record_id, project_ref, common_checklist, rail_checklist, industrial_checklist, additional_checklist, participants, "createdAt"
                FROM project_inspection_tour
                WHERE
                 json_array_length(participants) > 0 AND
                 "createdAt" >= $1 AND
                 "createdAt" <= $2
                 ${project_ref_filter}`,
            query_variables
        );

        sails.log.info(`Found ${rawResult.rows.length} inspections with participants.`);

        let participants = [];
        if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            let projectInspectionTours = rawResult.rows;
            let projectIds = []
            let participantsId = (projectInspectionTours).reduce((arr, item) => {
                arr.push(...item.participants);
                projectIds.push(item.project_ref);
                return _uniq(arr);
            }, []);

            let projects = await sails.models.project.find({
                where: {id: _uniq(projectIds)},
                select: ['id', 'name', 'project_type']
            });

            projectInspectionTours = projectInspectionTours.map(it => {
                it.project_ref = (projects || []).find(project => project.id == it.project_ref) || {};
                return it;
            });

            participants = await sails.models.user.find({
                where: {id: participantsId},
                select: ['id', 'first_name', 'last_name']
            })

            participants = (participants || []).sort((a,b) => (a.first_name > b.first_name) ? 1 : ((b.first_name > a.first_name) ? -1 : 0));

            participants = participants.map(participant => {
                participant.inspection_date = [];
                participant.inspections_participated = [];
                participant.projects = [];
                participant.inspection_rating = [];
                return participant;
            });

            sails.log.info(`Total participants: ${participants.length}`);

            let industrialProjectInspectionTours = (projectInspectionTours || []).filter(it => (it.project_ref.project_type && it.project_ref.project_type === 'industrial'));
            let normalProjectInspectionTours = (projectInspectionTours || []).filter(it => (it.project_ref.project_type && it.project_ref.project_type != 'industrial'));

            //rating labels
            let goodRatingLabel = `Good`;
            let fairRatingLabel = `Fair`;
            let poorRatingLabel = `Poor`;

            let goodRatingItemsCount = 0;
            let poorRatingItemsCount = 0;
            let fairRatingItemsCount = 0;
            let inspectionTourChecklist = [];
            (industrialProjectInspectionTours || []).forEach(function (inspectionTour, i) {
                let inspectionTourChecklist = [...inspectionTour.industrial_checklist, ...inspectionTour.additional_checklist];
                goodRatingItemsCount = 0;
                poorRatingItemsCount = 0;
                fairRatingItemsCount = 0;
                inspectionTourChecklist.forEach(function (item, j) {
                    goodRatingItemsCount += (item.answer === goodRatingLabel.toLowerCase()) ? 1 : 0;
                    poorRatingItemsCount += (item.answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    fairRatingItemsCount += (fairRatingLabel && item.answer === fairRatingLabel.toLowerCase()) ? 1 : 0;
                });

                participants = fillParticipants(inspectionTour, participants, (goodRatingItemsCount + poorRatingItemsCount + fairRatingItemsCount), goodRatingItemsCount);
            });

            goodRatingLabel = `Yes`;
            poorRatingLabel = `No`;
            (normalProjectInspectionTours || []).forEach(function (inspectionTour, i) {
                inspectionTourChecklist = [...inspectionTour.common_checklist];
                if (inspectionTour.project_ref.project_type === 'rail') {
                    inspectionTourChecklist.push(...inspectionTour.rail_checklist);
                }

                goodRatingItemsCount = 0;
                poorRatingItemsCount = 0;
                inspectionTourChecklist.forEach(function (item, j) {
                    goodRatingItemsCount += (item.answer === goodRatingLabel.toLowerCase()) ? 1 : 0;
                    poorRatingItemsCount += (item.answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                });

                participants = fillParticipants(inspectionTour, participants, (goodRatingItemsCount + poorRatingItemsCount), goodRatingItemsCount);
            });

            participants = participants.map(participant => {
                participant.first_inspection_date = moment(Math.min(...participant.inspection_date)).format('DD/MM/YYYY');
                participant.last_inspection_date = moment(Math.max(...participant.inspection_date)).format('DD/MM/YYYY');
                participant.total_inspections_participated = participant.inspections_participated.length;
                participant.total_number_of_projects = _uniq(participant.projects).length;

                let maxRating = participant.inspection_rating.reduce((max, rating) => max.percent > rating.percent ? max : rating);
                participant.best_inspection_rating = `${Math.floor(maxRating.percent)}% (${maxRating.project_name})`;

                let minRating = participant.inspection_rating.reduce((min, rating) => min.percent < rating.percent ? min : rating);
                participant.worst_inspection_rating = `${Math.floor(minRating.percent)}% (${minRating.project_name})`;

                let avgRating = ((participant.inspection_rating.reduce((a, b) => a + b.percent, 0)) / participant.inspection_rating.length);
                participant.average_inspection_rating = `${Math.floor(avgRating)}%`;
                return participant;
            });
        }

        let companyInfo = await sails.models.createemployer.findOne({
            where: {id: companyId},
            select: ['name']
        });

        let report_from = (+req.body.from_date) ? moment(+req.body.from_date).format('DD-MM-YYYY') : '';
        let report_to = (+req.body.to_date) ? moment(+req.body.to_date).format('DD-MM-YYYY') : '';

        let workbook = await getInspectionParticipants(participants, companyInfo, report_from, report_to);
        let fileName = `${companyInfo.name} - Inspection Participants [${report_from}-${report_to}].xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },

    subContractorDashboard: async (req, res) => {
        let companyId = +req.param('companyId');
        let type = req.param('type');
        let totalPages = 1;
        let where = {
            project_ref: (req.body.selected_projects || []),
            finalised: true
        };
        let selected_responsible_companies = (req.body.selected_responsible_companies || [])

        if (req.body.from_date && req.body.to_date) {
            where.createdAt = {'from': +req.body.from_date, 'to': +req.body.to_date};
        }

        sails.log.info('Fetch inspection tour filter.', where, "Requesting company:", companyId);
        let {projectInspectionTours, commonChecklist, railChecklist, industrialChecklist} = await getInspectionTours({where: where}, 'DESC');
        let taggedCompaniesInfo = [];
        if (projectInspectionTours.length) {
            let companyTimezone = await getTimezone(companyId, 'company');

            //sort by project name
            projectInspectionTours = (projectInspectionTours || []).sort((a, b) => (a.project_ref.name > b.project_ref.name) ? 1 : ((b.project_ref.name > a.project_ref.name) ? -1 : 0));
            let industrialProjectInspectionTours = (projectInspectionTours || []).filter(it => (it.project_ref.project_type && it.project_ref.project_type === 'industrial'));
            sails.log.info(`get industrial projects from inspection tour records.`);

            let normalProjectInspectionTours = (projectInspectionTours || []).filter(it => (it.project_ref.project_type && it.project_ref.project_type != 'industrial'));
            sails.log.info(`get normal projects from inspection tour records.`);

            let companiesWithCompanyPortal = await sails.models.createemployer.find({
                where: {has_company_portal: true},
                select: ['id', 'name']
            });

            let taggedCompaniesId = []
            let metaChecklist = [];
            let industrialProjectsRawItems = [];
            (industrialProjectInspectionTours || []).forEach(function (inspectionTour, i) {
                let inspectionTourChecklist = [...inspectionTour.industrial_checklist, ...inspectionTour.additional_checklist];
                metaChecklist = [...industrialChecklist, ...inspectionTour.additional_checklist];
                metaChecklist = metaChecklist.reduce((obj, que) => {
                    obj[que.question_id] = que;
                    return obj;
                }, {});

                let { rawItems, companiesId } =  prepareRawItems(inspectionTour, inspectionTourChecklist, metaChecklist, selected_responsible_companies, companiesWithCompanyPortal);
                industrialProjectsRawItems.push(...rawItems);
                taggedCompaniesId.push(...companiesId);
            });

            metaChecklist = [];
            let normalProjectsRawItems = [];
            (normalProjectInspectionTours || []).forEach(function (inspectionTour, i) {
                let inspectionTourChecklist = [...inspectionTour.common_checklist];
                metaChecklist = [...commonChecklist];
                if (inspectionTour.project_ref.project_type === 'rail') {
                    inspectionTourChecklist.push(...inspectionTour.rail_checklist);
                    metaChecklist = [...commonChecklist, ...railChecklist];
                }
                metaChecklist = metaChecklist.reduce((obj, que) => {
                    obj[que.question_id] = que;
                    return obj;
                }, {});

                let { rawItems, companiesId } =  prepareRawItems(inspectionTour, inspectionTourChecklist, metaChecklist, selected_responsible_companies, companiesWithCompanyPortal);
                normalProjectsRawItems.push(...rawItems);
                taggedCompaniesId.push(...companiesId);
            });

            if(!industrialProjectsRawItems.length && !normalProjectsRawItems.length) {
                sails.log.info('No records found in selected criteria: ', where);
                return res.send({'html': "<p style='text-align: center;margin-top: 100px;margin-bottom: auto;'>No records found in selected criteria.</p>", 'tagged_companies': []});
            }

            sails.log.info('Tagged Company Ids of Items, ', _uniq(taggedCompaniesId));
            taggedCompaniesInfo = await sails.models.createemployer.find({
                where: {id: _uniq(taggedCompaniesId)},
                select: ['name']
            });

            let taggedCompaniesIdNamePair = (taggedCompaniesInfo || []).reduce((obj, item) => {
                obj[item.id] = item.name;
                return obj;
            }, {});

            ([...industrialProjectsRawItems, ...normalProjectsRawItems]).forEach(function (item) {
                //store tagged count of company
                let companyRecord = (taggedCompaniesInfo || []).find(company => company.id == item.tagged_company_ref);
                if (companyRecord) {
                    companyRecord['tagged_count'] = (companyRecord['tagged_count']) ? companyRecord['tagged_count'] + 1 : 1;
                    let companyRecordIndex = (taggedCompaniesInfo || []).findIndex(company => company.id == item.tagged_company_ref);
                    taggedCompaniesInfo[companyRecordIndex] = companyRecord;
                }
            });

            taggedCompaniesInfo = taggedCompaniesInfo.map(company => {
                if(!company.tagged_count) {
                    company.tagged_count = 0;
                }
                return company;
            });

            taggedCompaniesInfo = ((taggedCompaniesInfo || []).sort((a,b) => (a.tagged_count < b.tagged_count) ? 1 : ((b.tagged_count < a.tagged_count) ? -1 : 0))).slice(0, 20);

            let { hasIndustrialDashboard, ind_issuesRaisedChart, ind_positiveItemsChart, ind_numberOfProjectsChart, ind_avgCloseoutTimeChart, ind_biggestProblemAreasChart} = await prepareSubcontractorDashboardCharts(industrialProjectsRawItems, taggedCompaniesIdNamePair, taggedCompaniesInfo, true, type);

            let { hasNormalDashboard, nor_issuesRaisedChart, nor_positiveItemsChart, nor_numberOfProjectsChart, nor_avgCloseoutTimeChart, nor_biggestProblemAreasChart} = await prepareSubcontractorDashboardCharts(normalProjectsRawItems, taggedCompaniesIdNamePair, taggedCompaniesInfo, false, type);

            sails.log.info('Industrial project inspection tours: ', industrialProjectInspectionTours.length);
            sails.log.info('Normal project inspection tours: ', normalProjectInspectionTours.length);

            sails.log.info(`${projectInspectionTours.length} inspection tours found.`);

            let {project_logo_file, companyName} = await getCompanyInfo({}, {id: companyId});

            let form_template = `pages/inspection-tour-subcontractor-dashboard-page`;
            let html = await sails.renderView(form_template, {
                title: `Subcontractor Dashboard`,
                layout: false,
                report_from: (+req.body.from_date) ? momentTz(+req.body.from_date).tz(companyTimezone).format('DD/MM/YYYY') : '',
                report_to: (+req.body.to_date) ? momentTz(+req.body.to_date).tz(companyTimezone).format('DD/MM/YYYY') : '',
                company_name: companyName,
                company_logo: project_logo_file,
                report_type: type,
                totalPages: (hasIndustrialDashboard && hasNormalDashboard) ? 4 : 2,

                hasIndustrialDashboard,
                ind_issuesRaisedChart,
                ind_positiveItemsChart,
                ind_numberOfProjectsChart,
                ind_avgCloseoutTimeChart,
                ind_biggestProblemAreasChart,

                hasNormalDashboard,
                nor_issuesRaisedChart,
                nor_positiveItemsChart,
                nor_numberOfProjectsChart,
                nor_avgCloseoutTimeChart,
                nor_biggestProblemAreasChart
            });

            if (type === 'pdf') {
                sails.log.info('Generating pdf');
                let fileName = 'Subcontractors-Dashboard-' + moment().format(dbDateFormat);
                return await instantPdfGenerator(req, res, html, 'subcontractors-dashboard', fileName, req.headers['user-agent'], { format: 'A3', landscape: true });
            }

            sails.log.info('Rendering html view');

            return res.send({'html': html, 'tagged_companies': taggedCompaniesInfo});
        }

        sails.log.info('No records found in selected criteria: ', where);
        return res.send({'html': "<p style='text-align: center;margin-top: 100px;margin-bottom: auto;'>No records found in selected criteria.</p>", 'tagged_companies': []});
    },

    taggedOwnerDashboard: async (req, res) => {
        let companyId = +req.param('companyId');
        let taggedOwnerId = +req.param('ownerId');
        let sortItemsBy = req.body.sort_items_by;
        let totalPages = 1;
        let where = {
            project_ref: (req.body.selected_projects || []),
            finalised: true
        };

        let industrial_projects_rating  =  (req.body.industrial_projects_rating || {});
        let normal_projects_rating = (req.body.normal_projects_rating || {});

        if (req.body.from_date && req.body.to_date) {
            where.createdAt = {'from': +req.body.from_date, 'to': +req.body.to_date};
        }

        sails.log.info('Fetch inspection tour filter.', where, "Requesting company:", companyId);
        let {
            projectInspectionTours,
            commonChecklist,
            railChecklist,
            industrialChecklist
        } = await getInspectionTours({where: where}, 'DESC' , true);

        let html = "<p style='text-align: center;margin-top: 100px;margin-bottom: auto;'>No records found in selected criteria.</p>";
        let taggedOwnerInfo = await sails.models.createemployer_reader.findOne({
            where: {id: taggedOwnerId},
            select: ['name']
        });
        if (projectInspectionTours.length && Object.keys(industrial_projects_rating).length && Object.keys(normal_projects_rating).length) {
            sails.log.info(`Preparing dashboard for tagged owner ${taggedOwnerInfo.name}`);

            //sort by project name
            projectInspectionTours = (projectInspectionTours || []).sort((a, b) => (a.project_ref.name > b.project_ref.name) ? 1 : ((b.project_ref.name > a.project_ref.name) ? -1 : 0));
            let industrialProjectInspectionTours = (projectInspectionTours || []).filter(it => (it.project_ref.project_type && it.project_ref.project_type === 'industrial'));
            sails.log.info(`get industrial projects from inspection tour records.`);

            let normalProjectInspectionTours = (projectInspectionTours || []).filter(it => (it.project_ref.project_type && it.project_ref.project_type != 'industrial'));
            sails.log.info(`get normal projects from inspection tour records.`);

            let metaChecklist = [];
            let industrialProjectsRawItems = [];
            for(let inspectionTour of industrialProjectInspectionTours) {
                let inspectionTourChecklist = [...inspectionTour.industrial_checklist, ...inspectionTour.additional_checklist];
                metaChecklist = [...industrialChecklist, ...inspectionTour.additional_checklist];
                metaChecklist = metaChecklist.reduce((obj, que) => {
                    obj[que.question_id] = que;
                    return obj;
                }, {});

                let {
                    rawItems,
                } = await prepareRawItemsByTaggedOwner(inspectionTour, inspectionTourChecklist, metaChecklist, taggedOwnerId, true, industrial_projects_rating);
                industrialProjectsRawItems.push(...rawItems);
            };

            metaChecklist = [];
            let normalProjectsRawItems = [];
            for(let inspectionTour of normalProjectInspectionTours) {
                let inspectionTourChecklist = [...inspectionTour.common_checklist];
                metaChecklist = [...commonChecklist];
                if (inspectionTour.project_ref.project_type === 'rail') {
                    inspectionTourChecklist.push(...inspectionTour.rail_checklist);
                    metaChecklist = [...commonChecklist, ...railChecklist];
                }
                metaChecklist = metaChecklist.reduce((obj, que) => {
                    obj[que.question_id] = que;
                    return obj;
                }, {});

                let {
                    rawItems
                } = await prepareRawItemsByTaggedOwner(inspectionTour, inspectionTourChecklist, metaChecklist, taggedOwnerId, false, normal_projects_rating);
                normalProjectsRawItems.push(...rawItems);
            };

            if(industrialProjectsRawItems.length || normalProjectsRawItems.length) {
                let {
                    donutChart: ind_donutChart,
                    openClosedBarChart: ind_openClosedBarChart,
                    ratingsByGroupChart: ind_ratingsByGroupChart,
                    no_of_projects: ind_no_of_projects,
                    no_of_items_tagged: ind_no_of_items_tagged,
                    no_of_positive_items: ind_no_of_positive_items,
                    no_of_issues_raised: ind_no_of_issues_raised,
                    itemsInfo: ind_itemsInfo
                } = await prepareTaggedOwnerDashboardCharts(industrialProjectsRawItems, true, industrial_projects_rating);

                let {
                    donutChart: nor_donutChart,
                    openClosedBarChart: nor_openClosedBarChart,
                    ratingsByGroupChart: nor_ratingsByGroupChart,
                    no_of_projects: nor_no_of_projects,
                    no_of_items_tagged: nor_no_of_items_tagged,
                    no_of_positive_items: nor_no_of_positive_items,
                    no_of_issues_raised: nor_no_of_issues_raised,
                    itemsInfo: nor_itemsInfo
                } = await prepareTaggedOwnerDashboardCharts(normalProjectsRawItems, false, normal_projects_rating);

                //expand item's info
                let userIdsToExpand = [];
                let appendixImageIds = [];
                let closeOutImagesId = [];
                let itemsInfo = [...ind_itemsInfo, ...nor_itemsInfo];
                for (let item of itemsInfo) {
                    if (item.inspected_by) {
                        userIdsToExpand.push(item.inspected_by);
                    }

                    if (item.item_assigned_to) {
                        userIdsToExpand.push(item.item_assigned_to);
                    }

                    if (item.item_images && item.item_images.length) {
                        appendixImageIds.push(...item.item_images);
                    }

                    if (item.item_closeout_images && item.item_closeout_images.length) {
                        closeOutImagesId.push(...item.item_closeout_images);
                    }
                }

                let expandedUsers = await sails.models.user_reader.find({
                    where: {id: _uniq(userIdsToExpand)},
                    select: ['id', 'first_name', 'middle_name', 'last_name', 'email', 'timezone']
                });

                let expandedImages = await sails.models.userfile_reader.find({
                    where: {id: _uniq([...appendixImageIds, ...closeOutImagesId])},
                    select: ['id', 'sm_url', 'md_url', 'file_url']
                });

                let closeOutImageRows = 0;
                let appendixImageRows = 0;
                let goodItems = [];
                let fairItems = [];
                let poorItems = [];
                itemsInfo = itemsInfo.map(item => {
                    let inspectedByUser = (expandedUsers).find(user => user.id == item.inspected_by);
                    item.inspected_by = (inspectedByUser) ? `${getUserFullName(inspectedByUser)} (${moment(item.inspected_at).format('DD/MM/YY HH:mm:ss')})` : null;
                    item.item_assigned_to = (item.item_assigned_to) ? getResponsibleUserInfo(expandedUsers, item.item_assigned_to) : null;

                    if (item.item_images && item.item_images.length) {
                        item.item_images = expandedImages.filter(image => {
                            if (item.item_images.includes(image.id)) {
                                return
                            }
                        });
                        appendixImageRows += Math.ceil(item.item_images / 3);
                    }

                    if (item.item_closeout_images && item.item_closeout_images.length) {
                        item.item_closeout_images = expandedImages.filter(image => item.item_closeout_images.includes(image.id));
                        closeOutImageRows += Math.ceil(item.item_closeout_images / 3);
                    }

                    if (item.item_answer == 'good' || item.item_answer == 'yes') {
                        goodItems.push(item);
                    }

                    if (item.item_answer == 'fair') {
                        fairItems.push(item);
                    }

                    if (item.item_answer == 'poor' || item.item_answer == 'no') {
                        poorItems.push(item);
                    }

                    return item;
                });

                //sorting items
                if(sortItemsBy == 'rating') {
                    itemsInfo = [...goodItems, ...fairItems, ...poorItems];
                } else  {
                    itemsInfo.sort((a, b) => parseFloat(b.inspected_at) - parseFloat(a.inspected_at));
                }

                totalPages = (industrialProjectsRawItems.length && normalProjectsRawItems.length) ? 2 : 1;

                sails.log.info('Total items in inspection tours Industrial project: ', ind_itemsInfo.length);
                sails.log.info('Total items in inspection tours Normal project: ', nor_itemsInfo.length);

                let {project_logo_file, companyName} = await getCompanyInfo({}, {id: companyId});

                let projectsParticipated = _uniq([...ind_no_of_projects, ...nor_no_of_projects]);
                let form_template = `pages/inspection-tour-tagged-owner-report`;
                html = await sails.renderView(form_template, {
                    title: `${taggedOwnerInfo.name} Inspection Report`,
                    report_from: moment(+req.body.from_date).format('DD/MM/YYYY'),
                    report_to: moment(+req.body.to_date).format('DD/MM/YYYY'),
                    companyName,
                    industrialProjectsRawItemsCount: industrialProjectsRawItems.length,
                    normalProjectsRawItemsCount: normalProjectsRawItems.length,
                    itemsInfo,
                    totalPages,
                    moment,
                    ind_donutChart,
                    ind_openClosedBarChart,
                    ind_ratingsByGroupChart,
                    nor_donutChart,
                    nor_openClosedBarChart,
                    nor_ratingsByGroupChart,
                    no_of_projects: projectsParticipated.length,
                    no_of_items_tagged: (ind_no_of_items_tagged + nor_no_of_items_tagged),
                    no_of_positive_items: (ind_no_of_positive_items + nor_no_of_positive_items),
                    no_of_issues_raised: (ind_no_of_issues_raised + nor_no_of_issues_raised),
                    project_logo_file,
                    layout: false,
                    unix(n, format) {
                        return moment.unix(n).format(format);
                    },
                    checkObjLength(obj) {
                        return Object.keys(obj).length;
                    }
                });
            }
        }

        sails.log.info('Generating pdf');
        let fileName = `${taggedOwnerInfo.name} Inspection Report`;
        return await instantPdfGenerator(req, res, html, 'tagged-owner-dashboard', fileName, req.headers['user-agent'], { format: 'A4' });
    },

    getItemsToCloseout: async (req, res) => {
        let projectId = +req.param('projectId');
        let userId = +req.param('userId');
        let filter = {
            where: {
                project_ref: [projectId],
                finalised: true
            }
        }
        let {projectInspectionTours, commonChecklist, railChecklist, industrialChecklist} = await getInspectionTours(filter, 'DESC', true, false);
        let items_to_closeout = [];
        let imageIds = [];
        let taggedCompanyIds = [];
        (projectInspectionTours || []).map(inspectionTour => {
            (inspectionTour.common_checklist || []).map(item => {
                if (item.responsible_user_ref && (item.responsible_user_ref == userId)
                    && (item.answer === 'no' || item.answer === 'poor' || item.answer === 'fair')
                    && item.close_out && !Object.keys(item.close_out).length) {
                    item.checklist_type = 'common_checklist';
                    item.inspection_id = inspectionTour.id;
                    item.inspection_created_at = inspectionTour.createdAt;
                    let metaItem = (commonChecklist || []).find(metaItem => metaItem.question_id == item.question_id);
                    if (item.appendix && item.appendix.length) {
                        imageIds.push(...item.appendix);
                    }
                    if (item.tagged_company_ref && item.tagged_company_ref.length) {
                        taggedCompanyIds.push(...item.tagged_company_ref);
                    }
                    items_to_closeout.push(_.merge(item, metaItem));
                }
            });

            (inspectionTour.rail_checklist || []).map(item => {
                if (item.responsible_user_ref && (item.responsible_user_ref == userId)
                    && (item.answer === 'no' || item.answer === 'poor' || item.answer === 'fair') && item.close_out && !Object.keys(item.close_out).length) {
                    item.checklist_type = 'rail_checklist';
                    item.inspection_id = inspectionTour.id;
                    item.inspection_created_at = inspectionTour.createdAt;
                    let metaItem = (railChecklist || []).find(metaItem => metaItem.question_id == item.question_id);
                    if (item.appendix && item.appendix.length) {
                        imageIds.push(...item.appendix);
                    }
                    if (item.tagged_company_ref && item.tagged_company_ref.length) {
                        taggedCompanyIds.push(...item.tagged_company_ref);
                    }
                    items_to_closeout.push(_.merge(item, metaItem));
                }
            });

            (inspectionTour.industrial_checklist || []).map(item => {
                if (item.responsible_user_ref && (item.responsible_user_ref == userId)
                    && (item.answer === 'no' || item.answer === 'poor' || item.answer === 'fair') && item.close_out && !Object.keys(item.close_out).length) {
                    item.checklist_type = 'industrial_checklist';
                    item.inspection_id = inspectionTour.id;
                    item.inspection_created_at = inspectionTour.createdAt;
                    let metaItem = (industrialChecklist || []).find(metaItem => metaItem.question_id == item.question_id);
                    if (item.appendix && item.appendix.length) {
                        imageIds.push(...item.appendix);
                    }
                    if (item.tagged_company_ref && item.tagged_company_ref.length) {
                        taggedCompanyIds.push(...item.tagged_company_ref);
                    }
                    items_to_closeout.push(_.merge(item, metaItem));
                }
            });

            (inspectionTour.additional_checklist || []).map(item => {
                if (item.responsible_user_ref && (item.responsible_user_ref == userId)
                    && (item.answer === 'no' || item.answer === 'poor' || item.answer === 'fair') && item.close_out && !Object.keys(item.close_out).length) {
                    item.checklist_type = 'additional_checklist';
                    item.inspection_id = inspectionTour.id;
                    item.inspection_created_at = inspectionTour.createdAt;
                    if (item.appendix && item.appendix.length) {
                        imageIds.push(...item.appendix);
                    }
                    if (item.tagged_company_ref && item.tagged_company_ref.length) {
                        taggedCompanyIds.push(...item.tagged_company_ref);
                    }
                    items_to_closeout.push(item);
                }
            });
        });

        let imagesObj = await sails.models.userfile_reader.find({
            where: {id: _uniq(imageIds)},
            select: ['id', 'sm_url', 'md_url', 'file_url']
        });

        sails.log.info('Tagged Company Ids of Items, ', taggedCompanyIds);
        let companiesInfo = await sails.models.createemployer.find({
            where: {id: _uniq(taggedCompanyIds)},
            select: ['name']
        });

        items_to_closeout = items_to_closeout.map(item => {
            if (item.appendix && item.appendix.length) {
                item.appendix = (imagesObj || []).filter(obj => item.appendix.includes(obj.id));
            }

            if (item.tagged_company_ref && item.tagged_company_ref.length) {
                item.tagged_company_ref = (companiesInfo || []).filter(obj => item.tagged_company_ref.includes(obj.id));
            }
            return item;
        });

        sails.log.info(`Number of inspection items ${items_to_closeout.length} needs to closeout on project ${projectId}`);
        return successResponse(res, {
            items_to_closeout
        });
    },

    shareInspectionTourReport: async(req, res) => {
        let id = +req.param('id');
        let email = req.body.email;
        let projectId = req.body.projectId;
        let byUser = req.user;
        sails.log.info('Share Inspection Tour Report request by user', byUser.id, ' for project ', projectId);
        let projectInspectionTour =  await sails.models.projectinspectiontour_reader.findOne({
            where: {id: id},
            select: ['id', 'project_ref', 'record_id']
        });
        [projectInspectionTour] = await populateProjectRefs([projectInspectionTour], 'project_ref', ['id', 'name']);

        if(projectInspectionTour && projectInspectionTour.id) {
            let html = await prepareViewOrDownloadInspectionTour(req, res, {id:id}, 'html');
            let attachmentName = `Inspection Tour-#${projectInspectionTour.project_ref.id}-${projectInspectionTour.record_id}-${moment().format(dbDateFormat)}-${projectInspectionTour.project_ref.name}`;
            ResponseService.successResponse(res, {message: `The Inspection Tour report is being prepared and will be shared shortly.`})
            await shareReportViaEmail(req, res, html, 'inspection-tour', attachmentName, "Inspection Tour", byUser, email, projectInspectionTour.project_ref.name);
            return;
        }
    },
}
