const dayjs = require('dayjs');
const {
    DEFAULT_PAGE_SIZE,
    displayDateFormat_DD_MM_YYYY_HH_mm_ss,
} = sails.config.constants;
const _uniq = require('lodash/uniq');
const {
    goodCallFn,
    inductionFn: { getUserInductionEmployer, },
} = require('./../sql.fn');
const {
    TokenUtil: {
        allProjectAdminsByOneOfDesignations,
        filterProjectUsersEmailEligibility,
        getCompanyInfo
    },
    UserRevisionService: {getLatestUserRevision},
    ResponseService: {
        errorResponse,
        successResponse
    },
    DataProcessingService: {
        getUserFullName,
        buildGCStatusMessage,
        getProjectTimezone,
        attachProfilePicWithUserRefInfo,
        expandProjectGoodCalls,
        sendMailToNominatedManagerCPA,
        populateUserRefs,
        populateEmployerRefs,
        populateProjectRefs,
        populateInductionEmployerRef,
    },
    ChartService: {
        getOpenClosedBar<PERSON>hart,
        getStackedBarChart,
        getScatterPlot,
        getStackedBarWithLineChart
    },
    EmailService: {
        sendMail
    },
    SharedService: {
        instantPdfGenerator,
        downloadPdfViaGenerator,
    },
    NotificationService: {
        NOTIFICATION_CATEGORY,
        sendPushNotification
    },
    ASiteService : { checkIfAsiteEnabled, getAsiteProjectToolMapping, uploadDocOnAsite },
    HttpService: {
        decodeURIParam,
    }
} = require('./../services');

const {
    dbDateFormat_slash_DDMMYYYY
} = sails.config.constants;

const dbDateFormat = 'DD-MM-YYYY';

const prepareWeeksData = (fromEpoch, toEpoch) => {
    let a = dayjs(fromEpoch).startOf('isoWeek');
    let b = dayjs(toEpoch).endOf('isoWeek');

    let daysBetweenDates = b.diff(a, 'days');
    let startOfWeeksArr = [a.valueOf()];

    if (daysBetweenDates > 6) {
        for (let i = 0; i < (daysBetweenDates/6); i++) {
            let nextVal = dayjs(startOfWeeksArr[i]).add(7, 'days').valueOf();
            if (nextVal > b.valueOf()) {
                break;
            }
            startOfWeeksArr.push(nextVal);
        }
    }

    let data = [];
    for (let i in startOfWeeksArr) {
        let item = {
            'week': +i + 1,
            'total_observation': 0,
            'closedout_observations': [],
            'start_of_week': startOfWeeksArr[i],
            'end_of_week': dayjs(startOfWeeksArr[i]).add(6, 'days').valueOf()
        };
        data.push(item);
    }

    return data;
};

const getTop10ObservationChartData = (observations) => {
    let top10ObservationChartData = [];
    let top10ObservationChartMax = 0;
    let ccGroupByHazardCategory = observations.reduce((obj, cc) => {
        obj[cc.hazard_category] = (obj[cc.hazard_category] || 0) + 1;
        return obj;
    }, {});

    for (let hazard_category in ccGroupByHazardCategory) {
        top10ObservationChartData.push({
            group: hazard_category,
            Count: ccGroupByHazardCategory[hazard_category]
        });
        top10ObservationChartMax =  (top10ObservationChartMax < ccGroupByHazardCategory[hazard_category]) ? ccGroupByHazardCategory[hazard_category] : top10ObservationChartMax
    }
    top10ObservationChartData = (top10ObservationChartData || []).sort((a,b) => (a.Count < b.Count) ? 1 : ((b.Count < a.Count) ? -1 : 0)).slice(0, 10);

    return { top10ObservationChartData, top10ObservationChartMax };
};

const getWeeklyObservationChartData = (from_date, to_date, observations) => {
    let weeklyObservationsChartData = prepareWeeksData(from_date, to_date);
    for (let index in observations) {
        let observation= observations[index];
        let weeklyObservation = weeklyObservationsChartData.find(item => (+observation.createdAt >= item.start_of_week && +observation.createdAt <= item.end_of_week));
        let weeklyObservationIndex = weeklyObservationsChartData.findIndex(item => (observation.createdAt >= item.start_of_week && observation.createdAt <= item.end_of_week ));
        if (weeklyObservation && weeklyObservationIndex != -1) {
            weeklyObservation['total_observation'] += 1;
            weeklyObservationsChartData[weeklyObservation] = weeklyObservation;
        }
    }

    let weeklyObservationsChartMax = 0;
    let weeklyObservationsChartXAxisValues = [''];
    weeklyObservationsChartData = weeklyObservationsChartData.reduce((arr, item) => {
        if (item.total_observation) {
            weeklyObservationsChartXAxisValues.push(item['week']);
            weeklyObservationsChartMax = (weeklyObservationsChartMax < item.total_observation) ? item.total_observation : weeklyObservationsChartMax;
            item.week = arr.length + 1;
            arr.push(item);
        }
        return arr;
    }, []);

    return { weeklyObservationsChartData, weeklyObservationsChartXAxisValues, weeklyObservationsChartMax};
};

const getContractorChartData = async (observations, topItems = 0) => {
    let contractorRatingsBarChartMax = 0;
    let contractorRatingsBarChartData = [];
    let contractorRatingsLineChartData = [];
    let taggedOwnerIds = [];
    let userEmployerIds = observations.reduce((arr, cc) => {
        if (cc.user_employer && cc.user_employer.employer) {
            //preparing chart data
            let existingItem = contractorRatingsBarChartData.find(item => (item.group == cc.user_employer.employer));
            let existingItemIndex = contractorRatingsBarChartData.findIndex(item => (item.group == cc.user_employer.employer));
            if (existingItem && existingItemIndex != -1) {
                existingItem.Rating += 1;
                contractorRatingsBarChartData[existingItemIndex] = existingItem;
            } else {
                contractorRatingsBarChartData.push({
                    group: cc.user_employer.employer, Rating: 1
                });
            }
        }

        if (cc.tagged_owner) {
            taggedOwnerIds.push(cc.tagged_owner);

            //preparing chart data
            let existingItem = contractorRatingsLineChartData.find(item => (item.company == cc.tagged_owner));
            let existingItemIndex = contractorRatingsLineChartData.findIndex(item => (item.company == cc.tagged_owner));
            if (existingItem && existingItemIndex != -1) {
                existingItem.Rating += 1;
                contractorRatingsLineChartData[existingItemIndex] = existingItem;
            } else {
                contractorRatingsLineChartData.push({
                    company: cc.tagged_owner, Rating: 1
                });
            }
        }
        return arr;
    }, []);

    let companiesInfo = await sails.models.createemployer.find({
        where: {id: _uniq([...userEmployerIds, ...taggedOwnerIds])},
        select: ['id', 'name']
    });

    let taggedOwners = companiesInfo.filter(company => taggedOwnerIds.includes(company.id));

    contractorRatingsBarChartData.map(item => {
        contractorRatingsBarChartMax = (contractorRatingsBarChartMax < item.Rating) ? item.Rating : contractorRatingsBarChartMax;
        return item;
    });

    contractorRatingsLineChartData.map(item => {
        let company = taggedOwners.find(emp => emp.id == item.company);
        if(company) {
            item.company = company.name;
            contractorRatingsBarChartMax = (contractorRatingsBarChartMax < item.Rating) ? item.Rating : contractorRatingsBarChartMax;

        }
        return item;
    });

    for (let index in contractorRatingsLineChartData) {
        let item = contractorRatingsLineChartData[index];
        let existingItemIndex = contractorRatingsBarChartData.findIndex(bItem => (bItem.group == item.company));
        if (existingItemIndex == -1) {
            contractorRatingsBarChartData.push({
                group: item.company, Rating: 0
            })
        }
    }

    if (topItems) {
        contractorRatingsBarChartData =  (contractorRatingsBarChartData || []).sort((a,b) => (a.Rating < b.Rating) ? 1 : ((b.Rating < a.Rating) ? -1 : 0)).slice(0, topItems);
    }

    contractorRatingsBarChartData =  (contractorRatingsBarChartData || []).sort((a,b) => (a.group > b.group) ? 1 : ((b.group > a.group) ? -1 : 0));

    let tempContractorRatingsLineChartData = contractorRatingsBarChartData.reduce((arr, item) => {
        let tempItem = contractorRatingsLineChartData.find(lItem => lItem.company == item.group);
        if (tempItem) {
            arr.push(tempItem);
        }
        return arr;
    }, []);

    contractorRatingsLineChartData = tempContractorRatingsLineChartData;

    return { contractorRatingsBarChartData, contractorRatingsLineChartData, contractorRatingsBarChartMax };
}

const cleanRecord = (record) => {
    sails.log.info("cleaning images", record.images);
    if (record.images && record.images.length) {
        record.images = (record.images || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
    }

    if (record.closeout_detail && record.closeout_detail.images && record.closeout_detail.images.length) {
        record.closeout_detail.images = (record.closeout_detail.images || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
    }

    if(record.tagged_owner) {
        record.tagged_owner = (record.tagged_owner && record.tagged_owner.id) ? record.tagged_owner.id : record.tagged_owner;
        sails.log.info('Tagged owner id is', record.tagged_owner);
    }

    return record;
}

const sendMailToTaggedOwner = async (goodCall, projectInfo, project_name, subject) => {
    let gcTaggedOwner = goodCall.tagged_owner && projectInfo.goodcall_setting.tagged_companies && projectInfo.goodcall_setting.tagged_companies.length
    let obrsTaggedOwner = goodCall.tagged_owner && projectInfo.custom_field.obsr_tagged_companies && projectInfo.custom_field.obsr_tagged_companies.length

    if (goodCall.data_type === "observation" ? obrsTaggedOwner : gcTaggedOwner) {
        let gcTaggedCompanies = projectInfo.goodcall_setting.tagged_companies
        let obrsTaggedCompanies = projectInfo.custom_field.obsr_tagged_companies
        let taggedOwners = goodCall.data_type === "observation" ? (obrsTaggedCompanies || []).filter(owner => owner.company_id == goodCall.tagged_owner) : (gcTaggedCompanies || []).filter(owner => owner.company_id == goodCall.tagged_owner);
        sails.log.info("taggedOwners", taggedOwners);
        let gcPhrase = projectInfo.custom_field.gc_phrase_singlr;
        let obrsPhrase = projectInfo.custom_field.obrs_phrase_singlr;
        let tz = getProjectTimezone(projectInfo);
        for (let key in taggedOwners) {
            let taggedOwner = taggedOwners[key];
            if (taggedOwner && taggedOwner.email) {
                let taggedCompany = await sails.models.createemployer_reader
                    .findOne({
                        where: {id: taggedOwner.company_id},
                        select: ['name']
                    });
                let emailHtml = await sails.renderView('pages/mail/mail-content', {
                    title: subject,
                    mail_body: 'gc-notify-tagged-owner',
                    project_name,
                    receiver_name: '',
                    company_name: taggedCompany.name,
                    images: goodCall.images,
                    gcPhrase: goodCall.data_type === "observation" ? obrsPhrase : gcPhrase,
                    feature_type: goodCall.data_type,
                    hazard_category: goodCall.hazard_category,
                    details: goodCall.details,
                    further_actions: goodCall.further_actions,
                    raised_date_time: dayjs(goodCall.createdAt).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss),
                    layout: false
                });
                sails.log.info('Sending mail to', taggedOwner.email);
                await sendMail(subject, [taggedOwner.email], emailHtml);
                sails.log.info('Email has been sent to tagged owner.');
            }
        }
    }
};

const sendPushNotificationToAssignedTo = async (req, projectInfo, goodCall, receiverUser, obrsPhrase, obrsPlrPhrase, category) => {
    let message = `${obrsPlrPhrase}: ${getUserFullName(req.user, true)} has assigned you an action on ${projectInfo.name}`;

    const notificationData = {
        category,
        document_id: goodCall.id,
        project_id: goodCall.project_ref,
        assigned_by: `${req.user.first_name} ${req.user.last_name}`
    }
    let firebaseMsgData = {
        category: category,
        document_id: goodCall.id.toString(),
        project_id: goodCall.project_ref.toString(),
        assigned_by: `${req.user.first_name} ${req.user.last_name}`
    };

    await sendPushNotification({
        message,
        messageTitle: `Tagged in ${obrsPhrase}`,
        recipientUserInfo: receiverUser,
        submittedByUserInfo: req.user,
        category,
        notificationData,
        firebaseMsgData
    });
};

const sendMailToAssignedTo = async (goodCall, projectInfo, assignedToUser, raisedByUser, assignedByUser, obrsPhrase) => {
    let subject = `${obrsPhrase}: ${projectInfo.name}`;
    let emailHtml = await sails.renderView('pages/mail/mail-content', {
        title: subject,
        mail_body: 'observation-assigned-to',
        project_name: projectInfo.name,
        obrsPhrase,
        goodCall,
        raised_datetime: dayjs(goodCall.createdAt).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss),
        assigned_to_name: getUserFullName(assignedToUser),
        raised_by_name: (goodCall.is_anonymous) ? 'Anonymous' : getUserFullName(raisedByUser),
        assigned_by_name: getUserFullName(assignedByUser),
        layout: false
    });
    sails.log.info('Sending mail to', assignedToUser.email);
    let sentStatus = await sendMail(subject, [assignedToUser.email], emailHtml);
    sails.log.info('Close out email has been sent');
}

const prepareViewOrDownloadGoodCall = async (req, res, whereClause, companyId, responseType = 'url') => {
    sails.log.info("good call find with: ", whereClause);
    let goodCall = await sails.models.goodcall.findOne(whereClause)
        .populate('user_ref')
        .populate('project_ref')
        .populate('tagged_owner')
        .populate('assigned_to');
    if (goodCall && goodCall.id) {
        let images = [];
        let closeout_images = [];
        if (goodCall.images && goodCall.images.length) {
            sails.log.info('Expanding good call images, Length: ', goodCall.images.length);
            goodCall.images = await sails.models.userfile.find({id: goodCall.images});
            goodCall.images.map(row => {
                if (row.img_translation && row.img_translation.length) {
                    images.push(...row.img_translation);
                } else if (row.file_url) {
                    images.push(row.sm_url || row.md_url || row.file_url);
                }
            });
        }
        let { project_logo_file, companyName } = await getCompanyInfo(goodCall.project_ref, (companyId ? {id: companyId} : null));

        let gcPhrase = goodCall.project_ref.custom_field ? goodCall.project_ref.custom_field.gc_phrase_singlr : 'Good Call';
        let obrsPhrase = goodCall.project_ref.custom_field ? goodCall.project_ref.custom_field.obrs_phrase_singlr : 'Observation';
        let tz = getProjectTimezone(goodCall.project_ref);
        let reportedBy = 'Anonymous';
        let employer = '';
        if (!goodCall.is_anonymous) {
            reportedBy = goodCall.user_ref.first_name+' '+goodCall.user_ref.last_name;

            let induction_requests = await sails.models.inductionrequest_reader.find({
                where:{ project_ref: goodCall.project_ref.id, user_ref: goodCall.user_ref.id, status_code: [2, 6] },
                select: ['additional_data'],
                sort:[
                    {id: 'DESC'},
                ],
                limit: 1
            });
            employer = ((induction_requests[0] && induction_requests[0].additional_data && induction_requests[0].additional_data.employment_detail && induction_requests[0].additional_data.employment_detail.employer) || '').toString().trim();

        }
        if (goodCall.closeout_detail && goodCall.closeout_detail.images && goodCall.closeout_detail.images.length) {
            sails.log.info('Expanding good call closeout images, Length: ', goodCall.closeout_detail.images.length);
            goodCall.closeout_detail.images = await sails.models.userfile.find({id: goodCall.closeout_detail.images});
            goodCall.closeout_detail.images.map(row => {
                if (row.img_translation && row.img_translation.length) {
                    closeout_images.push(...row.img_translation);
                } else if (row.file_url) {
                    closeout_images.push(row.sm_url || row.md_url || row.file_url);
                }
            });
        }

        let title = goodCall.data_type === "observation" ? obrsPhrase : gcPhrase;
        let project_line = `${goodCall.project_ref.name} (#${goodCall.project_ref.id}): ${companyName}`;
        let date_line = `Report Date: ${dayjs(goodCall.createdAt).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss)}`;
        let form_template = `pages/goodcall-form-page`;
        let html = await sails.renderView(form_template, {
            title,
            heading_line: title,
            date_line,
            project_line,
            record_id: goodCall.record_id,
            reported_by: reportedBy,
            is_anonymous: goodCall.is_anonymous,
            reported_by_employer: employer,
            hazard_category: goodCall.hazard_category,
            details: goodCall.details,
            feature_type: goodCall.data_type,
            further_actions: goodCall.further_actions,
            tagged_owner: (goodCall.tagged_owner && goodCall.tagged_owner.name) ? goodCall.tagged_owner.name : 'N/A',
            location: (goodCall.location && goodCall.location.description) ? goodCall.location.description : null,
            location_tag: (goodCall.location && goodCall.location.lat && goodCall.location.long) ? `${goodCall.location.lat}, ${goodCall.location.long}` : null,
            assigned_to: (goodCall.assigned_to && goodCall.assigned_to.id) ? getUserFullName(goodCall.assigned_to) : '',
            isSiteAdminProject: (goodCall.project_ref.project_category === 'default'),
            images: images,
            closeout_images: closeout_images,
            project_logo_file: project_logo_file,
            project: goodCall.project_ref,
            layout: false,
            raised_date_time: dayjs(goodCall.createdAt).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss),
            closeout_detail: goodCall.closeout_detail,
            dateFormatter: (timestamp) => dayjs(timestamp).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss)
        });

        let fileName = goodCall.data_type === "observation" ? obrsPhrase + '-Report-' + dayjs().format(dbDateFormat) : gcPhrase + '-Report-' + dayjs().format(dbDateFormat);
        return await downloadPdfViaGenerator({
            req,
            res,
            html,
            tool: 'good-call',
            file_name: fileName,
            heading_line: title,
            project_line,
            date_line,
            logo_file: project_logo_file,
            has_cover: true,
            has_one_page: true,
            responseType
        });
    }
    sails.log.info('Failed to find good call');
    return errorResponse(res, 'Failed to find good call');
}

const getUserGoodCallList = async (req, res) => {
    let projectId = +req.param('projectId', 0);
    // let companyId = +req.param('companyId', 0); // ?
    let dataType = req.param('dataType', '');
    let pageSize = +req.param('pageSize', 50);
    let pageNumber = +req.param('pageNumber', 0);
    let sortKey = req.param('sortKey', 'status');
    let sortDir = req.param('sortDir', 'asc');
    let category = req.param('category', null);
    let searchTerm = (req.param('q', '')).toString().trim();
    let statusCodes = (req.param('statusCodes', '')).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
    let onlyAssignToMe = req.param('assignTo', '').toString().trim() === 'me';

    let defaultResponse = {
        good_calls: [],
        totalCount: 0,
        q: searchTerm,
        pageSize,
        pageNumber,
        sortKey,
        sortDir,
        category,
        statusCodes,
        onlyAssignToMe,
        dataType,
    };

    if (!Object.values(goodCallFn.GOOD_CALL_DT).includes(dataType)) {
        return errorResponse(res, 'Invalid dataType supplied', defaultResponse);
    }
    let {
        total: totalCount,
        records: good_calls
    } = await goodCallFn.getUserGoodCallsPage(dataType, projectId, pageSize, (pageSize * pageNumber), sortKey, sortDir, {
        searchTerm, statusCodes, category,
        ...(onlyAssignToMe ? {
            assigned_to: req.user.id
        } : {
            userId: req.user.id
        }),
    });

    if (!good_calls.length) {
        sails.log.info(`No record found, for projectId: ${projectId}, q: "${searchTerm}" pageNumber: ${pageNumber} pageSize: ${pageSize} totalCount: ${totalCount}`);
        return successResponse(res, {...defaultResponse, totalCount});
    }

    let {images} = await goodCallFn.expandGCFiles(good_calls.map(gc => gc.id));

    for (let i = 0; i < good_calls.length; i++) {
        let images_row = (images || []).find(row => row.id === good_calls[i].id) || {};

        good_calls[i].images = images_row.images;
        good_calls[i].status_message = buildGCStatusMessage(good_calls[i].status);
    }

    return successResponse(res, {
        ...defaultResponse,
        good_calls,
        totalCount,
    });
};

const processGoodCallCloseOutAction = async (req, res, payload, existingGoodCall) => {
    payload = cleanRecord(payload);
    let closeoutImages = (req.body.is_closeout_request && req.body.closeout_detail && req.body.closeout_detail.images && req.body.closeout_detail.images.length) ? req.body.closeout_detail.images : [];
    if(closeoutImages && closeoutImages.length && existingGoodCall.project_ref && existingGoodCall.project_ref.goodcall_setting && existingGoodCall.project_ref.goodcall_setting.photo_disallowed){
        sails.log.warn(`creating good call / observations with photos not allow for given project, ${existingGoodCall.project_ref.id}`, payload);
        return errorResponse(res, sails.__('photos_not_allowed'));
    }
    let goodCall = await sails.models.goodcall.updateOne({id: +req.param('id')}).set(payload);
    if(goodCall && goodCall.id) {
        let projectInfo = existingGoodCall.project_ref;
        let gcPhrase = projectInfo.custom_field.gc_phrase_singlr;
        let obrsPhrase = projectInfo.custom_field.obrs_phrase_singlr;
        let obrsPlrPhrase = projectInfo.custom_field.obrs_phrase;
        //For Observation feature only
        if (req.body.is_closeout_request) {
            let project_name = projectInfo.name;
            let subject = `${obrsPhrase} Closeout: ${project_name}`;
            let user_name = req.body.user_name;
            let toUser = req.body.user_email;
            let emailHtml = await sails.renderView('pages/mail/mail-content', {
                title: subject,
                mail_body: 'observation-close-out',
                user_name,
                record_id: goodCall.record_id,
                project_name,
                obrsPhrase,
                closeout_description: req.body.closeout_detail.description,
                closeout_images: closeoutImages,
                hazard_category: goodCall.hazard_category,
                layout: false
            });

            sails.log.info('Sending mail to', toUser);
            let sentStatus = await sendMail(subject, [toUser], emailHtml);
            sails.log.info('Close out email has been sent');
        }

        goodCall.images = await sails.models.userfile_reader.find({
            where: {id: goodCall.images},
            select: ['file_url', 'sm_url']
        });
        if (existingGoodCall.tagged_owner != goodCall.tagged_owner) {
            let project_name = projectInfo.name;
            let subject = existingGoodCall.data_type === "observation" ? "Observation"+': Project - '+project_name : gcPhrase+': Project - '+project_name;
            sails.log.info('Starting process to send mail to tagged owners.');
            await sendMailToTaggedOwner(goodCall, projectInfo, project_name, subject);
        }

        if (existingGoodCall.assigned_to != goodCall.assigned_to) {
            sails.log.info('Sending push notification to user on assigning on observation.');
            const receiverUser = await sails.models.user_reader.findOne({
                where: {id: goodCall.assigned_to},
                select: ['id', 'first_name', 'last_name', 'email']
            });

            let notification_category = existingGoodCall.data_type === 'observation' ? NOTIFICATION_CATEGORY.OBSERVATION : NOTIFICATION_CATEGORY.GOOD_CALL;
            //push notification
            await sendPushNotificationToAssignedTo(req, projectInfo, goodCall, receiverUser, obrsPhrase, obrsPlrPhrase, notification_category);

            //email
            await sendMailToAssignedTo(goodCall, projectInfo, receiverUser, existingGoodCall.user_ref, req.user, obrsPhrase);
        }

        sails.log.info('good call updated successfully, id', goodCall ? goodCall.id : undefined);
        return ResponseService.successResponse(res, goodCall);
    }

    sails.log.info('Failed to update good call');
    return errorResponse(res, sails.__('Failed to update good call'));
};

const syncGoodCallToAsite = async (req, res, projectInfo, gcId, recordId, localsObj, userEmp = 0) => {
    sails.log.info(`[syncGoodCallToAsite] Starting execution for good call ${gcId} employer ID ${userEmp}`);

    let {workspace_id, matched_tool} = await getAsiteProjectToolMapping(projectInfo.id, 'good_calls', userEmp);
    if(!workspace_id || !matched_tool) {
        sails.log.info(`[syncGoodCallToAsite] Aborting execution for good call ${gcId} workspace_id or matched_tool not found.`);
        return;
    }

    let { employer } = await getCompanyInfo(projectInfo, null, ['id', 'company_initial']);

    sails.log.info(`[syncGoodCallToAsite] preparing PDF for good call ${gcId}`);
    res.locals = localsObj || res.locals;

    let fileData = await prepareViewOrDownloadGoodCall(req, res, {id: gcId}, userEmp, 'path');
    sails.log.info(`[syncGoodCallToAsite] PDF prepared, starting asite upload for good call ${gcId}`);

    //File name format -> {PROJECT/CONTRACT NUMBER} ({INNDEX PROJECT ID NUMBER})-{COMPANY INITIALS}-{TOOL NAME}-{REPORT TYPE}-{REPORT #}
    let toolPhraseForAsite = (projectInfo.custom_field.gc_phrase || '').replace(/\s/g, '_');
    fileData.name = `${projectInfo.project_number} (${projectInfo.id})-${employer.company_initial}-${toolPhraseForAsite}-GoodCall_Report-${recordId}.pdf`;
    sails.log.info(`[syncGoodCallToAsite] Filename to be used on asite ${fileData.name}`);

    await uploadDocOnAsite(employer.id, workspace_id, matched_tool.folder_id, fileData);
};

module.exports = {

    createGoodCall: async (req, res) => {
        const dataType = req.query.data_type
        sails.log.info('Create good call for project, by', req.user.id);
        let payload = _.pick((req.body || {}), [
            'project_ref',
            'company_ref',
            'hazard_category',
            'details',
            'further_actions',
            'images',
            'is_anonymous',
            'tagged_owner',
            'required_closeout',
            'location',
            'assigned_to',
        ]);
        if(dataType && dataType === "observation"){
            payload = {
                ...payload,
                data_type: "observation",
                status: (!payload.required_closeout) ? 3 : 1
            }
        }else{
            payload = {
                ...payload,
                data_type: "good-call"
            }
        }
        if (!payload.project_ref) {
            return errorResponse(res, 'Invalid request.');
        }

        payload.user_ref = req.user.id;
        let revision = await getLatestUserRevision(req.user.id);
        payload.user_revision_ref = revision.id;
        sails.log.info('creating good call with', payload);
        payload = cleanRecord(payload);

        let projectInfo = await sails.models.project_reader.findOne({where: {id: payload.project_ref}, select: ['name','project_category','parent_company', 'goodcall_setting', 'custom_field', 'project_number']});
        if(payload.images && payload.images.length && projectInfo.goodcall_setting && projectInfo.goodcall_setting.photo_disallowed){
            sails.log.warn(`creating good call / observation with photos not allow for given project, ${projectInfo.id}`, payload);
            return errorResponse(res, sails.__('photos_not_allowed'));
        }
        let goodCall = await sails.models.goodcall.create(payload);
        if(goodCall) {
            let project_name = projectInfo.name;
            let gcPhrase = projectInfo.custom_field.gc_phrase_singlr;
            let obrsPhrase = projectInfo.custom_field.obrs_phrase_singlr;
            let obrsPlrPhrase = projectInfo.custom_field.obrs_phrase;
            let subject = dataType === "observation" ? "Observation" +': Project - '+project_name : gcPhrase +': Project - '+project_name;
            let good_call_user = 'and set to anonymous';
            if (!goodCall.is_anonymous) {
                good_call_user = 'by '+req.user.first_name +' '+ req.user.last_name;
            }

            let projUsrResult = await allProjectAdminsByOneOfDesignations(goodCall.project_ref, ['nominated', 'custom']);
            let feature = dataType === "observation" ? "observations" : "good_calls";
            projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, feature);
            sails.log.info('Send email to nom managers, count:', projUsrResult.length);
            for (let j = 0, len = projUsrResult.length; j < len; j++) {
                let nomManager = projUsrResult[j].user_ref || {};
                let user_name = (nomManager.first_name) ? nomManager.first_name : '';
                let emailHtml = await sails.renderView('pages/mail/mail-content', {
                    title: subject,
                    mail_body: 'good-call',
                    user_name,
                    gcPhrase: dataType === "observation" ? "Observation" : gcPhrase,
                    good_call_user,
                    project_name,
                    layout: false
                });
                sails.log.info('Sending mail to', nomManager.email);
                await sendMail(subject, [nomManager.email], emailHtml);
                sails.log.info('Good call notification email has been sent');
            }

            goodCall.images = await sails.models.userfile_reader.find({
                where: {id: goodCall.images},
                select: ['file_url', 'sm_url']
            });
            //send mail to company project admins(Nominated Managers)
            if (projectInfo.project_category === 'company-project' && projectInfo.parent_company) {
                if(dataType && dataType === "observation"){
                    await sendMailToNominatedManagerCPA(projectInfo.parent_company, projectInfo, req.user, obrsPhrase, 'raised a '+obrsPhrase, goodCall.is_anonymous);
                }else{
                    await sendMailToNominatedManagerCPA(projectInfo.parent_company, projectInfo, req.user, gcPhrase, 'raised a '+gcPhrase, goodCall.is_anonymous);
                }
            } else {
                sails.log.info('Starting process to send mail to tagged owners.');
                await sendMailToTaggedOwner(goodCall, projectInfo, project_name, subject);
            }

            if (dataType && dataType === "observation" && goodCall.assigned_to) {
                const receiverUser = await sails.models.user_reader.findOne({
                    where: {id: goodCall.assigned_to},
                    select: ['id', 'first_name', 'last_name', 'email']
                });

                //push notification
                await sendPushNotificationToAssignedTo(req, projectInfo, goodCall, receiverUser, obrsPhrase, obrsPlrPhrase, NOTIFICATION_CATEGORY.OBSERVATION);

                //email
                await sendMailToAssignedTo(goodCall, projectInfo, receiverUser, req.user, req.user, obrsPhrase);
            }

            let hasAsite = await checkIfAsiteEnabled(projectInfo.id);
            if (hasAsite && dataType && dataType === "good-call" ){
                sails.log.info(`Project has asite enabled trying to sync good calls document for project ${projectInfo.id}`);

                let userEmployer = await getUserInductionEmployer({id: payload.user_ref}, payload.project_ref);
                // Check if user's induction employer not there, Load employer from profile data.
                if(!userEmployer) {
                    userEmployer = req.user.parent_company;
                }
                // Not awaiting syncGoodCallToAsite fn, to run the PDF generation and asite upload in backend.
                let localsObj = Object.assign({}, res.locals); // Cloning the res.locals, as the object gets cleared after the API response sent.
                syncGoodCallToAsite(req, res, projectInfo, goodCall.id, goodCall.record_id, localsObj, userEmployer).catch(sails.log.error);
            }
            sails.log.info('Created good call successfully.');
            return successResponse(res, {good_call: goodCall});
        }

        sails.log.info('Failed to create good call');
        return errorResponse(res, sails.__('Failed to create good call'));
    },

    updateGoodCall: async (req, res) => {
        sails.log.info('Update good call for project, by', req.user.id);
        let payload = _.pick((req.body || {}), [
            'hazard_category',
            'details',
            'further_actions',
            'images',
            'is_anonymous',
            'tagged_owner',
            'status',
            'required_closeout',
            'closeout_detail',
            'assigned_to'
        ]);

        let existingGoodCall = await sails.models.goodcall_reader.findOne({
            where: {id: +req.param('id')},
            select: ['project_ref', 'user_ref', 'tagged_owner','assigned_to','data_type']
        });
        existingGoodCall = await populateUserRefs([existingGoodCall], 'user_ref', []);
        [existingGoodCall] = await populateProjectRefs(existingGoodCall, 'project_ref', []);

        return processGoodCallCloseOutAction(req, res, payload, existingGoodCall);
    },

    closeOutGoodCallAction: async (req, res) => {
        sails.log.info('Update good call for project, by', req.user.id);
        let payload = _.pick((req.body || {}), [
            'status',
            'closeout_detail',
        ]);

        let existingGoodCall = await sails.models.goodcall_reader.findOne({
            where: {id: +req.param('id')},
            select: ['project_ref', 'user_ref', 'tagged_owner','assigned_to','data_type']
        });
        existingGoodCall = await populateUserRefs([existingGoodCall], 'user_ref', []);
        [existingGoodCall] = await populateProjectRefs(existingGoodCall, 'project_ref', []);

        if(existingGoodCall.assigned_to !== req.user.id) {
            sails.log.info('User not allowed to closed out good call.', req.user.id);
            return successResponse(res, {message: 'Unable to closeout, Access denied.'});
        }

        return processGoodCallCloseOutAction(req, res, payload, existingGoodCall);
    },

    getGoodCall: async (req, res) => {
        let id = +req.param('id');
        sails.log.info('Fetch good call, id:', id);

        let goodCall = await sails.models.goodcall_reader.findOne({id: id})
            .populate('user_ref')
            .populate('project_ref')
            .populate('tagged_owner')
            .populate('assigned_to');

        let imageIds = [...goodCall.images, ...(goodCall.closeout_detail.images || [])];
        if(imageIds.length) {
            sails.log.info('Expanding good call images.');
            let imagesObj = await sails.models.userfile_reader.find({
                where: {id: _uniq(imageIds)},
                select: ['sm_url', 'md_url', 'file_url', 'createdAt', 'img_translation', 'file_type']
            });

            goodCall.images = (imagesObj || []).filter(image => goodCall.images.includes(image.id));
            goodCall.closeout_detail.images = (imagesObj || []).filter(image => (goodCall.closeout_detail.images || []).includes(image.id));
        }
        return successResponse(res, {good_call: goodCall});
    },

    getProjectGoodCalls: async (req, res) => {
        let projectId = +req.param('projectId');
        let companyId = +req.param('companyId');
        let search = decodeURIParam((req.param('search', '')).toString().trim());
        let dataType = req.query.data_type
        let category = req.param('category') ? decodeURIParam(req.param('category', '').toString().trim()).split(/,(?!\s)/) : [];
        let assigned_to = req.param('assigned_to') ? req.param('assigned_to').split(",") : [];
        let raised_by = req.param('raised_by') ? req.param('raised_by').split(",") : [];
        let status =  (req.param('status') && req.param('status').toLowerCase() != 'null')? req.param('status').split(",").map(a=> +a) : [];
        let download = req.param('download');
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let tagged_owner = req.param('tagged_owner') ? req.param('tagged_owner').split(',').map(a=>+a): [];

        let filter;
        if (projectId) {
            filter = {project_ref: projectId};
        }

        if (assigned_to && assigned_to.length) {
            filter.assigned_to = assigned_to
        }

        if (raised_by && raised_by.length) {
            let is_anonymous = raised_by.includes('0');
            if (is_anonymous) {
                if (raised_by.length == 1) {
                    // This will filter for only anonymous records
                    filter.is_anonymous = true;
                } else {
                    // This will filter for both anonymous and raised_by records
                    filter.or = [
                        { is_anonymous: true },
                        {
                            and: [
                                { is_anonymous: false },
                                { user_ref: { in: raised_by.filter(record => record !== 0) } }
                            ]
                        }
                    ];
                }
            }

            if (!is_anonymous) {
                // This will filter for only for raised_by records
                filter.is_anonymous = false;
                filter.user_ref = { in: raised_by };
            }
        }

        let sortingOrder = [
            {id: 'DESC'}
        ];
        if(dataType && dataType === "observation"){
            filter.data_type = "observation";
            /*sortingOrder = [
                {status: 'ASC'},
                {id: 'DESC'}
            ]*/
        } else {
            filter.data_type = "good-call";
        }

        if (companyId && !is_inherited_project) {
            filter.company_ref = companyId;
        }
        if(download && download === 'true') {
            let projectGoodCallsDownload = await sails.models.goodcall_reader.find(filter).sort(sortingOrder);

            projectGoodCallsDownload = await populateEmployerRefs(projectGoodCallsDownload, 'tagged_owner', ['id', 'name', 'country_code', 'company_initial', 'logo_file_id', 'projects_alias']);
            projectGoodCallsDownload = await populateProjectRefs(projectGoodCallsDownload, 'project_ref', []);
            projectGoodCallsDownload = await populateUserRefs(projectGoodCallsDownload, 'user_ref', []);
            projectGoodCallsDownload = await populateUserRefs(projectGoodCallsDownload, 'assigned_to', ['first_name', 'last_name', 'email']);

            projectGoodCallsDownload = await expandProjectGoodCalls(projectGoodCallsDownload);
            return successResponse(res, {good_calls: projectGoodCallsDownload});
        }
        if(category && category.length){
            filter.hazard_category = {in: category};
        }

        if(status && status.length){
            filter.status = {in:status};
        }
        if(tagged_owner && tagged_owner.length) {
            filter.tagged_owner = { in: tagged_owner };
        }
        let allRecords = await sails.models.goodcall_reader.find({
            where: filter,
            select: ['user_ref', 'is_anonymous', 'tagged_owner', 'hazard_category', 'status', 'project_ref', 'record_id']
        }).sort(sortingOrder);

        allRecords = await populateEmployerRefs(allRecords, 'tagged_owner', ['id', 'name']);
        allRecords = await populateUserRefs(allRecords, 'user_ref', ['id', 'first_name', 'middle_name', 'last_name']);
        allRecords = await populateUserRefs(allRecords, 'assigned_to', ['first_name', 'last_name', 'email']);
        allRecords = await populateInductionEmployerRef(allRecords);

        let refIds = [];
        if(search && search !== 'null') {
            const val = search.toLowerCase();
            const temp = allRecords.filter(function (d) {
                return (
                    (d.is_anonymous && 'anonymous'.indexOf(val) !== -1) ||
                    (!d.is_anonymous && d.user_ref.first_name && d.user_ref.first_name.toLowerCase().indexOf(val) !== -1) ||
                    (!d.is_anonymous && d.user_ref.middle_name && d.user_ref.middle_name.toLowerCase().indexOf(val) !== -1) ||
                    (!d.is_anonymous && d.user_ref.last_name && d.user_ref.last_name.toLowerCase().indexOf(val) !== -1) ||
                    (!d.is_anonymous && d.user_employer && d.user_employer.employer && d.user_employer.employer.toLowerCase().indexOf(val) !== -1) ||
                    (d.hazard_category && d.hazard_category.toLowerCase().indexOf(val) !== -1) ||
                    (d.tagged_owner && d.tagged_owner.id && d.tagged_owner.name.toLowerCase().indexOf(val) !== -1) ||
                    (d.record_id && d.record_id.includes(search))  || !val
                );
            });

            temp.map(id => {
                refIds.push(id.id)
            })
            filter = {id: refIds};
        }
        sails.log.info('fetch all good-calls with filter:', filter, `is_inherited_project: ${is_inherited_project}`);
        let projectGoodCalls = await sails.models.goodcall_reader.find(filter)
            .sort(sortingOrder)
            .limit(pageSize)
            .skip(pageNumber * pageSize);

        projectGoodCalls = await populateEmployerRefs(projectGoodCalls, 'tagged_owner', ['id', 'name', 'country_code', 'company_initial', 'logo_file_id', 'projects_alias']);
        projectGoodCalls = await populateProjectRefs(projectGoodCalls, 'project_ref', []);
        projectGoodCalls = await populateUserRefs(projectGoodCalls, 'user_ref', []);
        projectGoodCalls = await populateUserRefs(projectGoodCalls, 'assigned_to', []);

        /*if(dataType && dataType === "observation") {
            let openObservations = (projectGoodCalls || []).filter(record => (record.status && record.status == 1));
            let restRecords = (projectGoodCalls || []).filter(record => (record.status != 1));
            restRecords = (restRecords || []).sort((a,b) => (a.createdAt < b.createdAt) ? 1 : ((b.createdAt < a.createdAt) ? -1 : 0));
            projectGoodCalls = [...openObservations, ...restRecords];
        }*/

        projectGoodCalls = await attachProfilePicWithUserRefInfo(projectGoodCalls);
        projectGoodCalls = await expandProjectGoodCalls(projectGoodCalls);

        allRecords = (refIds.length) ? (allRecords || []).filter(record => refIds.includes(record.id)) : allRecords;
        let types = [], openCount = 0;
        allRecords.filter(t => {
            if(t.hazard_category && t.hazard_category != '') {
                types.push(t.hazard_category);
            }
            openCount += (t.status == 1) ? 1 : 0;
        });
        let taggedOwners = allRecords.reduce((arr, record) => {
            if (record.tagged_owner &&
                record.tagged_owner.id &&
                !arr.find(item => record.tagged_owner.id == item.company_id)) {
                arr.push({
                    "company_id": record.tagged_owner.id,
                    "company_name": record.tagged_owner.name
                })
            }
            return arr;
        }, []);

        let emp = ['Anonymous'];
        allRecords.filter(t => {
            if(!t.is_anonymous && t.user_employer && t.user_employer.employer) {
                emp.push(t.user_employer.employer);
            }
        });
        let employersList = [...new Set(emp)];
        return successResponse(res, {good_calls: projectGoodCalls, total_open_count: openCount, total_record_count: allRecords.length, hazard_types: types, tagged_owners: taggedOwners, employer_list: employersList});
    },

    getProjectBasedGoodCallUtils: async (req, res) => {
        const projectId = +req.param('projectId');
        let dataType = req.query.data_type;

        sails.log.info(`Fetching good call utils: projectId: ${projectId}, dataType: ${dataType}`);

        if (!(dataType === goodCallFn.GOOD_CALL_DT.GOOD_CALL || dataType === goodCallFn.GOOD_CALL_DT.OBSERVATION)) {
            dataType = goodCallFn.GOOD_CALL_DT.GOOD_CALL;
        }

        let [
            hazard_types,
            tagged_owners,
            employer,
            assigned_to,
            raised_by,
        ] = await Promise.all([
            goodCallFn.getProjectGoodCallsHazardTypes(projectId, dataType),
            goodCallFn.getProjectGoodCallsTaggedOwners(projectId, dataType),
            goodCallFn.getProjectGoodCallsEmployer(projectId, dataType),
            goodCallFn.getProjectGoodCallsAssignedTo(projectId, dataType),
            goodCallFn.getProjectGoodCallsRaisedBy(projectId, dataType)
        ]);

        employer = ['Anonymous', ...employer];

        if (dataType === goodCallFn.GOOD_CALL_DT.GOOD_CALL) {
            raised_by = [{id: 0, name: "Anonymous"}, ...raised_by];
        }

        sails.log.info(`Fetched hazard types: ${hazard_types.length}, tagged owners: ${tagged_owners.length}, employer: ${employer.length}, projectId: ${projectId}`);

        let resObj = {
            hazard_types,
            tagged_owners,
            employer,
            assigned_to,
            raised_by,
        }

        return ResponseService.successResponse(res,resObj);
    },

    getUserGoodCalls: async (req, res) => {
        let userId = +req.param('userId');
        let projectId = +req.param('projectId');
        let companyId = +req.param('companyId');
        let dataType = req.query.data_type;
        let assignTo = (req.query.user_type || '').toString().trim() === 'assign_to';

        let filter = (assignTo) ? {assigned_to:userId} : {user_ref:userId};
        if (projectId) {
            filter.project_ref = projectId;
        }

        if (companyId) {
            filter.company_ref = companyId;
        }

        if(dataType && dataType === "observation"){
            filter.data_type = "observation"
        }else{
            filter.data_type = "good-call"
        }

        let userGoodCalls = await sails.models.goodcall_reader.find(filter)
            .sort([
                {status: 'ASC'}
            ]);

        userGoodCalls = await populateEmployerRefs(userGoodCalls, 'tagged_owner', ['id', 'name', 'country_code', 'company_initial', 'logo_file_id', 'projects_alias']);
        userGoodCalls = await populateProjectRefs(userGoodCalls, 'project_ref', []);
        userGoodCalls = await populateUserRefs(userGoodCalls, 'user_ref', []);
        userGoodCalls = await populateUserRefs(userGoodCalls, 'assigned_to', ['first_name', 'last_name', 'email']);

        userGoodCalls = await expandProjectGoodCalls(userGoodCalls);
        return successResponse(res, {good_calls: userGoodCalls});
    },

    downloadGoodCallV1: async (req, res) => {
        let id = +req.param('id');
        let companyId = +req.body.companyId || 0;
        let createdAt = +req.body.createdAt;
        sails.log.info('good call, id', id, "Requesting company:", companyId);

        let whereClause = {id: id, createdAt: createdAt};
        return await prepareViewOrDownloadGoodCall(req, res, whereClause, companyId);
    },

    projectObservationDashboard: async (req, res) => {
        let projectId = +req.param('projectId');
        let companyId = +req.param('companyId', 0);
        let type = req.param('type');
        let totalPages = 1;
        let where = {
            project_ref: projectId,
            data_type: 'observation'
        };

        if (req.body.from_date && req.body.to_date) {
            where.createdAt = {'>=': +req.body.from_date, '<=': +req.body.to_date};
        }

        sails.log.info('Fetch observations with filter: ', where, "Requesting company:", companyId);
        let observations = await sails.models.goodcall.find(where)
            .sort([
                {id: 'ASC'}
            ]);
        observations = await populateProjectRefs(observations, 'project_ref', []);
        observations = await populateUserRefs(observations, 'user_ref', []);
        observations = await populateUserRefs(observations, 'assigned_to', ['first_name', 'last_name', 'email']);
        observations = await populateInductionEmployerRef(observations);

        let projectInfo = await sails.models.project.findOne({
            where: {id: projectId},
            select: ['name', 'project_number', 'project_category', 'parent_company', 'client', 'contractor', 'project_type', 'custom_field', 'createdAt']
        });

        let obrsAlternetPhrase = (projectInfo.custom_field.obrs_phrase) ? projectInfo.custom_field.obrs_phrase : `Observation`;

        if (observations.length) {
            let { project_logo_file, companyName } = await getCompanyInfo(projectInfo, (companyId ? {id: companyId} : null));

            //form good call/day count
            let daysBetweenDateRange = dayjs(+req.body.to_date).diff(dayjs(+req.body.from_date), 'days');
            let observationsPerDay = parseFloat(observations.length/daysBetweenDateRange).toFixed(2);

            //Open & Closed Chart
            let openObservations = (observations || []).filter(cc => cc.status == 1);
            sails.log.info(`openObservation: ${openObservations.length}`);

            let openClosedChartData = [
                { name: 'Open', value: openObservations.length, type: 'Rating: Poor' },
                { name: 'Closed/No Closeout', value: observations.length - openObservations.length, type: '' }];
            let openClosedChartWidth = 750;
            let openClosedChartHeight = 10;
            let openClosedLabelFontSize = '.4em';
            let hasAllClosedOut = !(openObservations.length) ? `All ${observations.length} items closed out` : '';

            let openClosedAdjustments = {
                adj1: -2,
                adj2: 10,
                adj3: -14,
                adj4: 6,
                adj5: -2.5,
                adj6: .5,
                adj7: 18,
                adj8: (hasAllClosedOut) ? 5.2 : 4.7,
                adj9: (hasAllClosedOut) ? 8 : 17,
                adj10: (hasAllClosedOut) ? 1 : 0,
                adj11: -8.5,
                adj12: -3,
                adj13: 6.5,
            };
            let openClosedMarginTop = '0px';

            //Top 10 good call Chart
            let top10ObservationChartWidth = 472;
            let top10ObservationChartHeight = 250;
            let  { top10ObservationChartData, top10ObservationChartMax } = getTop10ObservationChartData(observations);

            //Weekly observations Chart
            let weeklyObservationsChartWidth = 472;
            let weeklyObservationsChartHeight = 250;
            let { weeklyObservationsChartData, weeklyObservationsChartXAxisValues, weeklyObservationsChartMax } = getWeeklyObservationChartData(+req.body.from_date, +req.body.to_date, observations);

            //Contractor Ratings Chart
            let contractorRatingsChartWidth = 472;
            let contractorRatingsChartHeight = 250;
            let contractorXAxisLabelAdj = 0;
            let lineChartAdj = 25;
            let contractorRatingsChartLegendAdj = 252;
            let { contractorRatingsBarChartData, contractorRatingsLineChartData, contractorRatingsBarChartMax } = await getContractorChartData(observations, 0);

            //sails.log.info('contractorRatingsBarChartData: ', contractorRatingsBarChartData);
            //sails.log.info('contractorRatingsLineChartData: ', contractorRatingsLineChartData);

            //Average Closeout Time Chart
            let averageCloseoutTimeChartData = prepareWeeksData(+req.body.from_date, +req.body.to_date);
            for (let index in observations) {
                let observation= observations[index];
                let weeklyObservation = averageCloseoutTimeChartData.find(item => (+observation.createdAt >= item.start_of_week && +observation.createdAt <= item.end_of_week));
                let weeklyObservationIndex = averageCloseoutTimeChartData.findIndex(item => (observation.createdAt >= item.start_of_week && observation.createdAt <= item.end_of_week ));
                if (weeklyObservation && weeklyObservationIndex != -1 && observation.status == 2 && observation.closeout_detail && observation.closeout_detail.closeout_at) {
                    weeklyObservation['closedout_observations'].push(observation);
                    averageCloseoutTimeChartData[weeklyObservation] = weeklyObservation;
                }
            }

            let averageCloseoutTimeChartWidth = 472;
            let averageCloseoutTimeChartHeight = 250;
            let averageCloseoutTimeChartMax = 0;
            averageCloseoutTimeChartData = averageCloseoutTimeChartData.map(item => {
                let tempArr = [];
                if (item.closedout_observations && item.closedout_observations.length) {
                    for (let index in item.closedout_observations) {
                        let cc = item.closedout_observations[index];
                        let day = ((+cc.closeout_detail.closeout_at- +cc.createdAt) / (60*60*1000)) / 24; //(hours)/24
                        tempArr.push(day);
                    }
                }
                item.average_closedout_time = (tempArr.length) ? (tempArr.reduce(function(a, b){return a + b;}, 0))/tempArr.length : 0;
                averageCloseoutTimeChartMax = (averageCloseoutTimeChartMax < item.average_closedout_time) ? item.average_closedout_time : averageCloseoutTimeChartMax;
                return item;
            });

            //removed week with 0 values
            let averageCloseoutTimeChartXAxisValues = [''];
            averageCloseoutTimeChartData = averageCloseoutTimeChartData.reduce((arr, item) => {
                if (item.average_closedout_time) {
                    averageCloseoutTimeChartXAxisValues.push(item['week']);
                    item.week = arr.length + 1;
                    arr.push(item);
                }
                return arr;
            }, []);

            //change setting for pdf
            if(type == 'pdf') {
                openClosedChartWidth = 1000;
                openClosedChartHeight = 7;
                openClosedAdjustments = {
                    adj1: -2,
                    adj2: 7,
                    adj3: -10,
                    adj4: 6,
                    adj5: -2.5,
                    adj6: .5,
                    adj7: 18,
                    adj8: 6,
                    adj9: 14,
                    adj10:0,
                    adj11: -8.5,
                    adj12: -3,
                    adj13: 6.5,
                };

                top10ObservationChartWidth = 700;
                top10ObservationChartHeight = 265;

                weeklyObservationsChartWidth = 700;
                weeklyObservationsChartHeight = 265;

                contractorRatingsChartWidth = 700;
                contractorRatingsChartHeight = 280;
                contractorXAxisLabelAdj = -60;
                lineChartAdj = 40;
                contractorRatingsChartLegendAdj = 400;

                averageCloseoutTimeChartWidth = 700;
                averageCloseoutTimeChartHeight = 280;
            }

            let form_template = `pages/observation-dashboard-page`;
            let html = await sails.renderView(form_template, {
                title: `${obrsAlternetPhrase}`,
                report_from: (+req.body.from_date) ? dayjs(+req.body.from_date).format(dbDateFormat_slash_DDMMYYYY): '',
                report_to: (+req.body.to_date) ? dayjs(+req.body.to_date).format(dbDateFormat_slash_DDMMYYYY): '',
                company_name: companyName,
                project_number: projectInfo.project_number,
                project_name: projectInfo.name,
                project_logo_file,
                report_type: type,
                total_observation: observations.length,
                open_cc_count: openObservations.length,
                closed_cc_count: observations.length - openObservations.length,
                observationsPerDay,
                openClosedBarChart: await getOpenClosedBarChart(openClosedChartData, hasAllClosedOut, openClosedChartWidth, openClosedChartHeight, openClosedLabelFontSize, openClosedMarginTop, openClosedAdjustments, ["#D60707", "#1FA61B"], []),
                top10ObservationChart: await getStackedBarChart(top10ObservationChartData, ['group', 'Count'], top10ObservationChartMax, top10ObservationChartWidth, top10ObservationChartHeight, ["#4472C4"], '7px','Number Raised', false, false, 0),
                weeklyObservationChart: await getScatterPlot(weeklyObservationsChartData, 'week', weeklyObservationsChartXAxisValues, ['total_observation'], weeklyObservationsChartMax, weeklyObservationsChartWidth, weeklyObservationsChartHeight, '7px', 'Week', `Number Raised`, 0, false, ["#ffc36a"], false, ""),
                contractorRatingsChart: await getStackedBarWithLineChart({
                    data: contractorRatingsBarChartData,
                    columns: [ 'group', 'Rating' ],
                    lineChartData: contractorRatingsLineChartData,
                    lineChartItemName: 'company',
                    lineChartAllGroup: [ 'Rating' ],
                    lineChartAdj,
                    maxRating: contractorRatingsBarChartMax,
                    width: contractorRatingsChartWidth,
                    height: contractorRatingsChartHeight,
                    colors: ['#D0CECE'],
                    axisFontSize: '7px',
                    yAxisLabel: `Number Raised`,
                    xAxisLabel: "Contractor",
                    xAxisLabelAdj: contractorXAxisLabelAdj,
                    legendData: [{key: 'Raised by', color: "#D0CECE"}, {key: 'Owner', color: "#ffc36a"}],
                    legendsAdj: contractorRatingsChartLegendAdj
                }),
                averageCloseoutTimeChart: await getScatterPlot(averageCloseoutTimeChartData, 'week', averageCloseoutTimeChartXAxisValues, ['average_closedout_time'], averageCloseoutTimeChartMax, averageCloseoutTimeChartWidth, averageCloseoutTimeChartHeight, '7px', 'Week', 'Days to Closeout', 0, false, ["#ffc36a"], false, ""),
                totalPages,
                layout: false,
            });
            if (type === 'pdf') {
                let fileName = `${obrsAlternetPhrase}-Dashboard-${dayjs().format('MM-DD-YYYY')}`;
                return await instantPdfGenerator(req, res, html, 'close-call-p-dashboard', fileName, req.headers['user-agent'], { format: 'A3', landscape: true }, 'url');
            }

            sails.log.info('Rendering html view');
            return res.send(html);
        }
        sails.log.info('No observations found in selected criteria: ', where);
        return res.send(`<p style='text-align: center;margin-top: 100px;margin-bottom: auto;'>No ${obrsAlternetPhrase} found in selected criteria.</p>`);
    },

    getUserGoodCallList: getUserGoodCallList,
    getUserGoodCallListV3: getUserGoodCallList,
};
