const {
    DEFAULT_PAGE_SIZE,
    EMAIL_NOTIFICATION_FEATURE_CODES,
    displayDateFormat_DD_MM_YYYY_HH_mm_ss,
    displayDateFormat_DD_MM_YYYY
} = sails.config.constants;
const {
    UserRevisionService: {getLatestUserRevision},
    ResponseService: {
        errorResponse,
        successResponse
    },
    EmailService: {
        sendMail,
        queueEmailNotifications
    },
    DataProcessingService: {
        getUserFullName,
        populateUserRefs,
        populateProjectRefs,
    },
    OptimaSyncService: {
        callOptimaBoxGateway,
        updateBadge
    },
    ExcelService: {
        streamExcelDownload,
        downloadAssignedConductCard,
    },
} = require('./../services');

const {
    CardValidator: {
        createRecord,
        updateRecord,
        assignCard,
        unassignedClause
    }
} = require('./../validators');
const dayjs = require('dayjs');

const getConductCards = async (req, res, companyId) => {
    sails.log.info('Fetching conduct cards for company portal, ', companyId);
    let card_conducts = await sails.models.conductcard_reader.find({company_ref: companyId});
    if(card_conducts)  {
        sails.log.info('conduct cards fetched successfully.');
        return successResponse(res, {card_conducts});
    }

    sails.log.info('Failed to fetch conduct cards for company, ', companyId);
    return errorResponse(res, `Failed to fetch conduct cards for company, ${companyId}`);
};

const getEpoch = (expire_in) => {
    let days = (Object.keys(expire_in).length && !isNaN(expire_in.month) && !isNaN(expire_in.day)) ? (+(expire_in.month) * 30) + +(expire_in.day) : (expire_in.month && !expire_in.day) ? (+(expire_in.month)*30) : (!expire_in.month && expire_in.day) ? +(expire_in.day) : null;
    sails.log.info(`Days for Epoch: ${days}`);
    return (!isNaN(days)) ? dayjs().add(+days, 'days').valueOf() : null;
};

const unAssignCard = async (req, res, id) => {
    sails.log.info(`unassigning conduct card ${id} for user.`);
    let where = {
        id: +(req.body.assigned_id),
        conduct_card_ref: id,
        user_ref: +(req.body.user_ref)
    }

    let {validationError, payload} = unassignedClause(where);
    if(validationError){
        return errorResponse(res, 'Invalid Request.', {validationError});
    }

    let main_unassigned_record = await sails.models.userconductcard.destroyOne(where);

    //Check & remove child records
    let associateRecords = await sails.models.userconductcard_reader.find({
        select: ['id'],
        where: {parent_ref: +main_unassigned_record.id}
    });

    sails.log.info(`Found ${associateRecords.length} child conduct cards`);

    if (associateRecords && associateRecords.length) {
        for(let i = 0, len = associateRecords.length; i < len; i++) {
            let unassigned_child = await sails.models.userconductcard.destroyOne(associateRecords[i].id);
            //Update induction status if no more negative card found for the user on the project
            if (unassigned_child.card_detail && unassigned_child.card_detail.card_type && unassigned_child.card_detail.card_type == 'Negative') {
                await updateInductionStatusOnUnassignedCard(req, unassigned_child);
            }
        }
    }


    //Update induction status if no more negative card found for the user on the project
    if (main_unassigned_record.card_detail && main_unassigned_record.card_detail.card_type && main_unassigned_record.card_detail.card_type == 'Negative') {
        await updateInductionStatusOnUnassignedCard(req, main_unassigned_record);
    }

    sails.log.info(`Conduct cards has been unassigned successfully: `, main_unassigned_record);
    return successResponse(res, {message: "Conduct cards has been unassigned successfully."});
};

const updateInductionStatusOnUnassignedCard = async (req, unassigned_record) => {
    let rawResult = await sails.sendNativeQuery(`SELECT id FROM user_conduct_card
                WHERE project_ref = $1 AND user_ref = $2 AND (expire_on >= $3 OR expire_on IS NULL) AND card_detail->>'card_type' = 'Negative'`,
        [+unassigned_record.project_ref, +unassigned_record.user_ref, dayjs().valueOf()]);

    sails.log.info(`Found ${rawResult.rows.length} negative conduct card for user ${+unassigned_record.user_ref} on project ${+unassigned_record.project_ref}`);

    if (rawResult.rows && !rawResult.rows.length) {
        let inductionRequest = await sails.models.inductionrequest_reader.findOne({
            where: {
                project_ref: +unassigned_record.project_ref,
                id: +unassigned_record.induction_ref
            },
            select: ['id', 'comments']
        });

        if (inductionRequest && inductionRequest.id) {
            sails.log.info(`Updating induction status to Approved because no more negative conduct cards found for the user ${+unassigned_record.user_ref}`)
            await sails.models.inductionrequest.updateOne({
                id: inductionRequest.id
            }).set({
                comments: [
                    ...(inductionRequest.comments || []),
                    {
                        timestamp : dayjs().valueOf(),
                        user_id : req.user.id,
                        name : getUserFullName(req.user),
                        origin : "system",
                        note : `conduct card remove`,
                        module : 'conduct-card-remove',
                    }
                ],
                status_code: 2 //Approved
            });
        }
    }
}

const revokeUserAccessFromProject = async (induction_request, inductionRequestId, projectId, requestedByUser, cardConduct) => {
    if (!induction_request || !induction_request.id) {
        sails.log.info(`Unable to block induction with ID ${inductionRequestId}, Induction not found.`);
        return false;
    }
    sails.log.info(`Revoking access of induction ${inductionRequestId} from project ${projectId}.`);

    let status_code = (cardConduct.card_action == 'Project Block') ? 4 : 5;

    sails.log.info(`Update status of induction-request ${inductionRequestId} for negative conduct card:`, `from: ${induction_request.status_code} to: ${status_code}`);

    let updated_induction = await sails.models.inductionrequest.updateOne({id: inductionRequestId})
        .set({
            comments: [
                ...(induction_request.comments || []),
                {
                    timestamp : dayjs().valueOf(),
                    user_id : requestedByUser.id,
                    name : getUserFullName(requestedByUser),
                    origin : "system",
                    note : `${cardConduct.card_name} issued`,
                    module : "conduct-card-assign"
                }
            ],
            status_code: status_code
        });

    if (!updated_induction.optima_badge_number) {
        return false;
    }
    let os_of_project = await sails.models.optimasetting_reader.findOne({
        project_ref: +projectId,
        biometric_source: 'optima',
        key: {'!=': null},
        site_id: {'!=': null}
    });
    if(!os_of_project || !os_of_project.site_id) {
        return false;
    }
    sails.log.info(`fetching access groups of ${os_of_project.project_ref}, site_id: ${os_of_project.site_id}`);
    let {success, status, data} = await callOptimaBoxGateway(os_of_project, {
        endpoint: 'Groups',
        method: 'GET',
    }, {});
    let deny_access_group = {};
    let label = `Deny Access`;
    if (success && status === 200) {
        deny_access_group = (data.groups || []).find((access_group => (access_group.libelle === label))) || {};
    }
    if(deny_access_group.id) {
        let updated = await updateBadge(os_of_project, updated_induction.optima_badge_number, {
            "groupId": deny_access_group.id,
        })
        sails.log.info(`Deny access groups of ${os_of_project.project_ref}, site_id: ${os_of_project.site_id}.`);
    } else {
        sails.log.info(`Deny access groups of ${os_of_project.project_ref}, site_id: ${os_of_project.site_id} NOT FOUND.`);
    }
};

const processUccExcel = async (conductCards, include = []) => {
    let recordsToExport = [];
    for (let i = 0; i < conductCards.length; i++) {
        let conductCard = conductCards[i];
        recordsToExport.push({
            ucc_ref: conductCard.company_record_id,
            induction_record_id: conductCard.induction_record_id,
            issue_on: dayjs(+conductCard.createdAt).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss),
            card_name: conductCard.card_detail.card_name,
            card_type: conductCard.card_detail.card_type,
            name: conductCard.name || conductCard.user_ref.name,
            company_name: conductCard.user_employer,
            project_name: conductCard.project_ref.name,
            issued_by: conductCard.assigned_by_user_employer ? `${conductCard.assigned_by} (${conductCard.assigned_by_user_employer})` : conductCard.assigned_by_ref.name,
            card_action: conductCard.card_detail.card_action,
            expire_on: conductCard.expire_on ? dayjs(+conductCard.expire_on).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : '-',
            comment: (conductCard.comment) ? conductCard.comment : '',
        });
    }
    return downloadAssignedConductCard(recordsToExport, include);
}

const {
    conductCardFn: {
        getPaginateUcc,
        getCompanyProjectUcc,
    },
    inductionFn: {
        getInductionEmployerByUserIdProjectId,
    }
} = require('../sql.fn');
module.exports = {
    createCard: async (req, res) => {
        sails.log.info('creating card on company portal, by', req.user.id);
        let requestBody = _.pick((req.body || {}), [
            'card_name',
            'card_type',
            'card_color',
            'card_action',
            'creator_ref',
            'company_ref',
            'expire_in',
            'indefinite'
        ]);

        requestBody.creator_ref = req.user.id;

        let {validationError, payload} = createRecord(requestBody);
        if(validationError){
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        let revision = await getLatestUserRevision(req.user.id);
        payload.user_revision_ref = revision.id;

        let cardInfo = await sails.models.conductcard.create(payload);
        if(cardInfo)  {
            sails.log.info('conduct card stored successfully.');
            return successResponse(res, {cardInfo});
        }

        sails.log.info('Failed to save conduct card.');
        return errorResponse(res, 'Failed to save conduct card.');
    },

    updateCard: async (req, res) => {
        let id = +req.param('id');
        sails.log.info('updating conduct card on company portal, by', req.user.id);
        let requestBody = _.pick((req.body || {}), [
            'card_name',
            'card_type',
            'card_color',
            'card_action',
            'expire_in',
            'indefinite'
        ]);

        let {validationError, payload} = updateRecord(requestBody);
        if(validationError){
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        let revision = await getLatestUserRevision(req.user.id);
        requestBody.user_revision_ref = revision.id;

        let cardInfo = await sails.models.conductcard.updateOne({id: id}).set(requestBody);
        if(cardInfo)  {
            sails.log.info('conduct card updated successfully.');
            return successResponse(res, {cardInfo});
        }

        sails.log.info('Failed to update conduct card.');
        return errorResponse(res, 'Failed to update conduct card.');
    },

    getCards: async (req, res) => {
        let companyId = +req.param('employerId');
        return await getConductCards(req, res, companyId);
    },

    getCardsByProjectCompany: async (req, res) => {
        let projectId = +req.param('projectId');
        let projectInfo = await sails.models.project_reader.findOne({where: {id: projectId}, select: ['contractor', 'custom_field']});
        let country_code = (projectInfo.custom_field && projectInfo.custom_field.country_code) || 'GB';
        let parentCompany = await sails.models.createemployer_reader.findOne({where: {name: projectInfo.contractor, country_code}, select: 'name'});
        if (!projectInfo.contractor || !parentCompany.id) {
            sails.log.info('Invalid request, project company not found.');
            return errorResponse(res, `Invalid request, project company not found for project, ${projectId}`);
        }

        let companyId = parentCompany.id;
        return await getConductCards(req, res, companyId);
    },

    getCardById: async (req, res) => {
        let id = +req.param('id');
        sails.log.info('Fetch conduct card by id', id);
        let card_conduct = await sails.models.conductcard_reader.findOne({id: id});
        if(card_conduct)  {
            sails.log.info('conduct card fetched successfully.');
            return successResponse(res, {card_conduct});
        }

        sails.log.info('Failed to fetch conduct card by id, ', id);
        return errorResponse(res, `Failed to fetch conduct card by id, ${id}`);
    },

    deleteCard: async (req, res) => {
        let id = +req.param('id');
        sails.log.info('Fetch conduct card by id', id);
        let deletedOneRecord = await sails.models.conductcard.destroy({
            id: id
        });

        let deletedAssignedCard = await sails.models.userconductcard.destroy({
            conduct_card_ref: id
        });

        sails.log.info(`An conduct card has been removed`, deletedOneRecord);
        sails.log.info(`Associated records with conduct card have been removed`, deletedAssignedCard);
        return successResponse(res, {message: "The conduct card has been deleted."});
    },

    assignConductCard: async (req, res) => {
        let id = +req.param('id');
        let inductionRequestId = +req.body.induction_id;
        let project_admins = req.param('project_admins');
        sails.log.info('Assigning conduct card, id', id);
        let card_conduct = await sails.models.conductcard_reader.findOne({id: id});
        if(!card_conduct)  {
            sails.log.info('Failed to fetch conduct card for id, ', id);
            return errorResponse(res, `Failed to fetch conduct card for id, ${id}`);
        }

        let requestBody = _.pick((req.body || {}), [
            'user_ref',
            'project_ref',
            'comment'
        ]);

        requestBody.induction_ref = inductionRequestId;
        requestBody.conduct_card_ref = id;
        requestBody.assigned_by_ref = req.user.id;
        requestBody.expire_on = (!card_conduct.indefinite && card_conduct.card_type === "Negative") ? getEpoch(card_conduct.expire_in) : null;
        requestBody.card_detail = {
            card_name: card_conduct.card_name,
            card_type: card_conduct.card_type,
            card_color: card_conduct.card_color,
            card_action: card_conduct.card_action,
            expire_in: card_conduct.expire_in,
            indefinite: card_conduct.indefinite
        };

        let projectInfo = await sails.models.project_reader.findOne({where: {id: requestBody.project_ref}, select: ['contractor', 'custom_field', 'name']});
        let country_code = (projectInfo.custom_field && projectInfo.custom_field.country_code) || 'GB';
        let parentCompany = await sails.models.createemployer_reader.findOne({where: {name: projectInfo.contractor, country_code}, select: 'name'});
        if (projectInfo.contractor && parentCompany.id) {
            requestBody.company_ref = parentCompany.id
        }

        let {validationError, payload} = assignCard(requestBody);
        if(validationError){
            return errorResponse(res, 'Invalid Request.', {validationError});
        }

        let main_assigned_card = await sails.models.userconductcard.create(payload);

        if (card_conduct.card_type === "Negative" && ['Project Block', 'Company Block'].includes(card_conduct.card_action)) {
            let induction_request = await sails.models.inductionrequest_reader.findOne({
                select: ['project_ref', 'user_ref', 'status_code', 'record_id', 'comments'],
                where: {
                    id: inductionRequestId,
                    project_ref: +requestBody.project_ref,
                }
            });
            await revokeUserAccessFromProject(induction_request, inductionRequestId, requestBody.project_ref, req.user, card_conduct);

            //When someone is assigned a card for company block on one project, the card should be assign on all inductions of the user on projects of same company
            if (card_conduct.card_action == 'Company Block') {
                sails.log.info(`Applying company block on induction of user ${requestBody.user_ref} from all projects of the company.`);
                let projects_of_same_contractor = await sails.models.project_reader.find({
                    select: ['id'],
                    where: {
                        disabled_on: null,
                        is_active: 1,
                        project_category: 'default', // Only standard project will be searchable
                        contractor: projectInfo.contractor,
                    }
                });
                let project_ids_of_same_contractor = projects_of_same_contractor.reduce((arr, p) => {
                    if(p.id && p.id != +requestBody.project_ref) {
                        arr.push(p.id);
                    }
                    return arr;
                }, []);
                let userInductionOnCompanyProjects = await sails.models.inductionrequest_reader.find({
                    where: {project_ref: project_ids_of_same_contractor, user_ref: requestBody.user_ref},
                    select: ['id', 'project_ref', 'user_ref', 'status_code', 'record_id', 'comments']
                });
                sails.log.info(`Found ${userInductionOnCompanyProjects.length} inductions of user ${requestBody.user_ref} on projects of same company.`);
                if (userInductionOnCompanyProjects.length) {
                    for(let i = 0, len = userInductionOnCompanyProjects.length; i < len; i++) {
                        payload.project_ref = userInductionOnCompanyProjects[i].project_ref;
                        payload.induction_ref = userInductionOnCompanyProjects[i].id;
                        payload.parent_ref = main_assigned_card.id;
                        await sails.models.userconductcard.create(payload);
                        await revokeUserAccessFromProject(userInductionOnCompanyProjects[i], userInductionOnCompanyProjects[i].id, userInductionOnCompanyProjects[i].project_ref, req.user, card_conduct);
                    }
                }
            }
        }

        if(main_assigned_card)  {
            //SEND MAIL TO USER
            let assignedToUser = await sails.models.user_reader.findOne({where: {id: payload.user_ref}, select: ['first_name','middle_name','last_name','email']});
            if ((card_conduct.card_action).toLowerCase() == 'send message') {
                let subject = `${getUserFullName(req.user)} assigned you a card`;
                let emailHtml = await sails.renderView('pages/mail/mail-content', {
                    title: subject,
                    mail_body: 'assign-conduct-card',
                    comment: payload.comment,
                    receiver_name: getUserFullName(assignedToUser),
                    layout: false
                });
                sails.log.info('Sending mail to', assignedToUser.email);
                await sendMail(subject, [assignedToUser.email], emailHtml);
            }

            if(project_admins.length){
                let admins = await sails.models.user_reader.find({
                    where: {id: project_admins, is_active:1},
                    select: ['id','first_name','email']
                });
                let induction_request = await sails.models.inductionrequest_reader.findOne({
                    select: ['additional_data'],
                    where: {
                        id: inductionRequestId,
                        project_ref: +requestBody.project_ref,
                    }
                });
                let subject = `${main_assigned_card.card_detail.card_name} Conduct Card issued: ${projectInfo.name}`;
                let employer = ((induction_request.additional_data && induction_request.additional_data.employment_detail && induction_request.additional_data.employment_detail.employer) || '').toString().trim();
                let emailHtml = await sails.renderView('pages/mail/assign-conduct-card-notification', {
                    title: subject,
                    comment: payload.comment,
                    user_name: getUserFullName(assignedToUser),
                    company_name: employer,
                    action: main_assigned_card.card_detail.card_action,
                    project_name: projectInfo.name,
                    issue_date: dayjs(+main_assigned_card.createdAt).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss),
                    card_name: main_assigned_card.card_detail.card_name,
                    layout: false,
                    replaceAll(str, find, replace) {
                        const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
                        return (str || '').replace(new RegExp(escapedFind, 'g'), replace);
                    },
                });
                await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.PLAIN_MESSAGE_ALERT, admins, {
                    messageInfo: {
                        mail_title: subject,
                        html_message: emailHtml,
                        origin_action: 'conduct-card-assign'
                    },
                });
            }

            sails.log.info('Conduct card assigned to user successfully.');
            return successResponse(res, {cardInfo:main_assigned_card});
        }

        sails.log.info('Failed to assign conduct card to user.');
        return errorResponse(res, 'Failed to assign conduct card to user.');
    },

    unassignedConductCard: async (req, res) => {
        await unAssignCard(req, res, +req.param('id'));
    },

    unassignedConductCardPp: async (req, res) => {
        await unAssignCard(req, res, +req.param('id'));
    },

    getAssignedConductCard: async (req, res) => {
        let companyId = +req.param('employerId');
        let pageNumber = +req.param('pageNumber', 0);
        let searchTerm = (req.param('search') == 'null') ? null : req.param('search');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        sails.log.info('Fetching assigned conduct cards for company portal, ', companyId);
        let card_conducts = await sails.models.conductcard_reader.find({
            where: {
                company_ref: companyId
            },
            select: ['id']
        });

        if(card_conducts && !card_conducts.length) {
            sails.log.info('No conduct cards found for company, ', companyId);
            return errorResponse(res, `No conduct cards found for company ${companyId}`);
        }

        let conductCardIds = (card_conducts).map(record => record.id);
        //get main (parent_ref: null) assigned card only
        let defaultResponse = {
            records: [],
            q: searchTerm,
            pageSize,
            pageNumber,
            totalCount: 0,
        };
        let {
            records,
            total: totalCount
        } = await getPaginateUcc(conductCardIds, pageSize, (pageSize * pageNumber), searchTerm);
        sails.log.info('Assigned conduct cards fetched successfully.');
        return successResponse(res, {...defaultResponse, records, totalCount});
    },

    downloadAssignedConductCard: async (req, res) => {
        let companyId = +req.param('employerId');
        sails.log.info(`Fetching assigned conduct cards for company portal: ${companyId}`);

        let employer = await sails.models.createemployer_reader.findOne({
            where: { id: companyId },
            select: ['name']
        });

        let {
            records: conductCards,
        } = await getCompanyProjectUcc(companyId);
        sails.log.info('Assigned conduct cards fetched successfully.');

        let workbook = await processUccExcel(conductCards, ['project_name', 'ucc_ref'])

        let fileName = `${employer.name}-Conduct Card Report-${dayjs().format(displayDateFormat_DD_MM_YYYY)}.xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },

    downloadAssignedProjectConductCard: async (req, res) => {
        let projectId = +req.param('projectId');
        sails.log.info(`Fetching assigned conduct cards for project: ${projectId}`);

        let project = await sails.models.project_reader.findOne({
            where: { id: projectId },
            select: ['name']
        });

        let {
            records: conductCards,
        } = await getCompanyProjectUcc(null, projectId);
        sails.log.info('Assigned conduct cards fetched successfully.');

        let workbook = await processUccExcel(conductCards)

        let fileName = `${projectId}-${project.name}-Conduct Card Report-${dayjs().format(displayDateFormat_DD_MM_YYYY)}.xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    }
}
