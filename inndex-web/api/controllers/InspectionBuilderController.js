/**
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-06-19
 */

const {
    UserRevisionService: {getLatestUserRevision},
    TokenUtil: {
        getCompanyInfo,
        allProjectAdminsByOneOfDesignations,
        filterProjectUsersEmailEligibility
    },
    DataProcessingService: {
        attachProfilePicWithUserRefInfo,
        sendMailToNominatedManagerCPA,
        getUserFullName,
        getUserFirstName,
        buildRecordRef,
        getProjectTimezone,
        sendInspectionMailToNomMngr,
        populateUserRefs,
        populateProjectRefs,
        getTimezone,
        shareReportViaEmail,
        capitalizeFirstLetter: capitalize
    },
    ResponseService: {
        errorResponse,
        successResponse
    },
    HttpService: {
        typeOf,
    },
    ChartService: {
        getDonutChart,
        getOpenClosedBarChart,
        getStackedBarChart,
        getPercentageGaugeChart,
        getScatterPlot,
        getVerLollipop<PERSON>hart,
        getHoriLoll<PERSON>
    },
    NotificationService: {
        queuePushNotifications,
        NOTIFICATION_CATEGORY,
       sendNotification
    },
    EmailService: {
        sendMail
    },
    SharedService: {
        instantPdfGenerator,
        downloadPdfViaGenerator,
        extractPlatformVersion,
    },
    ExcelService: {
        getInspectionParticipants,
        streamExcelDownload,
    },
    SupportFunctions: {
        cleanHTMLBreaking,
    },
    ASiteService : { checkIfAsiteEnabled, getAsiteProjectToolMapping, uploadDocOnAsite }
} = require('./../services');

const goodRatingColor = "#1FA61B"; // green
const poorRatingColor = "#D60707"; // red
const fairRatingColor = "#EDB531"; // yellow
const { inspectionFn: { getInspectionBuilderReportFn,getActiveIBsForCompanyFn, updateIbChecklistForProjectFn,getCompanyDependentProjectFn },
        inductionFn: { getProjectInductions, getUserInductionEmployer },
} = require('../sql.fn');
const {
    InspectionBuilderValidator: {
        createChecklistRecord,
        updateChecklistRecord,
        deleteIbReports,
        createIbClReport
    }
} = require('./../validators');
const scoreType = {
    1: {goodRatingLabel: 'Good', fairRatingLabel: 'Fair', poorRatingLabel:'Poor'},
    2: {goodRatingLabel: '3', fairRatingLabel: '2', poorRatingLabel:'1'},
    3: {goodRatingLabel: 'Yes', poorRatingLabel:'No'},
};

const strToLower = (string) => {
    return (string || '').toLowerCase();
}

const replaceAll = (str='', find, replace) => {
    const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
    return str.replace(new RegExp(escapedFind, 'g'), replace);
};

const prepareWeeksData = (fromEpoch, toEpoch) => {
    let a = moment(fromEpoch).startOf('isoWeek');
    let b = moment(toEpoch).endOf('isoWeek');

    let daysBetweenDates = b.diff(a, 'days');
    let startOfWeeksArr = [a.valueOf()];

    if (daysBetweenDates > 6) {
        for (let i = 0; i < (daysBetweenDates/6); i++) {
            let nextVal = moment(startOfWeeksArr[i]).add(7, 'days').valueOf();
            if (nextVal > b.valueOf()) {
                break;
            }
            startOfWeeksArr.push(nextVal);
        }
    }

    let data = [];
    for (let i in startOfWeeksArr) {
        let item = {
            'week': +i + 1,
            'unsatisfactory_items': 0,
            'total_items': 0,
            'start_of_week': startOfWeeksArr[i],
            'end_of_week': moment(startOfWeeksArr[i]).add(6, 'days').valueOf()
        };
        data.push(item);
    }

    return data;
};

const mathMinMax = (data, itemKey, type) => {
    if (type == 'max') {
        return Math.max.apply(Math, (data || []).map(item => { return item[itemKey] }));
    } else {
        return Math.min.apply(Math, (data || []).map(item => { return item[itemKey] }));
    }
};

const prepareLabels = (scoringSystem) => {
    let ratings = scoringSystem.values;
    let goodRatingLabel = '';
    let poorRatingLabel = '';
    let fairRatingLabel = '';
    if ([1,2].includes(scoringSystem.type)) {
        goodRatingLabel = ratings[0];
        fairRatingLabel = ratings[1];
        poorRatingLabel = ratings[2];
    } else if (scoringSystem.type == 3) {
        goodRatingLabel = ratings[0];
        poorRatingLabel = ratings[1];
    }
    let hasThreeRatingSystem = (fairRatingLabel != "");
    return {goodRatingLabel, fairRatingLabel, poorRatingLabel, hasThreeRatingSystem};
}

const associateHeadingWithItems = (items, heading) => {
    items = (items || []).map(item => {
        item.heading = heading;
        return item;
    });
    return items;
}

const colorGreen = "#1FA61B";
const colorRed = "#D60707";
const colorYellow = "#EDB531";
const _uniq = require('lodash/uniq');
const moment = require('moment');
const momentTz = require('moment-timezone');
const _groupBy = require("lodash/groupBy");
const dbDateFormat = 'DD-MM-YYYY';
const {DEFAULT_PAGE_SIZE, INSPECTION_ID} = sails.config.constants;
const putCorrectIDValue = (itemArr) => {
    return (itemArr || []).reduce((arr, value) => {
        if (value && typeOf(value, 'number')) {
            arr.push(value)
        } else if (value && typeOf(value, 'object') && value.id) {
            arr.push(value.id);
        }
        return arr;
    }, []);
}

const cleanItem = (item) => {
    if (item.images) {
        item.images = putCorrectIDValue(item.images);
    }

    ['category', 'checklist_name', 'idx', 'subIdx', 'number'].forEach(function(key) {
        delete item[key];
    });

    if (item.tagged_user_ref) {
        item.tagged_user_ref = (item.tagged_user_ref && item.tagged_user_ref.id) ? item.tagged_user_ref.id : item.tagged_user_ref;
    }

    if (item.tagged_company_ref) {
        item.tagged_company_ref = putCorrectIDValue(item.tagged_company_ref);
    }
    return item;
};

const sleep = (ms) => {
    sails.log.info('Sleep for', ms, 'ms');
    return new Promise(resolve => setTimeout(resolve, ms));
};

const createTime = (iso_string) => {
    if(iso_string){
        let d = moment.duration(iso_string);
        return moment.isDuration(d) ? d.asDays() : null;
    }
    return iso_string;
}

const getTaggedItemsRefs = async (ibClReport) => {
    let imageIds = [];
    let taggedUserIds = [];
    let taggedCompanyIds = [];
    if(ibClReport.has_subheadings) {
        [...ibClReport.checklist, ...ibClReport.additional_checklist].forEach(function(heading) {
            fillIdArrays(heading.subheadings, imageIds, taggedUserIds, taggedCompanyIds);
        });
    } else {
        fillIdArrays([...ibClReport.checklist, ...ibClReport.additional_checklist], imageIds, taggedUserIds, taggedCompanyIds);
    }

    sails.log.info('Image IDs of Items, ', imageIds);
    let itemImages = await sails.models.userfile.find({
        where: {id: _uniq(imageIds)},
        select: ['id', 'file_url', 'md_url', 'sm_url']
    });

    sails.log.info('Tagged User Ids of Items, ', taggedUserIds);
    let usersInfo = await sails.models.user_reader.find({
        where: {id: _uniq(taggedUserIds)},
        select: ['first_name', 'middle_name', 'last_name', 'email', 'timezone']
    });

    sails.log.info('Tagged Company Ids of Items, ', taggedCompanyIds);
    let companiesInfo = await sails.models.createemployer.find({
        where: {id: _uniq(taggedCompanyIds)},
        select: ['name']
    });

    return {allTaggedUsers: usersInfo, allTaggedCompanies: companiesInfo, allImages: itemImages};
};

const getTaggedCompanyInfo = (allTaggedCompanies, company_ids) => {
    sails.log.info(`Tagged Company Ids ${company_ids}`);
    let itemTaggedCompanies = allTaggedCompanies.filter(company => company_ids.includes(company.id));
    let taggedCompanyNames = (itemTaggedCompanies || []).map(company => company.name);
    return taggedCompanyNames.join(', ');
};

const fillIdArrays = (checklists, images = [], users = [], companies = []) => {
    (checklists || []).forEach(function(item) {
        if (item.images && item.images.length) {
            images.push(...item.images);
        }
        if (item.tagged_user_ref && !isNaN(item.tagged_user_ref)) {
            users.push(item.tagged_user_ref);
        }
        if (typeOf(item.tagged_company_ref, 'array') && item.tagged_company_ref.length) {
            companies.push(...item.tagged_company_ref);
        }
    });
};

/**
This function is being used to create map of inductionId: userId, for handling invalid payload from mobile app v4.0.11.
This function can be removed once there is no user using the app `v4.0.11`
**/
const getTaggedInductionRefs = async (ibReport, projectId) => {
    let taggedInductionIds = [];
    if(ibReport.has_subheadings) {
        [...ibReport.checklist, ...ibReport.additional_checklist].forEach(function(heading) {
            fillTaggedUserRefsArray(heading.subheadings, taggedInductionIds);
        });
    } else {
        fillTaggedUserRefsArray([...ibReport.checklist, ...ibReport.additional_checklist], taggedInductionIds);
    }

    taggedInductionIds = _uniq(taggedInductionIds);

    sails.log.info('Tagged Induction Ids of Items, ', taggedInductionIds);
    let inductionRequests = await sails.models.inductionrequest_reader.find({
        where: {
            id: taggedInductionIds,
            project_ref: projectId
        },
        select: ['user_ref'],
    });

    let inductionIdToUserRef = {};
    inductionRequests.forEach(induction => {
        inductionIdToUserRef[induction.id] = induction.user_ref;
    });

    return inductionIdToUserRef;
};

const fillTaggedUserRefsArray = (checklists, taggedUserRefs = []) => {
    (checklists || []).forEach(function(item) {
        if (item.tagged_user_ref && !isNaN(item.tagged_user_ref)) {
            taggedUserRefs.push(item.tagged_user_ref);
        }
    });
};

const populateObject = (checklists, itemImages, usersInfo, taggedCompaniesInfo) => {
    (checklists || []).forEach(function(item) {
        if (item.images && item.images.length) {
            item.images = (itemImages || []).filter(img => item.images.includes(img.id));
        }
        if (item.tagged_user_ref && !isNaN(item.tagged_user_ref)) {
            item.tagged_user_ref = (usersInfo || []).find(user => user.id == item.tagged_user_ref);
        }
        if (typeOf(item.tagged_company_ref, 'array')  && item.tagged_company_ref.length) {
            item.tagged_company_ref = (taggedCompaniesInfo || []).filter(company => item.tagged_company_ref.includes(company.id));
        }
    });
};

const getInspectionReport = async (filter, OrderBy, expandItems = false, expandParticipants = false, useReadReplica = true) => {
    sails.log.info('Fetching inspection reports for filter = ', filter);

    const { project_ref, ib_ref, user_ref, id, limit, offset, searchTerm, finalised } = filter;

    let { total, records: inspectionReport } = await getInspectionBuilderReportFn(project_ref, ib_ref, user_ref, id, finalised, limit, offset, searchTerm, OrderBy, useReadReplica);
    inspectionReport = inspectionReport.map(insp => ({
        ...insp,
        record_ref: buildRecordRef(insp),
        createdAt:  +insp.createdAt,
        updatedAt: +insp.updatedAt,
      }));
    inspectionReport = await populateProjectRefs(inspectionReport, 'project_ref', []);
    inspectionReport = await populateUserRefs(inspectionReport, 'user_ref', []);

    sails.log.info('Fetched project IB reports.', inspectionReport.length);

    if (inspectionReport && inspectionReport.length && expandItems) {
        let imageIds = [];
        let taggedUserIds = [];
        let taggedCompanyIds = [];
        let participants = [];
        if (expandParticipants) {
            let participantsIds = _uniq(inspectionReport.flatMap(obj => obj.participants));

            sails.log.info('Fetching info of participants. participantsIds = ', participantsIds);

            const inductionRecords = await getProjectInductions(project_ref ||  inspectionReport[0].project_ref.id, { limit: -1, searchUserIds: participantsIds }, [
                'status_code',
                "additional_data -> 'employment_detail' ->> 'employer' as employer",
                "additional_data -> 'employment_detail' ->> 'job_role' as job_role"
            ]);
            participants = inductionRecords.records;
        }

        for (ibReport of inspectionReport) {
            if(ibReport.has_subheadings) {
                [...ibReport.checklist, ...ibReport.additional_checklist].forEach(function(heading, i) {
                    fillIdArrays(heading.subheadings, imageIds, taggedUserIds, taggedCompanyIds);
                });
            } else {
                fillIdArrays([...ibReport.checklist, ...ibReport.additional_checklist], imageIds, taggedUserIds, taggedCompanyIds);
            }
        }

        sails.log.info('fetching IB checklist item images, ', _uniq(imageIds));
        let itemImages = await sails.models.userfile.find({
            where: {id: _uniq(imageIds)},
            select: ['id', 'name', 'file_url', 'md_url', 'sm_url', 'img_translation']
        });
        sails.log.info('fetching IB checklist tagged users, ', _uniq(taggedUserIds));
        let usersInfo = await sails.models.user_reader.find({
            where: {id: _uniq(taggedUserIds)},
            select: ['first_name', 'middle_name', 'last_name', 'email', 'timezone', 'parent_company']
        });

        sails.log.info('fetching IB checklist tagged companies, ', _uniq(taggedCompanyIds));
        let taggedCompaniesInfo = await sails.models.createemployer.find({
            where: {id: _uniq(taggedCompanyIds)},
            select: ['name']
        });

        for (ibReport of inspectionReport) {
            if (expandParticipants) {
                ibReport.participants = ibReport.participants.map(participantId => {
                    return participants.find(participant => participant.user_ref == participantId);
                });
            }
            if(ibReport.has_subheadings) {
                [...ibReport.checklist, ...ibReport.additional_checklist].forEach(function(heading, i) {
                    populateObject(heading.subheadings, itemImages, usersInfo, taggedCompaniesInfo);
                });
            } else {
                populateObject([...ibReport.checklist, ...ibReport.additional_checklist], itemImages, usersInfo, taggedCompaniesInfo);
            }
        }
    }
    return { total , inspectionReport};
};

const getIBChecklists = async (filter, OrderBy, select = ['*']) => {

    let ibChecklists = await sails.models.inspectionbuilder.find({
        where: filter,
        select: select
    }).sort([
        {id: OrderBy}
    ]);
    ibChecklists = await populateProjectRefs(ibChecklists, 'project_ref', []);
    ibChecklists = await populateUserRefs(ibChecklists, 'user_ref', []);

    sails.log.info('Fetched project checklists.', ibChecklists.length);
    return ibChecklists;
};

const isEnabledForProject = (inspection, projectId) => {
    let projectEntry = (inspection.activate_on_projects || []).find(item => item.project_id == projectId);
    return (!projectEntry || projectEntry.enabled);
}

const processAllCLItems = (ibChecklist, ibReport, item, i, type, j=0, heading=null, goodRatingLabel, fairRatingLabel, poorRatingLabel) => {
    let itemInfo = {};
    let tz = getProjectTimezone(ibChecklist.project_ref);

    if(heading) {
        itemInfo.title = `Item: ${i+1}.${j+1} – ${item.question}`;
    } else {
        itemInfo.title = `Item: ${i+1} – ${item.question}`;
    }
    itemInfo.answer = item.answer;

    itemInfo.summary = (item.summary) ? item.summary : 'N/A';


    itemInfo.item_action = (item.corrective_action_required) ? item.corrective_action_required : 'N/A';

    if (item.closeout_due_on && !ibChecklist.severity.has_severity) {
        itemInfo.item_closeout_due_on = tz ? momentTz(item.closeout_due_on).tz(tz).format('DD-MM-YYYY') : moment(item.closeout_due_on).format('DD-MM-YYYY');
    } else if(ibChecklist.severity.has_severity && item.severity && item.severity.rating && item.severity.days) {
        itemInfo.time_to_closeout = `${item.severity.rating} (Closeout Due: ${moment(ibReport.createdAt).add(+(createTime(item.severity.days)), 'days').format('DD/MM/YY')})`;
    }

    if (([strToLower(fairRatingLabel), strToLower(poorRatingLabel)].includes(strToLower(item.answer))) && item.close_out && Object.keys(item.close_out).length) {
        itemInfo.item_closeout_reviewd_by = (item.close_out.reviewed_by) ? item.close_out.reviewed_by : 'N/A';

        let closeoutAt = +item.close_out.close_out_at;
        itemInfo.item_closeout_at = closeoutAt ? (tz ? momentTz(closeoutAt).tz(tz).format('DD-MM-YYYY HH:mm:ss') : moment(closeoutAt).format('DD-MM-YYYY HH:mm:ss')) : 'N/A';
        itemInfo.item_closeout_detail = item.close_out.details;

        if (item.close_out.images && item.close_out.images.length) {
            itemInfo.item_closeout_images = [];
            item.close_out.images.forEach(function(image, i) {
                if (image.img_translation && image.img_translation.length) {
                    itemInfo.item_closeout_images.push(...image.img_translation);
                } else if (image.file_url) {
                    if(type === 'pdf') {
                        itemInfo.item_closeout_images.push((image.sm_url || image.file_url));
                    } else {
                        itemInfo.item_closeout_images.push((image.md_url || image.file_url));
                    }
                }
            });
        }
    }

    if (item.root_cause) {
        itemInfo.root_cause = item.root_cause;
    }

    if (item.location_tag && Object.keys(item.location_tag).length) {
        itemInfo.location = item.location_tag;
    }

    if (item.tagged_user_ref) {
        itemInfo.item_assigned_to = (item.tagged_user_ref && item.tagged_user_ref.id) ?
            `${getUserFullName(item.tagged_user_ref)} (${item.tagged_user_ref.email})` : '';
    }

    if (item.tagged_company_ref && typeof item.tagged_company_ref == 'object') {
        itemInfo.item_tagged_company = (item.tagged_company_ref || []).map(company => company.name).join(', ');
    }

    if (item.images && item.images.length) {
        itemInfo.images = [];
        item.images = (item.images || []).flatMap(img =>
            img.img_translation && img.img_translation.length ?
                img.img_translation.map(translation => translation) :
                [(type === 'pdf' && img.sm_url) ? img.sm_url : (img.md_url || img.file_url)]
        );

        itemInfo.images.push(...item.images);
    }

    return {itemInfo}
};

const sendMailToTaggedUser = async (ibChecklist, ibReport, subItem, item, allImages, allTaggedUsers, allTaggedCompanies, projectInfo, reportNumber, reportDateTime, userName, ib_title, goodRatingLabel, fairRatingLabel, poorRatingLabel) => {
    let tz = getProjectTimezone(projectInfo);
    let imagesSrc = [];
    if(subItem.images && subItem.images.length) {
        (allImages || []).map(imageObj => {
            if (imageObj.id && subItem.images.includes(imageObj.id)) {
                imagesSrc.push((imageObj.sm_url || imageObj.file_url));
            }
        })
    }

    let rating_color = '#000';
    if ([strToLower(goodRatingLabel)].includes(strToLower(subItem.answer))) {
        rating_color = `${colorGreen}`;
    } else if ([strToLower(poorRatingLabel)].includes(strToLower(subItem.answer))) {
        rating_color = `${colorRed}`;
    } else if ([strToLower(fairRatingLabel)].includes(strToLower(subItem.answer))) {
        rating_color = `${colorYellow}`;
    }

    let taggedUserInfo = allTaggedUsers.find(user => user.id ==  subItem.tagged_user_ref);
    let item_title = `${(item.heading) ? item.heading + ' - ' : ''}${subItem.question}`;
    sails.log.info(`Send email to ${taggedUserInfo.email} for item ${item_title}`);
    let taggedUserFname = taggedUserInfo.first_name;
    let subject = `Inspection: ${projectInfo.name}`;
    let html = await sails.renderView('pages/mail/mail-content', {
        mail_body: 'inspection-tour-item-notify',
        title: subject,
        responsible_user_fname: taggedUserFname,
        project_name: projectInfo.name,
        inspection_number: reportNumber,
        ib_title,
        inspection_date_time: reportDateTime,
        inspection_carried_out_by: userName,
        rating_label: (['yes', 'no'].includes(strToLower(subItem.answer))) ? 'Satisfactory' : 'Rating',
        item_title,
        item_responsible_company: getTaggedCompanyInfo(allTaggedCompanies, subItem.tagged_company_ref),
        closeout_due_date: (subItem.closeout_due_on) ? (tz ? momentTz(subItem.closeout_due_on).tz(tz).format('DD-MM-YYYY') : moment(subItem.closeout_due_on).format('DD-MM-YYYY')) : '',
        rating: (subItem.answer) ? subItem.answer.toUpperCase() : 'N/A',
        rating_color,
        summary: (subItem.summary) ? subItem.summary : 'N/A',
        action: (subItem.corrective_action_required) ? subItem.corrective_action_required : 'N/A',
        appendix_images: imagesSrc,
        root_cause: (subItem.root_cause) ? subItem.root_cause : "",
        time_to_closeout: (ibChecklist.severity.has_severity && subItem.severity && subItem.severity.rating && subItem.severity.days) ? `${subItem.severity.rating} (Closeout Due: ${moment(ibReport.createdAt).add(+(createTime(subItem.severity.days)), 'days').format('DD/MM/YY')})` : null,
        layout: false
    });

    await EmailService.sendMail(subject, [taggedUserInfo.email], html);
};

 const sendPushNotification = async({message, messageTitle, ib_id, ib_report_id, item_info, category, projectInfo, recipientUserInfo, submittedByUserInfo}) => {
     sails.log.info(`Sending push notification for inspection builder item to user: ${recipientUserInfo.id}`);
     let profile_pic = null;
     if(submittedByUserInfo.profile_pic_ref) {
         if (submittedByUserInfo.profile_pic_ref && (typeof submittedByUserInfo.profile_pic_ref === "number")) {
             submittedByUserInfo.profile_pic_ref = await sails.models.userfile.findOne({
                 where: {id: submittedByUserInfo.profile_pic_ref},
                 select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation', 'file_mime']
             });
         }
         profile_pic = submittedByUserInfo.profile_pic_ref.file_url;
     }

     let notificationData = {
         user_ref: recipientUserInfo.id,
         category,
         message,
         title: messageTitle,
         data: {
             category: category,
             profile_pic: profile_pic,
             project_ref: projectInfo.id,
             ib_id,
             ib_report_id,
             item_info: JSON.stringify(item_info)
         },
     };
     let notification = await sails.models.usernotifications.create(notificationData);
     let deviceInfo = await sails.models.registereddevices.find({user_ref: recipientUserInfo.id});
     sails.log.info(`Found ${deviceInfo.length} devices for user: #${recipientUserInfo.id}`);
     let messageData = {};
     for(const device of deviceInfo) {
         messageData = {
             notification: {
                 title: messageTitle,
                 body: message
             },
             android: {
                 notification: {
                     icon: 'ic_icon_inndex',
                     color: '#14152D'
                 }
             },
             data: {
                 category: category,
                 project_ref: projectInfo.id.toString(),
                 notification_ref: notification.id.toString(),
                 profile_pic: profile_pic || '',
                 ib_id: ib_id.toString(),
                 ib_report_id: ib_report_id.toString(),
                 item_info: JSON.stringify(item_info)
             },
             token: device.token
         };
         await sendNotification(messageData);
     }
 };

const getRatingPointInfo = (scoringSystem) => {
    let ratingPointAgainstRating = {};
    if (scoringSystem.has_rating_point && scoringSystem.rating_point  && scoringSystem.rating_point.length) {
        (scoringSystem.values || []).map((value, index) => {
            ratingPointAgainstRating[value.toLowerCase()] = (scoringSystem.rating_point[index] || 0);
            return value;
        });

        return  {hasRatingPoint: true, ratingPointAgainstRating}
    }
    return  {hasRatingPoint: false, ratingPointAgainstRating}
};

const fillParticipants = (inspection, participants, totalItems, goodItems)  => {
    let percent = (totalItems && goodItems) ? Math.round(100/totalItems * goodItems) : 0;

    let inspection_rating = {percent, project_name: inspection.project_ref.name};
    participants = participants.map(participant => {
        if ((inspection.participants || []).includes(participant.id)) {
            participant.inspection_date.push(inspection.createdAt);
            participant.inspections_participated.push(inspection.id);
            participant.projects.push(inspection.project_ref.id);
            participant.inspection_rating.push(inspection_rating);
        }
        return participant;
    });

    return participants;
}

const calculateRatingPercentage = async (inspectionReport, ib, goodRatingLabel, fairRatingLabel, poorRatingLabel, hasRatingPoint, ratingPointAgainstRating) => {
    let inspectionChecklist = [...inspectionReport.checklist, ...inspectionReport.additional_checklist];

    let goodRatingItemsCount = 0;
    let poorRatingItemsCount = 0;
    let fairRatingItemsCount = 0;
    if (ib.has_subheadings) {
        inspectionChecklist.forEach(function (heading, i) {
            heading.subheadings.forEach(function (item, j) {
                let answer = (item.answer || '').toLowerCase();
                goodRatingItemsCount += (answer === goodRatingLabel.toLowerCase()) ? 1 : 0;
                poorRatingItemsCount += (answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
                fairRatingItemsCount += (fairRatingLabel && answer === fairRatingLabel.toLowerCase()) ? 1 : 0;
            });
        });
    } else {
        inspectionChecklist.forEach(function (item, i) {
            let answer = (item.answer || '').toLowerCase();
            goodRatingItemsCount += (answer === goodRatingLabel.toLowerCase()) ? 1 : 0;
            poorRatingItemsCount += (answer === poorRatingLabel.toLowerCase()) ? 1 : 0;
            fairRatingItemsCount += (fairRatingLabel && answer === fairRatingLabel.toLowerCase()) ? 1 : 0;
        });
    }

    let ratingPointsFraction = 0;
    if (hasRatingPoint) {
        let maximumNumberOfPoints = ratingPointAgainstRating[goodRatingLabel.toLowerCase()] * (goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
        let totalNumberOfPoints = 0;
        totalNumberOfPoints += (ratingPointAgainstRating[goodRatingLabel.toLowerCase()] * goodRatingItemsCount) + (ratingPointAgainstRating[poorRatingLabel.toLowerCase()] * poorRatingItemsCount);
        totalNumberOfPoints += (fairRatingLabel && ratingPointAgainstRating[fairRatingLabel.toLowerCase()]) ? (ratingPointAgainstRating[fairRatingLabel.toLowerCase()] * fairRatingItemsCount) : 0;
        ratingPointsFraction = (100 * totalNumberOfPoints)/maximumNumberOfPoints;
        ratingPointsFraction = (isNaN(ratingPointsFraction) || Math.sign(ratingPointsFraction) == -1) ? 0 : ratingPointsFraction;
    }

    let ratingPercent = parseInt(100/(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount)*(goodRatingItemsCount));
    ratingPercent = (isNaN(ratingPercent) || Math.sign(ratingPercent) == -1) ? 0 : ratingPercent;

    return (hasRatingPoint) ? Math.round(ratingPointsFraction) : ratingPercent;
}

const prepareSubcontractorDashboardCharts = async(ibInfo, rawItems, taggedCompaniesIdNamePair, top20TaggedCompanies, returnType)  => {
    let {goodRatingLabel, fairRatingLabel, poorRatingLabel, hasThreeRatingSystem} = prepareLabels(ibInfo.scoring_system);

    let stackedChartWidth = 472;
    let stackedChartHeight = 193;
    let stackChartLegendAdj = 85;
    let horiLolipopChartWidth = 1120;
    let horiLolipopChartHeight = 400;
    let avgCloseoutLegendAdj = 10;

    if (returnType === 'pdf') {
        stackedChartWidth = 690;
        stackedChartHeight = 275;
        stackChartLegendAdj = 110;

        horiLolipopChartWidth = 1400;
        horiLolipopChartHeight = 478;
        avgCloseoutLegendAdj = 10;
    }

    //rating labels
    let allRatingsArr = [strToLower(goodRatingLabel), strToLower(poorRatingLabel)];
    let ratingsToCloseoutArr = [strToLower(poorRatingLabel)];
    if(hasThreeRatingSystem) {
        allRatingsArr = [strToLower(goodRatingLabel), strToLower(poorRatingLabel), strToLower(fairRatingLabel)];
        ratingsToCloseoutArr = [strToLower(poorRatingLabel), strToLower(fairRatingLabel)];
    }

    let goodRatingItemsCount = 0;
    let poorRatingItemsCount = 0;
    let fairRatingItemsCount = 0;

    let issuesRaisedChartData = [];
    let issuesRaisedChartColumns = ['group', poorRatingLabel];

    let positiveItemsChartData = [];
    let positiveItemsChartColumns = ['group', goodRatingLabel];

    let numberOfProjectsChartData = [];
    let numberOfProjectsChartColumns = ['group', 'Project'];

    let avgCloseoutTimeChartData = [];
    let avgCloseoutTimeChartGroups = [];
    let poorRatingData = [];
    let fairRatingData = [];

    let biggestProblemAreasChartData = [];
    let biggestProblemAreasChartColumns = ['group', poorRatingLabel];

    if(hasThreeRatingSystem) {
        issuesRaisedChartColumns = ['group', fairRatingLabel, poorRatingLabel];
        biggestProblemAreasChartColumns = ['group', fairRatingLabel, poorRatingLabel];
    }

    if (rawItems.length) {
        (rawItems || []).forEach(function (item) {
            let isCompanyAmongTop20 = (top20TaggedCompanies).find(c => c.id === item.tagged_company_ref);
            if (!isCompanyAmongTop20) {
                return;
            }
            let itemAnswerLC = strToLower(item.answer);

            goodRatingItemsCount += [strToLower(goodRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
            fairRatingItemsCount += [fairRatingLabel && strToLower(fairRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
            poorRatingItemsCount += [strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;

            if (allRatingsArr.includes(itemAnswerLC)) {
                //Stacked Chart: Data of issues raised chart
                if (ratingsToCloseoutArr.includes(itemAnswerLC)) {
                    let existingRecord = issuesRaisedChartData.find(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                    if (existingRecord) {
                        existingRecord[capitalize(item.answer)] += 1;
                        existingRecord['total'] += 1;
                        let existingRecordIndex = issuesRaisedChartData.findIndex(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                        issuesRaisedChartData[existingRecordIndex] = existingRecord;
                    } else {
                        let dataItem = {
                            'group': taggedCompaniesIdNamePair[item.tagged_company_ref]
                        };
                        if(hasThreeRatingSystem) {
                            dataItem[fairRatingLabel] = 0;
                        }
                        dataItem[poorRatingLabel] = 0;
                        dataItem['total'] = 1;
                        dataItem[capitalize(item.answer)] += 1;
                        issuesRaisedChartData.push(dataItem);
                    }

                    if (item.close_out && Object.keys(item.close_out).length) {
                        let existingRecord = avgCloseoutTimeChartData.find(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                        let hours = (+item.close_out.close_out_at - +item.it_createdAt) / (60 * 60 * 1000);
                        let day = 0;
                        if ([strToLower(poorRatingLabel)].includes(itemAnswerLC)) {
                            day = hours/24;
                        } else if(hasThreeRatingSystem && [strToLower(fairRatingLabel)].includes(itemAnswerLC)) {
                            day = hours/24;
                        }

                        if (existingRecord && day) {
                            let existingRecordIndex = avgCloseoutTimeChartData.findIndex(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                            existingRecord[capitalize(item.answer)].push(day);
                            avgCloseoutTimeChartData[existingRecordIndex] = existingRecord;
                        } else {
                            let dataItem = {
                                'group': taggedCompaniesIdNamePair[item.tagged_company_ref]
                            };
                            if(hasThreeRatingSystem) {
                                dataItem[fairRatingLabel] = [];
                            }
                            dataItem[poorRatingLabel] = [];
                            dataItem[capitalize(item.answer)].push(day);
                            if(day) {
                                avgCloseoutTimeChartData.push(dataItem);
                            }
                        }
                    }

                    //Stacked Chart: Data of Biggest Problem Areas chart
                    let dataItem = biggestProblemAreasChartData.find(dItem => dItem.group === item.itemCategory) || {};
                    if (Object.keys(dataItem).length) {
                        dataItem[capitalize(item.answer)] += 1;
                        dataItem['total'] += 1;
                        let dataItemIndex = biggestProblemAreasChartData.findIndex(dItem => dItem.group === item.itemCategory);
                        biggestProblemAreasChartData[dataItemIndex] = dataItem;
                    } else {
                        dataItem = {
                            'group': item.itemCategory
                        };
                        if(hasThreeRatingSystem) {
                            dataItem[fairRatingLabel] = 0;
                        }
                        dataItem[poorRatingLabel] = 0;
                        dataItem['total'] = 1;
                        dataItem[capitalize(item.answer)] += 1;
                        biggestProblemAreasChartData.push(dataItem);
                    }
                }

                //Stacked Chart: Data of positive items chart
                if (strToLower(goodRatingLabel) == itemAnswerLC) {
                    let existingRecord = positiveItemsChartData.find(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                    if (existingRecord) {
                        existingRecord[capitalize(item.answer)] += 1;
                        existingRecord['total'] += 1;
                        let existingRecordIndex = positiveItemsChartData.findIndex(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                        positiveItemsChartData[existingRecordIndex] = existingRecord;
                    } else {
                        let dataItem = {
                            'group': taggedCompaniesIdNamePair[item.tagged_company_ref]
                        };
                        dataItem[goodRatingLabel] = 0;
                        dataItem['total'] = 1;
                        dataItem[capitalize(item.answer)] += 1;
                        positiveItemsChartData.push(dataItem);
                    }
                }

                //Stacked Chart: Data of number of Projects chart
                let existingRecord = numberOfProjectsChartData.find(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                if (existingRecord) {
                    if(!existingRecord.projectIds.includes(item.project_id)) {
                        existingRecord.projectIds.push(item.project_id);
                        existingRecord['Project'] += 1;
                        let existingRecordIndex = numberOfProjectsChartData.findIndex(record => record.group === taggedCompaniesIdNamePair[item.tagged_company_ref]);
                        numberOfProjectsChartData[existingRecordIndex] = existingRecord;
                    }
                } else {
                    let dataItem = {
                        'group': taggedCompaniesIdNamePair[item.tagged_company_ref]
                    };
                    dataItem['Project'] = 1;
                    dataItem['projectIds'] = [item.project_id];
                    numberOfProjectsChartData.push(dataItem);
                }
            }
        });

        (issuesRaisedChartData || []).sort((a,b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0));
        (positiveItemsChartData || []).sort((a,b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0));
        (numberOfProjectsChartData || []).sort((a,b) => (a.Project < b.Project) ? 1 : ((b.Project < a.Project) ? -1 : 0));
        (biggestProblemAreasChartData || []).sort((a,b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0));

        //Horizontal Chart: Data of Average Issue Closeout Time
        (avgCloseoutTimeChartData || []).map(item => {
            let poorAvgDays = (item[poorRatingLabel].length) ? (item[poorRatingLabel].reduce(function (a, b) {
                return a + b;
            }, 0)) / item[poorRatingLabel].length : 0;

            if(poorAvgDays) {
                poorRatingData.push({
                    "group": item.group,
                    "rating": poorAvgDays
                });
            }

            let fairAvgDays = 0;
            if(hasThreeRatingSystem) {
                fairAvgDays = (item[fairRatingLabel].length) ? (item[fairRatingLabel].reduce(function (a, b) {
                    return a + b;
                }, 0)) / item[fairRatingLabel].length : 0;

                if(fairAvgDays) {
                    fairRatingData.push({
                        "group": item.group,
                        "rating": fairAvgDays
                    });
                }
            }

            if(poorAvgDays || fairAvgDays) {
                avgCloseoutTimeChartGroups.push(item.group);
            }
        });
    }

    let issuesRaisedChartMax = (issuesRaisedChartData.length) ? issuesRaisedChartData[0].total : 0;
    let positiveItemsChartMax = (positiveItemsChartData.length) ? positiveItemsChartData[0].total : 0;
    let numberOfProjectsChartMax = (numberOfProjectsChartData.length) ? numberOfProjectsChartData[0].Project : 0;
    let biggestProblemAreasChartMax = (biggestProblemAreasChartData.length) ? biggestProblemAreasChartData[0].total : 0;

    let issuesRaisedChart = await getStackedBarChart(issuesRaisedChartData, issuesRaisedChartColumns, issuesRaisedChartMax, stackedChartWidth, stackedChartHeight, [`${colorYellow}`, `${colorRed}`], '7px', 'Number', hasThreeRatingSystem, true, stackChartLegendAdj);
    let positiveItemsChart = await getStackedBarChart(positiveItemsChartData, positiveItemsChartColumns, positiveItemsChartMax, stackedChartWidth, stackedChartHeight, [`${colorGreen}`], '7px', 'Number', hasThreeRatingSystem, true, stackChartLegendAdj);
    let numberOfProjectsChart = await getStackedBarChart(numberOfProjectsChartData, numberOfProjectsChartColumns, numberOfProjectsChartMax, stackedChartWidth, stackedChartHeight, ["#6171a9"], '7px', 'Number', hasThreeRatingSystem, true, stackChartLegendAdj);

    let maxCloseoutTimePoor = mathMinMax(poorRatingData, 'rating', 'max');
    let maxCloseoutTimeFair = mathMinMax(fairRatingData, 'rating', 'max');
    let avgCloseoutTimeChartMax = Math.max(maxCloseoutTimePoor, maxCloseoutTimeFair);
    let avgCloseoutTimeChart = await getHoriLollipopChart(avgCloseoutTimeChartGroups, poorRatingData, fairRatingData, avgCloseoutTimeChartMax, horiLolipopChartWidth, horiLolipopChartHeight,"Days to closeout", '7px', [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}], avgCloseoutLegendAdj);

    let biggestProblemAreasChart = await getStackedBarChart(biggestProblemAreasChartData, biggestProblemAreasChartColumns, biggestProblemAreasChartMax, stackedChartWidth, stackedChartHeight, [`${colorYellow}`, `${colorRed}`], '7px', 'Number', hasThreeRatingSystem, true, stackChartLegendAdj);
    return { issuesRaisedChart, positiveItemsChart, numberOfProjectsChart, avgCloseoutTimeChart, biggestProblemAreasChart};
};

const prepareRawItems = (inspection, inspectionChecklist, selected_responsible_companies, companiesWithCompanyPortal) => {
    let rawItems = [];
    let companiesId = [];
    (inspectionChecklist || []).forEach(function (item, j) {
        if(item.tagged_company_ref && item.tagged_company_ref.length) {
            let itemCategory = (item.heading) ? item.heading : item.question;
            (item.tagged_company_ref || []).map(id => {
                let isCompanyHasPortal = (companiesWithCompanyPortal || []).find(company => company.id == id);
                //skip item if tagged company own company portal
                if (!isCompanyHasPortal) {
                    companiesId.push(id);
                    if(!selected_responsible_companies.length || selected_responsible_companies.includes(id)) {
                        rawItems.push({
                            ...item,
                            it_id: inspection.id,
                            it_createdAt: inspection.createdAt,
                            project_id: inspection.project_ref.id,
                            itemCategory,
                            tagged_company_ref: id
                        });
                    }
                }
            })
        }
    });

    return { rawItems, companiesId };
}

const prepareRawItemsByTaggedOwner = (inspection, inspectionChecklist, selectedTaggedCompany, goodRatingLabel, fairRatingLabel, poorRatingLabel) => {
    let goodRatingLabelLC = (goodRatingLabel) ? goodRatingLabel.toLowerCase() : goodRatingLabel;
    let fairRatingLabelLC = (fairRatingLabel) ? fairRatingLabel.toLowerCase() : fairRatingLabel;
    let poorRatingLabelLC = (poorRatingLabel) ? poorRatingLabel.toLowerCase() : poorRatingLabel;

    let rawItems = [];
    (inspectionChecklist || []).forEach(function (item, j) {
        let itemAnswerLC = (item.answer || '').toLowerCase();
        if([goodRatingLabelLC, fairRatingLabelLC, poorRatingLabelLC].includes(itemAnswerLC) && item.tagged_company_ref && item.tagged_company_ref.length && (item.tagged_company_ref || []).includes(selectedTaggedCompany)) {
            let itemCategory = (item.heading) ? item.heading : item.question;

            rawItems.push({
                ...item,
                question: item.question,
                it_id: inspection.id,
                it_createdAt: inspection.createdAt,
                it_user_ref: inspection.user_ref,
                project_id: inspection.project_ref.id,
                project_name: inspection.project_ref.name,
                item_category: itemCategory,
            });
        }
    });

    return { rawItems };
}

const prepareTaggedOwnerDashboardCharts = async (rawItems, goodRatingLabel, fairRatingLabel, poorRatingLabel) => {
    let goodRatingLabelLC = (goodRatingLabel) ? goodRatingLabel.toLowerCase() : goodRatingLabel;
    let fairRatingLabelLC = (fairRatingLabel) ? fairRatingLabel.toLowerCase() : fairRatingLabel;
    let poorRatingLabelLC = (poorRatingLabel) ? poorRatingLabel.toLowerCase() : poorRatingLabel;

    let goodRatingItemsCount = 0;
    let poorRatingItemsCount = 0;
    let fairRatingItemsCount = 0;
    let totalClosedOutItems = 0;
    let totalFairToCloseOut = 0;
    let totalPoorToCloseOut = 0;
    let ratingsByGroupChartData = [];
    let ratingsByGroupChartColumns = ['group', goodRatingLabel, fairRatingLabel, poorRatingLabel];
    ratingsByGroupChartColumns = ratingsByGroupChartColumns.filter(element => element !== undefined); //filter undefineds
    let totalRatingPerCategory = {};
    let itemsInfo = [];
    let participatedProjectIds = [];
    rawItems.forEach(function(item, i) {
        let itemAnswerLC = (item.answer || '').toLowerCase();
        let itemInfo = {};
        if ([goodRatingLabelLC, fairRatingLabelLC, poorRatingLabelLC].includes(itemAnswerLC)) {
            itemInfo.item_category = `Item: ${item.item_category}`;
            itemInfo.inspected_by = item.it_user_ref; //expand needed
            itemInfo.inspected_at = item.it_createdAt;
            goodRatingItemsCount += (goodRatingLabelLC && goodRatingLabelLC == itemAnswerLC) ? 1 : 0;
            fairRatingItemsCount += (fairRatingLabelLC && fairRatingLabelLC === itemAnswerLC) ? 1 : 0;
            poorRatingItemsCount += (poorRatingLabelLC && poorRatingLabelLC === itemAnswerLC) ? 1 : 0;

            if ([fairRatingLabelLC, poorRatingLabelLC].includes(itemAnswerLC) && item.close_out && Object.keys(item.close_out).length) {
                totalClosedOutItems += ([fairRatingLabelLC, poorRatingLabelLC].includes(itemAnswerLC)) ? 1 : 0;
                itemInfo.item_closeout_reviewd_by = (item.close_out.reviewed_by) ? item.close_out.reviewed_by : 'N/A';
                itemInfo.item_closeout_at = +item.close_out.close_out_at ? moment(+item.close_out.close_out_at).format('DD-MM-YYYY HH:mm:ss') : 'N/A';
                itemInfo.item_closeout_detail = item.close_out.details;
                itemInfo.item_closeout_images = (item.close_out.images && item.close_out.images.length) ? item.close_out.images : []; //expand needed
            } else {
                totalFairToCloseOut += (fairRatingLabelLC === itemAnswerLC) ? 1 : 0;
                totalPoorToCloseOut += (poorRatingLabelLC === itemAnswerLC) ? 1 : 0;
            }

            if (item.summary || item.corrective_action_required || (item.appendix && item.appendix.length) || (item.close_out && Object.keys(item.close_out).length) || item.responsible_user_ref || (item.tagged_company_ref && item.tagged_company_ref.length) || (item.location_tag && Object.keys(item.location_tag).length)) {
                itemInfo.item_title = `Item: ${item.display_number} – ${item.question}`;
                itemInfo.item_answer = item.answer;
                itemInfo.item_class = (itemAnswerLC == goodRatingLabelLC) ? 'item-ans-yes' : (itemAnswerLC == fairRatingLabelLC) ? 'item-ans-fair' : (itemAnswerLC == poorRatingLabelLC) ? 'item-ans-poor' : '';
                itemInfo.item_summary = (item.summary) ? item.summary : 'N/A';
                itemInfo.item_action = (item.corrective_action_required) ? item.corrective_action_required : 'N/A';
                itemInfo.item_assigned_to = (item.responsible_user_ref) ? item.responsible_user_ref : null; //expand needed
                itemInfo.item_closeout_due_on = (item.closeout_due_on) ? moment(item.closeout_due_on).format('DD-MM-YYYY') : null;
                itemInfo.item_images = (item.appendix && item.appendix.length) ? item.appendix : []; //expand needed
                itemInfo.location = (item.location_tag && Object.keys(item.location_tag).length) ? item.location_tag : null;
                itemInfo.project_name = item.project_name;

                itemsInfo.push(itemInfo);
            }

            let dataItem = ratingsByGroupChartData.find(dItem => dItem.group === item.item_category) || {};
            if (Object.keys(dataItem).length) {
                dataItem[capitalize(item.answer)] += 1;
                totalRatingPerCategory[item.item_category] += 1;
                let dataItemIndex = ratingsByGroupChartData.findIndex(dItem => dItem.group === item.item_category);
                ratingsByGroupChartData[dataItemIndex] = dataItem;
            } else {
                totalRatingPerCategory[item.item_category] = 0;
                totalRatingPerCategory[item.item_category] += 1;
                dataItem = {
                    'group': item.item_category
                };
                dataItem[goodRatingLabel] = 0;
                dataItem[fairRatingLabel] = 0;
                dataItem[poorRatingLabel] = 0;
                Object.keys(dataItem).forEach(key => key === 'undefined' ? delete dataItem[key] : {}); //filter undefined

                dataItem[capitalize(item.answer)] += 1;
                ratingsByGroupChartData.push(dataItem);
            }

            if (item.project_id) {
                participatedProjectIds.push(item.project_id);
            }
        }
    });
    let totalRatingCount = +(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
    let goodRatingPercentage = parseInt(100/(totalRatingCount)*(goodRatingItemsCount));
    let fairRatingPercentage = parseInt(100/(totalRatingCount)*(fairRatingItemsCount));
    let poorRatingPercentage = parseInt(100/(totalRatingCount)*(poorRatingItemsCount));
    let donutChartData = [
        {name: `${goodRatingLabel} (${goodRatingItemsCount})`, count: goodRatingItemsCount, percentage: goodRatingPercentage, color: `${goodRatingColor}`, label: goodRatingLabel},
        {name: `${fairRatingLabel} (${fairRatingItemsCount})`, count: fairRatingItemsCount, percentage: fairRatingPercentage, color: `${fairRatingColor}`, label: fairRatingLabel},
        {name: `${poorRatingLabel} (${poorRatingItemsCount})`, count: poorRatingItemsCount, percentage: poorRatingPercentage, color: `${poorRatingColor}`, label: poorRatingLabel}
    ];
    donutChartData = donutChartData.filter(item => item.label != undefined); //filter undefined

    let donutChartWidth = 374;
    let donutChartHeight = 219;
    let donutChartPercentageTxtAdj = -5;

    let openClosedBarChartData = [];
    let openClosedBarChartLegends = [];
    if(poorRatingLabel && fairRatingLabel) {
        openClosedBarChartData.push({name: `Open (${poorRatingLabel})`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`, label: poorRatingLabel});
        openClosedBarChartData.push({name: `Open (${fairRatingLabel})`, value: totalFairToCloseOut, type: `Rating: ${fairRatingLabel}`, label: fairRatingLabel});

        openClosedBarChartLegends = [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}];
    } else if (poorRatingLabel) {
        openClosedBarChartData.push({name: `Open`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`, label: poorRatingLabel});
        openClosedBarChartLegends = [{ name: fairRatingLabel, color: `${colorYellow}`}];
    } else if (fairRatingLabel) {
        openClosedBarChartData.push({name: `Open`, value: totalPoorToCloseOut, type: `Rating: ${fairRatingLabel}`, label: fairRatingLabel});
        openClosedBarChartLegends = [{ name: poorRatingLabel, color: `${colorRed}`}];
    }
    openClosedBarChartData.push({name: "Closed", value: totalClosedOutItems, type: '', label: "Closed"});
    openClosedBarChartData = openClosedBarChartData.filter(item => item.label != undefined); //filter undefined

    let hasAllClosed = (!totalPoorToCloseOut && !totalFairToCloseOut && totalClosedOutItems) ? `All ${totalClosedOutItems} items closed out` : '';
    openClosedBarChartLegends = openClosedBarChartLegends.filter(item => item.name != undefined); //filter undefined

    let openClosedBarChartWidth = 360;
    let openClosedBarChartHeight = 19;
    let openChartMarginTop = '0px';
    let adjustments = {
        adj1: -2,
        adj2: 20,
        adj3: -27,
        adj4: 8.5,
        adj5: -1.5,
        adj6: 4.5,
        adj7: 26,
        adj8: 2,
        adj9: 37,
        adj10: 4,
        adj11: 2,
        adj12: 2,
        adj13: 3,
    }
    let openClosedBarChartFont = '.8em';

    let ratingsByGroupChartWidth = 680;
    let ratingsByGroupChartHeight = 200;
    let maxRating = 0;
    if (Object.keys(totalRatingPerCategory).length) {
        let keyWithMaxRating = Object.keys(totalRatingPerCategory).reduce((a, b) => totalRatingPerCategory[a] > totalRatingPerCategory[b] ? a : b);
        maxRating = totalRatingPerCategory[keyWithMaxRating];
    }

    let donutGoodLabel = (goodRatingLabel) ? `${goodRatingLabel} (${goodRatingItemsCount})` : '';
    let donutFairLabel = (fairRatingLabel) ? `${fairRatingLabel} (${fairRatingItemsCount})` : '';
    let donutPoorLabel = (poorRatingLabel) ? `${poorRatingLabel} (${poorRatingItemsCount})` : '';
    let openClosedChartColor = [];
    let stackedBarChartColor = [];
    if (goodRatingLabel) {
        stackedBarChartColor.push(colorGreen);
    }

    if (fairRatingLabel) {
        openClosedChartColor.push(colorYellow);
        stackedBarChartColor.push(colorYellow);
    }

    if (poorRatingLabel) {
        openClosedChartColor.push(colorRed);
        stackedBarChartColor.push(colorRed);
    }
    openClosedChartColor.push("#2c961b");

    let hasThreeRating = (goodRatingLabel && fairRatingLabel && poorRatingLabel) ? true: false;

    let ind_donutChart = await getDonutChart(donutChartData, donutChartWidth, donutChartHeight, donutChartPercentageTxtAdj, 100, 60, "30 -13 260 270", donutGoodLabel, donutFairLabel, donutPoorLabel, true, hasThreeRating, 0, 0);
    let ind_openClosedBarChart = await getOpenClosedBarChart(openClosedBarChartData, hasAllClosed, openClosedBarChartWidth, openClosedBarChartHeight, openClosedBarChartFont, openChartMarginTop, adjustments, openClosedChartColor, openClosedBarChartLegends);
    let ind_ratingsByGroupChart = await getStackedBarChart(ratingsByGroupChartData, ratingsByGroupChartColumns, maxRating, ratingsByGroupChartWidth, ratingsByGroupChartHeight, stackedBarChartColor, '6.5px', '', hasThreeRating, false, 25);

    return {
        donutChart: ind_donutChart,
        openClosedBarChart: ind_openClosedBarChart,
        ratingsByGroupChart: ind_ratingsByGroupChart,
        no_of_projects: _uniq(participatedProjectIds),
        no_of_items_tagged: (goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount),
        no_of_positive_items: goodRatingItemsCount,
        no_of_issues_raised: (fairRatingItemsCount + poorRatingItemsCount),
        itemsInfo
    };
}

const getInspectionBuilderReportHtml = async (ibReport, project_logo_file, companyName, type = 'pdf') => {
    let participants = [];
    if (ibReport.participants && ibReport.participants.length) {
        participants = await sails.models.user_reader.find({
            where: {id: ibReport.participants},
            select: ['first_name', 'middle_name', 'last_name']
        });
    }

    let ibChecklist = ibReport.ib_ref;

    //rating labels
    let {goodRatingLabel, fairRatingLabel, poorRatingLabel, hasThreeRatingSystem} = prepareLabels(ibChecklist.scoring_system);

    let scoringSystem = ibChecklist.scoring_system;
    let allChecklists = ibReport.checklist;
    if(ibReport.additional_checklist && ibReport.additional_checklist.length && ibReport.has_subheadings) {
        (allChecklists || []).forEach(function (item) {
            let addClItem = (ibReport.additional_checklist || []).find(cl => cl.heading == item.heading);
            item.subheadings.push(...(addClItem && addClItem.subheadings) ? addClItem.subheadings : []);
        });
    } else {
        allChecklists.push(...ibReport.additional_checklist)
    }

    let subHeadings = [];
    if (ibReport.has_subheadings) {
        subHeadings = allChecklists.reduce((arr, cl) => {
            arr.push(...cl.subheadings);
            return arr;
        },[]);
    }

    let checklistLen = ((!ibReport.has_subheadings) ? allChecklists : subHeadings).reduce((count, item) => {
        count += Math.ceil(((item.question || '').length / 66));
        return count;
    }, 0);
    let closeOutImagesId = [];

    allChecklists.forEach(function (item, i) {
        if (ibReport.has_subheadings) {
            item.subheadings.forEach(function (subItem, j) {
                if (subItem.close_out && subItem.close_out.images && subItem.close_out.images.length) {
                    closeOutImagesId.push(...subItem.close_out.images);
                }
            });
        } else {
            if (item.close_out && item.close_out.images && item.close_out.images.length) {
                closeOutImagesId.push(...item.close_out.images);
            }
        }
    });

    let imagesObj = await sails.models.userfile_reader.find({
        where: {id: _uniq(closeOutImagesId)},
        select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation']
    });

    allChecklists.forEach(function (item, i) {
        if (ibReport.has_subheadings) {
            item.subheadings.forEach(function (subItem, j) {
                if (subItem.close_out && subItem.close_out.images && subItem.close_out.images.length) {
                    subItem.close_out.images = imagesObj.filter(image => subItem.close_out.images.includes(image.id));
                }
            });
        } else {
            if (item.close_out && item.close_out.images && item.close_out.images.length) {
                item.close_out.images = imagesObj.filter(image => item.close_out.images.includes(image.id));
            }
        }
    });

    //considering two columns here
    let checklistTableMaxRowsParColumn = Math.ceil((checklistLen + 2)/2);
    sails.log.info(`Checklist row per column: ${checklistTableMaxRowsParColumn}`, `& Total checklist row:  ${checklistLen}`);
    let itemsInfo = [];
    let goodRatingItemsCount = 0;
    let fairRatingItemsCount = 0;
    let poorRatingItemsCount = 0;
    let totalClosedOutItems = 0;
    let totalFairToCloseOut = 0;
    let totalPoorToCloseOut = 0;
    let ratingsByGroupChartData = [];
    let ratingsByGroupChartColumns = ['group', goodRatingLabel, fairRatingLabel, poorRatingLabel];
    let totalRatingPerCategory = {};
    if (ibReport.has_subheadings) {
        allChecklists.forEach(function (heading, i) {
            heading.subheadings.forEach(function (item, j) {
                let itemAnswerLC = strToLower(item.answer);

                goodRatingItemsCount += [strToLower(goodRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                fairRatingItemsCount += [fairRatingLabel && strToLower(fairRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                poorRatingItemsCount += [strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                if (item.summary || (item.images && item.images.length) || (item.close_out && Object.keys(item.close_out).length)||item.tagged_user_ref||(item.tagged_company_ref && item.tagged_company_ref.length)||(item.location_tag && Object.keys(item.location_tag).length)) {
                    let {itemInfo} = processAllCLItems(ibChecklist, ibReport, item, i, type, j, heading, goodRatingLabel, fairRatingLabel, poorRatingLabel);
                    itemsInfo.push(itemInfo);

                    if((item.close_out && Object.keys(item.close_out).length)) {
                        totalClosedOutItems += ([strToLower(fairRatingLabel)].includes(itemAnswerLC) || [(poorRatingLabel || '').toLowerCase()].includes(itemAnswerLC)) ? 1 : 0;
                    } else {
                        totalFairToCloseOut += [strToLower(fairRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                        totalPoorToCloseOut += [strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                    }
                } else {
                    totalFairToCloseOut += [strToLower(fairRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                    totalPoorToCloseOut += [strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                }

                let category = heading.heading;
                let dataItem = ratingsByGroupChartData.find(dItem => dItem.group === category) || {};
                if (Object.keys(dataItem).length) {
                    dataItem[capitalize(item.answer)] += 1;
                    totalRatingPerCategory[category] += [strToLower(goodRatingLabel), strToLower(fairRatingLabel), strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                    let dataItemIndex = ratingsByGroupChartData.findIndex(dItem => dItem.group === category);
                    ratingsByGroupChartData[dataItemIndex] = dataItem;
                } else {
                    totalRatingPerCategory[category] = 0;
                    totalRatingPerCategory[category] += [strToLower(goodRatingLabel), strToLower(fairRatingLabel), strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                    dataItem = {
                        'group': category
                    };
                    dataItem[goodRatingLabel] = 0;
                    dataItem[fairRatingLabel] = 0;
                    dataItem[poorRatingLabel] = 0;

                    dataItem[capitalize(item.answer)] += 1;
                    ratingsByGroupChartData.push(dataItem);
                }
            });
        });
    } else {
        allChecklists.forEach(function (item, i) {
            let itemAnswerLC = strToLower(item.answer);
            goodRatingItemsCount += [strToLower(goodRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
            fairRatingItemsCount += [fairRatingLabel && strToLower(fairRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
            poorRatingItemsCount += [strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;

            if (item.summary || (item.images && item.images.length) || (item.close_out && Object.keys(item.close_out).length)||item.tagged_user_ref||(item.tagged_company_ref && item.tagged_company_ref.length)||(item.location_tag && Object.keys(item.location_tag).length)) {
                let {itemInfo} = processAllCLItems(ibChecklist, ibReport, item, i, type, 0, null, goodRatingLabel, fairRatingLabel, poorRatingLabel);

                itemsInfo.push(itemInfo);
                if((item.close_out && Object.keys(item.close_out).length)) {
                    totalClosedOutItems += ([strToLower(fairRatingLabel)].includes(itemAnswerLC) || [strToLower(poorRatingLabel)].includes(itemAnswerLC)) ? 1 : 0;
                } else {
                    totalFairToCloseOut += [strToLower(fairRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                    totalPoorToCloseOut += [strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                }
            } else {
                totalFairToCloseOut += [strToLower(fairRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                totalPoorToCloseOut += [strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
            }

            let category = item.heading;
            let dataItem = ratingsByGroupChartData.find(dItem => dItem.group === category) || {};
            if (Object.keys(dataItem).length) {
                dataItem[capitalize(item.answer)] += 1;
                totalRatingPerCategory[category] += [strToLower(goodRatingLabel), strToLower(fairRatingLabel), strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                let dataItemIndex = ratingsByGroupChartData.findIndex(dItem => dItem.group === category);
                ratingsByGroupChartData[dataItemIndex] = dataItem;
            } else {
                totalRatingPerCategory[category] = 0;
                totalRatingPerCategory[category] += [strToLower(goodRatingLabel), strToLower(fairRatingLabel), strToLower(poorRatingLabel)].includes(itemAnswerLC) ? 1 : 0;
                dataItem = {
                    'group': category
                };
                dataItem[goodRatingLabel] = 0;
                dataItem[fairRatingLabel] = 0;
                dataItem[poorRatingLabel] = 0;

                dataItem[capitalize(item.answer)] += 1;
                ratingsByGroupChartData.push(dataItem);
            }
        });
    }

    let maxRating = 0;
    if (Object.keys(totalRatingPerCategory).length) {
        let keyWithMaxRating = Object.keys(totalRatingPerCategory).reduce((a, b) => totalRatingPerCategory[a] > totalRatingPerCategory[b] ? a : b);
        maxRating = totalRatingPerCategory[keyWithMaxRating];
    }

    //Donut Chart
    let totalRatingCount = +(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
    let goodRatingPercentage = Math.round(100/(totalRatingCount)*(goodRatingItemsCount));
    let fairRatingPercentage = Math.round(100/(totalRatingCount)*(fairRatingItemsCount));
    let poorRatingPercentage = Math.round(100/(totalRatingCount)*(poorRatingItemsCount));
    let donutChartData = [
        {name: `${goodRatingLabel} (${goodRatingItemsCount})`, count: goodRatingItemsCount, percentage: goodRatingPercentage, color: `${colorGreen}`, label: goodRatingLabel},
        {name: `${fairRatingLabel} (${fairRatingItemsCount})`, count: fairRatingItemsCount, percentage: fairRatingPercentage, color: `${colorYellow}`, label: fairRatingLabel},
        {name: `${poorRatingLabel} (${poorRatingItemsCount})`, count: poorRatingItemsCount, percentage: poorRatingPercentage, color: `${colorRed}`, label: poorRatingLabel}
    ];

    //Open and Closed Chart
    let openClosedBarChartData = [];
    if([goodRatingLabel].includes(goodRatingLabel)) {
        openClosedBarChartData.push({name: `Open (${poorRatingLabel})`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`});
    } else {
        openClosedBarChartData.push({name: `Open`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`});
    }
    openClosedBarChartData.push({name: `Open (${fairRatingLabel})`, value: totalFairToCloseOut, type: `Rating: ${fairRatingLabel}`});
    openClosedBarChartData.push({name: "Closed", value: totalClosedOutItems, type: ''});

    let openClosedBarChartLegends = [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}];
    if (![goodRatingLabel].includes(goodRatingLabel)) {
        openClosedBarChartLegends = [{ name: poorRatingLabel, color: `${colorRed}`}];
    }

    let hasAllClosed = (!totalPoorToCloseOut && !totalFairToCloseOut && totalClosedOutItems) ? `All ${totalClosedOutItems} items closed out` : '';

    let donutChartWidth = 574;
    let donutChartHeight = 250;
    let openClosedBarChartWidth = 492;
    let openClosedBarChartHeight = 16;
    let openClosedBarChartFont = '.7em';
    let openChartMarginTop = '0px';
    let adjustments = {
        adj1: -2,
        adj2: 16,
        adj3: -23.5,
        adj4: 7.5,
        adj5: -1.5,
        adj6: 3.5,
        adj7: 25.5,
        adj8: (hasAllClosed) ? 3.2 : 4.7,
        adj9: 17,
        adj10:0,
        adj9: 28,
        adj10: -1,
        adj11: 0,
        adj12: 0,
        adj13: 3,
    };

    let ratingsByGroupChartWidth = 981;
    let ratingsByGroupChartHeight = 200;
    let donutChartPercentageTxtAdj = -10;
    if(type == 'pdf') {
        donutChartWidth = 374;
        donutChartHeight = 219;
        openClosedBarChartWidth = 360;
        openClosedBarChartHeight = 19;
        openChartMarginTop = '0px';
        adjustments = {
            adj1: -2,
            adj2: 20,
            adj3: -27,
            adj4: 8.5,
            adj5: -1.5,
            adj6: 4.5,
            adj7: 26,
            adj8: 2,
            adj9: 37,
            adj10: 4,
            adj11: 2,
            adj12: 2,
            adj13: 3,
        }
        openClosedBarChartFont = '.8em';
        ratingsByGroupChartWidth = 680;
        ratingsByGroupChartHeight = 200;
        donutChartPercentageTxtAdj = -5;
    }

    //removing data with 0 values
    for (let category in totalRatingPerCategory) {
        let totalCount = totalRatingPerCategory[category];
        if (!totalCount) {
            let itemIndex = ratingsByGroupChartData.findIndex(item => item.group == category);
            ratingsByGroupChartData.splice(itemIndex, 1);
        }
    }

    let checklistTitle = (ibChecklist.ib_title) ? ibChecklist.ib_title : `Checklist Inspection`;
    let form_template = `pages/inspection-builder-report-form-page`;
    let report_datetime = (+ibReport.report_datetime) ? +ibReport.report_datetime : +ibReport.createdAt;
    // NOTE: Breaking the summery /n with <br> to preview as it is.
    /*let ibr_images = itemsInfo.flatMap(item =>
        (item.images || []).flatMap(img =>
            img.img_translation && img.img_translation.length ?
            img.img_translation.map(translation => translation) :
            [(type === 'pdf' && img.md_url) ? img.md_url : img.file_url]
        )
    );*/

    return await sails.renderView(form_template, {
        type,
        goodRating: strToLower(goodRatingLabel),
        fairRating: strToLower(fairRatingLabel),
        poorRating: strToLower(poorRatingLabel),
        title: checklistTitle,
        report_datetime,
        project: ibReport.project_ref,
        checklistTableMaxRowsParColumn,
        companyName,
        ibReport,
        ibChecklist,
        allChecklists,
        participants,
        itemsInfo,
        checklistLen,
        inspectedItemsCount: (fairRatingItemsCount + poorRatingItemsCount + goodRatingItemsCount),
        unsatisfactoryItems: (fairRatingItemsCount + poorRatingItemsCount),
        moment,
        dateFormat: 'DD/MM/YYYY',
        project_logo_file,
        layout: false,
        strToLower,
        donutChart: await getDonutChart(donutChartData, donutChartWidth, donutChartHeight, donutChartPercentageTxtAdj, 100, 60, "10 -13 260 270", `${goodRatingLabel} (${goodRatingItemsCount})`, `${fairRatingLabel} (${fairRatingItemsCount})`, `${poorRatingLabel} (${poorRatingItemsCount})`, true, hasThreeRatingSystem, 0, 0),
        openClosedBarChart: await getOpenClosedBarChart(openClosedBarChartData, hasAllClosed, openClosedBarChartWidth, openClosedBarChartHeight, openClosedBarChartFont, openChartMarginTop, adjustments, [`${colorRed}`, `${colorYellow}`, "#2c961b"], openClosedBarChartLegends),
        ratingsByGroupChart: await getStackedBarChart(ratingsByGroupChartData, ratingsByGroupChartColumns, maxRating, ratingsByGroupChartWidth, ratingsByGroupChartHeight, [`${colorGreen}`, `${colorYellow}`, `${colorRed}`], '6.5px', '', [(goodRatingLabel || '')].includes(goodRatingLabel), false, 25),
        unix(n, format) {
            return moment.unix(n).format(format);
        },
        momentTz(n, format) {
            return ibReport.user_ref.timezone ? momentTz(+n).tz(ibReport.user_ref.timezone).format(format) : moment(n).format(format);
        },
        getColorClass(prefix = '', answer) {
            let colorClass = prefix;
            if([strToLower(goodRatingLabel)].includes(strToLower(answer)) == true) {
                colorClass += 'green';
            } else if ([strToLower(fairRatingLabel)].includes(strToLower(answer)) == true){
                colorClass += 'yellow';
            } else if ([strToLower(poorRatingLabel)].includes(strToLower(answer)) == true){
                colorClass += 'red';
            }
            return colorClass;
        },
        getSequenceNum(question) {
            return  Math.ceil(((question || '').length / 66));
        },
        toFixed(number) {
            return (+number) ? (+number).toFixed(4) : 0;
        },
        showRatingPercentage(ibRef) {
            return !((INSPECTION_ID || []).includes(ibRef.id));
        }
    });
}

const processInspectionBuilderItemCloseOut = async (req, res, ibClReport, itemInfo, checklistType, itemIdx, itemSubIdx) => {
    if (ibClReport && ibClReport.id) {
        let category = '';
        let checklist = ibClReport[checklistType];
        checklist = (checklist || []).map((item, index) => {
            if (index == itemIdx) {
                if (!ibClReport.has_subheadings) {
                    item = itemInfo;
                } else {
                    item.subheadings = (item.subheadings || []).map((subItem, idx) => {
                        if (idx == itemSubIdx) {
                            category = item.heading;
                            subItem = itemInfo;
                        }
                        return subItem;
                    });
                }
            }
            return item;
        });

        sails.log.info('Closing out Ib checklist report item.');
        let updateRequest = {};
        updateRequest[checklistType] = checklist;
        // updateRequest["status"] = status;
        let updatedIbClReport = await sails.models.inspectionbuilderreport.updateOne({id: ibClReport.id}).set(updateRequest);

        sails.log.info('Closed out Ib checklist report item successfully.');
        successResponse(res, {project_ib_reports: updatedIbClReport});

        // Sleep for some time, as close out PDFs are still being converted to images for img_translation.
        await sleep(30000);

        sails.log.info('Sending close out item mail to inspection tour owner.')
        let closeOutImagesSrc = [];
        if(Object.keys(itemInfo.close_out).length && itemInfo.close_out.images && itemInfo.close_out.images.length) {
            let files = await sails.models.userfile.find({
                where: {id: _uniq(itemInfo.close_out.images)},
                select: ['id', 'file_url', 'img_translation']
            });
            closeOutImagesSrc = (files || []).reduce((arr, file) => {
                if (file.file_url && !file.img_translation.length) {
                    arr.push(file.file_url);
                } else if (file.img_translation.length) {
                    arr.push(...file.img_translation);
                }
                return arr;
            }, []);
        }

        sails.log.info(`Send email to ${ibClReport.user_ref.email} for close out item ${(category) ? category+' -' : ''} ${itemInfo.question}`);

        let tz = getProjectTimezone(ibClReport.project_ref);
        let inspectionDateTime = tz ? momentTz(+ibClReport.createdAt).tz(tz).format('DD-MM-YY HH:mm:ss') : moment(ibClReport.createdAt).format('DD-MM-YY HH:mm:ss');
        let subject = `Inspection Closeout: ${ibClReport.project_ref.name}`;
        let html = await sails.renderView('pages/mail/mail-content', {
            mail_body: 'inspection-tour-notify-on-item-close-out',
            title: subject,
            completed_by_name: getUserFirstName(ibClReport.user_ref),
            project_name: ibClReport.project_ref.name,
            closed_out_by: itemInfo.close_out.reviewed_by,
            inspection_number: buildRecordRef(ibClReport),
            inspection_date_time: inspectionDateTime,
            item_title: `${(category) ? category+' -' : ''} ${itemInfo.question}`,
            item_summary: itemInfo.summary,
            closeout_details: itemInfo.close_out.details,
            closeout_images: closeOutImagesSrc,
            layout: false
        });
        await EmailService.sendMail(subject, [ibClReport.user_ref.email], html);

        await checkAndSyncToAsite(req, res, ibClReport, updatedIbClReport);
        return;
    }

    sails.log.info('Failed to close out an Ib checklist item.');
    return errorResponse(res, sails.__('Failed to close out an Ib checklist item.'), failure);
}

const createUpdateIbReport = async (req, res, projectId, ibCreateUpdateRequest, ibRef, reportId) => {
    /** create update inspection
     * @DB: Retrieve user's latest revision and update it's id in request
     * @DB: Get data for associated inspection builder data
     * @IF: if ib builder does not found
     *      @RETURN: return error for not founded associated inspection builder
     * @LOGIC: Prepare label and calculate ratings score based on ib builders configuration and value which is being passed in req payload
     * @IF: if reportId present
     *      - Update the report with new data which is in req payload
     * @ELSE:
     *      - Create new report with req payload
     * @IF: If ibCreateUpdateRequest.finalised is true
     *      - Consider it as user is submitting the report
     *      - Fetch Project info, create full username
     *      - Tagged user company data expansion
     *      - Expand report checklist
     *      @Loop: [checklist+additional_checklist] running loop on concatenated list of each checklist item
     *          @IF: For item has_subheadings = true
     *              @LOOP: For each subitem in item.subheadings
     *                  @IF: in subitem it has tagged_user_ref
     *                      - Prepare title, body and user info from subitem
     *                      - Add rating for fair, poor and good in respective variable from subitem
     *                      - Send email and notification for tagged user
     *          @ELSE:
     *              @IF: in item it has tagged_user_ref
     *                  - Prepare title, body and user info from item
     *                  - Add rating for fair, poor and good in respective variable from item
     *                  - Send email and notification for tagged user
     *      - Send inspection mail to Project's Nominated Manager
     *      - @IF: project_category === 'company-project' && projectInfo.parent_company
     *          - Send mail to company project admins(Nominated Managers)
     * @ELSE:
     *      - Call `getInspectionReport` function to retrieve the report data same which is in get api and return it
     */
    sails.log.info(`[createUpdateIbReport] Inspection report change request by ${req.user.id} for finalised = ${ibCreateUpdateRequest.finalised}, version = ${ibCreateUpdateRequest.version}`);

    ibCreateUpdateRequest.user_ref = req.user.id;

    if (!ibCreateUpdateRequest.checklist) {
        ibCreateUpdateRequest.checklist = []
    }

    if (!ibCreateUpdateRequest.additional_checklist) {
        ibCreateUpdateRequest.additional_checklist = []
    }

    let revision = await getLatestUserRevision(ibCreateUpdateRequest.user_ref);
    ibCreateUpdateRequest.user_revision_ref = revision.id;

    sails.log.info('Adding IB checklist report.');
    let ibChecklist = await sails.models.inspectionbuilder_reader.findOne({
        where: {
            id: ibRef
        },
        select: ['scoring_system', 'has_subheadings', 'severity', 'root_cause']
    });

    if (!ibChecklist) {
        return ResponseService.errorResponse(res, 'Inspection builder does not exists');
    }

    //! Patch: For app version = 4.0.11 in the payload it's passing induction id in field of tagged_user_ref instead of user_ref. Below if block is handling to correct the id

    let platform = req.headers['platform'];
    let versionPattern = /^4\.0\.11$/
    if (platform && extractPlatformVersion(platform, versionPattern)) {
        sails.log.info("Request is from mobile app version: 4.0.11");
        let inductionIdToUserRef = {}
        let fetchedReport;
        if (reportId) {
            [fetchedReport, inductionIdToUserRef] = await Promise.all([
                sails.models.inspectionbuilderreport.findOne({ id: reportId, project_ref: projectId }),
                getTaggedInductionRefs(ibCreateUpdateRequest, projectId)
            ]);
        } else {
            inductionIdToUserRef = await getTaggedInductionRefs(ibCreateUpdateRequest, projectId);
        }

        // Edge case when started from web or mobile app other then 4.0.11 version, to check if tagged_user_ref is same as saved copy then do not update; Creating checklist and AdditionalChecklist tagged UserRef map to use later.
        const existingChecklistTaggedUserRefMap = {};
        const existingAddChecklistTaggedUserRefMap = {};
        if (fetchedReport) {
            if (fetchedReport.has_subheadings) {
                (fetchedReport.checklist || []).forEach(checklist => {
                    const checklistId = checklist.id;
                    if (!existingChecklistTaggedUserRefMap[checklistId]) existingChecklistTaggedUserRefMap[checklistId] = {};
                
                    (checklist.subheadings || []).forEach(subheading => {
                        existingChecklistTaggedUserRefMap[checklistId][subheading.item_id] = subheading.tagged_user_ref;
                    });
                });
                (fetchedReport.additional_checklist || []).forEach(checklist => {
                    const checklistId = checklist.id;
                    if (!existingAddChecklistTaggedUserRefMap[checklistId]) existingAddChecklistTaggedUserRefMap[checklistId] = {};
                
                    (checklist.subheadings || []).forEach(subheading => {
                        existingAddChecklistTaggedUserRefMap[checklistId][subheading.item_id] = subheading.tagged_user_ref;
                    });
                });
            } else {
                (fetchedReport.checklist || []).forEach(checklist => {
                    const checklistId = checklist.id;
                    existingChecklistTaggedUserRefMap[checklistId] = checklist.tagged_user_ref
                });
                (fetchedReport.additional_checklist || []).forEach(checklist => {
                    const checklistId = checklist.id;
                    existingAddChecklistTaggedUserRefMap[checklistId] = checklist.tagged_user_ref
                });
            }
        }

        sails.log.info(`Changing tagged_user_ref for reportId: ${fetchedReport && fetchedReport.id}, has_subheadings: ${ibCreateUpdateRequest.has_subheadings}`);
        if (ibCreateUpdateRequest.has_subheadings) {
            ibCreateUpdateRequest.checklist.forEach(checklist => {
                (checklist.subheadings || []).forEach(subheading => {
                    const originalId = subheading.tagged_user_ref;
                    if (originalId && inductionIdToUserRef[originalId] && (!fetchedReport || originalId !== existingChecklistTaggedUserRefMap[checklist.id]?.[subheading.item_id])) {
                        sails.log.info(`Updating tagged_user_ref ${originalId} -> ${inductionIdToUserRef[originalId]} for reportId: ${fetchedReport && fetchedReport.id}, has_subheadings: ${ibCreateUpdateRequest.has_subheadings}`);
                        subheading.tagged_user_ref = inductionIdToUserRef[originalId];
                    }
                });
            });
            ibCreateUpdateRequest.additional_checklist.forEach(checklist => {
                (checklist.subheadings || []).forEach(subheading => {
                    const originalId = subheading.tagged_user_ref;
                    if (originalId && inductionIdToUserRef[originalId] && (!fetchedReport || originalId !== existingAddChecklistTaggedUserRefMap[checklist.id]?.[subheading.item_id])) {
                        sails.log.info(`Updating tagged_user_ref ${originalId} -> ${inductionIdToUserRef[originalId]} for reportId: ${fetchedReport && fetchedReport.id}, has_subheadings: ${ibCreateUpdateRequest.has_subheadings}`);
                        subheading.tagged_user_ref = inductionIdToUserRef[originalId];
                    }
                });
            });
        } else {
            ibCreateUpdateRequest.checklist.forEach(checklist => {
                const originalId = checklist.tagged_user_ref;
                if (originalId && inductionIdToUserRef[originalId] && (!fetchedReport || originalId !== existingChecklistTaggedUserRefMap[checklist.id])) {
                    sails.log.info(`Updating tagged_user_ref ${originalId} -> ${inductionIdToUserRef[originalId]} for reportId: ${fetchedReport && fetchedReport.id}, has_subheadings: ${ibCreateUpdateRequest.has_subheadings}`);
                    checklist.tagged_user_ref = inductionIdToUserRef[originalId];
                }
            });
            ibCreateUpdateRequest.additional_checklist.forEach(checklist => {
                const originalId = checklist.tagged_user_ref;
                if (originalId && inductionIdToUserRef[originalId] && (!fetchedReport || originalId !== existingAddChecklistTaggedUserRefMap[checklist.id])) {
                    sails.log.info(`Updating tagged_user_ref ${originalId} -> ${inductionIdToUserRef[originalId]} for reportId: ${fetchedReport && fetchedReport.id}, has_subheadings: ${ibCreateUpdateRequest.has_subheadings}`);
                    checklist.tagged_user_ref = inductionIdToUserRef[originalId];
                }
            });
        }
    }

    let {goodRatingLabel, fairRatingLabel, poorRatingLabel} = prepareLabels(ibChecklist.scoring_system);
    let { hasRatingPoint, ratingPointAgainstRating } = getRatingPointInfo(ibChecklist.scoring_system);

    ibCreateUpdateRequest.rating_percentage = await calculateRatingPercentage(ibCreateUpdateRequest, ibChecklist, goodRatingLabel, fairRatingLabel, poorRatingLabel, hasRatingPoint, ratingPointAgainstRating);

    let ibReport;
    if (reportId) {
        sails.log.info(`[createUpdateIbReport] Update inspection report request by user = ${req.user.id} for reportId = ${reportId}, finalised = ${ibCreateUpdateRequest.finalised}, version = ${ibCreateUpdateRequest.version}`);
        ibReport = await sails.models.inspectionbuilderreport.updateOne({ id: reportId }).set(ibCreateUpdateRequest);
    } else {
        sails.log.info(`[createUpdateIbReport] Create inspection report request by ${req.user.id} for finalised = ${ibCreateUpdateRequest.finalised}, version = ${ibCreateUpdateRequest.version}`);
        ibReport = await sails.models.inspectionbuilderreport.create(ibCreateUpdateRequest);
    }

    if (ibCreateUpdateRequest.finalised) {
        let projectInfo = await sails.models.project.findOne({
            where: {id: ibReport.project_ref},
            select: ['name', 'project_category', 'parent_company', 'project_type', 'custom_field']
        });
        let tz = getProjectTimezone(projectInfo);
        let addedByUsr = req.user;
        let submittedByUserName = getUserFullName(addedByUsr);
        let reportNumber = buildRecordRef(ibReport);
        let {allTaggedUsers, allTaggedCompanies, allImages} = await getTaggedItemsRefs(ibReport);
        let reportDateTime = tz ? momentTz(+ibReport.createdAt).tz(tz).format('DD-MM-YY HH:mm:ss') : moment(ibReport.createdAt).format('DD-MM-YY HH:mm:ss');
        let [iBChecklist] = await getIBChecklists({id:ibReport.ib_ref}, 'DESC');

        let allChecklists = [...ibReport.checklist, ...ibReport.additional_checklist];
        let midRatedItemsCount = 0;
        let lowRatedItemsCount = 0;
        let highRatedItemsCount = 0;

        for (const item of allChecklists) {
            if (ibReport.has_subheadings) {
                for (const subItem of item.subheadings) {
                    if (subItem && subItem.tagged_user_ref) {
                        const taggedUserInfo = allTaggedUsers.find(user => user.id ==  subItem.tagged_user_ref);
                        const item_title = `${(item.heading) ? item.heading + ' - ' : ''}${subItem.question}`;
                        const message = `${submittedByUserName} has tagged you in ${item_title} on ${projectInfo.name}`;

                        sails.log.info(`[createUpdateIbReport] Sending mail & push notification for report = ${ibReport.id}`);

                        await Promise.all([
                            sendMailToTaggedUser(ibChecklist, ibReport, subItem, item, allImages, allTaggedUsers, allTaggedCompanies, projectInfo, reportNumber, reportDateTime, submittedByUserName, iBChecklist.ib_title, goodRatingLabel, fairRatingLabel, poorRatingLabel),
                            taggedUserInfo ? queuePushNotifications({ title: `Tagged in ${iBChecklist.ib_title} Item`, body: message }, {
                                category: NOTIFICATION_CATEGORY.INSPECTION_BUILDER,
                                project_ref: ibReport.project_ref.toString(),
                                profile_pic: addedByUsr.profile_pic_ref.file_url || '',
                                ib_id: ibReport.ib_ref.toString(),
                                ib_report_id: ibReport.id.toString(),
                            }, [taggedUserInfo.id]) : null
                        ].filter(Boolean));
                    }

                    midRatedItemsCount += [subItem.answer && strToLower(fairRatingLabel)].includes(strToLower(subItem.answer)) ? 1 : 0;
                    lowRatedItemsCount += [strToLower(poorRatingLabel)].includes(strToLower(subItem.answer)) ? 1 : 0;
                    highRatedItemsCount += [strToLower(goodRatingLabel)].includes(strToLower(subItem.answer)) ? 1 : 0;
                }
            } else {
                if (item && item.tagged_user_ref) {
                    sails.log.info(`[createUpdateIbReport] Sending emails for report = ${ibReport.id}`);

                    let taggedUserInfo = allTaggedUsers.find(user => user.id ==  item.tagged_user_ref);
                    let item_title = `${item.question}`;
                    let message = `${submittedByUserName} has tagged you in ${item_title} on ${projectInfo.name}`;

                    sails.log.info(`[createUpdateIbReport] Sending mail & push notification for report = ${ibReport.id}`);

                    await Promise.all([
                        sendMailToTaggedUser(ibChecklist, ibReport, item, {}, allImages, allTaggedUsers, allTaggedCompanies, projectInfo, reportNumber, reportDateTime, submittedByUserName, iBChecklist.ib_title, goodRatingLabel, fairRatingLabel, poorRatingLabel),
                        taggedUserInfo ? queuePushNotifications({ title: `Tagged in ${iBChecklist.ib_title} Item`, body: message }, {
                            category: NOTIFICATION_CATEGORY.INSPECTION_BUILDER,
                            project_ref: ibReport.project_ref.toString(),
                            profile_pic: addedByUsr.profile_pic_ref.file_url || '',
                            ib_id: ibReport.ib_ref.toString(),
                            ib_report_id: ibReport.id.toString(),
                        }, [taggedUserInfo.id]) : null
                    ].filter(Boolean));
                }
                midRatedItemsCount += [strToLower(fairRatingLabel)].includes(strToLower(item.answer)) ? 1 : 0;
                lowRatedItemsCount += [strToLower(poorRatingLabel)].includes(strToLower(item.answer)) ? 1 : 0;
                highRatedItemsCount += [strToLower(goodRatingLabel)].includes(strToLower(item.answer)) ? 1 : 0;
            }
        }

        let unsatisfactoryItems = (midRatedItemsCount + lowRatedItemsCount);
        let mailData = {
            title: iBChecklist.ib_title,
            report_num: buildRecordRef(ibReport),
            total_items: (unsatisfactoryItems + highRatedItemsCount),
            unsatisfactory_items: unsatisfactoryItems
        };
        //Send inspection mail to Project's Nominated Manager.
        sails.log.info(`[createUpdateIbReport] Sending inspection emails for report = ${ibReport.id} by ${req.user}`);
        await sendInspectionMailToNomMngr(projectInfo, req.user, `${iBChecklist.ib_title} Submitted`, mailData);

        //send mail to company project admins(Nominated Managers)
        if (projectInfo.project_category === 'company-project' && projectInfo.parent_company) {
            await sendMailToNominatedManagerCPA(
                projectInfo.parent_company,
                projectInfo,
                req.user,
                `${iBChecklist.ib_title} Submitted`,
                '',
                false,
                'inspection-mail-to-company-nm',
                mailData
            );
        }

        let ibClRep =  await sails.models.inspectionbuilderreport.findOne({
            where: {id: ibReport.id},
            select: ['checklist', 'additional_checklist', 'has_subheadings', 'user_ref', 'project_ref', 'record_id', 'createdAt']
        });
        [ibClRep] = await populateUserRefs([ibClRep], 'user_ref', ['id', 'first_name', 'last_name', 'email']);
        [ibClRep] = await populateProjectRefs([ibClRep], 'project_ref', ['id', 'name', 'custom_field', 'project_type', 'parent_company', 'project_number']);
        ibClRep.ib_ref = iBChecklist;

        await checkAndSyncToAsite(req, res, ibClRep, ibReport);
    } else {
        const filter = {
            id: ibReport.id,
            ib_ref: ibCreateUpdateRequest.ib_ref, // It will not apply in case of update report
            project_ref: projectId,
            user_ref: req.user.id
        };
        ibReport = await getInspectionReport(filter, 'DESC', true, true, false);
        // NOTE: If there is report exists with the reportId then only code will be driven upto here
        ibReport = ibReport.inspectionReport[0];
    }

    return ResponseService.successResponse(res, ibReport);
    // return successResponse(res, ibReport);
}

const createIBChecklistFn = async (req, res) => {
    sails.log.info('Create inspection builder checklist for project, by', req.user.id);

    let createRequest = _.pick((req.body || {}), [
        'enabled',
        'project_ref',
        'company_ref',
        'ib_type',
        'activate_on_projects',
        'ib_title',
        'scoring_system',
        'has_subheadings',
        'checklist',
        'dashboard_enabled',
        'record_id',
        'severity',
        'root_cause',
        'detail_fields',
    ]);

    let {validationError} = createChecklistRecord(req);
    if(validationError){
        return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
    }

    createRequest.user_ref = req.user.id;

    let revision = await getLatestUserRevision(createRequest.user_ref);
    createRequest.user_revision_ref = revision.id;

    sails.log.info('Adding inspection builder checklist.');
    let ibChecklist = await sails.models.inspectionbuilder.create(createRequest);

    sails.log.info('Created inspection builder checklist successfully.');
    return successResponse(res, ibChecklist);
};

const updateIBChecklistFn = async (req, res) => {
    let ibId = +req.param('ibId');
    let companyId = +req.param('companyId');
    let validateProject = req.param('validateProject', false);
    sails.log.info('Updating IB Checklist, id:', ibId);

    let updateRequest = _.pick((req.body || {}), [
        'enabled',
        'ib_title',
        'has_subheadings',
        'checklist',
        'dashboard_enabled',
        'activate_on_projects',
        'severity',
        'root_cause',
        'detail_fields',
    ]);

    let {validationError} = updateChecklistRecord(req);
    if(validationError){
        return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
    }

    sails.log.info('Updating Inspection builder checklist with: ', updateRequest);

    let ibChecklist = await sails.models.inspectionbuilder.updateOne({id: ibId}).set(updateRequest);
    if(companyId && validateProject){
        await validateProjectInspections(companyId);
    }

    sails.log.info('Updated inspection builder checklist successfully, id', (ibChecklist.id ? ibChecklist.id : null));
    return ResponseService.successResponse(res, {ib_checklist: ibChecklist});
}

/**
 ValidateProjectInspections - This function will use in the above updateIBChecklistFn to set ib_checklist flag with appropriate status
 + @DB - Get active Ib checklist which are enabled for the company-dependent projects (activeIBsForCompany)
    - If get any active Ib then stop the validation process, we got [] array it means we have this IB active across all the projects
 + @DB - Get company-dependent projects list where project_section_access ->> ib_checklist value also be true (companyDependentProjects)
    - If we got the projects list then proceed further.
 + @DB - Check the active Ib list of project which are active for the provided projects list (activeIBsForProjects)
    - If provided projects have active Ib then stop validation here for the list of projects.
    - For rest of projects will proceed further.
 + @DB - Get active Ib checklist which are enabled for the company-dependent projects (companyWideActiveIBChecklists)
    - If we don't get any list then stop the validation process and will set project_section_access ->> ib_checklist value false for all projects.
    - And if we found active Ibs then will proceed further.
 + @for loop - Will check the Ib is enabled for the specific project or not. (projectsWithoutActiveIBs)
    - If Ib is active for the project then skip this one.
    - If Ib is not active for the project then will set project_section_access ->> ib_checklist value false.
 @Complete - Validation is complete here no further process needed.
 * @param companyId
 */

const validateProjectInspections = async (companyId) => {
    sails.log.info(`Starting validation of project inspections for company ${companyId}`);

    // Fetch activeIBsForCompany efficiently
    let activeIBsForCompany = await getActiveIBsForCompanyFn(companyId);
    sails.log.info(`Found ${activeIBsForCompany.length} active IB checklists at the company level`);
    if(activeIBsForCompany.length){
        sails.log.info('Company-wide IB checklists apply to all projects, skipping further validation.');
        return false;
    }

    // Fetching company-dependent projects
    let companyDependentProjects = await getCompanyDependentProjectFn(companyId);
    companyDependentProjects = companyDependentProjects.map(project => project.id);
    sails.log.info(`Retrieved ${companyDependentProjects} active projects for company ${companyId}`);

    // Fetch activeIBsForProjects efficiently
    let activeIBsForProjects = [];
    if (companyDependentProjects.length) {
        sails.log.info(`Fetching active IBs for ${companyDependentProjects.length} projects.`);
        activeIBsForProjects = await sails.models.inspectionbuilder.find({
            where: {
                ib_type: "project",
                enabled: true,
                project_ref: companyDependentProjects,
            },
            select: ["project_ref"],
        });
    }
    let activeProjectRefsSet = [...new Set(activeIBsForProjects.map(item => item.project_ref))];
    sails.log.info(`Projects with active IBs: ${activeProjectRefsSet}`);

    let projectsWithoutActiveIBs = companyDependentProjects.filter(id => !activeProjectRefsSet.includes(id));
    sails.log.info(`Projects without active IBs: ${projectsWithoutActiveIBs}`);
    if(!projectsWithoutActiveIBs.length){
        sails.log.info('Found IB checklists that apply to all projects. skipping further validation.');
        return false;
    }

    let where = {
        company_ref: companyId,
        ib_type: 'company',
        enabled: true,
    };
    sails.log.info('Fetching company-wide IB checklists to determine IB enablement at the project level.', where);
    let companyWideActiveIBChecklists = await getIBChecklists(where, 'DESC', ['id', 'ib_type', 'activate_on_projects']);
    if(!companyWideActiveIBChecklists.length){
        sails.log.info(`No company-wide IB checklists found. Disabling IB checklist for ${projectsWithoutActiveIBs} projects.`);
        await updateIbChecklistForProjectFn(projectsWithoutActiveIBs);
        sails.log.info(`Completed validation of project inspections for the company ${companyId}`);
        return false;
    }
    sails.log.info(`Found company-wide IB checklists ${companyWideActiveIBChecklists.length}`);

    for(let i=0; i<projectsWithoutActiveIBs.length; i++) {
        let project_id = +projectsWithoutActiveIBs[i];
        const filteredList = companyWideActiveIBChecklists.filter(item =>
            (item.ib_type === "company" && item.activate_on_projects.find(project => Number(project.project_id) === project_id && project.enabled === false))
        );
        sails.log.info(`Found disabled inspections ${filteredList.length} for the project ${project_id}`);

        if(filteredList.length === companyWideActiveIBChecklists.length){
            await updateIbChecklistForProjectFn([project_id]);
            sails.log.info(`Completed validation of project inspections for the company ${companyId}`);
        }
    }
}

const createIBReportFn = async (req, res) => {
    let projectId = +req.param('projectId')
    sails.log.info(`Create inspection report for project: ${projectId}, by ${req.user.id}`);

    let createRequest = _.pick((req.body || {}), [
        'project_ref',
        'has_subheadings',
        'ib_ref',
        'location',
        'participants',
        'summary',
        'sign',
        'fields_data',
        'checklist',
        'additional_checklist',
        'report_datetime',
        'finalised',
    ]);

    if (!projectId) {
        sails.log.info(`Overriding project_ref for v1 api, project_ref: ${createRequest.project_ref}`);
        projectId = createRequest.project_ref;
    }

    if (createRequest.finalised != false) {
        // Set true default for backward compatibility
        createRequest.finalised = true;
    }

    if (createRequest.summary) {
        createRequest.summary = cleanHTMLBreaking(createRequest.summary);
    }

    let {validationError} = createIbClReport(req);
    if(validationError){
        return ResponseService.errorResponse(res, sails.__('validation error message'), {validationError});
    }

    return await createUpdateIbReport(req, res, projectId, createRequest, createRequest.ib_ref)
}


const updateIBReportFn = async (req, res) => {
    let projectId = +req.param('projectId');
    let reportId = +req.param('reportId');
    sails.log.info(`Validating IB Checklist, project: ${projectId}, report: ${reportId}`);

    if (!reportId) {
        sails.log.info(`[updateIBReportFn] Invalid reportId in URL: ${projectId}, user_ref: ${req.user.id}, platform: ${req.headers['platform']}, user_agent: ${req.headers['user-agent']}`);
        return ResponseService.errorResponse(res, 'Invalid Request.');
    }

    let updateRequest = _.pick((req.body || {}), [
        'has_subheadings',
        'location',
        'participants',
        'summary',
        'sign',
        'fields_data',
        'checklist',
        'additional_checklist',
        'report_datetime',
        'finalised',
    ]);

    if (updateRequest.summary) {
        updateRequest.summary = cleanHTMLBreaking(updateRequest.summary);
    }

    let {validationError} = createIbClReport(req);
    if(validationError){
        return ResponseService.errorResponse(res, sails.__('validation error message'), {validationError});
    }

    // check ib report is submitted or modified
    let ibReport = await sails.models.inspectionbuilderreport.findOne({
        where: {
            id: reportId
        },
        select: ['finalised', 'updatedAt', 'version',]
    });

    if (!ibReport) {
        return ResponseService.errorResponse(res, 'This report no longer exist', {deleted: true});
    }

    if(ibReport.finalised) {
        return ResponseService.errorResponse(res, 'This report already submitted', {submitted: true});
    }

    if(ibReport.updatedAt != req.body.updatedAt) {
        sails.log.info(`Report ${ibReport.id} is already modified`, {server: ibReport.updatedAt, request: req.body});
        return ResponseService.errorResponse(res, 'This report has been modified', {modified: true});
    }

    updateRequest.version = ibReport.version + 1

    sails.log.info(`Update IB checklist project: ${projectId} and report: ${reportId} successfully`);
    return await createUpdateIbReport(req, res, projectId, updateRequest, req.body.ib_ref, reportId);
}

const closeOutIbClReportItemFn = async (req, res) => {
    let id = +req.param('id');
    let itemInfo = cleanItem(req.body.item_info || {});
    let checklistType = req.body.checklist_type;
    // let status = +req.body.closeout_status;
    let itemIdx = +req.body.item_index;
    let itemSubIdx = +req.body.item_sub_index || 0;

    sails.log.info('Fetch project IB checklist report: ', id);

    let ibClReport =  await sails.models.inspectionbuilderreport.findOne({
        where: {id: id},
        select: ['checklist', 'additional_checklist', 'has_subheadings', 'user_ref', 'project_ref', 'record_id', 'createdAt']
    })
        .populate('ib_ref')
        .populate('project_ref')
        .populate('user_ref');

    return processInspectionBuilderItemCloseOut(req, res, ibClReport, itemInfo, checklistType, itemIdx, itemSubIdx);
};

const getGeneratedIbClReportPdf = async (req, res, type, ibReport) => {
    // Finding user's employer
    if (ibReport.user_ref && ibReport.user_ref.id) {
        const induction_requests = await sails.models.inductionrequest_reader.find({
            where:{ user_ref: ibReport.user_ref.id },
            select: ['additional_data'],
            sort:[
                {id: 'DESC'},
            ],
            limit: 1
        });
        const employer = ((induction_requests[0] && induction_requests[0].additional_data && induction_requests[0].additional_data.employment_detail && induction_requests[0].additional_data.employment_detail.employer) || '').toString().trim();
        ibReport.user_employer = employer
    }

    if (ibReport && ibReport.id) {
        sails.log.info('got record, id', ibReport ? ibReport.id : undefined);
        let checklistTitle = (ibReport.ib_ref.ib_title) ? ibReport.ib_ref.ib_title : `Checklist Inspection`;
        let { project_logo_file, companyName } = await getCompanyInfo(ibReport.project_ref, null);

        let html = await getInspectionBuilderReportHtml(ibReport, project_logo_file, companyName, type);

        if (type === 'pdf' || type === 'path') {
            sails.log.info('Generating pdf');
            let project = ibReport.project_ref;
            let tz = getProjectTimezone(project);
            let file_name = `${checklistTitle}-${project.id}-${ibReport.record_id}-${moment().format(dbDateFormat)}-${project.name}`;
            let project_line = `${(project.project_number != null) ? project.project_number + ' - ' + project.name : project.name} (#${project.id}): ${project.contractor}`;
            let report_datetime = (+ibReport.report_datetime) ? +ibReport.report_datetime : +ibReport.createdAt;
            let date_line = `Report Date: ${momentTz(report_datetime).tz(tz).format('DD-MM-YY HH:mm:ss')}`;
            return await downloadPdfViaGenerator({
                req,
                res,
                html,
                tool: 'inspection-builder',
                file_name,
                heading_line: checklistTitle,
                project_line,
                date_line,
                logo_file: project_logo_file,
                has_cover: true,
                responseType: type
            });
        }
        sails.log.info('Rendering html view');
        return res.send(html);
    }

    sails.log.info('Failed to find IB Checklist Inspec.');
    return errorResponse(res, sails.__('internal server error'));
};

const checkAndSyncToAsite = async (req, res, ibClReport, updatedIbClReport) => {
    let hasAsite = await checkIfAsiteEnabled(ibClReport.project_ref.id);
    if(!hasAsite){
        sails.log.info(`Asite config on Project ${ibClReport.project_ref.id} is disabled.`);
        return;
    }
    sails.log.info(`Project has asite enabled trying to sync Inspection Builder document for project ${ibClReport.project_ref.id}`);
    let [iBChecklist] = await getIBChecklists({ id: updatedIbClReport.ib_ref }, 'DESC', ['ib_title', 'scoring_system']);
    let { fairRatingLabel, poorRatingLabel } = prepareLabels(iBChecklist.scoring_system);
    let inspectionChecklist = [...updatedIbClReport.checklist, ...updatedIbClReport.additional_checklist];
    let countToCloseOut = 0;

    inspectionChecklist.forEach(function (item, i) {
        if (updatedIbClReport.has_subheadings) {
            item.subheadings.forEach(function (subItem, j) {
                let itemAnswerLC = strToLower(subItem.answer);
                if (itemAnswerLC && (poorRatingLabel.toLowerCase() == itemAnswerLC || itemAnswerLC === fairRatingLabel.toLowerCase()) && !(subItem.close_out && Object.keys(subItem.close_out).length)) {
                    countToCloseOut += 1;
                }
            });
        } else {
            let itemAnswerLC = strToLower(item.answer);
            if (itemAnswerLC && (poorRatingLabel.toLowerCase() == itemAnswerLC || itemAnswerLC === fairRatingLabel.toLowerCase()) && !(item.close_out && Object.keys(item.close_out).length)) {
                countToCloseOut += 1;
            }
        }
    });

    sails.log.info(`Items remaining to closeout ${countToCloseOut}`);
    if(countToCloseOut === 0) {
        let userEmployer = await getUserInductionEmployer({id: ibClReport.user_ref.id}, ibClReport.project_ref.id);
        // Check if user's induction employer not there, Load employer from profile data.
        if(!userEmployer) {
            userEmployer = ibClReport.user_ref.parent_company;
        }
        // Not awaiting syncIBReportToAsite fn, to run the PDF generation and asite upload in backend.
        let localsObj = Object.assign({}, res.locals); // Cloning the res.locals, as the object gets cleared after the API response sent.
        syncIBReportToAsite(req, res, ibClReport.project_ref, ibClReport, ibClReport.id, ibClReport.record_id, iBChecklist.ib_title, localsObj, userEmployer).catch(sails.log.error);
    }
}

const syncIBReportToAsite = async (req, res, projectInfo, ibReport, inspectionId, recordId, ib_title, localsObj, userEmp = 0) => {
    sails.log.info(`[syncIBReportToAsite] Starting execution for inspection builder ${ib_title} report ${inspectionId} employer ID ${userEmp}`);

    let {workspace_id, matched_tool} = await getAsiteProjectToolMapping(projectInfo.id, 'inspections', userEmp);
    if(!workspace_id || !matched_tool) {
        sails.log.info(`[syncIBReportToAsite] Aborting execution for inspection builder ${ib_title} report ${inspectionId} workspace_id or matched_tool not found.`);
        return;
    }

    let { employer } = await getCompanyInfo(projectInfo, null, ['id', 'company_initial']);

    sails.log.info(`[syncIBReportToAsite] preparing PDF for inspection builder ${ib_title} report ${inspectionId}`);
    res.locals = localsObj || res.locals;

    let fileData = await getGeneratedIbClReportPdf(req, res, 'path', ibReport);
    sails.log.info(`[syncIBReportToAsite] PDF prepared, starting asite upload for inspection builder ${ib_title} report ${inspectionId}`);

    //File name format -> {PROJECT/CONTRACT NUMBER} ({INNDEX PROJECT ID NUMBER})-{COMPANY INITIALS}-{TOOL NAME}-{REPORT TYPE}-{REPORT #}
    let toolPhraseForAsite = (ib_title || '').replace(/\s/g, '_');
    fileData.name = `${projectInfo.project_number} (${projectInfo.id})-${employer.company_initial}-${toolPhraseForAsite}-Report-${recordId}.pdf`;
    sails.log.info(`[syncIBReportToAsite] Filename to be used on asite ${fileData.name}`);

    await uploadDocOnAsite(employer.id, workspace_id, matched_tool.folder_id, fileData);
};

const downloadIbClReportFn = async (req, res) => {
    let id = +req.param('ibId');
    let type = req.body.type || 'pdf';

    sails.log.info('Fetch project IB checklist inspection:', id);
    let { inspectionReport: [ibReport] } = await getInspectionReport({ id: id }, 'DESC', true);

    return await getGeneratedIbClReportPdf(req, res, type, ibReport);
};

const inviteToNMsOnInspectionFn = async (req, res) => {
    let inspectionDetail = req.body.ibChecklist;
    let projectIds = (req.body.selected_project || []);
    let projects = await sails.models.project_reader.find({
        where: {
            id: projectIds
        },
        select: ['id', 'name']
    });
    sails.log.info('Invite to inspection email to nom managers of projects, count:', projects.length);
    let inviteesArray = [];
    for (let index in projects) {
        let project = projects[index];
        let inviteesByProject = {project: project.name, invitees: [], sent_at: moment().add(1, 'm').valueOf()}
        let projUsrResult = await allProjectAdminsByOneOfDesignations(project.id, [ 'nominated', 'custom']);
        projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, 'inspections');
        sails.log.info(`Invite to inspection email to nom managers on project '${project}', count:`, projUsrResult.length);
        let subject = `Inspection Invite - Project: ${project.name}`;
        for (let j = 0, len = projUsrResult.length; j < len; j++) {
            let nomManager = projUsrResult[j].user_ref || {};
            //storing all invitees
            inviteesByProject.invitees.push(getUserFullName(nomManager));
            let nm_firstname = nomManager.first_name;
            let emailHtml = await sails.renderView('pages/mail/mail-content', {
                title: subject,
                mail_body: 'invite-to-inspection-mail-to-project-nm',
                nm_firstname,
                submitted_by: getUserFullName(req.user),
                project_name: project.name,
                inspection_title: inspectionDetail.ib_title,
                layout: false
            });
            sails.log.info('Sending invite to inspection email to', nomManager.email);
            await sendMail(subject, [nomManager.email], emailHtml);
            sails.log.info(`Inspection Invite email has been sent`);
        }

        if(inviteesByProject.invitees.length) {
            inviteesArray.push(inviteesByProject);
        }
    }

    let ibChecklist = await sails.models.inspectionbuilder.updateOne({id: inspectionDetail.id}).set({invitees:[...inviteesArray, ...(inspectionDetail.invitees)]});

    return successResponse(res, {});
};

module.exports = {
    createIBChecklist: createIBChecklistFn,

    createIBChecklistV2: createIBChecklistFn,

    createCompanyIBChecklistV2: createIBChecklistFn,

    updateIBChecklist: updateIBChecklistFn,

    updateIBChecklistV2: updateIBChecklistFn,

    updateCompanyIBChecklistV2: updateIBChecklistFn,

    createIBReport: createIBReportFn,

    updateIBReport: updateIBReportFn,

    createIBReportV2: createIBReportFn,

    getProjectIBReports: async (req, res) => {
        let projectId = +req.param('projectId');
        let ibId = +req.param('ibId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let finalised = req.param('finalised', 'true') === 'true';
        let searchTerm = req.param('q') ? req.param('q') : '';
        let filter = {
            project_ref: projectId,
            ib_ref: ibId,
            finalised: finalised,
            offset : pageNumber * pageSize,
            limit: pageSize,
            searchTerm: searchTerm
        }

        if (!finalised) {
            filter = {...filter, user_ref: +req.user.id}
        }

        sails.log.info('Fetch IB reports filter.', filter);
        let {total: total_record_count, inspectionReport: projectIbReports} = await getInspectionReport(filter, 'DESC', true, true);
        projectIbReports = await attachProfilePicWithUserRefInfo(projectIbReports);

        return ResponseService.successResponse(res, {project_ib_reports: projectIbReports, total_record_count});
    },

    getProjectIBReport: async (req, res) => {
        let projectId = +req.param('projectId');
        let reportId = +req.param('reportId');

        sails.log.info(`Fetch Project: ${projectId} report id: ${reportId}`);
        let ibReport = await getInspectionReport({ id: reportId, project_ref: projectId }, 'DESC', true, true, false);

        if (!ibReport.inspectionReport) {
            return ResponseService.errorResponse(res, 'Report not found');
        } else {
            ibReport = ibReport.inspectionReport[0];
        }

        return ResponseService.successResponse(res, ibReport);
    },

    getUserIBReports: async (req, res) => {
        let userId = +req.user.id;
        let ibId = +req.param('ibId');
        let projectId = +req.param('projectId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let finalised = req.param('finalised', 'true') === 'true';

        let filter = {
            user_ref: userId,
            ib_ref: ibId,
            offset : pageNumber * pageSize,
            limit: pageSize,
            finalised,
        };

        if (projectId) {
            filter.project_ref = projectId;
        }

        sails.log.info('Fetch user IB reports filter.', filter);
        let { inspectionReport: userIbReports } = await getInspectionReport(filter, 'DESC', true);

        return ResponseService.successResponse(res, {user_ib_reports: userIbReports});
    },

    getProjectIBChecklists: async (req, res) => {
        let projectId = +req.param('projectId');
        let enabled_only = (req.param('all', 'true') === 'false');
        let where = { project_ref: projectId };
        if (enabled_only) {
            where.enabled = true;
        }
        sails.log.info('Fetch project IB checklists filter.', where);
        let projectIBChecklists = await getIBChecklists(where, 'DESC');

        //fetch inspections created on company portal
        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['id', 'name', 'contractor', 'parent_company', 'custom_field']
        });

        let { project_logo_file, companyName, employer: contractor } = await getCompanyInfo(projectInfo, null, [], false);
        where = {
            company_ref: contractor.id,
            ib_type: 'company'
        };
        sails.log.info('Fetch company IB checklists filter.', where);
        let companyIBChecklists = await getIBChecklists(where, 'DESC');
        if (enabled_only) {
            companyIBChecklists = (companyIBChecklists || []).filter(inspection => isEnabledForProject(inspection, projectId));
        }
        return ResponseService.successResponse(res, {project_ib_checklists: [...projectIBChecklists, ...companyIBChecklists]});
    },

    getCompanyIBChecklists: async (req, res) => {
        let companyId = +req.param('companyId');
        let where = {
            company_ref: companyId,
            ib_type: 'company'
        };

        sails.log.info('Fetch company IB checklists filter.', where);
        let companyIBChecklists = await getIBChecklists(where, 'DESC');

        return ResponseService.successResponse(res, {company_ib_checklists: companyIBChecklists});
    },

    closeOutIbClReportItem: closeOutIbClReportItemFn,

    closeOutIbClReportItemV2: closeOutIbClReportItemFn,

    closeOutIbClReportAction: async (req, res) => {
        let id = +req.param('id');
        let itemInfo = cleanItem(req.body.item_info || {});
        let checklistType = req.body.checklist_type;
        let itemIdx = +req.body.item_index;
        let itemSubIdx = +req.body.item_sub_index || 0;
        let user = req.user;

        sails.log.info(`[closeOutIbClReportAction] Fetch project IB checklist report ID: ${id}, checklistType: ${checklistType}`);

        let ibClReport =  await sails.models.inspectionbuilderreport.findOne({
                where: {id: id},
                select: ['checklist', 'additional_checklist', 'has_subheadings', 'user_ref', 'project_ref', 'record_id', 'createdAt']
            })
            .populate('ib_ref')
            .populate('project_ref')
            .populate('user_ref');

        let checklist = ibClReport[checklistType];
        let isAllowedToCloseOut = (checklist || []).findIndex((item, index) => index === itemIdx && (
            !ibClReport.has_subheadings ? item.tagged_user_ref === user.id :
            (item.subheadings || []).some((subItem, idx) => idx === itemSubIdx && subItem.tagged_user_ref === user.id)
        )) !== -1;

        if(!isAllowedToCloseOut) {
            sails.log.info('User not allowed to closed out checklist report item.', user.id);
            return successResponse(res, {message: 'Unable to closeout, Access denied.'});
        }

        return processInspectionBuilderItemCloseOut(req, res, ibClReport, itemInfo, checklistType, itemIdx, itemSubIdx);
    },

    downloadIbClReport: downloadIbClReportFn,

    downloadIbClReportV2: downloadIbClReportFn,

    getIBItemsToCloseout: async (req, res) => {
        let projectId = +req.param('projectId');
        let userId = +req.param('userId');
        let ibClReport =  await sails.models.inspectionbuilderreport.find({
            where: {project_ref: projectId},
            select: ['id', 'project_ref', 'ib_ref', 'has_subheadings', 'checklist', 'additional_checklist', 'createdAt']
        }).populate('ib_ref');

        let itemsToCloseout = [];
        let imageIds = [];
        let taggedCompanyIds = [];
        (ibClReport || []).map(ibReport => {
            let {goodRatingLabel, fairRatingLabel, poorRatingLabel} = prepareLabels(ibReport.ib_ref.scoring_system);

            (ibReport.checklist || []).map((item, item_index) => {
                if (ibReport.has_subheadings) {
                    (item.subheadings || []).forEach(function (subItem, item_sub_index) {
                        if (subItem.tagged_user_ref && (subItem.tagged_user_ref == userId) && [strToLower(fairRatingLabel), strToLower(poorRatingLabel)].includes(strToLower(subItem.answer)) && subItem.close_out && !Object.keys(subItem.close_out).length) {
                            let itemToCloseout = {
                                checklist_type	:	'checklist',
                                item_index	:	item_index,
                                item_sub_index	:	item_sub_index,
                                ib_ref: ibReport.ib_ref.id,
                                report_id: ibReport.id,
                                report_created_at: ibReport.createdAt
                            };
                            itemToCloseout.item_info = subItem;
                            if (subItem.images && subItem.images.length) {
                                imageIds.push(...subItem.images);
                            }
                            if (subItem.tagged_company_ref && subItem.tagged_company_ref.length) {
                                taggedCompanyIds.push(...subItem.tagged_company_ref);
                            }
                            itemsToCloseout.push(itemToCloseout);
                        }
                    });
                } else {
                    if (item.tagged_user_ref && (item.tagged_user_ref == userId) && [strToLower(fairRatingLabel), strToLower(poorRatingLabel)].includes(strToLower(item.answer)) && item.close_out && !Object.keys(item.close_out).length) {
                        let itemToCloseout = {
                            checklist_type	:	'checklist',
                            item_index	:	item_index,
                            item_sub_index	:	null,
                            ib_ref: ibReport.ib_ref.id,
                            report_id: ibReport.id,
                            report_created_at: ibReport.createdAt
                        };
                        itemToCloseout.item_info = item;
                        if (item.images && item.images.length) {
                            imageIds.push(...item.images);
                        }
                        if (item.tagged_company_ref && item.tagged_company_ref.length) {
                            taggedCompanyIds.push(...item.tagged_company_ref);
                        }
                        itemsToCloseout.push(itemToCloseout);
                    }
                }
            });

            (ibReport.additional_checklist || []).map((item, item_index) => {
                if (ibReport.has_subheadings) {
                    (item.subheadings || []).forEach(function (subItem, item_sub_index) {
                        if (subItem.tagged_user_ref && (subItem.tagged_user_ref == userId) && [strToLower(fairRatingLabel), strToLower(poorRatingLabel)].includes(strToLower(subItem.answer)) && subItem.close_out && !Object.keys(subItem.close_out).length) {
                            let itemToCloseout = {
                                checklist_type	:	'additional_checklist',
                                item_index	:	item_index,
                                item_sub_index	:	item_sub_index,
                                ib_ref: ibReport.ib_ref.id,
                                report_id: ibReport.id,
                                report_created_at: ibReport.createdAt
                            };
                            itemToCloseout.item_info = subItem;
                            if (subItem.images && subItem.images.length) {
                                imageIds.push(...subItem.images);
                            }
                            if (subItem.tagged_company_ref && subItem.tagged_company_ref.length) {
                                taggedCompanyIds.push(...subItem.tagged_company_ref);
                            }
                            itemsToCloseout.push(itemToCloseout);
                        }
                    });
                } else {
                    if (item.tagged_user_ref && (item.tagged_user_ref == userId) && [strToLower(fairRatingLabel), strToLower(poorRatingLabel)].includes(strToLower(item.answer)) && item.close_out && !Object.keys(item.close_out).length) {
                        let itemToCloseout = {
                            checklist_type	:	'additional_checklist',
                            item_index	:	item_index,
                            item_sub_index	:	null,
                            ib_ref: ibReport.ib_ref.id,
                            report_id: ibReport.id,
                            report_created_at: ibReport.createdAt
                        };
                        itemToCloseout.item_info = item;
                        if (item.images && item.images.length) {
                            imageIds.push(...item.images);
                        }
                        if (item.tagged_company_ref && item.tagged_company_ref.length) {
                            taggedCompanyIds.push(...item.tagged_company_ref);
                        }
                        itemsToCloseout.push(itemToCloseout)
                    }
                }
            });
        });

        let imagesObj = await sails.models.userfile_reader.find({
            where: {id: _uniq(imageIds)},
            select: ['id', 'sm_url', 'md_url', 'file_url']
        });

        sails.log.info('Tagged Company Ids of Items, ', taggedCompanyIds);
        let companiesInfo = await sails.models.createemployer.find({
            where: {id: _uniq(taggedCompanyIds)},
            select: ['name']
        });

        itemsToCloseout = itemsToCloseout.map(item => {
            if (item.item_info.images && item.item_info.images.length) {
                item.item_info.images = (imagesObj || []).filter(obj => item.item_info.images.includes(obj.id));
            }

            if (item.item_info.tagged_company_ref && item.item_info.tagged_company_ref.length) {
                item.item_info.tagged_company_ref = (companiesInfo || []).filter(obj => item.item_info.tagged_company_ref.includes(obj.id));
            }
            return item;
        });

        itemsToCloseout = _groupBy(itemsToCloseout, l => l.ib_ref);
        let inspection_builders = [];
        let ibIds = [];
        if(Object.keys(itemsToCloseout).length) {
            ibIds = Object.keys(itemsToCloseout);
            inspection_builders = await sails.models.inspectionbuilder.find({
                where: {
                    id: ibIds.sort((a,b) => (a.id > b.id) ? 1 : ((b.id > a.id) ? -1 : 0))
                },
                select: ['ib_title']
            }).sort([
                {id: 'ASC'}
            ]);
        }

        sails.log.info(`Found ${Object.keys(itemsToCloseout).length} items to closeout in inspection builders ${ibIds.join()}.`);
        return successResponse(res, {inspection_builders, items_to_closeout: itemsToCloseout});
    },

    inviteToNMsOnInspection: inviteToNMsOnInspectionFn,

    inviteToNMsOnInspectionV2: inviteToNMsOnInspectionFn,

    dashboardOfInspectionBuilder: async (req, res) =>  {
        let projectId = +req.param('projectId');
        let ibId = +req.param('ibId');
        let type = req.param('type');
        let companyId = +req.param('companyId', 0);
        let totalPages = 1;
        let where = {
            id: ibId
        };

        let ibInfo = await sails.models.inspectionbuilder_reader.findOne({
            where: where,
            select: ['ib_title', 'has_subheadings', 'checklist', 'scoring_system']
        });
        sails.log.info('Inspection builder record found with Id: ', ibInfo.id);
        let scoringSystem = ibInfo.scoring_system;
        let { hasRatingPoint, ratingPointAgainstRating } = getRatingPointInfo(scoringSystem);
        let inspectionsWhere = {
            ib_ref: ibInfo.id,
            project_ref: projectId,
            createdAt: {'>=': +req.body.from_date, '<=': +req.body.to_date}
        };
        sails.log.info('Fetch inspections filter.', inspectionsWhere, "Requesting company:", companyId);
        let ibChecklists = await sails.models.inspectionbuilderreport_reader.find({
            where: inspectionsWhere
        });

        if (ibChecklists.length) {
            let projectInfo = await sails.models.project_reader.findOne({id: projectId});

            //rating labels
            let {goodRatingLabel, fairRatingLabel, poorRatingLabel, hasThreeRatingSystem} = prepareLabels(ibInfo.scoring_system);

            let ratingsByGroupChartData = [];
            let ratingsByGroupChartColumns = ['group', goodRatingLabel, fairRatingLabel, poorRatingLabel];
            let totalRatingPerCategory = {};
            let goodRatingItemsCount = 0;
            let poorRatingItemsCount = 0;
            let fairRatingItemsCount = 0;
            let totalClosedOutItems = 0;
            let totalFairToCloseOut = 0;
            let totalPoorToCloseOut = 0;
            let scatteredChartData = [];
            let scatterPlotXAxisValues = [''];
            let daysArr = [];
            let inspectionCreatedDate = '';
            let datePrefix = '';
            let previousInspectionCreatedDate = '';
            let progressIssuesChartData = [];
            let progressIssuesChartXAxisValues = [''];
            let progressIssuesCountArr = [];
            let groupChartDataOfWorseItems = [];
            let totalGood = 0;
            (ibChecklists || []).forEach(function(inspection, i) {
                let inspectionChecklist = [...inspection.checklist, ...inspection.additional_checklist];

                if (ibInfo.has_subheadings) {
                    inspectionChecklist = (inspectionChecklist || []).reduce((arr, item) => {
                        item.subheadings = associateHeadingWithItems(item.subheadings, item.heading);
                        arr.push(...(item.subheadings || []));
                        return arr;
                    }, []);
                }

                let chartGroup = inspection.createdAt;
                let poorClosedOutDays = [];
                let fairClosedOutDays = [];
                let scatteredChartItem = {};
                let progressIssuesChartItem = {};
                let totalPoorIssues = 0;
                let totalFairIssues = 0;
                inspectionChecklist.forEach(function(item, j) {
                    let itemAnswerLC = strToLower(item.answer);
                    goodRatingItemsCount += (itemAnswerLC === goodRatingLabel.toLowerCase()) ? 1 : 0;
                    poorRatingItemsCount += (itemAnswerLC === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    fairRatingItemsCount += (fairRatingLabel && itemAnswerLC === fairRatingLabel.toLowerCase()) ? 1 : 0;

                    if (itemAnswerLC && (poorRatingLabel.toLowerCase() == itemAnswerLC || itemAnswerLC === fairRatingLabel.toLowerCase()) && item.close_out && Object.keys(item.close_out).length) {
                        let hours = (+item.close_out.close_out_at - +inspection.createdAt) / (60*60*1000);
                        if ([poorRatingLabel.toLowerCase()].includes(itemAnswerLC)) {
                            let day = hours/24;
                            poorClosedOutDays.push(day);
                        } else {
                            let day = hours/24;
                            fairClosedOutDays.push(day);
                        }
                        totalClosedOutItems += (itemAnswerLC && (itemAnswerLC === poorRatingLabel.toLowerCase() || itemAnswerLC === fairRatingLabel.toLowerCase())) ? 1 : 0;
                    } else {
                        totalFairToCloseOut += (itemAnswerLC && itemAnswerLC === fairRatingLabel.toLowerCase()) ? 1 : 0;
                        totalPoorToCloseOut += (itemAnswerLC === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    }

                    if (itemAnswerLC && (poorRatingLabel.toLowerCase() == itemAnswerLC || itemAnswerLC === fairRatingLabel.toLowerCase())) {
                        if (poorRatingLabel.toLowerCase() == itemAnswerLC) {
                            totalPoorIssues += 1;
                        } else {
                            totalFairIssues += 1;
                        }
                    }

                    if (itemAnswerLC && [goodRatingLabel.toLowerCase(),poorRatingLabel.toLowerCase(),fairRatingLabel.toLowerCase()].includes(itemAnswerLC)) {
                        let itemCategory = (item.heading) ? item.heading : item.question;
                        if ([poorRatingLabel.toLowerCase(), fairRatingLabel.toLowerCase()].includes(itemAnswerLC)) {
                            let dataItem = groupChartDataOfWorseItems.find(dItem => dItem.group === itemCategory) || {};
                            if (Object.keys(dataItem).length) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['total'] += 1;
                                let dataItemIndex = groupChartDataOfWorseItems.findIndex(dItem => dItem.group === itemCategory);
                                groupChartDataOfWorseItems[dataItemIndex] = dataItem;
                            } else {
                                dataItem = {
                                    'group': itemCategory
                                };
                                dataItem[fairRatingLabel] = 0;
                                dataItem[poorRatingLabel] = 0;
                                dataItem['total'] = 1;
                                dataItem[capitalize(item.answer)] += 1;
                                groupChartDataOfWorseItems.push(dataItem);
                            }
                        } else {
                            totalGood += 1;
                        }

                        let dataItem = ratingsByGroupChartData.find(dItem => dItem.group === chartGroup) || {};
                        if (Object.keys(dataItem).length) {
                            dataItem[capitalize(item.answer)] += 1;
                            totalRatingPerCategory[chartGroup] += 1;
                            let dataItemIndex = ratingsByGroupChartData.findIndex(dItem => dItem.group === chartGroup);
                            ratingsByGroupChartData[dataItemIndex] = dataItem;
                        } else {
                            totalRatingPerCategory[chartGroup] = 0;
                            totalRatingPerCategory[chartGroup] += 1;
                            dataItem = {
                                'group': chartGroup
                            };
                            dataItem[goodRatingLabel] = 0;
                            dataItem[fairRatingLabel] = 0;
                            dataItem[poorRatingLabel] = 0;

                            dataItem[capitalize(item.answer)] += 1;
                            ratingsByGroupChartData.push(dataItem);
                        }
                    }
                });
                inspectionCreatedDate = moment(inspection.createdAt).format('Do MMM YY');
                datePrefix = (previousInspectionCreatedDate == inspectionCreatedDate) ? datePrefix+' ' : '';
                scatteredChartItem.date = scatteredChartData.length + 1;
                scatterPlotXAxisValues.push(datePrefix+inspectionCreatedDate);

                let poorAvgDays = (poorClosedOutDays.length) ? (poorClosedOutDays.reduce(function(a, b){return a + b;}, 0))/poorClosedOutDays.length : 0;
                daysArr.push(poorAvgDays);
                scatteredChartItem[poorRatingLabel] = poorAvgDays;

                let fairAvgDays = (fairClosedOutDays.length) ? (fairClosedOutDays.reduce(function(a, b){return a + b;}, 0))/fairClosedOutDays.length : 0;
                daysArr.push(fairAvgDays);
                scatteredChartItem[fairRatingLabel] = fairAvgDays;
                scatteredChartData.push(scatteredChartItem);

                //Progress of issues
                progressIssuesChartItem.date =  progressIssuesChartData.length + 1;
                progressIssuesChartXAxisValues.push(datePrefix+inspectionCreatedDate);
                progressIssuesChartItem[poorRatingLabel] = totalPoorIssues;
                progressIssuesChartItem[fairRatingLabel] = totalFairIssues;
                progressIssuesCountArr.push(totalPoorIssues + totalFairIssues);
                progressIssuesChartData.push(progressIssuesChartItem);

                previousInspectionCreatedDate = inspectionCreatedDate;
            });

            let maximumNumberOfPoints = ratingPointAgainstRating[strToLower(goodRatingLabel)] * (goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);

            //get max total
            let progressIssuesCharMaxNumber = Math.max(...progressIssuesCountArr);
            let previousDate = '';
            let prefix = '';
            ratingsByGroupChartData = (ratingsByGroupChartData || []).map((data, i) => {
                if (data.group && +(data.group)) {
                    let date = moment(+data.group).format('Do MMM YY');
                    prefix = (previousDate == date) ? prefix+' ' : '';
                    data.group =  prefix+date;
                    previousDate = date;
                }
                return data;
            });
            let maxRating = 0;
            if (Object.keys(totalRatingPerCategory).length) {
                let keyWithMaxRating = Object.keys(totalRatingPerCategory).reduce((a, b) => totalRatingPerCategory[a] > totalRatingPerCategory[b] ? a : b);
                maxRating = totalRatingPerCategory[keyWithMaxRating];
            }

            //get top 10 items by total count
            groupChartDataOfWorseItems = (groupChartDataOfWorseItems.sort((a, b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0))).slice(0, 10);
            let maxRatingInWorse = (groupChartDataOfWorseItems.length) ? groupChartDataOfWorseItems[0].total : 0;

            let maxNumber = Math.max(...daysArr);
            let openClosedBarChartData = [];
            if(hasThreeRatingSystem) {
                openClosedBarChartData.push({name: `Open (${poorRatingLabel})`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`});
            } else {
                openClosedBarChartData.push({name: `Open`, value: totalPoorToCloseOut, type: `Rating: ${poorRatingLabel}`});
            }
            openClosedBarChartData.push({name: `Open (${fairRatingLabel})`, value: totalFairToCloseOut, type: `Rating: ${fairRatingLabel}`});
            openClosedBarChartData.push({name: "Closed", value: totalClosedOutItems, type: ''});

            let hasAllClosed = (!totalPoorToCloseOut && !totalFairToCloseOut && totalClosedOutItems) ? `All ${totalClosedOutItems} items closed out` : '';

            let totalRatingCount = +(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
            let goodRatingPercentage = Math.round(100/(totalRatingCount)*(goodRatingItemsCount));
            let fairRatingPercentage = Math.round(100/(totalRatingCount)*(fairRatingItemsCount));
            let poorRatingPercentage = Math.round(100/(totalRatingCount)*(poorRatingItemsCount));

            let donutChartData = [
                {name: `${goodRatingLabel} (${goodRatingItemsCount})`, count: goodRatingItemsCount, percentage: goodRatingPercentage, color: `${goodRatingColor}`, label: goodRatingLabel},
                {name: `${fairRatingLabel} (${fairRatingItemsCount})`, count: fairRatingItemsCount, percentage: fairRatingPercentage, color: `${fairRatingColor}`, label: fairRatingLabel},
                {name: `${poorRatingLabel} (${poorRatingItemsCount})`, count: poorRatingItemsCount, percentage: poorRatingPercentage, color: `${poorRatingColor}`, label: poorRatingLabel}
            ];

            let { project_logo_file, companyName } = await getCompanyInfo(projectInfo, (companyId ? {id: companyId} : null));

            sails.log.info('generating inspection builder dashboard. Total Pages: ', totalPages);

            let openClosedBarChartWidth = '600';
            let donutChartWidth = 400;
            let donutChartHeight = 315;
            let legendh = 180;
            let donutChartCentroidAdj = -35;
            let ratingsByGroupChartWidth = 537;
            let ratingsByGroupChartHeight = 200;
            let scatterPlotWidth = 537;
            let scatterPlotHeight = 235;
            let labelFontSize = '.4em';
            let marginTop = '0px';
            let stackHeight = 10;
            let adjustments = {
                adj1: -2,
                adj2: 10,
                adj3: -14,
                adj4: 6,
                adj5: -2,
                adj6: 1,
                adj7: 18,
                adj8: (hasAllClosed) ? 5.2 : 4.7,
                adj9: (hasAllClosed) ? 18 : 17,
                adj10: (hasAllClosed) ? -3 : 0,
                adj11: -6,
                adj12: -3.5,
                adj13: 3,
            };
            let problemAreasChartWidth = 331;
            let problemAreasChartHeight = 200;
            let stackChartLegendAdj = 20;
            let progressIssuesChartWidth = 331, progressIssuesChartHeight = 235;
            let scatterPlotLegendsAdj = 10;
            let progressIssuesChartLegendsAdj = 10;
            let donutViewbox = "10 -13 260 350";
            if (type === 'pdf') {
                openClosedBarChartWidth = '1000';
                donutChartWidth = 450;
                donutChartHeight = 360;
                donutViewbox = "50 -13 260 350";
                legendh = 210;
                donutChartCentroidAdj = -50;
                ratingsByGroupChartWidth = 700;
                ratingsByGroupChartHeight = 250;
                scatterPlotWidth = 700;
                scatterPlotHeight = 250;
                labelFontSize = '.4em';
                marginTop = '0px';
                stackHeight = 7;
                adjustments = {
                    adj1: -2,
                    adj2: 7,
                    adj3: -10,
                    adj4: 6,
                    adj5: -2.5,
                    adj6: .5,
                    adj7: 18,
                    adj8: 6,
                    adj9: 14,
                    adj10:0,
                    adj11: -6,
                    adj12: -3.5,
                    adj13: 3,
                }
                progressIssuesChartWidth = 450;
                progressIssuesChartHeight = 245;

                scatterPlotLegendsAdj = 5;
                progressIssuesChartLegendsAdj = 5;

                problemAreasChartWidth = 450;
                problemAreasChartHeight = 245;
                stackChartLegendAdj = 70;
            }

            let openClosedBarChartLegends = [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}];
            if (!hasThreeRatingSystem) {
                adjustments.adj2 = 10;
                openClosedBarChartLegends = [{ name: poorRatingLabel, color: `${colorRed}`}];
            }

            let percentageChartMax = (hasRatingPoint) ? maximumNumberOfPoints : (goodRatingItemsCount + poorRatingItemsCount + fairRatingItemsCount);
            let totalScore = 0;
            if (hasRatingPoint) {
                totalScore += (ratingPointAgainstRating[strToLower(goodRatingLabel)] * goodRatingItemsCount) + (ratingPointAgainstRating[strToLower(poorRatingLabel)] * poorRatingItemsCount);
                totalScore += (fairRatingLabel && ratingPointAgainstRating[strToLower(fairRatingLabel)]) ? (ratingPointAgainstRating[strToLower(fairRatingLabel)] * fairRatingItemsCount) : 0;
            }

            let percentageChartPart = (hasRatingPoint) ? totalScore : goodRatingItemsCount;
            let form_template = `pages/inspection-builder-dashboard-page`;
            let html = await sails.renderView(form_template, {
                title: `Inspection Dashboard`,
                report_from: (+req.body.from_date) ? moment(+req.body.from_date).format('DD/MM/YYYY'): '',
                report_to: (+req.body.to_date) ? moment(+req.body.to_date).format('DD/MM/YYYY'): '',
                company_name: companyName,
                project_number: projectInfo.project_number,
                project_name: projectInfo.name,
                project_logo_file,
                report_type: type,
                total_inspections: ibChecklists.length,
                totalRatedItems: goodRatingItemsCount + poorRatingItemsCount + fairRatingItemsCount,
                goodRatingItemsCount,
                poorRatingItemsCount,
                fairRatingItemsCount,
                totalClosedOutItems,
                totalFairToCloseOut,
                totalPoorToCloseOut,
                hasThreeRatingSystem,
                totalPages,
                openClosedBarChart: await getOpenClosedBarChart(openClosedBarChartData, hasAllClosed, openClosedBarChartWidth, stackHeight, labelFontSize, marginTop, adjustments, [`${colorRed}`, `${colorYellow}`, "#2c961b"], openClosedBarChartLegends),
                donutChart: await getDonutChart(donutChartData, donutChartWidth, donutChartHeight, donutChartCentroidAdj, 100, 60, donutViewbox, `${goodRatingLabel} (${goodRatingItemsCount})`, `${fairRatingLabel} (${fairRatingItemsCount})`, `${poorRatingLabel} (${poorRatingItemsCount})`, true, hasThreeRatingSystem, 0, (hasThreeRatingSystem) ? -125 : -105, legendh),
                ratingsByGroupChart: await getStackedBarChart(ratingsByGroupChartData, ratingsByGroupChartColumns, maxRating, ratingsByGroupChartWidth, ratingsByGroupChartHeight, [`${colorGreen}`, `${colorYellow}`, `${colorRed}`], '7px','Items', hasThreeRatingSystem, true, 25),
                scatterPlot: await getScatterPlot(scatteredChartData, 'date', scatterPlotXAxisValues, [fairRatingLabel, poorRatingLabel], maxNumber, scatterPlotWidth, scatterPlotHeight, '7px', '', 'Days to closeout', scatterPlotLegendsAdj, hasThreeRatingSystem, [`${colorYellow}`, `${colorRed}`], false, "220 0 250 350"),
                percentageGaugeChart: await getPercentageGaugeChart(200, 90, percentageChartMax, percentageChartPart, 'Project Rating'),
                progressIssuesChart: await getScatterPlot(progressIssuesChartData, 'date', progressIssuesChartXAxisValues,[fairRatingLabel, poorRatingLabel], progressIssuesCharMaxNumber, progressIssuesChartWidth, progressIssuesChartHeight, '7px', '', 'Issues', progressIssuesChartLegendsAdj, hasThreeRatingSystem, [ `${colorYellow}`, `${colorRed}`], false, "150 5 150 330"),
                groupChartDataOfWorseItemsChart: await getStackedBarChart(groupChartDataOfWorseItems, ['group', fairRatingLabel, poorRatingLabel], maxRatingInWorse, problemAreasChartWidth, problemAreasChartHeight, [`${colorYellow}`, `${colorRed}`], '7px', 'Issues', hasThreeRatingSystem, true, stackChartLegendAdj),

                layout: false
            });

            if (type === 'pdf') {
                let fileName = `${replaceAll(ibInfo.ib_title, ' ', '-')}-Dashboard-${moment().format('MM-DD-YYYY')}`;
                return await instantPdfGenerator(req, res, html, 'inspection-builder-p-dashboard', fileName, req.headers['user-agent'], { format: 'A3', landscape: true }, 'url');
            }

            sails.log.info('Rendering html view');
            return res.send(html);
        }

        sails.log.info('No inspection records found between the selected duration on the project.');
        return res.send("<p style='text-align: center;margin-top: 100px;margin-bottom: 100px;'>No inspection records found between the selected duration on the project.</p>");
    },

    companyDashboardOfInspectionBuilder: async (req, res) => {
        let companyId = +req.param('companyId');
        let ibId = +req.param('ibId');
        let type = req.param('type');

        let where = {
            company_ref: companyId,
            id: ibId
        };
        let totalPages = 1;

        let ibInfo = await sails.models.inspectionbuilder_reader.findOne({
            where: where,
            select: ['ib_title', 'has_subheadings', 'checklist', 'scoring_system']
        });
        sails.log.info('Inspection builder record found with Id: ', ibInfo.id);
        let { hasRatingPoint, ratingPointAgainstRating } = getRatingPointInfo(ibInfo.scoring_system);

        let inspectionsWhere = {
            ib_ref: ibInfo.id,
            project_ref: (req.body.selected_projects || []),
            createdAt: {'>=': +req.body.from_date, '<=': +req.body.to_date}
        };
        sails.log.info('Fetch inspections filter.', inspectionsWhere, "Requesting company:", companyId);
        let ibChecklists = await sails.models.inspectionbuilderreport.find({
            where: inspectionsWhere
        });
        ibChecklists = await populateProjectRefs(ibChecklists, 'project_ref', []);

        let projectIds = [];
        if (ibChecklists.length) {
            //rating labels
            let {goodRatingLabel, fairRatingLabel, poorRatingLabel, hasThreeRatingSystem} = prepareLabels(ibInfo.scoring_system);

            sails.log.info(goodRatingLabel, fairRatingLabel, poorRatingLabel);
            let goodRatingItemsCount = 0;
            let poorRatingItemsCount = 0;
            let fairRatingItemsCount = 0;
            let totalClosedOutItems = 0;
            let totalFairToCloseOut = 0;
            let totalPoorToCloseOut = 0;
            let metaChecklist = [];
            let inspectionCreatedDate = '';
            let datePrefix = '';
            let previousInspectionCreatedDate = '';
            let avgCloseoutTimePerProjectPoor = [];
            let avgCloseoutTimePerProjectFair = [];
            let groupChartDataOfWorseItems = [];
            let poorIssuesPerProject = [];
            let fairIssuesPerProject = [];
            let ratioPerProjectChartData = [];
            let maxCloseoutTimePerProject = 0;
            let maxIssuesPerProject = 0;
            let existingRecord = undefined;
            let existingRecordIndex = undefined;
            let weeklyUnsItemsChartData = [];
            if (ibChecklists.length) {
                weeklyUnsItemsChartData = prepareWeeksData(mathMinMax(ibChecklists, 'createdAt', 'min'), mathMinMax(ibChecklists, 'createdAt', 'max'));
            }
            let weeklyUnsItemsChartXAxisValues = [''];
            let weeklyUnsItemsMaxNumber = 0;
            let projectGroup = [];
            (ibChecklists || []).forEach(function(inspection, i) {
                projectIds.push(inspection.project_ref.id);

                let inspectionChecklist = [...inspection.checklist, ...inspection.additional_checklist];

                if (ibInfo.has_subheadings) {
                    inspectionChecklist = (inspectionChecklist || []).reduce((arr, item) => {
                        item.subheadings = associateHeadingWithItems(item.subheadings, item.heading);
                        arr.push(...(item.subheadings || []));
                        return arr;
                    }, []);
                }
                let poorClosedOutDays = [];
                let fairClosedOutDays = [];
                let totalGood = 0;
                let totalPoorIssues = 0;
                let totalFairIssues = 0;
                inspectionChecklist.forEach(function (item, j) {
                    let itemAnswerLC = strToLower(item.answer);
                    goodRatingItemsCount += (itemAnswerLC === goodRatingLabel.toLowerCase()) ? 1 : 0;
                    poorRatingItemsCount += (itemAnswerLC === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    fairRatingItemsCount += (fairRatingLabel && itemAnswerLC === fairRatingLabel.toLowerCase()) ? 1 : 0;

                    if (itemAnswerLC && (poorRatingLabel.toLowerCase() == itemAnswerLC || itemAnswerLC === fairRatingLabel.toLowerCase()) && item.close_out && Object.keys(item.close_out).length) {
                        let hours = (+item.close_out.close_out_at - +inspection.createdAt) / (60 * 60 * 1000);
                        if ([poorRatingLabel.toLowerCase()].includes(itemAnswerLC)) {
                            let day = hours/24;
                            poorClosedOutDays.push(day);
                        } else {
                            let day = hours/24;
                            fairClosedOutDays.push(day);
                        }

                        totalClosedOutItems += (itemAnswerLC && (itemAnswerLC === poorRatingLabel.toLowerCase() || itemAnswerLC === fairRatingLabel.toLowerCase())) ? 1 : 0;
                    } else {
                        totalFairToCloseOut += (itemAnswerLC && itemAnswerLC === fairRatingLabel.toLowerCase()) ? 1 : 0;
                        totalPoorToCloseOut += (itemAnswerLC === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    }

                    if (itemAnswerLC && (poorRatingLabel.toLowerCase() == itemAnswerLC || itemAnswerLC === fairRatingLabel.toLowerCase())) {
                        if (poorRatingLabel.toLowerCase() == itemAnswerLC) {
                            totalPoorIssues += 1;
                        } else {
                            totalFairIssues += 1;
                        }
                    }

                    if (itemAnswerLC && [goodRatingLabel.toLowerCase(), poorRatingLabel.toLowerCase(), fairRatingLabel.toLowerCase()].includes(itemAnswerLC)) {
                        let itemCategory = (item.heading) ? item.heading : item.question;
                        if (itemAnswerLC && [poorRatingLabel.toLowerCase(), fairRatingLabel.toLowerCase()].includes(itemAnswerLC)) {
                            let dataItem = groupChartDataOfWorseItems.find(dItem => dItem.group === itemCategory) || {};
                            if (Object.keys(dataItem).length) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['total'] += 1;
                                let dataItemIndex = groupChartDataOfWorseItems.findIndex(dItem => dItem.group === itemCategory);
                                groupChartDataOfWorseItems[dataItemIndex] = dataItem;
                            } else {
                                dataItem = {
                                    'group': itemCategory
                                };
                                dataItem[fairRatingLabel] = 0;
                                dataItem[poorRatingLabel] = 0;
                                dataItem['total'] = 1;
                                dataItem[capitalize(item.answer)] += 1;
                                groupChartDataOfWorseItems.push(dataItem);
                            }
                        } else {
                            totalGood += 1;
                        }

                        let projectName = inspection.project_ref.name;
                        dataItem = ratioPerProjectChartData.find(dItem => dItem.group === projectName) || {};
                        if (Object.keys(dataItem).length) {
                            if (itemAnswerLC && [poorRatingLabel.toLowerCase(), fairRatingLabel.toLowerCase()].includes(itemAnswerLC)) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['max_total'] += 1;
                            }
                            dataItem['total'] += 1;
                            let dataItemIndex = ratioPerProjectChartData.findIndex(dItem => dItem.group === projectName);
                            ratioPerProjectChartData[dataItemIndex] = dataItem;
                        } else {
                            dataItem = {
                                'group': projectName
                            };
                            dataItem[fairRatingLabel] = 0;
                            dataItem[poorRatingLabel] = 0;
                            dataItem['total'] = 1;
                            dataItem['max_total'] = 0;
                            if (itemAnswerLC && [poorRatingLabel.toLowerCase(), fairRatingLabel.toLowerCase()].includes(itemAnswerLC)) {
                                dataItem[capitalize(item.answer)] += 1;
                                dataItem['max_total'] = 1;
                            }
                            ratioPerProjectChartData.push(dataItem);
                        }
                    }
                });

                let weeklyUnsItem = weeklyUnsItemsChartData.find(item => (+inspection.createdAt >= item.start_of_week && +inspection.createdAt <= item.end_of_week));
                let weeklyUnsItemIndex = weeklyUnsItemsChartData.findIndex(item => (inspection.createdAt >= item.start_of_week && inspection.createdAt <= item.end_of_week ));
                if (weeklyUnsItem && weeklyUnsItemIndex != -1) {
                    weeklyUnsItem['unsatisfactory_items'] += (totalFairIssues + totalPoorIssues);
                    weeklyUnsItem['total_items'] += (totalGood + totalFairIssues + totalPoorIssues);
                    weeklyUnsItemsChartData[weeklyUnsItemIndex] = weeklyUnsItem;
                }

                //prepare variable poorIssuesPerProject
                existingRecord = poorIssuesPerProject.find(record => record.group === inspection.project_ref.name);
                if (existingRecord) {
                    existingRecordIndex = poorIssuesPerProject.findIndex(record => record.group === inspection.project_ref.name);
                    existingRecord["rating"].push(totalPoorIssues);
                    poorIssuesPerProject[existingRecordIndex] = existingRecord;
                } else {
                    poorIssuesPerProject.push({
                        "group": inspection.project_ref.name,
                        "rating": [totalPoorIssues]
                    })
                }

                //prepare variable fairIssuesPerProject
                existingRecord = fairIssuesPerProject.find(record => record.group === inspection.project_ref.name);
                if (existingRecord) {
                    existingRecordIndex = fairIssuesPerProject.findIndex(record => record.group === inspection.project_ref.name);
                    existingRecord["rating"].push(totalFairIssues);
                    fairIssuesPerProject[existingRecordIndex] = existingRecord;
                } else {
                    fairIssuesPerProject.push({
                        "group": inspection.project_ref.name,
                        "rating": [totalFairIssues]
                    })
                }

                inspectionCreatedDate = moment(inspection.createdAt).format('Do MMM YY');
                datePrefix = (previousInspectionCreatedDate == inspection) ? datePrefix + ' ' : '';

                if (projectGroup.indexOf(inspection.project_ref.name) == -1) {
                    projectGroup.push(inspection.project_ref.name);
                }
                let poorAvgDays = (poorClosedOutDays.length) ? (poorClosedOutDays.reduce(function (a, b) {
                    return a + b;
                }, 0)) / poorClosedOutDays.length : 0;
                existingRecord = avgCloseoutTimePerProjectPoor.find(record => record.group === inspection.project_ref.name);
                if (existingRecord) {
                    existingRecordIndex = avgCloseoutTimePerProjectPoor.findIndex(record => record.group === inspection.project_ref.name);
                    existingRecord["rating"].push(poorAvgDays);
                    avgCloseoutTimePerProjectPoor[existingRecordIndex] = existingRecord;
                } else {
                    avgCloseoutTimePerProjectPoor.push({
                        "group": inspection.project_ref.name,
                        "rating": [poorAvgDays]
                    })
                }

                let fairAvgDays = (fairClosedOutDays.length) ? (fairClosedOutDays.reduce(function (a, b) {
                    return a + b;
                }, 0)) / fairClosedOutDays.length : 0;
                existingRecord = avgCloseoutTimePerProjectFair.find(record => record.group === inspection.project_ref.name);
                if (existingRecord) {
                    existingRecordIndex = avgCloseoutTimePerProjectFair.findIndex(record => record.group === inspection.project_ref.name);
                    existingRecord["rating"].push(fairAvgDays);
                    avgCloseoutTimePerProjectFair[existingRecordIndex] = existingRecord;
                } else {
                    avgCloseoutTimePerProjectFair.push({
                        "group": inspection.project_ref.name,
                        "rating": [fairAvgDays]
                    })
                }

                previousInspectionCreatedDate = inspectionCreatedDate;
            });
            sails.log.info("getPercentageGaugeChart: ratingPointAgainstRating", ratingPointAgainstRating);

            let maximumNumberOfPoints = ratingPointAgainstRating[strToLower(goodRatingLabel)] * (goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
            sails.log.info("getPercentageGaugeChart: ", `goodRatingLabel: ${goodRatingLabel}`);
            weeklyUnsItemsChartData = weeklyUnsItemsChartData.reduce((arr, item) => {
                if (item.total_items) {
                    weeklyUnsItemsChartXAxisValues.push(item['week']);
                    item.week = arr.length + 1;
                    item.percent = 100/(item.total_items)*(item.unsatisfactory_items);
                    arr.push(item);
                    weeklyUnsItemsMaxNumber = (weeklyUnsItemsMaxNumber < item.percent) ? item.percent : weeklyUnsItemsMaxNumber;
                }
                return arr;
            }, []);

            poorIssuesPerProject = poorIssuesPerProject.map(item => {
                item['rating'] = Math.round(item['rating'].reduce((a, b) => a + b, 0)/item['rating'].length);
                maxIssuesPerProject = (maxIssuesPerProject < item['rating']) ? item['rating'] : maxIssuesPerProject;
                return item;
            });

            fairIssuesPerProject = fairIssuesPerProject.map(item => {
                item['rating'] = Math.round(item['rating'].reduce((a, b) => a + b, 0)/item['rating'].length);
                maxIssuesPerProject = (maxIssuesPerProject < item['rating']) ? item['rating'] : maxIssuesPerProject;
                return item;
            });

            avgCloseoutTimePerProjectPoor = (avgCloseoutTimePerProjectPoor || []).reduce((arr, item) => {
                let ratCount = item['rating'].reduce((a, b) => a + b, 0)/item['rating'].length;
                if (ratCount) {
                    item['rating'] = ratCount;
                    maxCloseoutTimePerProject = (maxCloseoutTimePerProject < item['rating']) ? item['rating'] : maxCloseoutTimePerProject;
                    arr.push(item);
                }
                return arr;
            }, []);
            let maxCloseoutTimePerProjectPoor = mathMinMax(avgCloseoutTimePerProjectPoor, 'rating', 'max');

            avgCloseoutTimePerProjectFair = (avgCloseoutTimePerProjectFair || []).reduce((arr, item) => {
                let ratCount = item['rating'].reduce((a, b) => a + b, 0) / item['rating'].length;
                if (ratCount) {
                    item['rating'] = ratCount;
                    maxCloseoutTimePerProject = (maxCloseoutTimePerProject < item['rating']) ? item['rating'] : maxCloseoutTimePerProject;
                    arr.push(item);
                }
                return arr;
            }, []);
            let maxCloseoutTimePerProjectFair = mathMinMax(avgCloseoutTimePerProjectFair, 'rating', 'max');
            maxCloseoutTimePerProject = Math.max(maxCloseoutTimePerProjectPoor, maxCloseoutTimePerProjectFair);

            //get top 10 items by total count
            groupChartDataOfWorseItems = (groupChartDataOfWorseItems.sort((a, b) => (a.total < b.total) ? 1 : ((b.total < a.total) ? -1 : 0))).slice(0, 10);
            let maxRatingInWorse = (groupChartDataOfWorseItems.length) ? groupChartDataOfWorseItems[0].total : 0;


            let openClosedBarChartData = [];
            openClosedBarChartData.push({
                name: `Open (${poorRatingLabel})`,
                value: totalPoorToCloseOut,
                type: `Rating: ${poorRatingLabel}`
            });
            openClosedBarChartData.push({
                name: `Open (${fairRatingLabel})`,
                value: totalFairToCloseOut,
                type: `Rating: ${fairRatingLabel}`
            });
            openClosedBarChartData.push({name: "Closed", value: totalClosedOutItems, type: ''});

            let hasAllClosed = (!totalPoorToCloseOut && !totalFairToCloseOut && totalClosedOutItems) ? `All ${totalClosedOutItems} items closed out` : '';

            let totalRatingCount = +(goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
            let goodRatingPercentage = Math.round(100 / (totalRatingCount) * (goodRatingItemsCount));
            let fairRatingPercentage = Math.round(100 / (totalRatingCount) * (fairRatingItemsCount));
            let poorRatingPercentage = Math.round(100 / (totalRatingCount) * (poorRatingItemsCount));

            let donutChartData = [
                {
                    name: `${goodRatingLabel} (${goodRatingItemsCount})`,
                    count: goodRatingItemsCount,
                    percentage: goodRatingPercentage,
                    color: `${goodRatingColor}`,
                    label: goodRatingLabel
                },
                {
                    name: `${fairRatingLabel} (${fairRatingItemsCount})`,
                    count: fairRatingItemsCount,
                    percentage: fairRatingPercentage,
                    color: `${fairRatingColor}`,
                    label: fairRatingLabel
                },
                {
                    name: `${poorRatingLabel} (${poorRatingItemsCount})`,
                    count: poorRatingItemsCount,
                    percentage: poorRatingPercentage,
                    color: `${poorRatingColor}`,
                    label: poorRatingLabel
                }
            ];

            let openClosedBarChartWidth = '600';
            let donutViewBox = "70 10 280 310";
            let donutChartWidth = 400;
            let donutChartHeight = 280;
            let donutChartCentroidAdj = -28;
            let donutLegendsAdj = -360;
            let donutLegendh = -190;
            let lollipopChartWidth = 705;
            let lollipopChartHeight = 278;
            let ratioPerProjectChartWidth = 472;
            let ratioPerProjectChartHeight = 193;
            let problemAreasChartWidth = 472;
            let problemAreasChartHeight = 193;
            let stackChartLegendAdj = 85;
            let avgCloseoutTimePerProjectWidth = 1120;
            let avgCloseoutTimePerProjectHeight = 400;
            let weeklyUnsItemsChartWidth = 1040;
            let weeklyUnsItemsChartHeight = 300;
            let avgCloseoutLegendAdj = 10;
            let labelFontSize = '.4em';
            let marginTop = '0px';
            let stackHeight = 10;
            let adjustments = {
                adj1: -2,
                adj2: 10,
                adj3: -14,
                adj4: 6,
                adj5: -2,
                adj6: 1,
                adj7: 18,
                adj8: (hasAllClosed) ? 5.2 : 4.7,
                adj9: (hasAllClosed) ? 18 : 17,
                adj10: (hasAllClosed) ? 1 : 0,
                adj11: -6,
                adj12: -3.5,
                adj13: 3,
            };

            if (type === 'pdf') {
                openClosedBarChartWidth = '1000';
                donutChartWidth = 500;
                donutChartHeight = 350;
                donutChartCentroidAdj = -70;
                donutLegendsAdj = -480;
                donutLegendh = -280;
                donutViewBox = "120 50 280 310";
                lollipopChartWidth = 932;
                lollipopChartHeight = 348;
                ratioPerProjectChartWidth = 690;
                ratioPerProjectChartHeight = 280;
                problemAreasChartWidth = 690;
                problemAreasChartHeight = 265;
                stackChartLegendAdj = 110;
                avgCloseoutTimePerProjectWidth = 1400;
                avgCloseoutTimePerProjectHeight = 478;
                weeklyUnsItemsChartWidth = 1400;
                weeklyUnsItemsChartHeight = 350;
                avgCloseoutLegendAdj = 10;
                labelFontSize = '.4em';
                marginTop = '0px';
                stackHeight = 7;
                adjustments = {
                    adj1: -2,
                    adj2: 7,
                    adj3: -10,
                    adj4: 6,
                    adj5: -2.5,
                    adj6: .5,
                    adj7: 18,
                    adj8: 6,
                    adj9: 14,
                    adj10:0,
                    adj11: -6,
                    adj12: -3.5,
                    adj13: 3,
                }
            }

            let ratioPerProjectChartDataMax = 0;
            ratioPerProjectChartData = (ratioPerProjectChartData || []).map(item => {
                ratioPerProjectChartDataMax = (ratioPerProjectChartDataMax < item['max_total']) ? item['max_total'] : ratioPerProjectChartDataMax;
                return item;
            });

            let percentageChartMax = (hasRatingPoint) ? maximumNumberOfPoints : (goodRatingItemsCount + poorRatingItemsCount + fairRatingItemsCount);
            sails.log.info("getPercentageGaugeChart: ", `hasRatingPoint: ${hasRatingPoint}, maximumNumberOfPoints: ${maximumNumberOfPoints}, goodRatingItemsCount: ${goodRatingItemsCount}, poorRatingItemsCount: ${poorRatingItemsCount}, fairRatingItemsCount: ${fairRatingItemsCount}`)
            let totalScore = 0;
            if (hasRatingPoint) {
                totalScore += (ratingPointAgainstRating[strToLower(goodRatingLabel)] * goodRatingItemsCount) + (ratingPointAgainstRating[strToLower(poorRatingLabel)] * poorRatingItemsCount);
                totalScore += (fairRatingLabel && ratingPointAgainstRating[strToLower(fairRatingLabel)]) ? (ratingPointAgainstRating[strToLower(fairRatingLabel)] * fairRatingItemsCount) : 0;
            }
            sails.log.info("getPercentageGaugeChart: ", `totalScore: ${totalScore}, goodRatingItemsCount: ${goodRatingItemsCount}`);

            let percentageChartPart = (hasRatingPoint) ? totalScore : goodRatingItemsCount;
            let legendInfo = (hasThreeRatingSystem) ? [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}] : [{ name: poorRatingLabel, color: `${colorRed}`}];
            let totalRatedItems = goodRatingItemsCount + poorRatingItemsCount + fairRatingItemsCount;
            let openClosedBarChart = await getOpenClosedBarChart(openClosedBarChartData, hasAllClosed, openClosedBarChartWidth, stackHeight, labelFontSize, marginTop, adjustments, [`${colorRed}`, `${colorYellow}`, "#2c961b"], [{ name: fairRatingLabel, color: `${colorYellow}`}, { name: poorRatingLabel, color: `${colorRed}`}]);
            let donutChart = await getDonutChart(donutChartData, donutChartWidth, donutChartHeight, donutChartCentroidAdj, 100, 60, donutViewBox, `${goodRatingLabel} (${goodRatingItemsCount})`, `${fairRatingLabel} (${fairRatingItemsCount})`, `${poorRatingLabel} (${poorRatingItemsCount})`, false, true, 0, donutLegendsAdj, donutLegendh);
            let percentageGaugeChart = await getPercentageGaugeChart(200, 90, percentageChartMax, percentageChartPart, 'Rating');
            let avgCloseoutTimePerProject = await getHoriLollipopChart(projectGroup, avgCloseoutTimePerProjectPoor, avgCloseoutTimePerProjectFair, maxCloseoutTimePerProject, avgCloseoutTimePerProjectWidth, avgCloseoutTimePerProjectHeight,"Days to closeout", '7px', legendInfo, avgCloseoutLegendAdj);
            let issuesPerProjectChart = await getVerLollipopChart(projectGroup, poorIssuesPerProject, fairIssuesPerProject, maxIssuesPerProject, lollipopChartWidth, lollipopChartHeight,"Issues", '7px', legendInfo, 10);
            let ratioPerProjectChart = await getStackedBarChart(ratioPerProjectChartData, ['group', fairRatingLabel, poorRatingLabel], ratioPerProjectChartDataMax, ratioPerProjectChartWidth, ratioPerProjectChartHeight, [`${colorYellow}`, `${colorRed}`],"7px", "Number", true, true, stackChartLegendAdj);
            let weeklyUnsatisfactoryItemsChart = await getScatterPlot(weeklyUnsItemsChartData, 'week', weeklyUnsItemsChartXAxisValues, ['percent'], weeklyUnsItemsMaxNumber, weeklyUnsItemsChartWidth, weeklyUnsItemsChartHeight, '7px', 'Week', '% Unsatisfactory Items', 10, true, [`${colorRed}`], false, "");
            let groupChartDataOfWorseItemsChart = await getStackedBarChart(groupChartDataOfWorseItems, ['group', fairRatingLabel, poorRatingLabel], maxRatingInWorse, problemAreasChartWidth, problemAreasChartHeight, [`${colorYellow}`, `${colorRed}`], '7px', 'Issues', hasThreeRatingSystem, true, stackChartLegendAdj);

            sails.log.info('Number of inspections: ', ibChecklists.length);

            let {project_logo_file, companyName} = await getCompanyInfo({}, {id: companyId});
            projectIds = _uniq(projectIds);

            let form_template = `pages/inspection-builder-company-dashboard-page`;
            let html = await sails.renderView(form_template, {
                title: `Inspection Dashboard`,
                layout: false,
                report_from: (+req.body.from_date) ? moment(+req.body.from_date).format('DD/MM/YYYY') : '',
                report_to: (+req.body.to_date) ? moment(+req.body.to_date).format('DD/MM/YYYY') : '',
                company_name: companyName,
                company_logo: project_logo_file,
                report_type: type,
                totalPages,
                projectsCount: projectIds.length,
                total_inspections: ibChecklists.length,
                totalRatedItems,
                goodRatingItemsCount,
                poorRatingItemsCount,
                fairRatingItemsCount,
                totalClosedOutItems,
                totalFairToCloseOut,
                totalPoorToCloseOut,
                openClosedBarChart,
                donutChart,
                percentageGaugeChart,
                issuesPerProjectChart,
                avgCloseoutTimePerProject,
                ratioPerProjectChart,
                weeklyUnsatisfactoryItemsChart,
                groupChartDataOfWorseItemsChart,
            });

            if (type === 'pdf') {
                let fileName = `${replaceAll(ibInfo.ib_title, ' ', '-')}-Dashboard-${moment().format('MM-DD-YYYY')}`;
                return await instantPdfGenerator(req, res, html, 'inspection-builder-c-dashboard', fileName, req.headers['user-agent'], { format: 'A3', landscape: true }, 'url');
            }

            sails.log.info('Rendering html view');
            return res.send(html);
        }
        sails.log.info('No inspection records found in selected criteria: ', where);
        return res.send("<p style='text-align: center;margin-top: 100px;margin-bottom: 100px;'>No inspection records found in selected criteria.</p>");
    },

    downloadParticipantsList: async (req, res) => {
        let companyId = +req.param('companyId');
        let ibId = +req.param('ibId');

        let where = {
            company_ref: companyId,
            id: ibId
        };

        sails.log.info('Fetch inspections of inspection builder', "Requesting company:", companyId);
        let ibInfo = await sails.models.inspectionbuilder_reader.findOne({
            where: where,
            select: ['ib_title', 'has_subheadings', 'checklist', 'scoring_system']
        });
        sails.log.info('Inspection builder record found with Id: ', ibInfo.id);
        let { hasRatingPoint, ratingPointAgainstRating } = getRatingPointInfo(ibInfo.scoring_system);
        sails.log.info('Has point rating system? : ', hasRatingPoint);

        let inspectionsWhere = {
            ib_ref: ibInfo.id,
            createdAt: {'>=': +req.body.from_date, '<=': +req.body.to_date}
        };
        sails.log.info('Fetch inspections filter.', inspectionsWhere);
        let inspections = await sails.models.inspectionbuilderreport_reader.find({
            where: inspectionsWhere
        });
        sails.log.info(`Found ${inspections.length} inspections with participants.`);

        let participants = [];
        if (inspections.length) {
            let projectIds = []
            let participantsId = (inspections).reduce((arr, item) => {
                arr.push(...item.participants);
                projectIds.push(item.project_ref);
                return _uniq(arr);
            }, []);

            let projects = await sails.models.project.find({
                where: {id: _uniq(projectIds)},
                select: ['id', 'name', 'project_type']
            });

            inspections = inspections.map(inspection => {
                inspection.project_ref = (projects || []).find(project => project.id == inspection.project_ref) || {};
                return inspection;
            });

            participants = await sails.models.user_reader.find({
                where: {id: participantsId},
                select: ['id', 'first_name', 'last_name']
            })

            participants = (participants || []).sort((a,b) => (a.first_name > b.first_name) ? 1 : ((b.first_name > a.first_name) ? -1 : 0));

            participants = participants.map(participant => {
                participant.inspection_date = [];
                participant.inspections_participated = [];
                participant.projects = [];
                participant.inspection_rating = [];
                return participant;
            });

            sails.log.info(`Total participants: ${participants.length}`);
            //rating labels
            let {goodRatingLabel, fairRatingLabel, poorRatingLabel, hasThreeRatingSystem} = prepareLabels(ibInfo.scoring_system);

            let goodRatingItemsCount = 0;
            let poorRatingItemsCount = 0;
            let fairRatingItemsCount = 0;
            (inspections || []).forEach(function (inspection, i) {
                let inspectionChecklist = [...inspection.checklist, ...inspection.additional_checklist];
                sails.log.info("IB has subheadings? ", ibInfo.has_subheadings);

                if (ibInfo.has_subheadings) {
                    inspectionChecklist = (inspectionChecklist || []).reduce((arr, item) => {
                        arr.push(...(item.subheadings || []));
                        return arr;
                    }, []);
                }
                goodRatingItemsCount = 0;
                poorRatingItemsCount = 0;
                fairRatingItemsCount = 0;
                inspectionChecklist.forEach(function (item, j) {
                    let itemAnswerLC = strToLower(item.answer);
                    goodRatingItemsCount += (itemAnswerLC === goodRatingLabel.toLowerCase()) ? 1 : 0;
                    poorRatingItemsCount += (itemAnswerLC === poorRatingLabel.toLowerCase()) ? 1 : 0;
                    fairRatingItemsCount += (fairRatingLabel && itemAnswerLC === fairRatingLabel.toLowerCase()) ? 1 : 0;
                });
                let maximumNumberOfPoints = ratingPointAgainstRating[strToLower(goodRatingLabel)] * (goodRatingItemsCount + fairRatingItemsCount + poorRatingItemsCount);
                let totalItemsCount = (hasRatingPoint) ? maximumNumberOfPoints : (goodRatingItemsCount + poorRatingItemsCount + fairRatingItemsCount);


                let totalScore = 0;
                if (hasRatingPoint) {
                    totalScore += (ratingPointAgainstRating[strToLower(goodRatingLabel)] * goodRatingItemsCount) + (ratingPointAgainstRating[strToLower(poorRatingLabel)] * poorRatingItemsCount);
                    totalScore += (fairRatingLabel && ratingPointAgainstRating[strToLower(fairRatingLabel)]) ? (ratingPointAgainstRating[strToLower(fairRatingLabel)] * fairRatingItemsCount) : 0;
                }
                let goodItemsCount = (hasRatingPoint) ? totalScore : goodRatingItemsCount;

                participants = fillParticipants(inspection, participants, totalItemsCount, goodItemsCount);
            });

            participants = participants.map(participant => {
                participant.first_inspection_date = moment(Math.min(...participant.inspection_date)).format('DD/MM/YYYY');
                participant.last_inspection_date = moment(Math.max(...participant.inspection_date)).format('DD/MM/YYYY');
                participant.total_inspections_participated = participant.inspections_participated.length;
                participant.total_number_of_projects = _uniq(participant.projects).length;

                let maxRating = participant.inspection_rating.reduce((max, rating) => max.percent > rating.percent ? max : rating);
                participant.best_inspection_rating = `${Math.floor(maxRating.percent)}% (${maxRating.project_name})`;

                let minRating = participant.inspection_rating.reduce((min, rating) => min.percent < rating.percent ? min : rating);
                participant.worst_inspection_rating = `${Math.floor(minRating.percent)}% (${minRating.project_name})`;

                let avgRating = ((participant.inspection_rating.reduce((a, b) => a + b.percent, 0)) / participant.inspection_rating.length);
                participant.average_inspection_rating = `${Math.floor(avgRating)}%`;
                return participant;
            });
        }

        let companyInfo = await sails.models.createemployer_reader.findOne({
            where: {id: companyId},
            select: ['name']
        });

        let report_from = (+req.body.from_date) ? moment(+req.body.from_date).format('DD-MM-YYYY') : '';
        let report_to = (+req.body.to_date) ? moment(+req.body.to_date).format('DD-MM-YYYY') : '';

        let workbook = await getInspectionParticipants(participants, companyInfo, report_from, report_to);
        let fileName = `${companyInfo.name} - Inspection Participants [${report_from}-${report_to}].xlsx`;
        return streamExcelDownload(res, workbook, fileName);
    },

    subContractorDashboard: async (req, res) => {
        let companyId = +req.param('companyId');
        let ibId = +req.param('ibId');
        let type = req.param('type');
        let selected_responsible_companies = (req.body.selected_responsible_companies || []);
        let where = {
            company_ref: companyId,
            id: ibId
        };
        let totalPages = 1;

        let ibInfo = await sails.models.inspectionbuilder_reader.findOne({
            where: where,
            select: ['ib_title', 'has_subheadings', 'checklist', 'scoring_system']
        });
        sails.log.info('Inspection builder record found with Id: ', ibInfo.id);

        let inspectionsWhere = {
            ib_ref: ibInfo.id,
            project_ref: (req.body.selected_projects || []),
            createdAt: {'>=': +req.body.from_date, '<=': +req.body.to_date}
        };
        sails.log.info('Fetch inspections filter.', inspectionsWhere, "Requesting company:", companyId);
        let ibChecklists = await sails.models.inspectionbuilderreport.find({
            where: inspectionsWhere
        });
        ibChecklists = await populateProjectRefs(ibChecklists, 'project_ref', []);

        let taggedCompaniesInfo = [];
        if (ibChecklists.length) {
            let companyTimezone = await getTimezone(companyId, 'company');

            let companiesWithCompanyPortal = await sails.models.createemployer.find({
                where: {has_company_portal: true},
                select: ['id', 'name']
            });

            let taggedCompaniesId = []
            let reportRawItems = [];

            (ibChecklists || []).forEach(function (inspection, i) {
                let inspectionChecklist = [...inspection.checklist, ...inspection.additional_checklist];
                if (ibInfo.has_subheadings) {
                    inspectionChecklist = (inspectionChecklist || []).reduce((arr, item) => {
                        item.subheadings = associateHeadingWithItems(item.subheadings, item.heading);
                        arr.push(...(item.subheadings || []));
                        return arr;
                    }, []);
                }

                let { rawItems, companiesId } =  prepareRawItems(inspection, inspectionChecklist, selected_responsible_companies, companiesWithCompanyPortal);
                reportRawItems.push(...rawItems);
                taggedCompaniesId.push(...companiesId);
            });

            sails.log.info('Tagged Company Ids of Items, ', _uniq(taggedCompaniesId));
            taggedCompaniesInfo = await sails.models.createemployer.find({
                where: {id: _uniq(taggedCompaniesId)},
                select: ['name']
            });

            let taggedCompaniesIdNamePair = (taggedCompaniesInfo || []).reduce((obj, item) => {
                obj[item.id] = item.name;
                return obj;
            }, {});

            (reportRawItems).forEach(function (item) {
                //store tagged count of company
                let companyRecord = (taggedCompaniesInfo || []).find(company => company.id == item.tagged_company_ref);
                if (companyRecord) {
                    companyRecord['tagged_count'] = (companyRecord['tagged_count']) ? companyRecord['tagged_count'] + 1 : 1;
                    let companyRecordIndex = (taggedCompaniesInfo || []).findIndex(company => company.id == item.tagged_company_ref);
                    taggedCompaniesInfo[companyRecordIndex] = companyRecord;
                }
            });

            taggedCompaniesInfo = taggedCompaniesInfo.map(company => {
                if(!company.tagged_count) {
                    company.tagged_count = 0;
                }
                return company;
            });

            taggedCompaniesInfo = ((taggedCompaniesInfo || []).sort((a,b) => (a.tagged_count < b.tagged_count) ? 1 : ((b.tagged_count < a.tagged_count) ? -1 : 0))).slice(0, 20);

            let { issuesRaisedChart, positiveItemsChart, numberOfProjectsChart, avgCloseoutTimeChart, biggestProblemAreasChart} = await prepareSubcontractorDashboardCharts(ibInfo, reportRawItems, taggedCompaniesIdNamePair, taggedCompaniesInfo, type);

            sails.log.info('Number of ib-inspections to prepare subcontractor dashboard: ', ibChecklists.length);

            let {project_logo_file, companyName} = await getCompanyInfo({}, {id: companyId});

            let form_template = `pages/ib-inspection-subcontractor-dashboard-page`;
            let html = await sails.renderView(form_template, {
                title: `Subcontractor Dashboard`,
                layout: false,
                report_from: (+req.body.from_date) ? momentTz(+req.body.from_date).tz(companyTimezone).format('DD/MM/YYYY') : '',
                report_to: (+req.body.to_date) ? momentTz(+req.body.to_date).tz(companyTimezone).format('DD/MM/YYYY') : '',
                company_name: companyName,
                company_logo: project_logo_file,
                report_type: type,
                totalPages: 2,

                issuesRaisedChart,
                positiveItemsChart,
                numberOfProjectsChart,
                avgCloseoutTimeChart,
                biggestProblemAreasChart,
            });

            if (type === 'pdf') {
                sails.log.info('Generating pdf');
                let fileName = 'Subcontractors-Dashboard-' + moment().format(dbDateFormat);
                return await instantPdfGenerator(req, res, html, 'subcontractors-dashboard', fileName, req.headers['user-agent'], { format: 'A3', landscape: true }, 'url');
            }

            sails.log.info('Rendering html view');

            return res.send({'html': html, 'tagged_companies': taggedCompaniesInfo});
        }

        sails.log.info('No records found in selected criteria: ', where);
        return res.send({'html': "<p style='text-align: center;margin-top: 100px;margin-bottom: auto;'>No records found in selected criteria.</p>", 'tagged_companies': []});
    },

    taggedOwnerDashboard: async (req, res) => {
        let companyId = +req.param('companyId');
        let taggedOwnerId = +req.param('ownerId');
        let ibId = +req.param('ibId');
        let sortItemsBy = req.body.sort_items_by;
        let totalPages = 1;
        let where = {
            company_ref: companyId,
            id: ibId
        };
        let ib_scoring_system  =  (req.body.ib_scoring_system || {});

        /*if (req.body.from_date && req.body.to_date) {
            where.createdAt = {'>=': +req.body.from_date, '<=': +req.body.to_date};
        }*/

        sails.log.info('Fetch inspection builder filter.', where, "Requesting company:", companyId);

        let ibInfo = await sails.models.inspectionbuilder_reader.findOne({
            where: where,
            select: ['ib_title', 'has_subheadings', 'checklist', 'scoring_system']
        });

        if (!ibInfo || !ibInfo.ib_title) {
            sails.log.info("Failed to fetch inspection detail, please try again.");
            return errorResponse(res, "Failed to fetch inspection detail, please try again.");
        }

        //rating labels
        let {goodRatingLabel, fairRatingLabel, poorRatingLabel, hasThreeRatingSystem} = prepareLabels(ibInfo.scoring_system);
        goodRatingLabel = (ib_scoring_system[goodRatingLabel]) ? goodRatingLabel : undefined;
        fairRatingLabel = (ib_scoring_system[fairRatingLabel]) ? fairRatingLabel : undefined;
        poorRatingLabel = (ib_scoring_system[poorRatingLabel]) ? poorRatingLabel : undefined;

        sails.log.info('Inspection builder record found with Id: ', ibInfo.id);

        let inspectionsWhere = {
            ib_ref: ibInfo.id,
            project_ref: (req.body.selected_projects || []),
            createdAt: {'>=': +req.body.from_date, '<=': +req.body.to_date}
        };
        sails.log.info('Fetch inspections filter.', inspectionsWhere, "Requesting company:", companyId);
        let ibChecklists = await sails.models.inspectionbuilderreport_reader.find({
            where: inspectionsWhere
        });
        ibChecklists = await populateProjectRefs(ibChecklists, 'project_ref', []);
        let html = "<p style='text-align: center;margin-top: 100px;margin-bottom: auto;'>No records found in selected criteria.</p>";
        let taggedOwnerInfo = await sails.models.createemployer_reader.findOne({
            where: {id: taggedOwnerId},
            select: ['name']
        });
        if (ibChecklists.length && Object.keys(ib_scoring_system).length) {
            sails.log.info(`Preparing dashboard for tagged owner ${taggedOwnerInfo.name}`);

            //sort by project name
            ibChecklists = (ibChecklists || []).sort((a, b) => (a.project_ref.name > b.project_ref.name) ? 1 : ((b.project_ref.name > a.project_ref.name) ? -1 : 0));

            let reportRawItems = [];
            (ibChecklists || []).forEach(function (inspection, i) {
                let inspectionChecklist = [...inspection.checklist, ...inspection.additional_checklist];
                if (ibInfo.has_subheadings) {
                    inspectionChecklist = (inspectionChecklist || []).reduce((arr, item) => {
                        item.subheadings = associateHeadingWithItems(item.subheadings, item.heading);
                        arr.push(...(item.subheadings || []));
                        return arr;
                    }, []);
                }

                let {
                    rawItems
                } = prepareRawItemsByTaggedOwner(inspection, inspectionChecklist, taggedOwnerId, goodRatingLabel, fairRatingLabel, poorRatingLabel);

                reportRawItems.push(...rawItems);
            });

            if(reportRawItems.length) {
                let {
                    donutChart,
                    openClosedBarChart,
                    ratingsByGroupChart,
                    no_of_projects,
                    no_of_items_tagged,
                    no_of_positive_items,
                    no_of_issues_raised,
                    itemsInfo
                } = await prepareTaggedOwnerDashboardCharts(reportRawItems, goodRatingLabel, fairRatingLabel, poorRatingLabel);

                //expand item's info
                let userIdsToExpand = [];
                let appendixImageIds = [];
                let closeOutImagesId = [];
                for (let item of itemsInfo) {
                    if (item.inspected_by) {
                        userIdsToExpand.push(item.inspected_by);
                    }

                    if (item.item_assigned_to) {
                        userIdsToExpand.push(item.item_assigned_to);
                    }

                    if (item.item_images && item.item_images.length) {
                        appendixImageIds.push(...item.item_images);
                    }

                    if (item.item_closeout_images && item.item_closeout_images.length) {
                        closeOutImagesId.push(...item.item_closeout_images);
                    }
                }

                let expandedUsers = await sails.models.user_reader.find({
                    where: {id: _uniq(userIdsToExpand)},
                    select: ['id', 'first_name', 'middle_name', 'last_name', 'email', 'timezone']
                });

                let expandedImages = await sails.models.userfile_reader.find({
                    where: {id: _uniq([...appendixImageIds, ...closeOutImagesId])},
                    select: ['id', 'sm_url', 'md_url', 'file_url']
                });

                let closeOutImageRows = 0;
                let appendixImageRows = 0;
                let goodItems = [];
                let fairItems = [];
                let poorItems = [];
                itemsInfo = itemsInfo.map(item => {
                    let inspectedByUser = (expandedUsers).find(user => user.id == item.inspected_by);
                    item.inspected_by = (inspectedByUser) ? `${getUserFullName(inspectedByUser)} (${moment(item.inspected_at).format('DD/MM/YY HH:mm:ss')})` : null;
                    item.item_assigned_to = (item.item_assigned_to) ? getResponsibleUserInfo(expandedUsers, item.item_assigned_to) : null;

                    if (item.item_images && item.item_images.length) {
                        item.item_images = expandedImages.filter(image => {
                            if (item.item_images.includes(image.id)) {
                                return
                            }
                        });
                        appendixImageRows += Math.ceil(item.item_images / 3);
                    }

                    if (item.item_closeout_images && item.item_closeout_images.length) {
                        item.item_closeout_images = expandedImages.filter(image => item.item_closeout_images.includes(image.id));
                        closeOutImageRows += Math.ceil(item.item_closeout_images / 3);
                    }

                    if (item.item_answer == 'good' || item.item_answer == 'yes') {
                        goodItems.push(item);
                    }

                    if (item.item_answer == 'fair') {
                        fairItems.push(item);
                    }

                    if (item.item_answer == 'poor' || item.item_answer == 'no') {
                        poorItems.push(item);
                    }

                    return item;
                });

                //sorting items
                if(sortItemsBy == 'rating') {
                    itemsInfo = [...goodItems, ...fairItems, ...poorItems];
                } else  {
                    itemsInfo.sort((a, b) => parseFloat(b.inspected_at) - parseFloat(a.inspected_at));
                }

                totalPages = 1;
                sails.log.info('Total items in inspection tours Industrial project: ', itemsInfo.length);

                let {project_logo_file, companyName} = await getCompanyInfo({}, {id: companyId});

                let projectsParticipated = _uniq(no_of_projects);
                let form_template = `pages/ib-report-tagged-owner-report`;
                html = await sails.renderView(form_template, {
                    title: `${taggedOwnerInfo.name} Inspection Report`,
                    report_from: moment(+req.body.from_date).format('DD/MM/YYYY'),
                    report_to: moment(+req.body.to_date).format('DD/MM/YYYY'),
                    companyName,
                    reportRawItemsCount: reportRawItems.length,
                    itemsInfo,
                    totalPages,
                    moment,
                    donutChart,
                    openClosedBarChart,
                    ratingsByGroupChart,
                    no_of_projects: projectsParticipated.length,
                    no_of_items_tagged,
                    no_of_positive_items,
                    no_of_issues_raised,
                    project_logo_file,
                    goodRatingLabel: strToLower(goodRatingLabel),
                    fairRatingLabel: strToLower(fairRatingLabel),
                    poorRatingLabel: strToLower(poorRatingLabel),
                    layout: false,
                    unix(n, format) {
                        return moment.unix(n).format(format);
                    },
                    checkObjLength(obj) {
                        return Object.keys(obj).length;
                    },
                    toLowerCase(string) {
                        return strToLower(string);
                    }
                });
            }
        }

        sails.log.info('Generating pdf');
        let fileName = `${taggedOwnerInfo.name} Inspection Report`;
        return await instantPdfGenerator(req, res, html, 'tagged-owner-dashboard', fileName, req.headers['user-agent'], { format: 'A4' });
    },

    shareInspectionBuilderReport: async(req, res) => {
        let id = +req.param('id');
        let email = req.body.email;
        let projectId = req.body.projectId;
        let byUser = req.user;
        sails.log.info('Share Inspection Bulder Report request by user', byUser.id, ' for project ', projectId);
        let { inspectionReport: [ibReport] } = await getInspectionReport({id:id}, 'DESC', true);
        if(ibReport && ibReport.id) {
            let checklistTitle = (ibReport.ib_ref.ib_title) ? ibReport.ib_ref.ib_title : `Checklist Inspection`;
            let { project_logo_file, companyName } = await getCompanyInfo(ibReport.project_ref, null);

            let html = await getInspectionBuilderReportHtml(ibReport, project_logo_file, companyName, 'pdf');
            let attachmentName = `${checklistTitle}-#${ibReport.project_ref.id}-${ibReport.record_id}-${moment().format(dbDateFormat)}-${ibReport.project_ref.name}`;

            ResponseService.successResponse(res, {message: `The ${checklistTitle} report is being prepared and will be shared shortly.`});
            await shareReportViaEmail(req, res, html, 'inspection-builder', attachmentName, checklistTitle, byUser, email, ibReport.project_ref.name);
            return;
        }
    },

    deleteInspectionReport: async(req, res) => {
        let reportId = +req.param('reportId');
        sails.log.info('Delete inspection report ', reportId);

        let deletedOneRecords = await sails.models.inspectionbuilderreport.destroyOne({
            id: reportId,
            finalised: false,
        });

        return ResponseService.successResponse(res, {
            deleted: deletedOneRecords
        });
    },

    deleteInspectionReports: async(req, res) => {
        const projectId = +req.param('projectId');
        const userId = +req.user.id;

        sails.log.info(`Validating IB reports, project: ${projectId}, user: ${userId}`);

        let deleteRequest = _.pick((req.body || {}), [
            'reports',
            'ib_ref'
        ]);

        let {validationError} = deleteIbReports(req);
        if (validationError) {
            return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
        }

        let filter = {
            project_ref: projectId,
            finalised: false,
            user_ref: userId
        }

        // Delete ib reports from report id
        if (deleteRequest.reports) {
            let reportIds = _uniq(deleteRequest.reports);

            filter = {
                ...filter,
                id: reportIds,
            }
            sails.log.info('Fetch IB reports filter.', filter);
            let totalInspectionReports = await sails.models.inspectionbuilderreport_reader.count({
                where: filter
            });

            if (totalInspectionReports != reportIds.length) {
                return ResponseService.errorResponse(res, 'Invalid reports, Either anyone report is submitted or not owned by you');
            }
        }

        // Delete all ib reports of ib builder for perticular user on specified project
        if (deleteRequest.ib_ref) {
            filter = {
                ...filter,
                ib_ref: deleteRequest.ib_ref,
            }
        }

        sails.log.info(`Deleting ib reports for project: ${projectId}, user: ${userId}, filters: ${JSON.stringify(filter)}`);

        let deletedOneRecords = await sails.models.inspectionbuilderreport.destroy(filter);

        sails.log.info(`IB reports deleted reports for user ${userId}, project: ${projectId}`);

        return ResponseService.successResponse(res, {
            deleted: deletedOneRecords
        });
    },
}
