/**
 * Created by spatel on 7/6/19.
 */

const dbDateFormat = 'YYYY-MM-DD';
const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const {
    inductionFn,
} = require('./../sql.fn');
const {
    TokenUtil: {
        getProjectLogo,
        getCompanyInfo
    },
    ExcelService: {
        streamExcelDownload,
        exportDailyDeclarationOfUser,
        exportInductionInviteListReport,
        exportTotalTimeReportOfProject,
        companyProjectTimeSheetReport,
    },
    TimeLogService: {
        totalTimeRecordsOfProjectForAllSources,
        // VALID_SOURCES,
        // expandDailyLogInfo,
    },
    SharedService: {
        instantPdfGenerator
    },
    FeatureExclusionUtil :{
        isProfileEmploymentEmpNbrMandatory
    }
} = require('./../services');
const ResponseService = require('./../services/ResponseService');
const {
    buildRatio,
    expandUserDocs,
    showDurationAsHours,
    getVisitorsTimeLogForDates,
    eventOfToday,
    haveFireMarshal,
    getDailyTimeEventForDay,
    getDailyTimeEventV2,
    isMentalHealthFirstAider,
    isStillOnSite,
    getUserFullName,
    isFirstAider,
    haveSMSTSOrSSSTSDoc,
    sendWeeklyTimesheetProcess,
    populateUserRefs,
    populateProjectRefs,
} = require('./../services/DataProcessingService');
const {updateOrCreateWeatherLog, DEFAULT_COUNTY_CODE_GB, isNonUKProject} = require('./../services/WeatherSyncService');
const HttpService = require('./../services/HttpService');
const {sendRawEmail} = require('./../services/EmailService');
const moment = require('moment');
const momentTz = require('moment-timezone');
const get = require('lodash/get');
const _pick = require('lodash/pick');
const _uniq = require('lodash/uniq');
const underscore = require('underscore');
const _sortBy = require('lodash/sortBy');
const _groupBy = _.groupBy;

const getWeatherLogs = async (location_key, postcode, day, country_code, nextDays = 4, dateFormat= `YYYY-MM-DD`) => {
    if(!location_key || !location_key.length){
        sails.log.info(`Project Location Key not attached postcode: ${postcode}`);
        return [];
    }
    let date = moment(day, dateFormat).startOf('day');
    let d = [date.format('YYYY-MM-DD')];
    //let lastDay = date;
    for(let i = 1; i <= nextDays; i++){
        d.push(date.clone().add(i, 'days').format('YYYY-MM-DD'));
        //lastDay = date.clone().add(i, 'days');
    }

    sails.log.info('get weather logs for ', d);
    try {
        let logs = await sails.models.weatherlog.find({
            location_key,
            day: {'in': d}
        }).sort('day ASC');
        let missingDays = [];
        if(logs && logs.length){
            // REDUNTENt dATe
            const alreadyFilledDates= [];
            for(let logIndx=0;logIndx<logs.length;logIndx++) {
                if(alreadyFilledDates.includes(logs[logIndx].day)) {
                    logs.splice(logIndx, 1)
                    logIndx-=1;
                } else {
                    alreadyFilledDates.push(logs[logIndx].day)
                }
            }
            for(let i = 0, len = d.length; i < len; i++){
                if(logs.findIndex(l => l.day === d[i]) === -1){
                    missingDays.push(d[i]);
                }
            }

        }else{
            missingDays = d;
            logs = [];
        }

        if(missingDays && missingDays.length && missingDays.length >= 1){
            sails.log.info('get future weather via API', missingDays);
            let accuweather_key = (sails.config.custom.ACCU_WEATHER_API_KEY || '');
            let response = await HttpService.makeGET(`http://dataservice.accuweather.com/forecasts/v1/daily/5day/${location_key}`, {
                apikey: accuweather_key,
                metric: true, // for celcius in response
                details: true
            }, {}, true, 20000);
            if(response.success && response.data.DailyForecasts && response.data.DailyForecasts.length) {
                sails.log.info(`found future-forecast for ${location_key}, days`, response.data.DailyForecasts.map(d => d.Date));
                //logs = logs.concat(...response.data.DailyForecasts);

                let promises = response.data.DailyForecasts.map(dailyForecast => {
                    let forecastDate = moment(dailyForecast.Date, isNonUKProject(country_code) ? 'YYYY-MM-DDTHH:mm:ss' : 'YYYY-MM-DDTHH:mm:ssZ').format('YYYY-MM-DD');
                    return updateOrCreateWeatherLog(location_key, forecastDate, dailyForecast);
                });

                let moreLogs = await Promise.all(promises);
                moreLogs.map(weather_log => {
                     if(missingDays.indexOf(weather_log.day)>=0){
                        logs.push(weather_log);
                    }
                })
                //logs = logs.concat(...moreLogs);
            }else{
                sails.log.info(`failed to get future-forecast of ${location_key}`);
            }
        }
        return logs;
    }catch (fetchError) {
        sails.log.error('Failed to fetch weather log', fetchError);
        return [];
    }

    return d;
};

const getLogByWeekDay = (logs, day, secOnly = true) => {
    let log = logs.find(l => moment(l.day_of_yr, dbDateFormat).format('ddd') === day) || {};
    if(!log.effective_time && log.adjustment){
        return secOnly ? (+log.adjustment * 60) : log;
    }
    return secOnly ? (+log.effective_time || 0) : log;
};

const projectDashboard = async (req, res) => {
    let project_id = req.param('projectId');
    let nowMs = +req.param('nowMs');
    if (isNaN(nowMs)) {
        return ResponseService.errorResponse(res, 'nowMs is required');
    }
    const client_time_now = moment(nowMs);
    let for_date = null;
    let request_date = moment((req.query.for_date || '--'), dbDateFormat);
    if (request_date.isValid()) {
        for_date = request_date;//.format(dbDateFormat);
    }
    sails.log.info(`processing dashboard request of ${project_id}, for_date: ${for_date}, nowMs: ${nowMs}`);
    try{
        let project = await sails.models.project.findOne({
            select: ['id', 'name', 'postcode', 'start_date', 'end_date', 'project_type', 'custom_field',
                'weather_location_key', 'contractor', 'project_footer', 'close_call_phrase', 'project_number'],  // @deprecated `close_call_phrase` by Satyam Hardia since 11th April 2022
            where: {
                id: project_id,
                is_active: 1,
                disabled_on: null
            }
        });// .populate('logo_file_id');

        if(!project || !project.id){
            return ResponseService.errorResponse(res, 'Given project is not found / live.');
        }

        let reviewed_inductions = await inductionFn.getInductionsForDashboard(project_id);

        let unique_users = _uniq(reviewed_inductions.map(ir => ir.user_ref));
        let operative_users = [...unique_users];
        if(for_date){
            let time_log_for_day = await getDailyTimeEventForDay(project_id, for_date, unique_users);

            operative_users = _uniq(time_log_for_day.filter(isStillOnSite).map((log) => log.user_id));

            sails.log.info(`Present users today are: ${operative_users}`);
            reviewed_inductions = reviewed_inductions.filter(ir => operative_users.includes(ir.user_ref));

            let from_date = for_date.clone().subtract(1, 'day').format(dbDateFormat);
            let to_date = for_date.clone().add(1, 'day').format(dbDateFormat);
            let target_day = for_date.clone().format(dbDateFormat);
            let two_days_visitor_logs = await getVisitorsTimeLogForDates(project_id, from_date, to_date);
            let visitor_groups = _groupBy(two_days_visitor_logs, l => l.visitor_id);
            let operative_visitors = Object.keys(visitor_groups).map(visitor_id => {
                return eventOfToday(target_day, visitor_groups[visitor_id], client_time_now);
            }).filter(l => l && l.visitor_id).filter(isStillOnSite);

            sails.log.info(`Total visitors of project:${project_id} On-site:`, operative_visitors.length);
            project.operative_visitors = operative_visitors.length;
        }else{
            sails.log.info('Querying Close call reports data');
            let rawResult = await sails.sendNativeQuery(`SELECT hazard_category, count(*) as calls
                    FROM close_call
                    WHERE project_ref = $1
                    GROUP BY hazard_category
                    ORDER BY calls DESC LIMIT 5`, [project_id]);
            let close_call_logs = [];
            if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
                close_call_logs = rawResult.rows;
            }
            project.close_calls = close_call_logs;
        }

        // removing expired documents.
        reviewed_inductions = await expandUserDocs(reviewed_inductions);

        project.total_inductions = (reviewed_inductions || []).length;
        project.total_operatives = operative_users;
        project.total_sub_contractors = 0;
        project.total_close_call = {open: '-', total: '-'};
        project.gender_count = {};
        project.workers_per_company = {};
        project.first_aider_count = 0;
        project.smsts_count = 0;
        project.job_role_site_counts = {
            site: 0,
            office: 0
        };

        let firstAiderSet = {};
        let employerMap = {};
        let genderMap = {};
        let smstsSet = {};
        let fireMarshallSet = {};
        let mentalHealthFirstAiderSet = {};
        let smstsOrssstsSetArr = [];
        let firstAiderSetArr = [];
        (reviewed_inductions || []).map(ir => {
            let userId = ir.user_ref;

            let gender = String(ir.gender || '-').toLowerCase().trim();
            if(!genderMap[gender]){
                genderMap[gender] = {};
            }
            genderMap[gender][userId] = true;

            let employer = String(ir.employer || '-').trim();
            if(!employerMap[employer]){
                employerMap[employer] = {};
            }
            employerMap[employer][userId] = true;

            let is_first_adider = isFirstAider(ir.user_doc_ids);
            if(is_first_adider && !firstAiderSet[userId]){
                firstAiderSet[userId] = 1;
            }

            let hasSmsts = (ir.user_doc_ids || []).findIndex(document => document && (document.name || '').toString().toLowerCase().trim() === `smsts`) !== -1;
            if(hasSmsts && !smstsSet[userId]){
                smstsSet[userId] = 1;
            }

            let hasFireMarshall = !!(haveFireMarshal(ir.user_doc_ids) || {}).id;
            if(hasFireMarshall && !fireMarshallSet[userId]){
                fireMarshallSet[userId] = 1;
            }

            let hasMentalHealthFirstAider = !!(isMentalHealthFirstAider(ir.user_doc_ids) || {}).id;
            if(hasMentalHealthFirstAider && !mentalHealthFirstAiderSet[userId]){
                mentalHealthFirstAiderSet[userId] = 1;
            }

            if(is_first_adider) {
                firstAiderSetArr.push({
                    user_ref: userId,
                    name: ir.creator_name,
                    file_url: ir.pic_url,
                    mobile_no: ir.mobile_no,
                    mobile_number: ir.mobile_number,
                    doc_name: is_first_adider.name || ''
                });
            }

            let has_SMSTS_or_SSSTS = haveSMSTSOrSSSTSDoc(ir.user_doc_ids);
            if(has_SMSTS_or_SSSTS) {
                smstsOrssstsSetArr.push({
                    user_ref: userId,
                    name: ir.creator_name,
                    file_url: ir.pic_url,
                    mobile_no: ir.mobile_no,
                    mobile_number: ir.mobile_number,
                    doc_name: has_SMSTS_or_SSSTS.name || ''
                });
            }
        });

        if(employerMap && employerMap[(project.contractor || '').trim()]) {
            let principal_contractor = _.head(Object.keys(employerMap[(project.contractor || '').trim()]));

            if(smstsOrssstsSetArr.length > 1) {
                let smsts_or_sssts_user = _.findIndex(smstsOrssstsSetArr, (user) => parseInt(user.user_ref) == parseInt(principal_contractor));
                if(smsts_or_sssts_user != -1) {
                    let p_c_as_smsts_or_sssts = smstsOrssstsSetArr.filter(user => parseInt(user.user_ref) == parseInt(principal_contractor));
                    smstsOrssstsSetArr.splice(smsts_or_sssts_user, 1);
                    smstsOrssstsSetArr = [...p_c_as_smsts_or_sssts, ...smstsOrssstsSetArr];
                }
            }

            if(firstAiderSetArr.length > 1) {
                let first_aider_user = _.findIndex(firstAiderSetArr, (user) => parseInt(user.user_ref) == parseInt(principal_contractor));
                if(first_aider_user != -1) {
                    let p_c_as_first_aider = firstAiderSetArr.filter(user => parseInt(user.user_ref) == parseInt(principal_contractor));
                    firstAiderSetArr.splice(first_aider_user, 1);
                    firstAiderSetArr = [...p_c_as_first_aider, ...firstAiderSetArr];
                }
            }
        }

        Object.keys(employerMap).map(employer => {
            project.workers_per_company[employer] = Object.keys(employerMap[employer]).length || 0;
        });

        Object.keys(genderMap).map(gender => {
            project.gender_count[gender] = Object.keys(genderMap[gender]).length || 0;
        });

        project.first_aider_count = Object.keys(firstAiderSet).length || 0;

        project.smsts_count = Object.keys(smstsSet).length || 0;
        project.smstsOrssstsSetArr = _.uniq(smstsOrssstsSetArr, 'user_ref');
        project.firstAiderSetArr = _.uniq(firstAiderSetArr, 'user_ref');
        project.fire_marshall_count = Object.keys(fireMarshallSet).length || 0;
        project.mental_health_first_aider_count = Object.keys(mentalHealthFirstAiderSet).length || 0;
        project.first_aid_ratio = buildRatio(project.total_inductions, project.first_aider_count);
        project.smsts_ratio = buildRatio(project.total_inductions, project.smsts_count);
        project.job_role_site_ratio = buildRatio(project.job_role_site_counts.site, project.job_role_site_counts.office);

        // (total number of different employers - 1) , the 1 is because you subtract the main contractor
        let employers = Object.keys(project.workers_per_company).length;
        project.total_sub_contractors = employers ? (employers) : 0;

        // query 5 days weather
        let daily_accu_weather_day_start_at = moment().minute(2).hour(6); // hour 0-23 and minute 0-59
        let use_previous_day = client_time_now.isBefore(daily_accu_weather_day_start_at);
        let target_day = use_previous_day ? client_time_now.subtract(1, 'days').format(dbDateFormat) : client_time_now.format(dbDateFormat);
        sails.log.info(`Weather Logs Target Day: ${target_day}, used previous day? ${use_previous_day}`);
        let country_code = (project.custom_field && project.custom_field.country_code) || DEFAULT_COUNTY_CODE_GB;
        project.weather_logs = await getWeatherLogs(project.weather_location_key, project.postcode, target_day, country_code);

        let close_call_filter = {
            project_ref:project.id
        };

        sails.log.info('Querying Total Close call data, filter is', close_call_filter);
        let close_call = await sails.models.closecall_reader.find({
            where: close_call_filter,
            select: ['id', 'status']
        });
        project.total_close_call.open = (close_call.filter(cc => cc.status === 1)).length;
        project.total_close_call.total = close_call && close_call.length;
        // project.has_optima_setting = (project_optima_setting && project_optima_setting.id) ? 1 : 0; // not being used anymore
        return ResponseService.successResponse(res, {
            project,
        });
    }catch (fetchError) {
        sails.log.error('Failed to fetch project dash report', fetchError);
        return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
    }

};

const projectInfoData = async (projectId, includeVisitors = false) => {
    sails.log.info(`Fetching project info for ${projectId}`);
    let project = await sails.models.project.findOne({
        select: [
            'name',
            'contractor',
            'project_number',
            'postcode',
            'start_date',
            'end_date',
            'custom_field',
            'project_category'
        ],
        where: {
            id: projectId,
            project_category: 'default', // Only site-admin project
        }
    });
    if(!project){
        sails.log.info('project info not found, id:', projectId);
        return {};
    }
    if (project.contractor) {
        let country_code = project.custom_field && project.custom_field.country_code;
        project.project_logo_ref = await getProjectLogo('contractor', project.contractor, country_code);
    }

    let for_date = moment();
    let time_log_for_day = await getDailyTimeEventForDay(projectId, for_date, []);
    let operative_users = time_log_for_day.filter(isStillOnSite);
    let userIds = (operative_users || []).map(ou => ou.user_id);
    sails.log.info('Total User on-site :', userIds.length);
    project.total_operatives = userIds.length;

    if(includeVisitors){
        let target_day = for_date.clone().format(dbDateFormat);
        let from_date = for_date.clone().subtract(1, 'day').format(dbDateFormat);
        let to_date = for_date.clone().add(1, 'day').format(dbDateFormat);
        let two_days_visitor_logs = await getVisitorsTimeLogForDates(projectId, from_date, to_date);
        let visitor_groups = _groupBy(two_days_visitor_logs, l => l.visitor_id);

        let visitors_on_site = Object.keys(visitor_groups).map(visitor_id => {
            return eventOfToday(target_day, visitor_groups[visitor_id], for_date);
        }).filter(l => l && l.visitor_id).filter(isStillOnSite);

        sails.log.info(`Total visitors of project:${projectId} On-site:`, visitors_on_site.length);
        project.total_operatives = (project.total_operatives + visitors_on_site.length);
    }

    project.first_aiders = [];
    project.supervisors = [];
    project.fire_marshals = [];
    project.mental_health_a_first_aiders = [];

    if(project.total_operatives){
        let inductions = await sails.models.inductionrequest_reader.find({
            select: ['id', 'record_id', 'status_code', 'additional_data', 'user_doc_ids'],
            where: {
                project_ref: projectId,
                user_ref: userIds
            }
        });
        let expanded_inductions = await expandUserDocs(inductions);
        sails.log.info(`Processing inductions and operatives data for project: ${projectId}`);

        let {first_aiders, supervisors, fire_marshals, mental_health_a_first_aiders} = (expanded_inductions || []).reduce((result, induction) => {
            let {additional_data: {user_info, contact_detail}} = induction;

            let user_meta = {
                user_ref: (user_info ? user_info.id : null),
                first_name: (user_info ? user_info.first_name : null),
                middle_name: (user_info ? user_info.middle_name : null),
                last_name: (user_info ? user_info.last_name : null),
                name: (user_info ? user_info.name : null),
                pic: (user_info ? user_info.profile_pic_ref : {}),
                mobile_number: (contact_detail ? contact_detail.mobile_number : null),
                phone_number: (contact_detail ? contact_detail.mobile_no : null),       //  @deprecated: we would switch to `mobile_number` (object based field)
            };

            let docs = (induction.user_doc_ids || []);
            let hasFirstAid = isFirstAider(docs);
            let hasFireMarshal = haveFireMarshal(docs);
            let hasMentalHealthFirstAider = isMentalHealthFirstAider(docs);
            let has_SMSTS_or_SSSTS = haveSMSTSOrSSSTSDoc(docs);
            if(hasFirstAid){
                result.first_aiders.push(user_meta);
            }
            if(hasFireMarshal){
                result.fire_marshals.push(user_meta);
            }
            if(hasMentalHealthFirstAider){
                result.mental_health_a_first_aiders.push(user_meta);
            }
            if(has_SMSTS_or_SSSTS){
                result.supervisors.push(user_meta);
            }
            return result;
        }, {
            first_aiders: [],
            supervisors: [],
            fire_marshals: [],
            mental_health_a_first_aiders: [],
        });
        project.first_aiders = underscore.uniq(first_aiders, 'user_ref');
        project.supervisors = underscore.uniq(supervisors, 'user_ref');
        project.fire_marshals = underscore.uniq(fire_marshals, 'user_ref');
        project.mental_health_a_first_aiders = underscore.uniq(mental_health_a_first_aiders, 'user_ref');
    }
    return project;
};

const getCompanyProjectTimeSheetByWeekFn = async (req, res) => {
    let projects = (req.body.projects || '');
    let user_ids = (req.body.users || []);
    let companyId = (req.body.companyId || null);
    let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
    let format = req.body.format || '';
    let from_date = moment((req.body.from_date || '--'), dbDateFormat);
    let to_date = moment((req.body.to_date || '--'), dbDateFormat);
    let signatureImage = req.body.signatureImage;

    if(!HttpService.typeOf(user_ids, 'array')){
        user_ids = [];
    }
    if (!from_date.isValid() || !to_date.isValid() ||  !companyId || !projects.length || projects.split(',').filter(id => isNaN(+id)).length > 0) {
        sails.log.info('Invalid Request');
        return ResponseService.errorResponse(res, 'Invalid date filter provided.',{from_date, to_date, projects});
    }
    if(to_date.isSameOrBefore(from_date) || to_date.diff(from_date, 'days') > 6){
        sails.log.info('Incorrect {from,to} date in request', {from_date, to_date, projects});
        return res.status(400).header('message', 'Invalid date range provided').send({
            error: true,
            message: 'Invalid date range provided',
            ...{from_date: from_date.format(), to_date: to_date.format(), projects},
        });
    }

    try {
        let projectIds = projects.split(',').map(i => +i);
        let projectId = projectIds.shift();
        let visible_to_date = to_date.clone().format("DD-MM-YYYY");
        from_date = from_date.format(dbDateFormat);
        to_date = to_date.add(1, 'days').format(dbDateFormat);

        sails.log.info(`processing company project time-sheet request, from, to: ${from_date}  <= date < ${to_date} companyId: ${companyId} users: ${user_ids} is_inherited_project: ${is_inherited_project}`);

        let projectInfo = await sails.models.project.findOne({
            select: ['name', 'parent_company', 'client', 'main_contact_name', 'main_contact_number', 'custom_field'],
            where: {
                id: projectId
            }
        });//.populate('parent_company');

        let { project_logo_file } = await getCompanyInfo(projectInfo, (companyId ? {id: companyId} : null));

        let time_logs = await getDailyTimeEventV2(projectId, from_date, to_date, user_ids, false, (is_inherited_project ? null : companyId));
        let today = moment().format(dbDateFormat);
        if(moment(today).isBefore(to_date)  && time_logs.length == 0) {
            sails.log.error('Failed to build time-sheet report', {});
            return res.status(400).header('message', 'Invalid date range provided').send({
                error: true,
                message: 'Invalid date range provided or bad request',
                ...{from_date: from_date, to_date: to_date, projects},
            });
        }

        let unique_users = _.uniq(time_logs.map(l => +l.user_id));
        sails.log.info(`unique_users: ${unique_users}`);

        let user_employees_details = await sails.models.userempdetail.find({
            //where: {employer: employerInfo.name},
            where: {user_ref: unique_users},
            select: ['id', 'employer', 'job_role', 'operative_type', 'user_ref', 'employment_company', 'type_of_employment'],
            // sort: ['user_ref ASC'],
        });
        user_employees_details = await populateUserRefs(user_employees_details, 'user_ref', []);

        let user_time_group_log = _groupBy(time_logs, (l) => l.user_id);
        (user_employees_details || []).forEach(u => {
            let id = u.user_ref && u.user_ref.id || '';
            u.time_logs = user_time_group_log[id] || [];
            let total = 0;
            moment.weekdaysShort().forEach(day => {
                u[day] = getLogByWeekDay(u.time_logs, day);
                total += u[day];
            });
            u.user_id = id;
            u['Total'] = total;
            u['total_string'] = showDurationAsHours(total, false, 'mins');
        });
        user_employees_details = user_employees_details.sort((a, b) => a.user_id - b.user_id);

        let userEmploymentDetail = await sails.models.userempdetail_reader.findOne({
            where: {user_ref: req.user.id},
            select: ['job_role']
        });

        if(format === 'json') {
            return res.send(user_employees_details);
        }

        // Sharing same set of functions b/w excel & pdf Utilities
        const fn = {
            duration: moment.duration,
            moment,
            getUserFullName,
            unix: (epoch_sec) => {
                return req.user.timezone ? momentTz(+epoch_sec, 'X').tz(req.user.timezone) : moment.unix(+epoch_sec);
            },
            showEmploymentCompany: (user_emp_detail) => {
                return (user_emp_detail && user_emp_detail.type_of_employment && user_emp_detail.type_of_employment.toString().toLowerCase() !== 'direct' && user_emp_detail.employment_company) ? user_emp_detail.employment_company : user_emp_detail.employer;
            },
        };
        if(format === 'xls'){
            let workbook = await companyProjectTimeSheetReport({
                visible_to_date,
                project: projectInfo,
                company_logo: project_logo_file,
                records: user_employees_details,
                comment: req.body.comment,
                orderNumber: req.body.orderNumber,
                departmentNumber: req.body.departmentNumber,
                signature: signatureImage,

                user: req.user || {},
                user_employment: userEmploymentDetail,
            }, fn);
            return streamExcelDownload(res, workbook);
        }
        else {
            // Group Records in Chunk of 21 size, so that 21 records per page.
            let user_log_groups = [];
            while (user_employees_details.length) {
                if(user_employees_details.length > 17 && user_employees_details.length <= 21){
                    user_log_groups.push(user_employees_details.splice(0, 17));
                }else{
                    user_log_groups.push(user_employees_details.splice(0, 21));
                }
            }
            let html = await sails.renderView(`pages/company-timesheet/company-timesheet-page`, {
                title: `Company Project Timesheet`,
                user: req.user || {},
                // from_date,
                signatureImage,
                // to_date,
                visible_to_date,
                project_logo_file,
                comment: req.body.comment || '',
                orderNumber: req.body.orderNumber || '',
                departmentNumber: req.body.departmentNumber || '',
                get,
                user_log_groups,
                ...fn,
                projectInfo,
                userEmploymentDetail,
                "layout": false
            });
            // return res.send(html);
            let fileName = `${projectInfo.name} - Timesheet - (${moment((req.body.from_date || '--'), 'DD-MM-YYYY')} to ${moment((req.body.to_date || '--'), 'DD-MM-YYYY')})`;
            sails.log.info(`Generating PDF with name ${fileName}`);
            return await instantPdfGenerator(req, res, html, 'project-rams', fileName, req.headers['user-agent'], {format: 'A4', landscape: true}, 'url');
        }

    }catch (reportGenerationError) {
        sails.log.error('Failed to build time-sheet report', reportGenerationError);
        return ResponseService.errorResponse(res, sails.__('internal server error'), reportGenerationError);
    }
};

module.exports = {

    getProjectUsersPostcode: async (req, res) => {
        let status_code = +(req.query.status_code);
        let project_id = req.param('projectId');
        let for_date = null;
        let request_date = moment((req.query.for_date || '--'), dbDateFormat);
        if (request_date.isValid()) {
            for_date = request_date;
        }
        if(isNaN(status_code)){
            status_code = [2, 6];
        }else if(!HttpService.typeOf(status_code, 'array')){
            status_code = [status_code];
        }
        sails.log.info(`fetch user's postcode for project ${project_id} status_code: ${status_code}`);
        if (!project_id || isNaN(+project_id)) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Project id is required.');
        }

        try {
            let project = await sails.models.project.findOne({
                select: ['postcode', 'name', 'description', 'is_active', 'disabled_on'],
                where: {
                    id: project_id,
                    is_active: 1,
                    disabled_on: null
                }
            });
            if(!project || !project.id){
                sails.log.info('Project not found / published');
                return ResponseService.errorResponse(res, 'Given project is not found / live.');
            }
            let startingNoOfEscaped = 1;
            let rawResult = await sails.sendNativeQuery(
                `SELECT
                    id, project_ref as project_id, record_id,
                    optima_badge_number::numeric,
                    additional_data->'contact_detail'->>'post_code'::text as post_code,
                    user_ref as user_id
                --	additional_data->'user_info'->>'first_name' as first_name
                FROM induction_request
                WHERE
                    project_ref = $1 AND status_code IN (${status_code.map(() => {
                    startingNoOfEscaped++;
                    return `$${startingNoOfEscaped}`;
                }).join(',')});`,
                [project_id, ...status_code]
            );
            let project_users = [];
            if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
                project_users = rawResult.rows;

                if(for_date){
                    let time_log_for_day = await getDailyTimeEventForDay(project_id, for_date, project_users.map(ir => ir.user_id));

                    /*let todays_badge_logs = await getDailyTimeEventV2(
                        project_id,
                        for_date.format(dbDateFormat),
                        for_date.add(1, 'day').format(dbDateFormat),
                        project_users.map(ir => ir.user_id)
                    );*/

                    let total_operatives = [];
                    time_log_for_day.filter(isStillOnSite).map((log) => {
                        if(!total_operatives.includes(log.user_id)){
                            total_operatives.push(+log.user_id);
                        }
                    });

                    sails.log.info('Present badges are', total_operatives);
                    project_users = project_users.filter(ir => total_operatives.includes(ir.user_id));
                }
            }
            ResponseService.successResponse(res, {
                project_users,
                project
            });
        } catch (queryError) {
            sails.log.error('Failed to fetch site users postcode report', queryError);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, queryError);
        }
    },

    // @deprecated: not being used.
    getProjectPostcodes: async (req, res) => {
        let is_active = +(req.query.is_active || 1);
        //let project_id = req.param('projectId');
        sails.log.info(`fetch project's postcode for is_active: ${is_active}`);
        if (isNaN(is_active)) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'is_active should be numeric.');
        }

        try{
            let projects = await sails.models.project.find({
                select: ['postcode', 'name', 'description', 'is_active', 'disabled_on'],
                where: {
                    project_category: 'default', // Only standard project will be searchable
                    is_active,
                    disabled_on: null
                }
            });
            ResponseService.successResponse(res, {
                projects,
            });
        }catch (fetchError) {
            sails.log.error('Failed to fetch project postcode report', fetchError);
            return ResponseService.errorResponse(res, INTERNAL_SERVER_ERROR, fetchError);
        }
    },

    getProjectDashboard: projectDashboard,

    getUserTimeSheet: async (req, res) => {
        let projects = (HttpService.decodeURIParam(req.query.projects || '')).split(',').map(i => +i);
        let from_date = moment((req.query.from_date || '--'), dbDateFormat);
        let to_date = moment((req.query.to_date || '--'), dbDateFormat);

        if (!from_date.isValid() || !to_date.isValid() || !projects.length || projects.filter(id => isNaN(id)).length > 0) {
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'Invalid date filter provided.',{from_date, to_date, projects});
        }
        projects = _uniq(projects);
        from_date = from_date.format(dbDateFormat);
        to_date = to_date.add(1, 'days').format(dbDateFormat);
        sails.log.info(`get user time-sheet, user: ${req.user.id}, from, to: ${from_date}  <= date < ${to_date} projects:`, projects);
        let all_logs = await getDailyTimeEventV2(projects, from_date, to_date, [req.user.id], true);
        ResponseService.successResponse(res, {
            all_logs,
        });
    },

    sendUserTimeSheet: async (req, res) => {
        // req.user.id = 102;
        let projects = (HttpService.decodeURIParam(req.query.projects || ''));
        let from_date = moment((req.query.from_date || '--'), dbDateFormat);
        let to_date = moment((req.query.to_date || '--'), dbDateFormat);

        if (!from_date.isValid() || !to_date.isValid() || !projects.length || projects.split(',').filter(id => isNaN(+id)).length > 0) {
            sails.log.info('Invalid Request', {from_date, to_date, projects});
            return ResponseService.errorResponse(res, 'Invalid filter provided.',{from_date, to_date, projects});
        }
        const range_duration = to_date.diff(from_date, 'month');
        if (range_duration >= 1) {
            sails.log.info('Date range limit exceeded ', {from_date, to_date, projects});
            return ResponseService.errorResponse(res, 'Export limit exceeded. You can only export data for one month at a time. Please adjust your selection and try again', {
                from_date,
                to_date,
                projects
            });
        }

        let projectIds = _uniq(projects.split(',').map(i => +i));
        sails.log.info(`Email user time-sheet request, from, to: ${from_date} : ${to_date}, range_duration: ${range_duration} projects:`, projectIds);
        from_date = from_date.format(dbDateFormat);
        to_date = to_date.add(1, 'days').format(dbDateFormat);

        let response = await sendWeeklyTimesheetProcess(req.user, projectIds, from_date, to_date);
        if (response.success) {
            ResponseService.successResponse(res, { message: response.message,});
        } else {
            ResponseService.errorResponse(res, response.message);
        }
    },

    getCompanyProjectTimeSheetByWeek: getCompanyProjectTimeSheetByWeekFn,

    getCompanyProjectTimeSheetByWeekV2: getCompanyProjectTimeSheetByWeekFn,

    getInducteeTrainingsByProject: async(req, res) => {
        let projectId = req.param('projectId');
        if (!projectId) {
            sails.log.info('Project id is required.');
            return ResponseService.errorResponse(res, 'Project id is required.');
        }
        sails.log.info('Inductions Training records for Project Id', projectId);
        let induction_requests = await sails.models.inductionrequest_reader.find({
            where:{ project_ref: projectId, status_code: [2, 6] },
            sort:[
                {record_id: 'DESC'},
                {id: 'ASC'},
            ]
        });
        induction_requests = await populateUserRefs(induction_requests, 'user_ref', []);
        induction_requests = await populateUserRefs(induction_requests, 'inductor_ref', []);
        if (induction_requests) {
            let record = {};
            let data = [];
            induction_requests.forEach(function (emp) {
                record = {};
                record.userId = emp.additional_data.user_info.id;
                record.userDocuments = emp.additional_data.user_docs || [];
                record.employmentDetail = emp.additional_data.employment_detail;
                record.name = emp.additional_data.user_info.name;
                record.inductionId = emp.id;
                data.push(record);
            });
            return ResponseService.successResponse(res, {data: data});
        }

    },

    getProjectDashboardLiveTv: projectDashboard,

    getProjectInfo: async (req, res) => {
        let project_info = await projectInfoData(req.param('projectId', 0), true);
        ResponseService.successResponse(res, {project_info});
    },

    downloadInductionInviteReport: async (req, res) => {
        let project_ref = req.param('projectId');
        sails.log.info('export invites list of project', project_ref);

        let project = await sails.models.project.findOne({
            select: ['name'],
            where: {id: project_ref},
        });
        let records = await sails.models.invitationlog.find({
            select: ['user_ref', 'email', 'name', 'mobile_no', 'invited_by', 'type', 'createdAt'],
            where: {source_component: `project-induction:${project_ref}`},
            sort: ['createdAt DESC']
        });

        let workbook = await exportInductionInviteListReport(records, project || {}, req.user.timezone);
        return streamExcelDownload(res, workbook, `Induction invites list-${project_ref}.xls`);
    },

    exportTotalTimeReportOfProjectForAll: async (req, res) => {
        let project_id = req.param('projectId');
        let payload = _pick((req.body || {}), [
            'source',
            'include_breaks',
            'visitors',
            'users',
            'from_date',
            'to_date',
            'separate_contractors'
        ]);
        let project = await sails.models.project_reader.findOne({
            where: {id: +project_id},
            select: ['name', 'custom_field', 'id', 'project_number', 'project_initial', 'use_prefix']
        });

        let {custom_field: {timezone: project_timezone}} = project;
        let processed_logs = await totalTimeRecordsOfProjectForAllSources(project, payload, project_timezone, true, true);
        if(processed_logs.error){
            return ResponseService.sendResponse(res, processed_logs);
        }

        let record = await sails.models.inndexsetting_reader.findOne({ name: 'exclusion_by_country_code' });
        let exclusion_setting = (record && record.value) || {};
        let validatorList = {};
        const country_code = project?.custom_field?.country_code;

        validatorList.activeExclusions = (exclusion_setting[country_code] || {}).exclude || [];
        validatorList.globalExclusions = (exclusion_setting['ALL'] || {}).exclude || [];
        validatorList.activeMandatoryItems = (exclusion_setting[country_code] || {}).mandatory || [];
        validatorList.globalMandatoryItems = (exclusion_setting['ALL'] || {}).mandatory || [];

        let includeEmployeeNumber = !(isProfileEmploymentEmpNbrMandatory(validatorList));

        let workbook = await exportTotalTimeReportOfProject(project_id, processed_logs, project_timezone, true, (payload.include_breaks || false), (payload.separate_contractors || false), includeEmployeeNumber);
        return streamExcelDownload(res, workbook, `Total-time-${project_id}.xls`);
    },

    exportDailyDeclarationReportOfUser: async (req, res) => {
        let project_ref = +req.param('projectId');
        let user_ref = +req.param('userId');
        let from_time = +req.param('from_epoch');
        let to_time = +req.param('to_epoch');
        let induction_id = +req.param('induction_id');

        if(!project_ref || !user_ref || !from_time || !to_time){
            sails.log.info('Invalid Request for exportDailyDeclarationReportOfUser');
            return ResponseService.errorResponse(res, 'Invalid request required.');
        }
        sails.log.info(`export daily declaration answers report, project: ${project_ref}, user: ${user_ref} time frame (${from_time} => ${to_time})`);
        let events = await sails.models.usertimelog_reader.find({
            select: ['event_date_time', 'extras', 'event_type'], // 'user_location'
            where: {
                project_ref,
                user_ref,
                event_date_time: {
                    '>': from_time,
                    '<=': to_time
                },
                extras: {'!=': {}}
            },
            sort: 'event_date_time DESC'
        });

        let induction_data = {};
        if(induction_id){
            // sails.log.info('get user induction data, id:', induction_id);
            induction_data = await sails.models.inductionrequest_reader.findOne({
                select: ['additional_data', 'status_code'],
                where: {id: induction_id, user_ref},
            });
        }
        let {additional_data: {user_info, employment_detail, project}} = induction_data;
        let full_name = (user_info.name || user_info.first_name);
        sails.log.info('Processing request for user', full_name);
        let workbook = await exportDailyDeclarationOfUser(events, {
            full_name,
            employer: employment_detail.employer
        }, req.user.timezone);
        return streamExcelDownload(res, workbook, `Daily_Declaration_Report-${project.name}-${full_name}-${employment_detail.employer}.xls`);
    },
};
