/**
 * Created by spatel on 15/05/20.
 */
const {DEFAULT_PAGE_SIZE} = sails.config.constants;
const dbDateFormat = 'YYYY-MM-DD';
const moment = require('moment');
const _pick = require('lodash/pick');
const _uniq = require('lodash/uniq');
const _groupBy = require('lodash/groupBy');
const {
    DataProcessingService: {
        eventOfToday,
        getAllVisitorsTimeEvents,
        populateEmployerRefs,
        populateJobRoles,
        getVisitorsTimeLogForDates,
        isStillOnSite,
        sortBadgeLogsByDay,
        deriveOnSiteVisitorsLocation,
    },
    HttpService: {
        typeOf,
    },
    ResponseService: {
        errorResponse,
        successResponse,
        errorObject
    },
    TokenUtil: {
        hasOnlySiteManagementAccess
    }
} = require('./../services');
const {
    VisitorValidator: {
        storeVisitorInfoRequest,
    }
} = require('./../validators');

const {
    visitorFn: {
        getPaginatedVisitor,
        getVisitorsBySearch,
    },
    inductionFn: {
        getInductionEmployerByUserIds
    },
} = require('../sql.fn');

const visitorsOnSite = async (req, res) => {
    let projectId = req.param('projectId') || (req.project && req.project.id);
    let nowMs = +req.param('nowMs', 0);
    let extraInclude = req.param('include', '').split(',');
    sails.log.info('get on site visitors for project', projectId, ',now', nowMs, 'and extraInclude', extraInclude);
    if (isNaN(nowMs) || !projectId) {
        return errorObject('projectId & nowMs is required');
    }
    let for_date = moment(nowMs);
    let target_day = for_date.clone().format(dbDateFormat);
    sails.log.info(`Visitors On-site for day => ${target_day} (${nowMs})`);
    let prev_day = for_date.clone().subtract(1, 'd').format(dbDateFormat);
    let next_day = for_date.clone().add(1, 'd').format(dbDateFormat);

    let two_days_visitor_logs = await getVisitorsTimeLogForDates(projectId, prev_day, next_day, [], false, null, extraInclude.includes('location'));

    let visitor_groups = _groupBy(two_days_visitor_logs, l => l.visitor_id);

    let visitors_on_site = Object.keys(visitor_groups).map(visitor_id => {
        return eventOfToday(target_day, visitor_groups[visitor_id], for_date);
    }).filter(l => l && l.visitor_id).filter(isStillOnSite);

    if (extraInclude.includes('location') && visitors_on_site.length) {
        let visitor_locations = deriveOnSiteVisitorsLocation(visitors_on_site, projectId);

        visitors_on_site = (visitors_on_site || []).map(log => {
            log.location = (visitor_locations[log.visitor_id] || '')
            return log;
        });
    }

    sails.log.info(`Total visitors of project:${projectId} On-site:`, visitors_on_site.length);

    return visitors_on_site;
}

const getOnSiteVisitorOfProject = async (req, res) => {
    let projectId = +(req.param('projectId', 0));
    let visitors_on_site = await visitorsOnSite(req, res);
    if (visitors_on_site.error) {
        return errorResponse(res, visitors_on_site.message);
    }
    let visitors = [];
    if (visitors_on_site.length) {
        let unique_visitor_ids = _uniq(visitors_on_site.map(l => +l.visitor_id));
        sails.log.info('fetch visitor info of', unique_visitor_ids);
        visitors = await sails.models.visitor.find({
            where: {id: unique_visitor_ids},
        }).populate('job_role_ref');
        visitors = await populateEmployerRefs(visitors, 'employer_ref');
    }

    let on_site_visitors = visitors_on_site.map(log => {
        let v = visitors.find(v => v.id === log.visitor_id) || {};
        log.name = `${v.first_name ? v.first_name : ''}${v.last_name ? ' '+v.last_name : '' }`.trim();

        log.country_code = v.country_code;
        log.employer_id = v.employer_ref && v.employer_ref.id;
        log.job_role_id = v.job_role_ref && v.job_role_ref.id;

        log.employer = v.employer_ref && v.employer_ref.name;
        log.job_role = v.job_role_ref && v.job_role_ref.name;
        log.visitor = v;
        return log;
    });

    if (projectId) {
        let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
        sails.log.info("Fetching on-site visitors on project", projectId, `hasOnlySiteManagement: ${hasOnlySiteManagement}`);
        if (hasOnlySiteManagement) {
            let [userInfo] = await getInductionEmployerByUserIds([req.user.id], projectId, [2, 6]);
            on_site_visitors = on_site_visitors.filter(visitor => visitor.employer && visitor.employer && visitor.employer === userInfo.user_employer);
        }
    }

    return successResponse(res, {
        on_site_visitors
    });
};

const searchVisitorByEmail = async(req, res) => {
    let email = (req.query.email || '').toString().trim().toLowerCase();
    let projectId = req.param('projectId') || (req.project && req.project.id);
    if (!email.length) {
        return errorResponse(res, 'email is required');
    }

    sails.log.info(`Search visitor info for project ${(req.project && req.project.id)}, email: ${email}`);

    let visitors = await sails.models.visitor.find({
        where: {
            email,
            project_ref: projectId
        },
    }).populate('job_role_ref').populate('employer_ref');

    sails.log.info(`got visitors, total:`, visitors.length);
    return successResponse(res, {visitors});
};

const storeVisitorInfo = async (req, res) => {

    let createRequest = _pick((req.body || {}), [
        'name',
        'first_name',
        'last_name',
        'email',
        'visiting_to',
        'country_code',
            'job_role_ref',
        'employer_ref',
        'travel_time',
        'has_rmc',
        'rmc_detail',
    ]);

    let {validationError, payload} = storeVisitorInfoRequest(req);
    if(validationError){
        return ResponseService.errorResponse(res, 'Validation Error: ', {validationError});
    }

    if ((!payload.name && !payload.first_name) || !payload.email) {
        return errorResponse(res, 'Invalid request.');
    }
    payload.email = payload.email.toString().trim().toLowerCase();
    payload.project_ref = req.param('projectId') || (req.project && req.project.id); // this project reference doesn't guarantee, that visitor will not have visits to other projecsearchVisitorByEmailts.

    sails.log.info('creating visitor record', payload);
    if(payload.name){
        let [first_name, ...last_name] = (payload.name).toString().split(' ');
        payload.first_name = first_name;
        payload.last_name = last_name ? last_name.join(' ') : '';
        payload.name = undefined;
    }
    let visitor = await sails.models.visitor.create(payload);

    successResponse(res, {visitor});
};

const updateVisitorById = async (req, res) => {
    let visitorId = +(req.param('visitorId', 0));
    let updatedVisitor = _.pick((req.body || {}), [
        'has_rmc',
        'first_name',
        'last_name',
        'rmc_detail',
        'country_code',
        'job_role_ref',
        'employer_ref',
        'travel_time',
        'visiting_to',
    ]);
    sails.log.info('Request for update visitor', visitorId, 'payload:', updatedVisitor);
    let updated = await sails.models.visitor.updateOne({id: visitorId}).set(updatedVisitor);
    return successResponse(res, {visitor: updated});
}

module.exports = {
    storeVisitorInfo: storeVisitorInfo,

    storeVisitorInfoAlias: storeVisitorInfo,

    updateVisitorById: updateVisitorById,

    updateVisitorByIdAlias: updateVisitorById,

    getVisitorTimeLogs: async (req, res) => {
        let employer_id = req.param('employerId', null);
        let project_id = req.param('projectId');
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let nowMs = +req.param('nowMs', 0);
        if(!nowMs){
            // fallback
            nowMs = moment().valueOf();
        }
        let day_of_yr_today = moment(nowMs).format(dbDateFormat);

        sails.log.info('Time data for project', project_id, 'employer:', employer_id, `is_inherited_project: ${is_inherited_project}`);

        let daily_log = await getAllVisitorsTimeEvents(project_id, [], (is_inherited_project ? null : employer_id));
        if (!daily_log || !daily_log.length) {
            sails.log.info('No time log found, project_id:', project_id);
            return successResponse(res, {visitor_logs: []});
        }

        let unique_visitor_ids = _uniq(daily_log.map(l => +l.visitor_id));
        sails.log.info('fetch visitor info of', unique_visitor_ids);

        let visitors = await sails.models.visitor_reader.find({
            where: {id: unique_visitor_ids},
        });
        visitors = await populateEmployerRefs(visitors, 'employer_ref', ['id', 'name']);
        visitors = await populateJobRoles(visitors, 'job_role_ref');

        let visitor_time_logs = visitors.map(v => {
            let visitor_logs = daily_log.filter(l => l.visitor_id === v.id);
            // ideally get called for today's date only
            let event_of_today = eventOfToday(day_of_yr_today, visitor_logs, moment(nowMs));
            return {
                visitor_id: v.id,
                name: `${v.first_name ? v.first_name : ''}${v.last_name ? ' '+v.last_name : '' }`.trim(),
                employer: v.employer_ref && v.employer_ref.name,
                country_code: v.country_code,
                job_role: v.job_role_ref && v.job_role_ref.name,
                has_rmc: v.has_rmc,
                travel_time: v.travel_time,
                rmc_detail: v.rmc_detail,
                is_still_on_site: isStillOnSite(event_of_today),
                daily_logs: sortBadgeLogsByDay(visitor_logs),
                // event_of_today,
            }
        });

        return successResponse(res, {visitor_logs: visitor_time_logs});
    },

    getVisitorTimeLogsByDay: async (req, res) => {
        let employer_id = req.param('employerId', null);
        let project_id = req.param('projectId');
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let request_date = moment((req.query.for_date || '--'), dbDateFormat);
        if (!request_date.isValid()) {
            sails.log.info('Invalid Request');
            return errorResponse(res, 'for_date query parameter not found');
        }
        let next_day = request_date.clone().add(1, 'd').format(dbDateFormat);
        let day_of_yr_today = request_date.format(dbDateFormat);

        sails.log.info('Querying visitors logs for project:', project_id, 'for_date:', day_of_yr_today, `is_inherited_project: ${is_inherited_project}`);
        let daily_log = await getVisitorsTimeLogForDates(project_id, day_of_yr_today, next_day, [], false, (is_inherited_project ? null : employer_id), true);
        if (!daily_log || !daily_log.length) {
            sails.log.info('No visitors log found, project_id:', project_id);
            return successResponse(res, {visitor_logs: []});
        }

        let unique_visitor_ids = _uniq(daily_log.map(l => +l.visitor_id));
        sails.log.info('fetch visitor info of', unique_visitor_ids);

        let visitors = await sails.models.visitor_reader.find({
            where: {id: unique_visitor_ids},
        }).populate('job_role_ref').populate('employer_ref');

        return successResponse(res, {
            visitor_logs: daily_log.map(log => {
                let v = visitors.find(v => v.id === log.visitor_id) || {};
                log.name = `${v.first_name ? v.first_name : ''}${v.last_name ? ' '+v.last_name : '' }`.trim();
                log.travel_time = v.travel_time;
                log.country_code = v.country_code;
                log.employer = v.employer_ref && v.employer_ref.name;
                log.job_role = v.job_role_ref && v.job_role_ref.name;
                log.has_rmc = v.has_rmc;
                log.rmc_detail = v.rmc_detail;
                return log;
            }),
        });
    },

    searchVisitorByEmail: searchVisitorByEmail,

    // same API with different Auth policy
    searchVisitorByEmailAlias: searchVisitorByEmail,

    getOnSiteVisitorOfProject: getOnSiteVisitorOfProject,

    getOnSiteVisitorCountOfProject: async (req, res) => {
        let visitors_on_site = await visitorsOnSite(req, res);
        if (visitors_on_site.error) {
            return errorResponse(res, visitors_on_site.message);
        }
        let unique_visitor_ids = [];
        if (visitors_on_site.length) {
            unique_visitor_ids = _uniq(visitors_on_site.map(l => +l.visitor_id));
        }
        sails.log.info(`on site visitors count is ${unique_visitor_ids.length} for project ${req.project.id}.`);

        return successResponse(res, {on_site_visitors: unique_visitor_ids.length});
    },

    // same API with different Auth policy
    getOnSiteVisitorOfProjectAlias: getOnSiteVisitorOfProject,

    getVisitorsOfProject: async (req, res) => {
        let project_id = req.param('projectId');
        sails.log.info('get all visitors of project', project_id);
        let rawResult = await sails.sendNativeQuery(`SELECT distinct visitor_ref
                                                     FROM user_daily_log
                                                     WHERE (project_ref = $1 AND visitor_ref IS NOT null)`,
            [project_id]
        );
        let visitor_ids = (typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows.map(r => r.visitor_ref) : [];
        sails.log.info('fetch visitor info of', visitor_ids);
        let visitors = [];
        if (visitor_ids.length) {

            visitors = await sails.models.visitor_reader.find({
                where: {id: visitor_ids},
            }).populate('job_role_ref');
            visitors = await populateEmployerRefs(visitors, 'employer_ref');
        }
        return successResponse(res, {visitors});
    },

    getProjectVisitorEvents: async (req, res) => {
        let projectId = +req.param('projectId');
        let pageNumber = +(req.query.pageNumber || 1);
        let limit = +(req.query.limit || 50);
        let searchTerm = (req.param('q', '')).toString().trim();

        if (isNaN(projectId) || isNaN(pageNumber) || isNaN(limit)) {
            sails.log.info('Invalid Request');
            return errorResponse(res, 'Invalid request required.');
        }

        let skip = ((pageNumber - 1) * limit);
        sails.log.info('fetch all visitors log for project', projectId, `page: ${pageNumber} skip: ${skip} search: ${searchTerm}`);
        let {records: visitors} = await getVisitorsBySearch(projectId, searchTerm)

        let visitorIds = (visitors || []).reduce((arr, record) => {
            if (record.id && typeof record.id == 'number') {
                arr.push(record.id);
            }
            return arr;
        }, []);
        visitorIds = _uniq(visitorIds);
        let total = await sails.models.usertimelog_reader.count({
            project_ref: projectId,
            visitor_ref: visitorIds,
        });
        let events = await sails.models.usertimelog_reader.find({
            select: ['event_date_time', 'visitor_ref', 'event_type', 'temperature'], // , 'attachment_ref', 'user_location'
            where: {
                project_ref: projectId,
                visitor_ref: visitorIds,
            },
            skip,
            limit,
            sort: 'event_date_time DESC'
        }).populate('visitor_ref');

        sails.log.info(`responding with all visitors events, count:${events.length}`);
        return successResponse(res, {
            events,
            pageNumber,
            limit,
            total,
        });
    },

    getVisitorEventDetail: async (req, res) => {
        let project_id = +req.param('projectId');
        let visitor_id = +(req.param('visitorId'));
        let event_log_id = +(req.param('eventLogId') || 0);
        let event_type = req.param('interaction_type');
        let day_of_yr_today = moment((req.query.day_of_yr || '--'), dbDateFormat);

        const ALLOWED_EVENT_TYPE = {
            clock_in: 'IN',
            clock_out: 'OUT'
        };

        if ((!event_log_id && !day_of_yr_today.isValid()) || !visitor_id || !project_id) {
            sails.log.info('Invalid Request for visitor log detail');
            return errorResponse(res, 'Invalid Request, required fields are missing', {required: ['projectId', 'visitorId', 'eventLogId']});
        }
        sails.log.info(`Querying visitor log for project_id: ${project_id}, visitor_id: ${visitor_id}, event_log_id: ${event_log_id} day_of_yr: '${day_of_yr_today.format()}'`);

        let log = {};
        if(event_log_id){
            log = await sails.models.usertimelog_reader.findOne({
                where: {
                    id: event_log_id,
                    visitor_ref: visitor_id,
                    project_ref: project_id
                },
                select: ['visitor_ref', 'project_ref', 'attachment_ref', 'event_date_time', 'event_type', 'user_location', 'temperature']
            }).populate('visitor_ref').populate('project_ref').populate('attachment_ref');
        }else{
            log = await sails.models.userdailylog_reader.findOne({
                where: {day_of_yr: day_of_yr_today.format(dbDateFormat), project_ref: project_id, visitor_ref: visitor_id},
                select: ['visitor_ref', 'project_ref', 'day_of_yr', 'first_in', 'last_out', 'comments'],
            });

            if (log && log.id) {
                log.day_of_yr = moment(log.day_of_yr).format(dbDateFormat);
                let timestamp = (ALLOWED_EVENT_TYPE[event_type] === 'IN') ? log.first_in : log.last_out;
                if (timestamp) {
                    let where = {
                        event_date_time: +timestamp,
                        visitor_ref: visitor_id,
                        project_ref: project_id
                    };
                    sails.log.info('Target timestamp:', timestamp, 'event_type:', event_type, 'where:', where);

                    let raw_logs = await sails.models.usertimelog_reader.find({
                        where,
                        select: ['visitor_ref', 'project_ref', 'attachment_ref', 'event_date_time', 'event_type', 'user_location', 'temperature']
                    }).sort([
                        {event_date_time: (ALLOWED_EVENT_TYPE[event_type] === 'IN') ? 'ASC' : 'DESC'}
                    ]).limit(1).populate('visitor_ref').populate('project_ref').populate('attachment_ref');

                    let exact_log = raw_logs.length ? raw_logs.shift() : {};

                    // Merging Processed log with Raw log.
                    log = {...log, ...exact_log};
                }
            }
        }

        return successResponse(res, {log});
    },

    getPreviousVisitorsOfProject: async (req, res) => {
        let pageNumber = +req.param('pageNumber', 0);
        let searchTerm = (req.param('q', '')).toString().trim();
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);

        let defaultResponse = {
            records: [],
            q: searchTerm,
            pageSize,
            pageNumber,
            totalCount: 0,
        };
        let {
            records,
            total: totalCount
        } = await getPaginatedVisitor(req.project.id, pageSize, (pageSize * pageNumber), searchTerm);

        sails.log.info('Previous visitors fetched successfully.');
        return successResponse(res, {...defaultResponse, records, totalCount});
    },
};
