const { ResponseService } = require("./../services");
const { ASSET_TYPES, COMPANY_SETTING_KEY } = sails.config.constants;
const dayjs = require('dayjs');
const {v4: uuidv4} = require('uuid');
const {
    DataToSeedService: {
        seedVehicleAssetsConfig,
        seedEquipmentAssetsConfig,
        seedTemporaryWorksConfig
    }
} = require('./../services');

const getUniqueId = () => {
    let timestamp = Date.now();
    let random = Math.floor(Math.random() * 10000);
    return parseInt(`${timestamp}${random}`);
};


const saveSeedingStatus = async(companyId, successTypes) => {
    let company_additonal_setting  = await sails.models.companysetting_reader.findOne({
        where: {
            company_ref: companyId,
            name: COMPANY_SETTING_KEY.ADDITIONAL_SETTINGS
        }
    });
    company_additonal_setting = company_additonal_setting || { name: COMPANY_SETTING_KEY.ADDITIONAL_SETTINGS, company_ref: companyId, enabled_on: dayjs().valueOf()};

    company_additonal_setting.value = {
        ...company_additonal_setting.value,  
        asset_seeding: {
            ...company_additonal_setting.value?.asset_seeding,  
            [ASSET_TYPES.AssetVehicle]: company_additonal_setting.value?.asset_seeding?.[ASSET_TYPES.AssetVehicle] || successTypes.includes(ASSET_TYPES.AssetVehicle),
            [ASSET_TYPES.AssetEquipment]: company_additonal_setting.value?.asset_seeding?.[ASSET_TYPES.AssetEquipment] || successTypes.includes(ASSET_TYPES.AssetEquipment),
            [ASSET_TYPES.AssetTemporaryWork]: company_additonal_setting.value?.asset_seeding?.[ASSET_TYPES.AssetTemporaryWork] || successTypes.includes(ASSET_TYPES.AssetTemporaryWork)
        }
    };
    if(company_additonal_setting.id) {
        company_additonal_setting = await sails.models.companysetting.updateOne({id: company_additonal_setting.id}).set(company_additonal_setting);
    } else {
        company_additonal_setting = await sails.models.companysetting.create(company_additonal_setting);
    }
    return company_additonal_setting;
}

const saveInitialChecklistVersions = async(companyId, assetType) => {
    sails.log.info("Save inital checklist versions information for company: ", companyId, ' asset type: ', assetType);
    let assets = await sails.models.assetcustomconfig.find({company_ref:companyId, asset_type: assetType});
    let assetVersions = assets.map(a=> {
        return {
            company_ref: companyId,
            asset_custom_config_ref: a.id,
            key: a.key,
            asset_type: a.asset_type,
            inspection_checklist: a.inspection_checklist,
            checklist_version: a.checklist_version
        }
    });
    await sails.models.assetinspectionchecklists.destroy({
        company_ref: companyId,
        asset_type: assetType,
    });
    await sails.models.assetinspectionchecklists.createEach(assetVersions);
    return;
};

module.exports = {

    getCompanyAssetConfig: async (req, res) => {
        let companyId = +req.param('employerId');
        //let assetType = req.body.assetType;
        let assetType = (req.param('assetType', '')).toString().trim();
        sails.log.info('get company asset config for company ', companyId, ' of type ', assetType);
        let assetTypes = [ASSET_TYPES.AssetEquipment, ASSET_TYPES.AssetVehicle, ASSET_TYPES.AssetTemporaryWork];
        if(!assetTypes.includes(assetType)) {
            return ResponseService.errorResponse(res, `Invalid request, requested asset type not found`);
        }
        let assetConfig = await sails.models.assetcustomconfig.find({company_ref: companyId, asset_type: assetType}).sort([{id: 'ASC'}]);
        return ResponseService.successResponse(res, {records: assetConfig});
    },

    getProjectAssetConfig: async (req, res) => {
        let projectId = +req.param('projectId');
        let assetType = (req.param('assetType', '')).toString().trim();
        let assetKey = (req.param('assetKey', '')).toString().trim();
        let extraColumns = req.param('extra') ? req.param('extra', '').split(','): [];
        sails.log.info('getProjectAssetConfig: get company asset config for projectId ', projectId, ' of type ', assetType);
        let assetTypes = [ASSET_TYPES.AssetEquipment, ASSET_TYPES.AssetVehicle, ASSET_TYPES.AssetTemporaryWork];
        let project = await sails.models.project_reader.findOne({where:{id: projectId}, select: ['id', 'parent_company']});
        if (!project) {
            return ResponseService.errorResponse(res, `Invalid project ID: ${projectId}`);
        }
        if (!assetTypes.includes(assetType)) {
            return ResponseService.errorResponse(res, `Invalid request, requested asset type not found: ${assetType}`);
        }
        let companyId = project.parent_company;
        let filter = {company_ref: companyId, asset_type: assetType};
        
        //if (assetKey) filter.key = assetKey;
        let columns = ['name', 'key', 'alternate_phrase', 'asset_type', 'company_ref', 'parent_key']
        if (extraColumns.includes('fields')) {
            columns.push('default_fields', 'custom_fields');
        }
        if(extraColumns.includes('checklists')) {
            columns.push('inspection_checklist_type', 'inspection_checklist', 'checklist_version');
        }
        let assetConfig = await sails.models.assetcustomconfig.find({where: filter, select: columns}).sort([{id: 'ASC'}]);
        let assetList = assetConfig;
        if (assetKey) {
            assetList = assetConfig.filter(a=> a.key === assetKey);
        }

        const parentKeys = new Set(assetConfig.map(m => m.parent_key).filter(key => key != null));
        assetList = assetList.map(a => {
            a.sub_types = [];
            if(parentKeys.has(a.key)) {
                a.sub_types = assetConfig.filter(c=> c.parent_key === a.key);
            }
            return a;
        });

        return ResponseService.successResponse(res, {records: assetList});
    },

    saveCompanyAssetConfig: async (req, res) => {
        let companyId = +req.param('employerId');
        let assetId = +req.param('assetId');
        sails.log.info('Save Company Asset Config request for company for asset', companyId, assetId);
        let updateRequest = _.pick((req.body || {}), [
            'alternate_phrase',
            'default_fields',
            'custom_fields',
            'inspection_checklist',
            'inspection_checklist_type',
            'checklist_draft',
            'isPublish',
            'checklist_version'
        ]);
        let assetConfig;
        if(updateRequest.isPublish) {
            let currentVersion = await sails.models.assetinspectionchecklists.find({
                where: { asset_custom_config_ref: assetId, company_ref: companyId },
                sort: 'checklist_version DESC',
                limit: 1
            });
            currentVersion = currentVersion.length ? currentVersion[0]: {};
            let updatedVersion = currentVersion.checklist_version + 1;
            let newerVersion = {
                company_ref: companyId,
                asset_custom_config_ref: assetId,
                key: currentVersion.key,
                asset_type: currentVersion.asset_type,
                inspection_checklist: updateRequest.checklist_draft,
                checklist_version: updatedVersion,
                inspection_checklist_type: updateRequest.inspection_checklist_type
            }
            await sails.models.assetinspectionchecklists.create(newerVersion);
            updateRequest.inspection_checklist = updateRequest.checklist_draft;
            updateRequest.checklist_version = updatedVersion;
        }
        assetConfig = await sails.models.assetcustomconfig.updateOne({company_ref: companyId, id: assetId}).set(updateRequest);
        return ResponseService.successResponse(res, {record: assetConfig});
    },

    seedCompanyAssetConfig: async (req,res) => {
        let companyId = +req.param('employerId');
        let assetTypes = req.param('assetTypes') ? (req.param('assetTypes')).toString().trim().split(",") : [];
        if (!companyId) {
            return ResponseService.errorResponse(res, 'Invalid or missing employerId.');
        }
        if (assetTypes.length === 0) {
            return ResponseService.errorResponse(res, 'No asset types provided.');
        }

        sails.log.info("Processing asset seeding for companyId:", companyId, "Asset Types:", assetTypes);
        let employer = await sails.models.createemployer.findOne({where: {id: companyId}, select: ['id', 'features_status']});
        if (!employer) {
            return ResponseService.errorResponse(res, 'Company record not found.');
        }
        let successTypes = [];
        for(let i=0; i<assetTypes.length; i++) {
            let assetType = assetTypes[i].trim();
            console.log('assettype ', assetType)
            switch(assetType) {
                case ASSET_TYPES.AssetVehicle:
                    await seedVehicleAssetsConfig(companyId);
                    successTypes.push(assetType);
                    break;
                case ASSET_TYPES.AssetEquipment:
                    await seedEquipmentAssetsConfig(companyId);
                    successTypes.push(assetType);
                    break;
                case ASSET_TYPES.AssetTemporaryWork:
                    await seedTemporaryWorksConfig(companyId);
                    successTypes.push(assetType);
                    break;
            } 
        }
        let company_additonal_setting = await saveSeedingStatus(companyId, successTypes);
        let message = `Assets meta config updated successfully for: ${successTypes.join(', ')}`;
        sails.log.info(message);
        return ResponseService.successResponse(res, {message: message, company_additonal_setting: company_additonal_setting});
    },

};
