const moment = require('moment');
const momentTz = require('moment-timezone');
const INTERNAL_SERVER_ERROR = sails.__('internal server error');
const {DEFAULT_PAGE_SIZE} = sails.config.constants;
const {
    incidentFn,
    inductionFn: { getUserInductionEmployer, },
} = require('./../sql.fn');
const get = require('lodash/get');
const {
    TokenUtil: {
        getCompanyInfo,
        allResourceAdminsWithOneOfDesignations,
        filterProjectUsersEmailEligibility,
        replaceBrToNl,
        getToolByKey,
        renderChanges
    },
    DataProcessingService: {
        expandIncidentReports,
        sendMailToNominatedManagerCPA,
        getUserFullName,
        getProjectTimezone,
        buildRecordRef,
        getUserFirstName,
        populateUserRefs,
        populateProjectRefs,
        shareReportViaEmail,
        buildIncidentReportStatus,
        sendMailToNominatedManagerPA,
    },
    UserRevisionService: {
        getLatestUserRevision
    },
    ResponseService: {
        successResponse,
        errorResponse,
        errorObject,
        sendResponse,
        successObject
    },
    NotificationService: {
        NOTIFICATION_CATEGORY,
        sendPushNotification
    },
    EmailService: {
        sendMail
    },
    HttpService: {
        typeOf,
        decodeURIParam
    },
    SharedService: {
        downloadPdfViaGenerator
    },
    ChangeLogService: {
        createLog, 
        getAllLogsOfARecord, 
        AUDIT_ACTION_TYPE, 
        AUDIT_EVENT_TYPE
    },
    ASiteService : { checkIfAsiteEnabled, getAsiteProjectToolMapping, uploadDocOnAsite },
} = require('./../services');
const {
   IncidentReportValidator
} = require('./../validators');
const dayjs = require('dayjs');
const _uniq = require("lodash/uniq");
const _groupBy = require("lodash/groupBy");

const cleanActionItem = (item) => {
    item.close_out.images = (item.close_out.images || []).reduce((arr, value) => {
        if (value && (typeof value === "number")) {
            arr.push(value)
        } else if (value && (typeof value === "object") && value.id) {
            arr.push(value.id);
        }
        return arr;
    }, []);

    item.tag_user_ref = (item.tag_user_ref && item.tag_user_ref.id) ? item.tag_user_ref.id : item.tag_user_ref;

    return item;
};

const sleep = (ms) => {
    sails.log.info('Sleep for', ms, 'ms');
    return new Promise(resolve => setTimeout(resolve, ms));
};

// TODO - Utkarsh, Remove after Aug 2, 2025 – added to sanitize <br> for source app compatibility (Assignee: Amit, Ticket: IDX-1763).
const sanitizeBrTags = (record) => {
    const sanitize = (value) => (value ? replaceBrToNl(value) : value);

    record.incident_details = sanitize(record.incident_details);
    record.action_details = sanitize(record.action_details);

    record.witnesses?.forEach(witness => {
        witness.comments = sanitize(witness.comments);
    });

    if (record.site_treatment && record.site_treatment.details) {
        record.site_treatment.details = sanitize(record.site_treatment.details);
    }

    record.thirdparty_vehicle?.forEach(vehicle => {
        vehicle.address = sanitize(vehicle.address);
        vehicle.details = sanitize(vehicle.details);
    });

    if (record.driver_details && record.driver_details.address) {
        record.driver_details.address = sanitize(record.driver_details.address);
    }

    if (record.vehicle_details && record.vehicle_details.damage_details) {
        record.vehicle_details.damage_details = sanitize(record.vehicle_details.damage_details);
    }

    return record;
}

const getIncidentReportHtml = async (incidentReport, project_logo_file, companyName) => {

    let reportedBy = (incidentReport.user_ref && incidentReport.user_ref.id) ? `${incidentReport.user_ref.first_name} ${incidentReport.user_ref.last_name}` : '';
    let company = incidentReport.company_ref;
    let project = incidentReport.project_ref;
    let closed_actions = (incidentReport.incident_actions || []).filter(action => action.close_out.close_out_at);
    let closedOutBy = incidentReport.closeout_user_ref? getUserFullName(incidentReport.closeout_user_ref): '';
    let tz = getProjectTimezone(project);
    const formatWithMomentTz = (n, format) => {
        return momentTz(+n).tz(tz).format(format);
    };
    let DateTimeFormat = "DD-MM-YYYY HH:mm:ss";
    let {
        data: { logs },
    } = await getAllLogsOfARecord(
        project.id,
        getToolByKey("incident_report"),
        incidentReport.id
    );
    if (logs && logs.length) {
        const attachmentIds = [];
        const userIds = [];
    
        const formatValueIfNeeded = (val) =>
            val && val !== "N/A" ? formatWithMomentTz(val, DateTimeFormat) : val;
    
        const pushIfValidUser = (val) => {
            if (val && val !== "N/A") userIds.push(val);
        };
    
        const pushIfValidAttachment = (val) => {
            if (val && !isNaN(val)) attachmentIds.push(val);
        };
    
        logs.forEach((log) => {
            userIds.push(log.user_ref);
            const changes = log.changes || {};

            const addedAtKeys = [
                "Harm",
                "Project Personnel Relevant to the Incident",
                "Events Leading up to the Incident",
                "Investigation Findings",
                "Conclusions",
                "History of Similar Incidents",
                "Immediate Causes",
                "Underlying Causes",
                "Root Causes", 
                "Recommendations",
                "Action attachments"
            ];
                
            addedAtKeys.forEach(key => {
                if (changes[key]) {
                    Object.values(changes[key] || {}).forEach(item => {
                        const addedAt = item["Added at"];
                        if (addedAt) {
                            addedAt.oldValue = formatValueIfNeeded(addedAt.oldValue);
                            addedAt.newValue = formatValueIfNeeded(addedAt.newValue);
                        }
                    
                        if (item.File) {
                            pushIfValidAttachment(item.File.oldValue);
                            pushIfValidAttachment(item.File.newValue);
                        }
                    });
                }
            });

            if (changes["Date of Incident"]) {
                const dateChange = changes["Date of Incident"];
                dateChange.oldValue = formatValueIfNeeded(dateChange.oldValue);
                dateChange.newValue = formatValueIfNeeded(dateChange.newValue);
            }
    
            if (changes["Attachments"]) {
                Object.values(changes["Attachments"]).forEach((item) => {
                    pushIfValidAttachment(item?.file?.oldValue);
                    pushIfValidAttachment(item?.file?.newValue);
                });
            }
    
            if (changes["Incident Actions"]) {
                Object.values(changes["Incident Actions"]).forEach((action) => {
                    ["Tagged User", "Assigned by"].forEach((key) => {
                        const val = action[key];
                        if (val) {
                            pushIfValidUser(val.oldValue);
                            pushIfValidUser(val.newValue);
                        }
                    });
    
                    const dueDate = action["Due date"];
                    if (dueDate) {
                        dueDate.oldValue = formatValueIfNeeded(dueDate.oldValue);
                        dueDate.newValue = formatValueIfNeeded(dueDate.newValue);
                    }
    
                    const closeOut = action["Close Out"];
                    if (closeOut) {
                        const closedAt = closeOut["Closed out at"];
                        if (closedAt) {
                            closedAt.oldValue = formatValueIfNeeded(closedAt.oldValue);
                            closedAt.newValue = formatValueIfNeeded(closedAt.newValue);
                        }
    
                        if (closeOut.Images) {
                            Object.values(closeOut.Images).forEach((img) => {
                                pushIfValidAttachment(img.newValue);
                                pushIfValidAttachment(img.oldValue);
                            });
                        }
                    }
                });
            }
        });
    
        const [populatedAttachments, populatedUserRefs] = await Promise.all([
            sails.models.userfile_reader.find({
                where: { id: attachmentIds },
                select: ["id", "name", "sm_url", "file_url", "file_mime"],
            }),
            sails.models.user_reader.find({
                where: { id: _uniq(userIds) },
            }),
        ]);
    
        const attachmentMap = Object.fromEntries(
            populatedAttachments.map((f) => [f.id, f])
        );
    
        const userRefMap = Object.fromEntries(
            populatedUserRefs.map((u) => [u.id, u])
        );
    
        logs.forEach((log) => {
            const user = userRefMap[log.user_ref];
            log.user_ref = { ...user, name: getUserFullName(user) };
    
            const changes = log.changes || {};
    
            if (changes["Attachments"]) {
                Object.values(changes["Attachments"]).forEach((item) => {
                    ["oldValue", "newValue"].forEach((key) => {
                        const id = item?.file?.[key];
                        const file = attachmentMap[id];
                        if (file) {
                            item.file[key] = ["image/jpeg", "image/png"].includes(file.file_mime)
                                ? `<img src="${file.sm_url || file.file_url}" style="max-width: 200px;">`
                                : `<a target="_blank" rel="noopener noreferrer" href="${file.file_url}">${file.name}</a>`;
                        }
                    });
                });
            }
            if (changes["Action attachments"]) {
                Object.values(changes["Action attachments"]).forEach((aa) => {
                    if (aa.File) {
                        ["oldValue", "newValue"].forEach((key) => {
                            const id = aa.File[key];
                            const file = attachmentMap[id];
                            if (file) {
                                aa.File[key] = [
                                    "image/jpeg",
                                    "image/png",
                                ].includes(file.file_mime)
                                    ? `<img src="${
                                          file.sm_url || file.file_url
                                      }" style="max-width: 200px;">`
                                    : `<a target="_blank" rel="noopener noreferrer" href="${file.file_url}">${file.name}</a>`;
                            }
                        });
                    }
                });
            }
    
            if (changes["Incident Actions"]) {
                Object.values(changes["Incident Actions"]).forEach((action) => {
                    if (action["Close Out"]?.Images) {
                        Object.values(action["Close Out"].Images).forEach((img) => {
                            ["oldValue", "newValue"].forEach((key) => {
                                const id = img[key];
                                const file = attachmentMap[id];
                                if (file) {
                                    img[key] = ["image/jpeg", "image/png"].includes(file.file_mime)
                                        ? `<img src="${file.sm_url || file.file_url}" style="max-width: 200px;">`
                                        : `<a target="_blank" rel="noopener noreferrer" href="${file.file_url}">${file.name}</a>`;
                                }
                            });
                        });
                    }
    
                    ["Tagged User", "Assigned by"].forEach((key) => {
                        const ref = action[key];
                        if (ref) {
                            ["oldValue", "newValue"].forEach((valKey) => {
                                const userId = ref[valKey];
                                if (userId && userId !== "N/A" && userRefMap[userId]) {
                                    ref[valKey] = getUserFullName(userRefMap[userId]);
                                }
                            });
                        }
                    });
                });
            }
        });
    }
    

    let form_template = `pages/incident-report-page`;
    return await sails.renderView(form_template, {
        title: 'Incident & Investigation Report',
        project,
        companyName,
        ir: incidentReport,
        reportedBy,
        company,
        closedOutBy,
        auditData: logs,
        daysOpen: dayjs().diff(dayjs(+incidentReport.createdAt), 'days'),
        report_datetime: +incidentReport.createdAt,
        getUserFullName,
        renderChanges,
        momentTz: formatWithMomentTz,
        replaceAll(str, find, replace) {
            const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
            return (str || '').replace(new RegExp(escapedFind, 'g'), replace);
        },
        getMultilineCount(text) {
            let textBr = (((text || '').match(/\r?\n|\r/gi) || []).length * 0.77);
            let textChar = (((text || '').length / 70) * 0.77);
            return Math.ceil((textBr > textChar) ? textBr : textChar) || 1;
        },
        project_logo_file: project_logo_file,
        project,
        actions_status: (!incidentReport.incident_actions.length) ? 'None' : (closed_actions.length == incidentReport.incident_actions.length) ? 'All Closed' : `${incidentReport.incident_actions.length - closed_actions.length}/${incidentReport.incident_actions.length}`,
        layout: false
    });
};

const processIRDownload  = async (req, res, whereClause, companyId, type, responseType='pdf') => {
    if (!whereClause.id) {
        return errorResponse(res, 'incidentReport id is required');
    }

    sails.log.info('Fetch incidentReport, id:', whereClause.id, "Requesting company:", companyId);

    let incidentReport =  await sails.models.projectincidentreport_reader.findOne(whereClause)
        .populate('user_ref')
        .populate('company_ref')
        .populate('project_ref')
        .populate('closeout_user_ref');

    if (incidentReport && incidentReport.id) {
        sails.log.info('got record, id', incidentReport.id, 'Expanding Project Incident Reports.');
        let [expandedProjectIR] = await expandIncidentReports([incidentReport]);
        incidentReport = (expandedProjectIR.length) ? expandedProjectIR : incidentReport;
        let { project_logo_file, companyName } = await getCompanyInfo(incidentReport.project_ref, (companyId ? {id: companyId} : null));
        let html = await getIncidentReportHtml(incidentReport, project_logo_file, companyName);

        if(type === 'html') {
            sails.log.info('Rendering html view');
            return res.send(html);
        }

        let project = incidentReport.project_ref;
        let tz = getProjectTimezone(project);
        let project_line = `${(project.project_number != null) ? project.project_number + ' - ' + project.name : project.name} (#${project.id}): ${project.contractor}`;
        let file_name = 'Incident Report- #' + buildRecordRef(incidentReport) + ' - ' + moment().format('YYYY-MM-DD');
        let date_line = `Report Date: ${momentTz(+incidentReport.createdAt).tz(tz).format('DD-MM-YY HH:mm:ss')}`;

        return await downloadPdfViaGenerator({
            req,
            res,
            html,
            tool: 'incident-report',
            file_name,
            heading_line: `Incident & Investigation Report`,
            project_line,
            date_line,
            logo_file: project_logo_file,
            has_cover: true,
            responseType
        });
    }
    sails.log.info('Failed to find incidentReport');
    return errorResponse(res, sails.__('internal server error'));
}

const reqKeys = [
    'finalised',
    'location',
    'incident_details',
    'incident_date',
    'injury_details',
    'is_onsite_treatment',
    'site_treatment',
    'attachment_file_ids',
    'any_witnesses',
    'witnesses',
    'incident_type',
    'incident_category',
    'abuse_type',
    'is_chartered',
    'potential_severity',
    'actual_severity',
    'person_affected',
    'vehicle_details',
    'is_thirdparty_vehicle',
    'thirdparty_vehicle',
    'loc_env_details',
    'driver_details',
    'action_details',
    'incident_actions',
    'lighting_condition',
    'weather_conditions',
    'act_type',
    'actual_outcome',
    'injury_caused_by',
    'injury_caused_by_additional',
    'expected_root_cause',
];

const createIRecord = async(req) => {
    let createRequest = _.pick((req.body || {}), ['project_ref', ...reqKeys]);

    // backward compatibility
    // for driver details
    if (createRequest.driver_details && !createRequest.driver_details.contact_number && createRequest.driver_details.contact) {
        createRequest.driver_details.contact_number = { code: null, number: createRequest.driver_details.contact }
    } else if (createRequest.driver_details && createRequest.driver_details.contact_number) {
        createRequest.driver_details.contact = createRequest.driver_details.contact_number.number
    }

    if(!["Injury", "Health", "Violence or Abuse"].includes(createRequest.incident_type)) {
        createRequest.person_affected = [];
    }
    // TODO - Utkarsh, Remove after Aug 2, 2025 – added to sanitize <br> for source app compatibility (Assignee: Amit, Ticket: IDX-1763).
    createRequest = sanitizeBrTags(createRequest);

    if(createRequest.incident_type !== 'Road Traffic') {
        if(createRequest.was_unattanded)   {
            createRequest.passengers_count = null;
        }
        if(createRequest.was_stationary)   {
            createRequest.speed = null;
        }
        createRequest.vehicle_details = {}; 
        createRequest.loc_env_details = {}; 
        createRequest.driver_details = {}; 
    }
    if(createRequest.incident_type !== "Injury") {
        createRequest.injury_caused_by = null;
    }

    if(createRequest.incident_type !== "Injury" ||  createRequest.injury_caused_by !== "Fell from height") {
        createRequest.injury_caused_by_additional = null;
    }

    if(!createRequest.is_onsite_treatment) {
        createRequest.site_treatment = {};
    }
    // for witness contact
    if (createRequest.witnesses) {
        for (let i = 0; i < createRequest.witnesses.length; i++) {
            if (!createRequest.witnesses[i].contact_number && createRequest.witnesses[i].contact) {
                createRequest.witnesses[i].contact_number = { code: null, number: createRequest.witnesses[i].contact }
            } else if (createRequest.witnesses[i].contact_number && !createRequest.witnesses[i].contact) {
                createRequest.witnesses[i].contact = createRequest.witnesses[i].contact_number.number
            }
        }
    }

    // for person affected
    if (createRequest.person_affected && createRequest.person_affected.length) {
        if(!['Health','Injury','Violence or Abuse'].includes(createRequest.incident_type)) {
            createRequest.person_affected = [];
        } else {
            for (let i = 0; i < createRequest.person_affected.length; i++) {
                if (!createRequest.person_affected[i].contact_number && createRequest.person_affected[i].contact) {
                    createRequest.person_affected[i].contact_number = { code: null, number: createRequest.person_affected[i].contact }
                } else if (createRequest.person_affected[i].contact_number && !createRequest.person_affected[i].contact) {
                    createRequest.person_affected[i].contact = createRequest.person_affected[i].contact_number.number
                }
            }
        }
    }

    let project = await sails.models.project_reader.findOne({where: {id: createRequest.project_ref}, select: ['name','parent_company','contractor', 'project_category', 'custom_field']});
    if(project.project_category === 'default' && project.contractor){
        let employer = await sails.models.createemployer_reader.findOne({
            where: {name: project.contractor, country_code: (project.custom_field && project.custom_field.country_code)},
            select: ['id']
        });
        createRequest.project_company = (employer && employer.id) || null;
    } else if (project.project_category === 'company-project' && project.parent_company) {
        createRequest.project_company = project.parent_company || null;
    }

    createRequest.company_ref = (req.user.parent_company && req.user.parent_company.id) || req.user.parent_company || null;
    createRequest.user_ref = req.user.id;

    let revision = await getLatestUserRevision(createRequest.user_ref);
    createRequest.user_revision_ref = revision.id;
    sails.log.info('Creating incident report.');
    let incidentReport =  await sails.models.projectincidentreport.create(createRequest);
    createLog(req.user.id, incidentReport.project_ref, incidentReport.id, AUDIT_ACTION_TYPE.CREATE, getToolByKey('incident_report'), incidentReport, 'ProjectIncidentReportContoller.createIRecord', AUDIT_EVENT_TYPE.USER).catch(sails.log.error);
    return incidentReport;
}

const updateIRecord = async(req, updateRequest, id, projectId) => {
        let originalRecord = await sails.models.projectincidentreport_reader.findOne({id: id, project_ref: projectId});
        sails.log.info('Updating incident report.');
        let updatedRecord =  await sails.models.projectincidentreport.updateOne({id: id, project_ref: projectId}).set(updateRequest);
        createLog(req.user.id, originalRecord.project_ref, originalRecord.id, AUDIT_ACTION_TYPE.UPDATE, getToolByKey('incident_report'), updatedRecord, 'ProjectIncidentReportContoller.updateIRecord', AUDIT_EVENT_TYPE.USER);
        return updatedRecord
}

const getIRecord = async(filter, expand = false) => {
    let projectIncidentReports = await sails.models.projectincidentreport_reader.find(filter);
    projectIncidentReports = await populateProjectRefs(projectIncidentReports, 'project_ref', []);
    projectIncidentReports = await populateUserRefs(projectIncidentReports, 'user_ref', []);
    if(projectIncidentReports && expand) {
        let expandedProjectIRs = await expandIncidentReports(projectIncidentReports);
        projectIncidentReports = (expandedProjectIRs.length) ? expandedProjectIRs : projectIncidentReports;
    }
    return projectIncidentReports;
}

const sendIncidentAlertEmail = async(ir, projectInfo, userInfo, is_updated = false) => {
    //fetching nominated managers
    let projUsrResult = await allResourceAdminsWithOneOfDesignations(ir.project_ref, ['nominated', 'custom'], true);
    projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, 'incident_report');

    //fetching alert recipients for company portal
    let severityLevels = ["Minor", "Moderate", "Serious (High)", "High Potential", "Critical (Major)"];
    let applicableSeverities = [];

    for (let i = 0, len = severityLevels.length; i < len; i++) {
        applicableSeverities.push(severityLevels[i]);
        if(ir.actual_severity == severityLevels[i]) {
            break;
        }
    }

    let incidentAlertRecipients = await sails.models.incidentalertpreference_reader.find({
        where: {company_ref: ir.project_company, severity_level: {in: applicableSeverities}},
        select: ['id', 'recipient_ref', 'severity_level']
    });
    incidentAlertRecipients = await populateUserRefs(incidentAlertRecipients, 'recipient_ref', []);
    sails.log.info('Send email to nom managers/CP incident alert recipients, count:', projUsrResult.length, incidentAlertRecipients.length);

    totalRecipients = [...projUsrResult, ...incidentAlertRecipients];
    let submitted_by = getUserFullName(userInfo);
    let subject = `Incident Report ${is_updated? 'Updated':'Submitted'} (${ir.incident_type}) - Project: ${projectInfo.name}`;
    for (let j = 0, len = totalRecipients.length; j < len; j++) {
        let recipient = totalRecipients[j].user_ref || totalRecipients[j].recipient_ref || {};
        let user_name = getUserFullName(recipient);
        let emailHtml = await sails.renderView('pages/mail/mail-content', {
            title: subject,
            mail_body: 'incident-report-mail-to-nom-manager',
            user_name,
            incidentDate: dayjs(+ir.incident_date).format('DD-MM-YYYY'),
            submitted_by,
            ir,
            is_updated,
            project: projectInfo,
            layout: false
        });

        sails.log.info('Sending mail to', recipient.email);
        await EmailService.sendMail(subject, [recipient.email], emailHtml);
        sails.log.info(`Incident Report notification email has been sent`);
    }
}

const cleanRequest = (incidentReport) => {
    if(incidentReport.incident_actions && incidentReport.incident_actions.length) {
        incidentReport.incident_actions = (incidentReport.incident_actions || []).reduce((arr, action) => {
            if (action.tag_user_ref && (typeof action.tag_user_ref === "number")) {
                arr.push(action);
            } else if (action.tag_user_ref && (typeof action.tag_user_ref === "object") && action.tag_user_ref.id) {
                action.tag_user_ref = action.tag_user_ref.id;
                arr.push(action);
            }
            return arr;
        }, []);
    }

    return incidentReport;
};

const sendIncidentActionNotification = async(incidentReport, recentAction, req, projectInfo) => {

    let responsibleUserInfo = await sails.models.user_reader.findOne({
        where: {id: recentAction.tag_user_ref},
        select: ['id', 'first_name', 'last_name', 'email']
    });

    //send email and push notification to tagged user
    let message = `${getUserFullName(req.user, true)} has assigned you an action on incident report ${incidentReport.record_ref} @ ${projectInfo.name}.`;
    let messageTitle = 'Tagged in incident report action';
    let category = NOTIFICATION_CATEGORY.INCIDENT_REPORT;
    let notificationData = {
        category: category,
        project_ref: projectInfo.id,
        incident_id: incidentReport.id,
        item_info: JSON.stringify(recentAction)
    };
    let firebaseMsgData = {
        category: category,
        project_ref: projectInfo.id.toString(),
        incident_id: incidentReport.id.toString(),
        item_info: JSON.stringify(recentAction),
    };
    await sendPushNotification({
        message,
        messageTitle,
        category,
        recipientUserInfo: responsibleUserInfo,
        submittedByUserInfo: req.user,
        notificationData,
        firebaseMsgData
    });

    //send email as well
    let subject = `Tagged in incident report action`;
    let html = await sails.renderView('pages/mail/mail-content', {
        mail_body: 'notify-on-incident-report-action-assign',
        title: subject,
        responsibleUserInfo,
        submittedByUsername: getUserFullName(req.user),
        projectInfo,
        recentAction,
        incident_number: buildRecordRef(incidentReport),
        incident_date: moment(incidentReport.createdAt).format('DD-MM-YYYY'),
        incident_type: incidentReport.incident_type,
        action_due_date: moment(recentAction.due_date).format('DD-MM-YYYY'),
        layout: false
    });
    await sendMail(subject, [responsibleUserInfo.email], html);
}
/*const getInductedUsersByProjectIds = async(projectIds) => {
    let induction_requests = await sails.models.inductionrequest_reader.find({
        select: ['status_code', 'project_ref', 'user_ref', 'additional_data'],
        where: {
            status_code: [2, 6], // `approved` OR `In review` one only
            project_ref: { in: projectIds }
        }
    });
    let resultArr = {};
    let user_ids = _uniq((induction_requests.length) ? induction_requests.map(r => r.user_ref) : []);
    sails.log.info('fetch users info of IDs: ', user_ids);
    if (user_ids.length) {
        let usersInfo = await sails.models.user_reader.find({
            where: {'id': user_ids},
            select: ['email', 'first_name', 'last_name', 'parent_company', 'profile_pic_ref', 'gender']
        }).populate('profile_pic_ref').populate('parent_company');

        let rows = _groupBy(induction_requests, l => l.project_ref);
        let userIds = [];
        for (let projectId in rows) {
            let projectUsers = (rows[projectId] || []).reduce((arr, record) => {
                if (record.user_ref && !userIds.includes(record.user_ref)) {
                    userIds.push(record.user_ref);
                    let user = usersInfo.find(user => user.id == record.user_ref);
                    if(user) {
                        user.job_role = (record.additional_data.employment_detail && record.additional_data.employment_detail.job_role) ? record.additional_data.employment_detail.job_role : null;
                        user.contact = get(record, 'additional_data.contact_detail.mobile_no', ''),
                        user.gender = user.gender || '',
                        user.address = get(record, 'additional_data.contact_detail.street', ''),
                        arr.push(user);
                    }
                }
                return arr;
            }, []);
            resultArr[projectId] = projectUsers.sort((a,b) => (a.first_name > b.first_name) ? 1 : ((b.first_name > a.first_name) ? -1 : 0));
        }
    }
    return resultArr;
}*/

getProjectIncidentList = async (req, res) => {
    let projectId = +req.param('projectId', 0);
    let projectCompany = +req.param('companyId', 0);
    let sortKey = req.param('sortKey', 'is_closed');
    let sortDir = req.param('sortDir', 'asc');
    let pageSize = +req.param('pageSize', 50);
    let pageNumber = +req.param('pageNumber', 0);
    let raisedBy = +req.param('raisedBy', 0);
    let searchTerm = (req.param('q', '')).toString().trim();
    let incidentTypes = req.param('incident_type') ? req.param('incident_type').split(','): [];
    let statuses = (req.param('status', '')).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
    let projectIds = (req.param('project', '')).split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);


    if(!projectId && !projectCompany) {
        return errorResponse(res, 'Required parameters are missing to fetch incident reports.');
    }

    if(searchTerm){
        searchTerm = decodeURIParam(searchTerm);
    }
    let defaultResponse = {
        incident_reports: [],
        totalCount: 0,
        projectId,
        q: searchTerm,
        pageSize,
        pageNumber,
        sortKey,
        sortDir,
        raisedBy,
        incidentTypes,
        statuses,
        open_record_count: 0
    };

    if(raisedBy !== 0 && raisedBy !== req.user.id) {
        sails.log.info(`Returning 0 incident record, for projectId: ${projectId}, projectCompany: ${projectCompany} raisedBy: ${raisedBy}. ERR_mismatch_raisedBy.`);
        return successResponse(res, defaultResponse);
    }

    let {
        total: totalCount,
        records: incident_reports,
        companyIncidentsProjectIds
    } = await incidentFn.getProjectIncidentReportPage(projectId, pageSize, (pageSize * pageNumber), sortKey, sortDir, {
        searchTerm, incidentTypes, projectCompany, projectIds, statuses, ...(raisedBy && {userId: raisedBy}),
    });

    if (!incident_reports.length) {
        sails.log.info(`No incident record found, for projectId: ${projectId}, projectCompany: ${projectCompany} raisedBy: ${raisedBy}, q: "${searchTerm}" pageNumber: ${pageNumber} pageSize: ${pageSize} totalCount: ${totalCount}`);
        return successResponse(res, {...defaultResponse, totalCount});
    }

    //company level incidents
    let projects = [];
    if (companyIncidentsProjectIds.length && projectCompany) {
        projects = await sails.models.project_reader.find({
            select: ['id', 'name'],
            where: {
                id: _uniq(companyIncidentsProjectIds)
            }
        })

        defaultResponse.company_incident_projects = projects;
        defaultResponse.projectCompany = projectCompany;
        defaultResponse.projectIds = projectIds;
    }

    let incidentFileIds = [];
    let taggedUserIds = [];
    for (let i = 0; i < incident_reports.length; i++) {
        incident_reports[i].project_ref = (projects || []).find(project => project.id === incident_reports[i].project_ref) || incident_reports[i].project_ref;
        incident_reports[i].status_message = buildIncidentReportStatus(incident_reports[i].status);
        incident_reports[i].record_ref = buildRecordRef(incident_reports[i]);

        (incident_reports[i].incident_actions || []).map(item => {
            if(Object.keys(item.close_out).length && item.close_out.images && item.close_out.images.length) {
                incidentFileIds.push(...item.close_out.images);
            }

            if (item.tag_user_ref && (typeof item.tag_user_ref === "number")) {
                taggedUserIds.push(item.tag_user_ref);
            }
            return item;
        });
    }

    let files = await sails.models.userfile_reader.find({
        where: {id: _uniq(incidentFileIds)},
        select: ['id', 'file_url', 'md_url', 'sm_url', 'img_translation']
    });


    let usersInfo = await sails.models.user_reader.find({
        where: {id: _uniq(taggedUserIds)},
        select: ['id', 'first_name', 'last_name', 'email']
    });

    for (let i = 0; i < incident_reports.length; i++) {
        incident_reports[i].incident_actions = (incident_reports[i].incident_actions || []).map(item => {
            if(Object.keys(item.close_out).length && item.close_out.images && item.close_out.images.length) {
                item.close_out.images = files.filter(file => (item.close_out.images || []).includes(file.id));
            }

            if (item.tag_user_ref && (typeof item.tag_user_ref === "number")) {
                item.tag_user_ref = usersInfo.find(user => (user.id == item.tag_user_ref));
            }
            return item;
        });
    }

    let openRecordCount = 0;
    if (pageNumber == 0) {
        let whereClause = {finalised: true, status: {in: [1, 2]}};
        if (projectId) {
            whereClause.project_ref = projectId
        } else if (projectCompany) {
            whereClause.project_company = projectCompany
        }
        openRecordCount = await sails.models.projectincidentreport.count(whereClause);
    }
    return successResponse(res, {
        ...defaultResponse,
        incident_reports,
        totalCount,
        open_record_count: openRecordCount,
    });
}

const sendNotificationsForIncident =  async (incidentReport, req, isUpdated = false) => {

    let projectInfo = await sails.models.project_reader.findOne({where: {id: incidentReport.project_ref}, select: ['name','project_category','parent_company']});
    await sendIncidentAlertEmail(incidentReport, projectInfo, req.user, isUpdated);

    if (req.body.incident_actions && req.body.incident_actions.length) {
        for (let i = 0, len = req.body.incident_actions.length; i < len; i++) {
            let action = req.body.incident_actions[i];
            // Send app and email notification to tagged user.
            await sendIncidentActionNotification(incidentReport, action, req, projectInfo);
        }
    }

    //send mail to company project admins(Nominated Managers)
    if (projectInfo.project_category === 'company-project' && projectInfo.parent_company) {
        sails.log.info("Send mail to CPA(Nominated Managers");
        await sendMailToNominatedManagerCPA(projectInfo.parent_company, projectInfo, req.user, 'Incident Report', `${isUpdated ?  'updated an incident on site': 'reported an incident on site'}`);
    } else {
        sails.log.info("Send mail to PA(Nominated Managers)");
        await sendMailToNominatedManagerPA(projectInfo, req.user, 'Incident Report', `${isUpdated ?  'updated an incident on site': 'reported an incident on site'}`);
    }

    sails.log.info('Deleting all the pending incident reports by user on a project, As an inspecion is finalized.');
    await sails.models.projectincidentreport.destroy({
        project_ref: incidentReport.project_ref,
        user_ref: req.user.id,
        finalised: false
    });

    return;
}

const processIncidentCloseoutAction =  async (req, res, irId, existingIncident, itemInfo) => {

    if (existingIncident && existingIncident.id) {
        let actionIndex = (existingIncident.incident_actions || []).findIndex(action => action.id == itemInfo.id);
        if (actionIndex == -1) {
            sails.log.info(`Failed to update action of incident report, No action found with matched id: ${itemInfo.id}`);
            return errorResponse(res, `Failed to update action of incident report, No action found with matched id: ${itemInfo.id}`);
        }
        existingIncident.incident_actions[actionIndex] = itemInfo;

        sails.log.info('Updating incident report to close out an action.');
        let updateRequest = {};
        updateRequest.incident_actions = existingIncident.incident_actions;
        let updatedIncidentReport = await sails.models.projectincidentreport.updateOne({id: irId}).set(updateRequest);

        createLog(req.user.id, updatedIncidentReport.project_ref, updatedIncidentReport.id, AUDIT_ACTION_TYPE.UPDATE, getToolByKey('incident_report'), updatedIncidentReport, 'ProjectIncidentReportContoller.processIncidentCloseoutAction', AUDIT_EVENT_TYPE.USER).catch(sails.log.error);
        sails.log.info('Updated action of incident report successfully.');
        successResponse(res, {project_incident_report: updatedIncidentReport});

        // @todo: Added a sleep here to wait for completing pdf(close out images) to images translation
        await sleep(30000);

        sails.log.info('Sending close out item mail to incident report action owner.')
        let closeOutImagesSrc = [];
        if(Object.keys(itemInfo.close_out).length && itemInfo.close_out.images && itemInfo.close_out.images.length) {
            let files = await sails.models.userfile_reader.find({
                where: {id: _uniq(itemInfo.close_out.images)},
                select: ['id', 'file_url', 'img_translation']
            });
            closeOutImagesSrc = (files || []).reduce((arr, file) => {
                if (file.file_url && !file.img_translation.length) {
                    arr.push(file.file_url);
                } else if (file.img_translation.length) {
                    arr.push(...file.img_translation);
                }
                return arr;
            }, []);
        }

        let usersInfo = await sails.models.user_reader.find({
            where: {id: [itemInfo.assigned_by_user_ref, itemInfo.tag_user_ref]},
            select: ['id', 'first_name', 'last_name', 'email']
        });

        let assignedByUser = (usersInfo || []).find(user => user.id == itemInfo.assigned_by_user_ref) || {};

        let taggedUser = (usersInfo || []).find(user => user.id == itemInfo.tag_user_ref) || {};

        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: existingIncident.project_ref},
            select: ['id', 'name']
        });

        sails.log.info(`Send email to ${assignedByUser.email} for close out item ${itemInfo.category} - ${itemInfo.id}`);

        let subject = `Incident Action Closeout: ${projectInfo.name}`;
        let html = await sails.renderView('pages/mail/mail-content', {
            mail_body: 'incident-report-notify-on-action-close-out',
            title: subject,
            receiver_name: getUserFirstName(assignedByUser),
            tagged_user_name: getUserFirstName(taggedUser),
            project_name: projectInfo.name,
            action_info: itemInfo,
            incident_number: buildRecordRef(updatedIncidentReport),
            incident_date: moment(updatedIncidentReport.createdAt).format('DD-MM-YYYY'),
            incident_type: updatedIncidentReport.incident_type,
            closed_out_by: itemInfo.close_out.reviewed_by,
            closed_out_at: moment(itemInfo.close_out.close_out_at).format('DD-MM-YYYY HH:mm:ss'),
            closeout_details: itemInfo.close_out.details,
            closeout_images: closeOutImagesSrc,
            layout: false
        });
        await sendMail(subject, [assignedByUser.email], html);

        return;
    }

    sails.log.info(`Failed to update action of incident report with id: ${irId}`);
    return errorResponse(res, `Failed to update action of incident report with id: ${irId}`);
};

const closeOutIncidentReportFn = async (req, res) => {
    let id = +req.param('id');
    sails.log.info('Close out incident, id', id);
    if (!id) {
        return errorResponse(res, 'incident report id is required');
    }

    let closeOutRequest = req.body.closeout_request;
    let request = _.pick((closeOutRequest || {}), [
        'closeout_comment',
        'closeout_sign',
    ]);

    request.status = 3; // Incident Closed
    request.is_closed = true;
    request.closeout_user_ref = req.user.id;
    request.closed_out_date = dayjs().valueOf();

    sails.log.info('Closing out incident report with', request);

    let incidentReport = await sails.models.projectincidentreport.updateOne({id: id}).set(request);

    let hasAsite = await checkIfAsiteEnabled(incidentReport.project_ref);
    if (hasAsite){
        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: incidentReport.project_ref}, select: ['name','project_category','parent_company', 'custom_field', 'project_number']
        });

        sails.log.info(`Project has asite enabled trying to sync incident reports document for project ${incidentReport.project_ref}`);

        let userEmployer = await getUserInductionEmployer({id: incidentReport.user_ref}, incidentReport.project_ref);
        // Check if user's induction employer not there, Load employer from profile data.
        if(userEmployer) {
            let [irPopulated] = await populateUserRefs([incidentReport], 'user_ref', ['parent_company']);
            userEmployer = irPopulated.user_ref.parent_company;
        }
        // Not awaiting syncIncidentReportToAsite fn, to run the PDF generation and asite upload in backend.
        let localsObj = Object.assign({}, res.locals); // Cloning the res.locals, as the object gets cleared after the API response sent.
        syncIncidentReportToAsite(req, res, projectInfo, incidentReport.id, incidentReport.record_id, localsObj, userEmployer).catch(sails.log.error);
    }
    return successResponse(res, {incident_report: incidentReport});
};

const syncIncidentReportToAsite = async (req, res, projectInfo, irId, recordId, localsObj, userEmp = 0) => {
    sails.log.info(`[syncIncidentReportToAsite] Starting execution for incident report ${irId} employer ID ${userEmp}`);

    let {workspace_id, matched_tool} = await getAsiteProjectToolMapping(projectInfo.id, 'incident_report', userEmp);
    if(!workspace_id || !matched_tool) {
        sails.log.info(`[syncIncidentReportToAsite] Aborting execution for incident report ${irId} workspace_id or matched_tool not found.`);
        return;
    }

    let { employer } = await getCompanyInfo(projectInfo, null, ['id', 'company_initial']);

    sails.log.info(`[syncIncidentReportToAsite] preparing PDF for incident report ${irId}`);
    res.locals = localsObj || res.locals;

    let fileData = await processIRDownload(req, res, {id: irId}, null, 'pdf', 'path');
    sails.log.info(`[syncIncidentReportToAsite] PDF prepared, starting asite upload for incident report ${irId}`);

    //File name format -> {PROJECT/CONTRACT NUMBER} ({INNDEX PROJECT ID NUMBER})-{COMPANY INITIALS}-{TOOL NAME}-{REPORT TYPE}-{REPORT #}
    fileData.name = `${projectInfo.project_number} (${projectInfo.id})-${employer.company_initial}-Incident_Report-${recordId}.pdf`;
    sails.log.info(`[syncIncidentReportToAsite] Filename to be used on asite ${fileData.name}`);

    await uploadDocOnAsite(employer.id, workspace_id, matched_tool.folder_id, fileData);
};

const updatePartialIncidentReport = async (req, id, reqKeys) => {
    const requestBody = req.body || {};
    const projectId = +req.param('projectId');
    let updatedRequest = _.pick(requestBody, reqKeys);
    if(reqKeys.includes('incident_actions')){
        updatedRequest = cleanRequest(updatedRequest);
    }

   let incidentReport = await updateIRecord(req, updatedRequest, id, projectId);
    if(!incidentReport) {
        sails.log.error('Failed to update incident report.');
        return errorObject(sails.__('Failed to update incident report.'))
    } else {
        if (requestBody.adding_incident_action) {
            let recentAction = req.body.incident_actions[requestBody.incident_actions.length-1];
            let projectInfo = await sails.models.project_reader.findOne({
                where: {id: incidentReport.project_ref},
                select: ['name', 'project_category', 'parent_company', 'project_type']
            });
            // Send app and email notification to tagged user.
            await sendIncidentActionNotification(incidentReport, recentAction, req, projectInfo);
        }
        let [expandedProjectIR] = await expandIncidentReports([incidentReport]);
        incidentReport = (expandedProjectIR.length) ? expandedProjectIR : incidentReport;
        sails.log.info('updated incident report successful, id', incidentReport.id);
        return successObject({incident_report: incidentReport});
    }
}

module.exports = {
    createOrUpdateIncidentReport: async (req, res) => {
        let id = +req.body.id || 0;
        let finalised = +req.body.finalised || false;
        let projectId = +req.param('projectId');
        
        sails.log.info('Partial create/update incident record for project, by', req.user.id, 'recordId: ', id);

        let incident_meta_data = await incidentFn.getIncidentMetaData();
        let incidentReport = {};
        let isUpdated = false;
        if(id) {
            let requestBody = (req.body || {});
            sails.log.info('Update incident report request', id);
            isUpdated = true;
            let pendingIncidentRecordCount = await sails.models.projectincidentreport.count({id: id});
            if(pendingIncidentRecordCount == 0){
                sails.log.info('No pending incident report found with ID: ', id);
                return errorResponse(res, sails.__('No pending incident report found with ID: '+ id));
            }
            requestBody = cleanRequest(requestBody);
            let updateIncidentRecord = IncidentReportValidator(incident_meta_data).updateIncidentRecord;
            let {validationError} = updateIncidentRecord(requestBody);
            if(validationError){
                return errorResponse(res, 'Invalid Request.', {validationError});
            }
            let updateRequest = _.pick(req.body, reqKeys)
            incidentReport = await updateIRecord(req, (updateRequest || {}), id, projectId);
        } else {
            sails.log.info('Create partial incident report request');
            let createIncidentRecord = IncidentReportValidator(incident_meta_data).createIncidentRecord;
            let {validationError} = createIncidentRecord(req);
            if(validationError){
                return errorResponse(res, 'Invalid Request.', {validationError});
            }
            incidentReport = await createIRecord(req);
        }
        if (incidentReport) {
            if (finalised) {
                await sendNotificationsForIncident(incidentReport, req, isUpdated);
            }
            sails.log.info('Created/updated partial incident report successfully, finalised:', finalised);
            return successResponse(res, {incident_report: incidentReport});
        }
        sails.log.info('Failed to create/update incident report.');
        return errorResponse(res, sails.__('Failed to create/update incident report.'));
    },

    partiallyUpdateIncidentReport: async (req, res) => {
        const id = +req.param('id', 0);
        const projectId = +req.param('projectId', 0);
        const action = req.param('action', '-');
        const reqKeys = ['root_causes', 'incident_conclusions', 'incident_recommendations',
            'relevant_personnel_user_refs', 'incident_events', 'incident_harm', 'investigation_findings',
            'similar_incidents', 'immediate_causes', 'underlying_causes']
        const editCommentReqKeys = [...reqKeys];
        const deleteCommentReqKeys = [...reqKeys, 'review_photo_ids'];
        const saveReviewFieldsReqKeys = [...reqKeys];
        const allowedActions = ['saveReviewField', 'editComment', 'deleteComment', 'saveAttachmentFiles', 'addIncidentAction'];

        if (!id || !allowedActions.includes(action)) {
            return errorResponse(res, 'Invalid request');
        }
        sails.log.info(`Partially update incident report request id ${id}, action ${action}, and projectId ${projectId}`);
        if(action === 'saveReviewField'){
            let incidentReport = await updatePartialIncidentReport(req, id, saveReviewFieldsReqKeys);
            return sendResponse(res, incidentReport);
        } else if(action === 'editComment') {
            let incidentReport = await updatePartialIncidentReport(req, id, editCommentReqKeys);
            return sendResponse(res, incidentReport)
        } else if(action === 'deleteComment') {
            let incidentReport = await updatePartialIncidentReport(req, id, deleteCommentReqKeys);
            return sendResponse(res, incidentReport)
        } else if(action === 'saveAttachmentFiles') {
            let incidentReport = await updatePartialIncidentReport(req, id, ['review_photo_ids']);
            return sendResponse(res, incidentReport)
        } else if(action === 'addIncidentAction') {
            let incidentReport = await updatePartialIncidentReport(req, id, ['incident_actions']);
            return sendResponse(res, incidentReport)
        }
    },

    createIncidentReport: async (req, res) => {
        let incidentReport = await createIRecord(req);

        if(incidentReport)  {
            await sendNotificationsForIncident(incidentReport, req);

            sails.log.info('Incident report created successfully.')
            return successResponse(res, {incident_report: incidentReport});
        }
        sails.log.info('Failed to create incident report');
        return errorResponse(res, sails.__('Failed to create incident report'));
    },

    updateIncidentReport: async (req, res) => {
        let id = +req.param('id');
        let finalised = +req.body.finalised || false;
        sails.log.info('update incident report , id', id);

        let updateRequest = _.pick((req.body || {}), [
            'relevant_personnel_user_refs',
            'incident_events',
            'incident_harm',
            'investigation_findings',
            'similar_incidents',
            'immediate_causes',
            'underlying_causes',
            'root_causes',
            'incident_actions',
            'incident_conclusions',
            'incident_recommendations',
            'review_photo_ids',
            ...reqKeys
        ]);

        updateRequest = cleanRequest(updateRequest);
        updateRequest.status = 2; // Set status as reviewed.
        let incidentReport = await sails.models.projectincidentreport.updateOne({id: id}).set(updateRequest);

        if(incidentReport && incidentReport.id) {
            if (finalised) {
                await sendNotificationsForIncident(incidentReport, req);
                sails.log.info('updated partial incident report successfully, finalised:', finalised);
            }

            if (req.body.adding_incident_action) {
                let recentAction = req.body.incident_actions[req.body.incident_actions.length-1];
                let projectInfo = await sails.models.project_reader.findOne({
                    where: {id: incidentReport.project_ref},
                    select: ['name', 'project_category', 'parent_company', 'project_type']
                });
                // Send app and email notification to tagged user.
                await sendIncidentActionNotification(incidentReport, recentAction, req, projectInfo);
            }
            let [expandedProjectIR] = await expandIncidentReports([incidentReport]);
            incidentReport = (expandedProjectIR.length) ? expandedProjectIR : incidentReport;
            sails.log.info('updated incident report successful, id', incidentReport.id);
            return successResponse(res, {incident_report: incidentReport});
        }

        sails.log.info('updated incident report successful, id', incidentReport.id);
        return successResponse(res, {incident_report: incidentReport});
    },

    closeOutIncidentReport: closeOutIncidentReportFn,

    closeOutIncidentReportV2: closeOutIncidentReportFn,

    getPendingIncidentReport: async (req, res) => {
        let projectId = +req.param('projectId', 0);
        let userId = +req.user.id;

        sails.log.info('Fetch pending incident report for project, projectId:', projectId, 'userId', userId);

        let [incidentReport] =  await sails.models.projectincidentreport_reader.find({
            where: {
                project_ref: projectId,
                user_ref: userId,
                finalised: false
            },
            limit: 1,
            sort: 'updatedAt DESC'
        })
            .populate('project_ref')
            .populate('company_ref')
            .populate('user_ref');

        if(incidentReport && incidentReport.id) {
            let [firstExpandedProjectIRs] = await expandIncidentReports([incidentReport]);
            incidentReport = firstExpandedProjectIRs;
        }
        return successResponse(res, {incident_report: incidentReport || null});
    },

    getIncidentReport: async (req, res) => {
        let id = +req.param('id');
        sails.log.info('Fetch incident report, id:', id);

        let incidentReport = await sails.models.projectincidentreport_reader.findOne({id: id})
            .populate('user_ref')
            .populate('project_ref');
        sails.log.info('got incident report record, id', incidentReport ? incidentReport.id : undefined);
        return successResponse(res, {incident_report: incidentReport});
    },

    getProjectIncidentReports: async (req, res) => {
        let projectId = +req.param('projectId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');

        let whereFilter = {
            where: {project_ref: projectId, finalised: true},
            limit: pageSize,
            skip: pageSize * pageNumber,
            sort: ['is_closed ASC', 'incident_date DESC']
        };

        sails.log.info('fetch all incident-reports with projectId:', projectId, `is_inherited_project: ${is_inherited_project}`);
        let projectIncidentReports = await getIRecord(whereFilter, true);
        let resultArr = {};
        if (pageNumber == 0) {
            // resultArr = await getInductedUsersByProjectIds([projectId]);
        }
        let totalRecordCount = await sails.models.projectincidentreport.count({project_ref: projectId, finalised: true});
        let openRecordCount = await sails.models.projectincidentreport.count({project_ref: projectId, finalised: true, status: {in: [1, 2]}});
        sails.log.info('got incident reports.', projectIncidentReports.length);
        return successResponse(res, {incident_reports: projectIncidentReports, total_record_count:totalRecordCount, project_inducted_users: resultArr, open_record_count:openRecordCount});
    },

    getUserIncidentReports: async (req, res) => {
        let userId = +req.param('userId');
        let projectId = +req.param('projectId');
        let filter;
        if (userId ) {
            filter = {user_ref:userId, finalised: true};
        } else {
            return errorResponse(res, 'user id is required');
        }

        if (projectId) {
            filter.project_ref = projectId;
        }

        var userIncidentReports = await getIRecord(filter, true);
        sails.log.info('got incident reports.', userIncidentReports.length);
        return successResponse(res, {incident_reports: userIncidentReports});
    },

    // @todo vishal: this method can be removed in Jan 2025
    getCompanyIncidentReports: async (req, res) => {
        let employerId = +req.param('companyId');
        let incidentType = req.param('incident_type') || '';
        let pageSize = +req.param('pageSize', 100);
        let pageNumber = +req.param('pageNumber', 0);
        let filter, countFilter;
        if (employerId) {
            filter = { where: {project_company: employerId, finalised: true}, sort: ['is_closed ASC', 'incident_date DESC'] };
        }
        if (incidentType) { filter.where.incident_type = incidentType; }
        countFilter = filter.where;
        if(pageSize) {
            filter.limit = pageSize;
            filter.skip = (pageSize * pageNumber);
        }

        sails.log.info('fetch all incident-reports with employerId:', employerId);
        let companyIncidentReports = await getIRecord(filter, true);

        sails.log.info('got incident reports.', companyIncidentReports.length);
        let allIrRecords = [];
        let resultArr = {};
        if (pageNumber == 0) {
            allIrRecords = await sails.models.projectincidentreport_reader.find({
                where: countFilter,
                select: ['project_ref']
            });
            /*
            let projectIds = allIrRecords.reduce((arr, record) => {
                arr.push(record.project_ref);
                return arr;
            }, []);

            resultArr = await getInductedUsersByProjectIds(_uniq(projectIds));*/
        }
        let openRecordCount = await sails.models.projectincidentreport.count({...countFilter, status: {in: [1, 2]}});
        return successResponse(res, {incident_reports: companyIncidentReports, total_record_count:allIrRecords.length, projects_inducted_users: resultArr, open_record_count:openRecordCount});
    },

    // @todo: Satyam Hardia: deprecated as of Jun 15, 2021. API and all related code should be removed by Sept 11, 2021.
    downloadIncidentReport: async (req, res) => {
        let irId = +req.param('irId');
        let updatedAt = +req.param('timestamp');
        let type = req.param('type');
        let companyId = +req.param('companyId', 0);

        let whereClause = {
            id: irId,
            updatedAt: updatedAt
        };

        return await processIRDownload(req, res, whereClause, companyId, type);
    },

    downloadIncidentReportV2: async (req, res) => {
        let irId = +req.param('irId');
        let type = req.body.type;
        let createdAt = +req.body.createdAt || 0;
        let companyId = +req.body.companyId || 0;

        let whereClause = {
            id: irId,
            createdAt: createdAt
        };

        return await processIRDownload(req, res, whereClause, companyId, type);
    },

    shareIncidentReport: async(req, res) => {
        let id = +req.param('id');
        let email = req.body.email;
        let projectId = req.body.projectId;
        let byUser = req.user;
        sails.log.info('Share Incident Report request by user', byUser.id, ' for project ', projectId);
        let incidentReport = await sails.models.projectincidentreport_reader.findOne({id: id});
        if(incidentReport && incidentReport.id) {
            incidentReport = await populateProjectRefs([incidentReport], 'project_ref', []);
            incidentReport = await populateUserRefs(incidentReport, 'user_ref', []);
            sails.log.info('Expanding Project Incident Reports.');
            let expandedProjectIR = await expandIncidentReports(incidentReport);
            incidentReport = (expandedProjectIR.length) ? expandedProjectIR[0] : incidentReport;

            let { project_logo_file, companyName } = await getCompanyInfo(incidentReport.project_ref, null);
            let html = await getIncidentReportHtml(incidentReport, project_logo_file, companyName);
            let attachmentName = 'Incident Report- #' + buildRecordRef(incidentReport) + ' - ' + moment().format('YYYY-MM-DD');
            successResponse(res, {message: 'The Incident report is being prepared and will be shared shortly.'})
            await shareReportViaEmail(req, res, html, 'incident-reports', attachmentName, "Incident", byUser, email, incidentReport.project_ref.name);
            return;
        }
    },

    closeOutIncidentAction: async (req, res) => {
        let irId = +req.param('irId');

        let itemInfo = _.pick((req.body.item_info || {}), [
            "id",
            "category",
            "action_detail",
            "tag_user_ref",
            "priority",
            "due_date",
            "close_out", //{images, details, reviewed_by, close_out_at}
            "assigned_by_user_ref"
        ]);

        itemInfo = cleanActionItem(itemInfo);

        let existingIncident = await sails.models.projectincidentreport_reader.findOne({
            where: {
                id: irId
            },
            select: ['project_ref', 'incident_actions'],
        });

        return processIncidentCloseoutAction(req, res, irId, existingIncident, itemInfo);
    },

    closeOutAssignedIncidentAction: async (req, res) => {
        let irId = +req.param('irId');

        let itemInfo = _.pick((req.body.item_info || {}), [
            "id",
            "category",
            "action_detail",
            "tag_user_ref",
            "priority",
            "due_date",
            "close_out", //{images, details, reviewed_by, close_out_at}
            "assigned_by_user_ref"
        ]);

        itemInfo = cleanActionItem(itemInfo);

        let existingIncident = await sails.models.projectincidentreport_reader.findOne({
            where: {
                id: irId
            },
            select: ['project_ref', 'incident_actions'],
        });

        let user = req.user;
        let isAllowedToCloseOut = (existingIncident.incident_actions || []).findIndex((action, index) => action.id == itemInfo.id && (
            action.tag_user_ref === user.id
        )) !== -1;

        if(!isAllowedToCloseOut) {
            sails.log.info('User not allowed to closed out close call.', user.id);
            return successResponse(res, {message: 'Unable to closeout, Access denied.'});
        }

        return processIncidentCloseoutAction(req, res, irId, existingIncident, itemInfo);
    },

    createOrUpdateIncidentAlertRecipients: async(req, res) => {
        let recipients = (req.body.alert_recipients || []).map(r => ({
            id: r.id,
            recipient_ref: (r.recipient_ref && r.recipient_ref.id) || r.recipient_ref,
            company_ref: r.company_ref,
            severity_level: r.severity_level
        }));

        for(const recipient of recipients) {
            console.log(recipient);
            if(recipient.id) {
                await sails.models.incidentalertpreference.updateOne({id: recipient.id}).set(recipient)
            } else {
                await sails.models.incidentalertpreference.create(recipient);
            }
        }
        return successResponse(res, {});
    },

    getIncidentAlertRecipients: async (req, res) => {
        let employerId = +req.param('employerId');
        let filter;
        if (employerId ) {
            filter = {company_ref:employerId};
        } else {
            return errorResponse(res, 'employerId is required');
        }

        let incidentAlertRecipients = await sails.models.incidentalertpreference_reader.find(filter);
        incidentAlertRecipients = await populateUserRefs(incidentAlertRecipients, 'recipient_ref', []);
        sails.log.info('got incident alert recipients.', incidentAlertRecipients.length);
        return successResponse(res, {incident_alert_recipients: incidentAlertRecipients});
    },

    deleteIncidentAlertRecipient: async(req, res) => {
        let recipientId = +req.body.recipient_id;
        sails.log.info('deleting Incident Alert Recipient with id: ', recipientId);
        await sails.models.incidentalertpreference.destroy({id: recipientId});
        return successResponse(res, {});
    },

    getIncidentActionsToCloseout: async (req, res) => {
        let projectId = +req.param('projectId');
        let userId = +req.param('userId');

        let incidentReports =  await sails.models.projectincidentreport_reader.find({
            where: {project_ref: projectId},
            select: ['id', 'createdAt', 'incident_actions']
        });

        let actions_to_closeout = [];
        (incidentReports || []).map(incidentReport => {
            (incidentReport.incident_actions || []).map(action => {
                if (action.tag_user_ref && (action.tag_user_ref == userId)
                    && action.close_out && !Object.keys(action.close_out).length) {
                    action.incident_id = incidentReport.id;
                    action.incident_created_at = incidentReport.createdAt;
                    actions_to_closeout.push(action);
                }
            });
        });

        sails.log.info(`Number of incident actions ${actions_to_closeout.length} needs to closeout on project ${projectId}`);
        return successResponse(res, {
            actions_to_closeout
        });
    },

    getProjectIncidentListSA: getProjectIncidentList,
    getProjectIncidentList: getProjectIncidentList,
    getCompanyIncidentList: getProjectIncidentList,
};
