const {
    ProcoreService: {
        constructSettingObject,
        getProjectSetting,
        projectHasProCoreSetting,
        createUsersTimeCardEntries,
        getAllPermissionTemplates,
    },
    ResponseService: {
        sendResponse,
        errorResponse,
        successResponse,
    },
    HttpService: {
        makeGET,
        makePOST,
    },
    TouchByteService: {
        getJSON,
    },
} = require('./../services');
const {
    COMPANY_SETTING_KEY,
    dbDateFormat_YYYY_MM_DD,
    PROJECT_SETTING_KEY: {PROCORE_INTEGRATION_PROJECT_INFO},
    AUTH_SERVER_URL, API_SERVER_URL, CALL_BACK_URL, PROCORE_APIs,
} = sails.config.constants;
const {
    baseUrl,
    PUBLIC_URL,
    CONTACT_MAIL_ADDRESS,
    PRO_CORE_APP_CLIENT_ID: APP_CLIENT_ID,
    PRO_CORE_APP_CLIENT_SECRET: APP_CLIENT_SECRET
} = sails.config.custom;

const dayjs = require('dayjs');
const _pick = require('lodash/pick');
const _groupBy = require('lodash/groupBy');
const _uniq = require('lodash/uniq');

let auth_redirect_url = state => `${AUTH_SERVER_URL}/${PROCORE_APIs.OAUTH_AUTHORIZE_URL}?response_type=code&client_id=${APP_CLIENT_ID}&redirect_uri=${baseUrl}${CALL_BACK_URL}&state=${state}`;

const quickEncode = (input) => Buffer.from(input).toString('base64');
const quickDecode = (input) => Buffer.from(input, 'base64').toString('ascii');

module.exports = {

    authorizeRedirect: async (req, res) => {
        let project_id = req.param('projectId');
        let user_id = req.param('userId');
        let payload = quickEncode(JSON.stringify({project: project_id, user: user_id}));
        sails.log.info('redirect to procore:', auth_redirect_url(payload));
        return res.redirect(302, auth_redirect_url(payload));
    },

    authCallback: async (req, res) => {
        let resultRoute = `${baseUrl}procore/auth/result`;
        let params = req.allParams();
        // let body = req.body;
        sails.log.info('Procore callback got', params);
        let stateDecoded = getJSON(quickDecode(params.state));
        if (!params.code || !stateDecoded.project) {
            return res.redirect(302, resultRoute + '?inndex_error=Validation error');
            // return errorResponse(res, 'Invalid request', {params, stateDecoded});
        }

        let tokenResponse = await makePOST(`${AUTH_SERVER_URL}/${PROCORE_APIs.OAUTH_TOKEN_URL}`, {
            "grant_type": "authorization_code",
            "client_id": APP_CLIENT_ID,
            "client_secret": APP_CLIENT_SECRET,
            "code": params.code,
            "redirect_uri": baseUrl + CALL_BACK_URL
        });
        sails.log.info('Response from procore n/w call', tokenResponse.status);
        let {error, error_description} = tokenResponse.data;
        // sails.log.info('Response from n/w call', tokenResponse.data);
        if (error) {
            return res.redirect(302, resultRoute + '?inndex_error=' + error_description);
        }


        let me = await makeGET(`${API_SERVER_URL}/${PROCORE_APIs.GET_ME_URL}`, {}, {
            Authorization: `Bearer ${tokenResponse.data.access_token}`
        });
        sails.log.info('Response from procore n/w call', me.status);
        if (me.error) {
            return res.redirect(302, resultRoute + '?inndex_error=Failed while authorizing');
        }

        let setting = {
            project_ref: stateDecoded.project,
            name: PROCORE_INTEGRATION_PROJECT_INFO,
            value: constructSettingObject(tokenResponse.data, {
                me: me.data,
                user_ref: stateDecoded.user,
                procore_meta: { // procore Target company & project id.
                    company: null,
                    project: null,
                }
            })
        };

        let existingSetting = await getProjectSetting(stateDecoded.project, PROCORE_INTEGRATION_PROJECT_INFO);
        if (existingSetting && existingSetting.id) {
            await sails.models.projectsetting.updateOne({id: existingSetting.id}).set({value: setting.value});
        } else {
            await sails.models.projectsetting.create(setting);
        }

        // let refreshed = await refreshProcoreOAuthToken(setting);
        // save project setting and redirect

        return res.redirect(302, resultRoute + '?inndex_success=true');
    },

    authCallbackResult: async (req, res) => {
        // res.ok((req.query.inndex_success || '').length ? `Finished` : 'Failed');
        let redirect_to = `${PUBLIC_URL}result-view?inndex_success=${(req.query.inndex_success || '').length ? `Finished` : 'Failed'}`;
        sails.log.info('redirecting to', redirect_to);
        return res.redirect(302, redirect_to);
    },

    getUserCompaniesList: async (req, res) => {
        let project_id = req.param('projectId');
        let existingSetting = await projectHasProCoreSetting(project_id);
        if (!existingSetting.id) {
            return sendResponse(res, existingSetting);
        }
        let {status: get_companies_status, data: companies} = await makeGET(`${API_SERVER_URL}/${PROCORE_APIs.GET_COMPANIES_URL}`, {}, {
            Authorization: `Bearer ${existingSetting.value.access_token}`
        });
        sails.log.info('Response from procore n/w call', get_companies_status);
        successResponse(res, {
            companies
        });
    },

    getCompanyProjectsList: async (req, res) => {
        let project_id = req.param('projectId');
        let procore_company_id = +req.param('proCoreCompanyId', 0); // 30814
        let existingSetting = await projectHasProCoreSetting(project_id);
        if (!existingSetting.id) {
            return sendResponse(res, existingSetting);
        }
        let {status: get_projects_status, data: projects} = await makeGET(`${API_SERVER_URL}/${PROCORE_APIs.GET_PROJECTS_URL}`, {
            company_id: procore_company_id,
            'filters[by_status]': 'Active'
        }, {
            Authorization: `Bearer ${existingSetting.value.access_token}`,
            'Procore-Company-Id': procore_company_id
        });
        sails.log.info('Response from procore n/w call', get_projects_status);

        successResponse(res, {
            projects,
        });
    },

    saveProcoreProjectReference: async (req, res) => {
        let project_id = req.param('projectId');
        let payload = _pick((req.body || {}), [
            'proCoreCompanyId',
            'proCoreProjectId',
        ]);
        if(!payload.proCoreCompanyId || !payload.proCoreProjectId){
            return errorResponse(res, 'Invalid request.');
        }
        let existingSetting = await projectHasProCoreSetting(project_id);
        if (!existingSetting.id) {
            return sendResponse(res, existingSetting);
        }

        const {me, access_token} = existingSetting.value;
        let {status, data: user_info_data, success} = await makeGET(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_USER_INFO(payload.proCoreProjectId, me.id)}`, {
        }, {
            Authorization: `Bearer ${access_token}`,
            'Procore-Company-Id': payload.proCoreCompanyId
        }, true, 10000);
        let is_employee = (user_info_data && user_info_data.id) ? user_info_data.is_employee : false;
        if(!is_employee){
            return successResponse(res, {
                warning: `In order to proceed with this integration, you need to mark yourself as an employee in the Procore Project Directory using the checkbox provided. If you have any issues doing this, please contact us at ${CONTACT_MAIL_ADDRESS}`,
            });
        }

        existingSetting.value.procore_meta = {
            company: +payload.proCoreCompanyId,
            project: +payload.proCoreProjectId,
        };
        // sails.log.info('old value was', existingSetting.value);
        // sails.log.info('new value will be', constructSettingObject(existingSetting.value, existingSetting.value));
        let updatedSetting = await sails.models.projectsetting.updateOne({id: existingSetting.id}).set({
            value: constructSettingObject(existingSetting.value, existingSetting.value)
        });
        sails.log.info('Saved procore project reference', updatedSetting.value.procore_meta);

        successResponse(res, {
            done: true,
        });
    },

    createUsersTimecardEntries: async (req, res) => {
        let project_id = +req.param('projectId', 0);
        let user_ids = (req.body.users || '').length ? (req.body.users || '').split(',') : [];
        let target_date = dayjs((req.body.date || '--'), dbDateFormat_YYYY_MM_DD);
        if (!project_id || !user_ids.length || !target_date.isValid()) {
            return errorResponse(res, 'user ids & date are required', {body: req.body});
        }
        let project = await sails.models.project_reader.findOne({where: {id: project_id}, select: ['id', 'name', 'parent_company', 'custom_field']});
        // fetch company setting
        let company_setting = await sails.models.companysetting_reader.findOne({
            select: ['id', 'company_ref', 'value'],
            where: {
                name: COMPANY_SETTING_KEY.PROCORE_CONFIG,
                company_ref: project.parent_company
            }
        });
        let companyConf = ((company_setting && company_setting.id) ? company_setting.value : {});
        let createdEntries = await createUsersTimeCardEntries(project_id, target_date, user_ids, companyConf, (project.custom_field || {}).timezone);
        if (createdEntries.error) {
            return sendResponse(res, createdEntries);
        }
        successResponse(res, {
            createdEntries
        });
    },

    getProjectPermissionTemplates : async (req, res) => {
        let project_id = req.param('projectId');
        let project = await sails.models.project_reader.findOne({
            select: ['id', 'name', 'is_active', 'custom_field'],
            where: {id: +project_id}
        });
        if (!project || !project.custom_field || (project.custom_field.disable && project.custom_field.disable.procore_permissions)) {
            sails.log.info(`Project ID: ${project_id} Not found OR project have procore_permissions disabled`);
            return successResponse(res, {templates: []});
        }
        let existingSetting = await projectHasProCoreSetting(project_id);
        if (!existingSetting.id) {
            return successResponse(res, {templates: []});
        }

        let procore_meta = (existingSetting.value && existingSetting.value.procore_meta) || {};
        let token = (existingSetting.value && existingSetting.value.access_token) || '';

        if(!procore_meta.project || !procore_meta.company){
            sails.log.info('getProjectPermissionTemplates: Procore project/company not set.');
            return errorResponse(res,'Please relink your procore project', {procore_meta});
        }

        let {status: get_permission_templates_status, data: templates, success} = await makeGET(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_PERMISSION_TEMPLATES(procore_meta.project)}`, {
            'filters[assignables_only]': true,
        }, {
            Authorization: `Bearer ${token}`,
            'Procore-Company-Id': procore_meta.company
        }, true, 15000);

        sails.log.info('Response from procore n/w call', get_permission_templates_status, success);
        successResponse(res, {
            templates
        });
    },
};
