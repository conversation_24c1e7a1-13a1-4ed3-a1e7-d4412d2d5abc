const _uniq = require('lodash/uniq');
const _groupBy = require('lodash/groupBy');
const {displayDateFormat_DD_MM_YYYY, dbDateFormat_YYYY_MM_DD} =  sails.config.constants;
const {
    UserRevisionService: {getLatestUserRevision},
    VehicleService: {
        fetchVehicleRegDetails,
    },
    ExcelService: {
        streamExcelDownload,
        downloadGateBookingsXLSX
    },
    EmailService: {
        sendMail
    },
    DataProcessingService: {
        getDistanceMatrixInfo,
        getUserFullName,
        deriveDistanceMatrixRegion,
        getDistanceMatrix
    },
    HttpService: { makePOST },
    TokenUtil: {
        allResourceAdminsWithOneOfDesignations,
        getCompanyInfo,
    },
    ResponseService,
    SharedService: {
        rectifyDateFormat,
        downloadPdfViaGenerator
    },
} = require('./../services');
const {
    GateBookingValidator: {
        createGateBookingRecord,
        updateGateBookingRecord,
    }
} = require('./../validators');
const moment = require('moment');

let sendEmail = async (subject, user_name, user_email, alert_type, project_name, nominated_manager_name, booking_date, booking_slot) => {
    try {
        let emailHtml = await sails.renderView('pages/mail/amend-or-delete-gate-booking', {
            title: subject,
            user_name,
            nominated_manager_name,
            alert_type,
            project_name,
            booking_date,
            booking_slot,
            layout: false
        });

        sails.log.info('Sending mail to', user_email);
        await sendMail(subject, [user_email], emailHtml);
    } catch(e) {
        sails.log.info('Failed to update send email', e);
    }
};

let sendEmailToDeliveryManager = async (subject, mail_body, delivery_manager_info, user_name, user_employer, gate_name, project_name, booking_date, booking_slot, blockInfo = null, booking_details) => {
    try {
        let emailHtml = await sails.renderView('pages/mail/mail-content', {
            title: subject,
            mail_body,
            user_name,
            user_employer,
            gate_name,
            project_name,
            booking_date,
            booking_slot,
            delivery_manager_info,
            user_employer,
            blockInfo,
            booking_details,
            layout: false
        });

        let dmUserInfo = await sails.models.user.findOne({
            where: {id: delivery_manager_info.user_ref},
            select: ['first_name', 'last_name', 'email']
        });

        sails.log.info('Sending mail to delivery manager', dmUserInfo.email);
        await sendMail(subject, [dmUserInfo.email], emailHtml);
    } catch(e) {
        sails.log.info('Failed to update send email', e);
    }
};

const bookingSlotsToDisplay = (booking_slots) => {
    //get start time and end time from all selected slots
    if (booking_slots.length > 1) {
        let prePart = booking_slots[0].split('–');
        let postPart = booking_slots[booking_slots.length-1].split('–').pop();
        return prePart[0].trim()+' – '+postPart.trim();
    }
    return (booking_slots.length) ? booking_slots[0] : '';
};

const replicateGateBookings = async (booking, projectIds) => {
    sails.log.info('Replicating gate booking on following project: ', projectIds);
    let parentGate = await sails.models.projectgate.findOne({
        where: {
            id: booking.gate_id
        },
        select: ['identifier']
    });

    let replicatedGates = await sails.models.projectgate.find({
        where: {
            identifier: parentGate.identifier,
            project_id: projectIds
        },
        select: ['id', 'project_id']
    });

    for (let j in replicatedGates) {
        booking.project_id = replicatedGates[j].project_id;
        booking.gate_id = replicatedGates[j].id;
        let payload = Object.assign({}, booking);
        delete payload.id;
        delete payload.createdAt;
        delete payload.updatedAt;
        await sails.models.projectgatebooking.create(payload);
    }
};

const updatedReplicatedBookings = async (booking, updateRequest) => {
    let gateSetting = (await sails.models.projectsetting.findOne({where: {project_ref: booking.project_id, name: 'gate_booking_setting', value: {'!=': null}}, select: ['value']})) || {};
    if (gateSetting && gateSetting.id && gateSetting.value.length && booking) {
        let projectIds = gateSetting.value;
        sails.log.info('Update replicated booking on following project: ', projectIds);

        let replicatedBookings = await sails.models.projectgatebooking.find({
            where: {
                identifier: booking.identifier,
                project_id: projectIds
            },
            select: ['id']
        });
        if (replicatedBookings.length) {
            for (let j in replicatedBookings) {
                let booking = replicatedBookings[j];
                await sails.models.projectgatebooking.updateOne({
                    id: booking.id,
                }).set(updateRequest);
            }
        }
    }
}

/**
This function will return status wording based on status type for gate booking;
*/
const buildStatusMessage = (status) => {
    if (status === 2) {
        return 'Approved';
    } else if (status === 0) {
        return 'Declined';
    } else {
        return 'Pending';
    }
}


const getDistanceAndEmissions = async(post_code, projectPostCode, country_code, vehicle_reg) => {
    let vehicleInfo = {};
    let distance = null;
    const region = deriveDistanceMatrixRegion(country_code);
    const origins = `${post_code} ${region}`;
    const destinations = `${projectPostCode} ${region}`;
    let distanceMatrix = await getDistanceMatrix(origins, destinations, region);
    if(distanceMatrix.some(d => d.elements[0].distance)) {
        distance = distanceMatrix[0].elements[0].distance.value;
        if(vehicle_reg) {
            vehicleInfo = await fetchVehicleRegDetails(vehicle_reg);
        }
    }
    return {vehicleInfo: vehicleInfo.vehicle, distance: distance};
};

const sendBookingStatusUpdateEmails = async(bookingRequest) => {
    let user = await sails.models.user_reader.findOne({
        where: {id: bookingRequest.userId},
        select: ['first_name', 'last_name', 'email']
    });
    let emailSubject = "Project Gate Booking: Booking accepted";
    let status = 'accepted';
    let declined_comment = '';
    if(bookingRequest.status === 0) {
        emailSubject = "Project Gate Booking: Booking rejected";
        status = 'declined';
        declined_comment = `<li>Comment: ${bookingRequest.declined_comment} </li>`;
    }
    let mail_content =
        `The following booking has been ${status} by ${bookingRequest.admin_name} @ ${bookingRequest.project}.
        <br><br>
        <ul>
        <li>Booking date: ${bookingRequest.booking_date}</li>
        <li>Booking slot: ${bookingRequest.booking_slot}</li>
        <li>Supplier: ${bookingRequest.supplier} </li>
        ${declined_comment}
        </ul>`;
    let emailHtml = await sails.renderView('pages/mail/fatigue-management', {
        title: emailSubject,
        recipient_name: user.first_name,
        mail_content,
        layout: false
    });
    sails.log.info('Sending mail to', user.email);
    await sendMail(emailSubject, [user.email], emailHtml);
    return;
};

const getAllDeliveryManager = async (projectId) => {
    sails.log.info(`get all delivery managers of project: ${projectId}`);
    let designatedProjectUsers = await allResourceAdminsWithOneOfDesignations(projectId, ['delivery_management'], false);

    return designatedProjectUsers.filter(pu => pu.flags && pu.flags.is_delivery_manager);
};

const getSortedBookingsObject = (bookingsObj, dateList) => {
    let sortedDateKeys = Object.keys(bookingsObj).sort((a, b) => {
        return dateList.indexOf(b) - dateList.indexOf(a);
    });
    let sortedBookingsObj = {};
    sortedDateKeys.forEach(key => {
        sortedBookingsObj[key] = bookingsObj[key];
    });
    return sortedBookingsObj;
};

const duplicateBookingCheck = async (bookingData, bookingId) => {
    let filter = {
        gate_id: bookingData.gate_id,
        project_id: bookingData.project_id,
        booking_date: bookingData.booking_date
    };
    filter = bookingId?  {...filter, id:{ '!=': bookingId }}: filter;
    let bookingsOfTheDay = await sails.models.projectgatebooking_reader.find(filter);
    for(let i=0; i<bookingData.booking_slots.length; i++) {
        let slot = bookingData.booking_slots[i];
        bookingsOfTheDay = (bookingsOfTheDay || []).filter(b => (b.booking_slots || []).includes(slot));
        if(bookingsOfTheDay.length) {
            return true;
        }
    }
    return false;
};

/*
@todo vishal: this fn can be removed in Jan 2025 after checking warn logs
*/
const getValidBookingDate = (req) => {
    let booking_date = (req.body.booking || {}).booking_date;
    if (booking_date && (moment(booking_date, displayDateFormat_DD_MM_YYYY, true).isValid())) {
        sails.log.warn(`Got booking_date in invalid date format.`);
        booking_date =  moment(booking_date, displayDateFormat_DD_MM_YYYY).format(dbDateFormat_YYYY_MM_DD);
    }
    return booking_date;
};

const createBookingFn = async(req, res) => {
        req.body.booking.booking_date = getValidBookingDate(req);
        sails.log.info('Create request for booking');
        let {validationError} = createGateBookingRecord(req);
        if(validationError){
            return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
        }
        let createRequest = _.pick((req.body.booking || {}), [
            'gate_id',
            'project_id',
            'user_id',
            'booking_date',
            'booking_slots',
            'duration',
            'booking_ref',
            'supplier',
            'contact_name',
            'contact_number',
            'fors_no',
            'fors_no_badge',
            'vehicle_reg',
            'vehicle_make_model',
            'haulage_company',
            'driver_name',
            'driver_number',
            'dispatch_post_code',
            'return_postcode',
            'materials',
            'booking_type',
            'drop_off_address',
            'waste_collection_company',
            'vehicle_type',
            'laden_percent',
            'co2_emission_gm',
            'handling_equipment',
            'blockDates',
            'block_dates_info',
            'status',
            'booking_details'
        ]);
        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: +createRequest.project_id},
            select: ['id', 'name', 'postcode', 'custom_field']
        });
        createRequest = await cleanFields(projectInfo, createRequest, createRequest.booking_type);
        let booking_slots = createRequest.booking_slots;
        let status = 1;
        let requestCopy = Object.assign({}, createRequest);
        let deliveryManagers = await getAllDeliveryManager(projectInfo.id);
        if(!projectInfo.custom_field.booking_approval_process) {
            status = 2;
            //check for duplicate booking
            let checkDuplication = await duplicateBookingCheck(createRequest);
            if(checkDuplication) {
                return ResponseService.errorResponse(res,'Duplicate request. Booking already available for the requested time slot.');
            }
        } else {
            let isDeliveryManager = deliveryManagers.find(d=> d.user_ref === createRequest.user_id);
            if (isDeliveryManager) {
                sails.log.info(`Auto approving booking request as user is delivery manager userId: ${req.user.id}`);
                status = 2;
                createRequest.status_details = {
                    "comment": null,
                    "admin_name": getUserFullName(req.user),
                    "userId": req.user.id,
                    "timestamp": moment().valueOf(),
                    "status": buildStatusMessage(status),
                };
            }
        }
        createRequest.status = status;
        createRequest.user_id = req.user.id;
        let revision = await getLatestUserRevision(createRequest.user_id);
        createRequest.user_revision_ref = revision.id;
        let blockDates = createRequest.blockDates;
        let deliveryNote = _.pick((req.body.deliveryInfo || {}), [
            'project_ref',
            'dn_supplier',
            'po_number',
            'delivery_ref_no',
            'dn_images',
            'dn_location',
            'dn_description',
            'dn_delivered_on',
            'data_type'
        ]);
        let deliveryInfoCopy = Object.assign({}, deliveryNote);
        if (Object.keys(deliveryNote).length) {
            deliveryNote.user_ref = req.user.id;
            deliveryNote.user_revision_ref = revision.id;
            if(!deliveryNote.data_type) {
                deliveryNote.data_type = createRequest.booking_type === 'delivery' ? 'delivery-note': 'collection-note';
                deliveryInfoCopy.data_type = deliveryNote.data_type;
            }
            sails.log.info('adding Delivery Note along with booking too ', deliveryNote);
            let delivery = await sails.models.deliverynote.create(deliveryNote);
            createRequest.delivery_notes_ref = delivery.id;
        }
        if(createRequest.dispatch_post_code) {
            let dispatch_distance_travelled = (createRequest.dispatch_distance_matrix && createRequest.dispatch_distance_matrix.distance && createRequest.dispatch_distance_matrix.distance.value) ? (+createRequest.dispatch_distance_matrix.distance.value / 1000) : 0;
            let return_distance_travelled = (createRequest.return_distance_matrix && createRequest.return_distance_matrix.distance && createRequest.return_distance_matrix.distance.value) ? (+createRequest.return_distance_matrix.distance.value / 1000) : 0;
            createRequest.distance_travelled = Math.floor(dispatch_distance_travelled + return_distance_travelled) || null;
            createRequest.vehicle_info = (createRequest.vehicle_reg) ? await fetchVehicleRegDetails(createRequest.vehicle_reg) : {};
        }
        sails.log.info('create booking with', createRequest);

        let booking = await sails.models.projectgatebooking.create(createRequest);
        //replicate booking based on identifier if gate setting available
        let projectIds = [];
        let gateSetting = (await sails.models.projectsetting.findOne({where: {project_ref: booking.project_id, name: 'gate_booking_setting', value: {'!=': null}}, select: ['value']})) || {};
        if (gateSetting && gateSetting.id && gateSetting.value.length) {
            projectIds = gateSetting.value;
            await replicateGateBookings(booking, projectIds);
        }
        if(blockDates && blockDates.length) {
            for(let d of blockDates) {
                let bookingObj = Object.assign({}, requestCopy);
                let deliveryObj = Object.assign({}, deliveryInfoCopy);
                deliveryObj.user_ref = req.user.id;
                deliveryObj.user_revision_ref = revision.id;
                if(createRequest.delivery_notes_ref) {
                    let delivery = await sails.models.deliverynote.create(deliveryObj);
                    bookingObj.delivery_notes_ref = delivery.id;
                }
                bookingObj.booking_date = d;
                bookingObj.booking_slots = booking_slots;
                bookingObj.status = status;
                booking = await sails.models.projectgatebooking.create(bookingObj);
                if (projectIds && projectIds.length && booking) {
                    await replicateGateBookings(booking, projectIds);
                }
            }
        }

        if(booking) {
            let emailInfo = _.pick((req.body.emailInfo || {}), [
                'user_id',
                'project_name',
                'booking_date',
                'booking_slot',
                'gate_name',
                // 'delivery_managers',
                'booking_details'
            ]);
            /*let {error: emailInfoError, input_date: emailInfoDate} = rectifyDateFormat(emailInfo.booking_date);
            if (emailInfoError) {
                sails.log.warn(`Invalid date provided while saving booking.`, emailInfoDate);
                // return ResponseService.errorResponse(res, 'Invalid date provided while saving booking.');
            }*/
            emailInfo.delivery_managers = deliveryManagers;
            if (Object.keys(emailInfo).length && emailInfo.delivery_managers.length) {
                sails.log.info('Sending mail to delivery manager.', emailInfo.delivery_managers);
                let userInfo = await sails.models.user.findOne({
                    where: {id: emailInfo.user_id},
                    select: ['first_name', 'last_name', 'email']
                });
                if (userInfo && userInfo.id) {
                    let user_name = userInfo.first_name + ' ' + userInfo.last_name;
                    let project_name = emailInfo.project_name;
                    let booking_date = emailInfo.booking_date;
                    let booking_slot = emailInfo.booking_slot;
                    let booking_details = emailInfo.booking_details;
                    let gate_name = emailInfo.gate_name;

                    //sending mail to delivery manager
                    sails.log.info('fetch employer details of user');
                    let employerInfo = await sails.models.userempdetail.findOne({
                        where: {user_ref: emailInfo.user_id},
                        select: ['employer']
                    });
                    let subject = `Delivery Booking - ${project_name}`;
                    let blockInfo = createRequest.block_dates_info && createRequest.block_dates_info.info ? createRequest.block_dates_info.info : null;
                    for (let i = 0; i < emailInfo.delivery_managers.length; i++) {
                        await sendEmailToDeliveryManager(subject, 'new-gate-booking', emailInfo.delivery_managers[i], user_name, employerInfo.employer, gate_name, project_name, booking_date, booking_slot, blockInfo, booking_details);
                    }
                }
            }

        sails.log.info('created booking successful, id', booking ? booking.id : undefined);
        return ResponseService.successResponse(res, {booking});
    };
};

const updateBookingFn = async(req, res) => {
    let bookingId = req.param('bookingId');
    sails.log.info('update booking request', bookingId);
    if (!bookingId) {
        return ResponseService.errorResponse(res, 'booking id is required');
    }
    req.body.booking.booking_date = getValidBookingDate(req);
    let {validationError} = updateGateBookingRecord(req);
    if(validationError){
        return ResponseService.errorResponse(res, 'Invalid Request.', {validationError});
    }
    let updateRequest = _.pick((req.body.booking || {}), [
        'booking_date',
        'booking_slots',
        'duration',
        'booking_ref',
        'supplier',
        'contact_name',
        'contact_number',
        'fors_no',
        'fors_no_badge',
        'vehicle_reg',
        'vehicle_make_model',
        'haulage_company',
        'driver_name',
        'driver_number',
        'dispatch_post_code',
        'return_postcode',
        'materials',
        'delivery_notes_ref',
        'amendment_log',
        'booking_closeout',
        'booking_type',
        'drop_off_address',
        'waste_collection_company',
        'vehicle_type',
        'laden_percent',
        'co2_emission_gm',
        'handling_equipment',
        'status',
        'booking_details'
    ]);
    let projectInfo = await sails.models.project_reader.findOne({
        where: {id: +req.body.booking.project_id},
        select: ['id', 'name', 'postcode', 'custom_field']
    });
    updateRequest = await cleanFields(projectInfo, updateRequest, updateRequest.booking_type);
    sails.log.info(`update booking with date: ${updateRequest.booking_date}, platform:`, req.headers['platform']);
    if(!projectInfo.custom_field.booking_approval_process) {
        updateRequest.status = 2;
        //check for duplicate booking
        let existingBooking = await sails.models.projectgatebooking_reader.findOne({where: {id: bookingId}, select: ['id', 'gate_id', 'project_id', 'booking_date']});
        updateRequest.gate_id = existingBooking.gate_id;
        updateRequest.project_id = existingBooking.project_id;
        let checkDuplication = await duplicateBookingCheck(updateRequest, bookingId);
        if(checkDuplication) {
            return ResponseService.errorResponse(res,'Duplicate request. Booking already available for the requested time slot.');
        }
    }
    let deliveryNote = _.pick((req.body.deliveryInfo || {}), [
        'dn_supplier',
        'id',
        'project_ref',
        'po_number',
        'delivery_ref_no',
        'dn_images',
        'dn_location',
        'dn_description',
        'dn_delivered_on',
        'data_type'
    ]);
    if (Object.keys(deliveryNote).length) {
        if(deliveryNote.id) {
            sails.log.info('adding Delivery Note along with booking too ', deliveryNote);
            let note = await sails.models.deliverynote.updateOne({id: deliveryNote.id}).set(deliveryNote);
            updateRequest.delivery_notes_ref = note.id;
        } else {
            deliveryNote.user_ref = req.user.id;
            let revision = await getLatestUserRevision(req.user.id);
            deliveryNote.user_revision_ref = revision.id;
            if(!deliveryNote.data_type) {
                deliveryNote.data_type = updateRequest.booking_type === 'delivery' ? 'delivery-note': 'collection-note';
            }
            sails.log.info('adding Delivery Note along with booking too ', deliveryNote);
            let delivery = await sails.models.deliverynote.create(deliveryNote);
            updateRequest.delivery_notes_ref = delivery.id;
        }
    }

    let replicateUpdateReq = Object.assign({}, updateRequest);
    if(updateRequest.dispatch_post_code) {
        let dispatch_distance_travelled = (updateRequest.dispatch_distance_matrix && updateRequest.dispatch_distance_matrix.distance && updateRequest.dispatch_distance_matrix.distance.value) ? (+updateRequest.dispatch_distance_matrix.distance.value / 1000) : 0;
        let return_distance_travelled = (updateRequest.return_distance_matrix && updateRequest.return_distance_matrix.distance && updateRequest.return_distance_matrix.distance.value) ? (+updateRequest.return_distance_matrix.distance.value / 1000) : 0;
        updateRequest.distance_travelled = Math.floor(dispatch_distance_travelled + return_distance_travelled) || null;
        updateRequest.vehicle_info = (updateRequest.vehicle_reg) ? await fetchVehicleRegDetails(updateRequest.vehicle_reg) : {};
    }
    let booking = await sails.models.projectgatebooking.updateOne({id: bookingId}).set(updateRequest);
    //update booking based on identifier if gate setting available
    await updatedReplicatedBookings(booking, replicateUpdateReq);

    let emailInfo = _.pick((req.body.emailInfo || {}), [
        'user_id',
        'project_name',
        'nominated_manager_name',
        'booking_date',
        'booking_slot',
        'gate_name',
        // 'delivery_managers',
        'booking_details'
    ]);
    /*let {error: emailInfoError, input_date: emailInfoDate} = rectifyDateFormat(emailInfo.booking_date);
    if (emailInfoError) {
        sails.log.warn(`Invalid date provided while saving booking.`, emailInfoDate);
        // return ResponseService.errorResponse(res, 'Invalid date provided while saving booking.');
    }*/
    if (Object.keys(emailInfo).length) {
        emailInfo.delivery_managers = await getAllDeliveryManager(projectInfo.id);
        sails.log.info('Sending mail to THE USER WHO MADE THE BOOKING.');
        let userInfo = await sails.models.user.findOne({
            where: {id: emailInfo.user_id},
            select: ['first_name', 'last_name', 'email']
        });
        if(userInfo && userInfo.id) {
            let subject = "Booking has been updated by Nominated Manager";
            let user_name = userInfo.first_name+' '+userInfo.last_name;
            let user_email = userInfo.email;
            let alert_type = 'amended';
            let project_name = emailInfo.project_name;
            let nominated_manager_name = emailInfo.nominated_manager_name;
            let booking_date = emailInfo.booking_date;
            let booking_slot = emailInfo.booking_slot;
            let booking_details = emailInfo.booking_details;
            let gate_name = emailInfo.gate_name;
            if(emailInfo.user_id != req.user.id) {
                await sendEmail(subject, user_name, user_email, alert_type, project_name, nominated_manager_name, booking_date, booking_slot);
            }

            //sending mail to delivery manager
            if (emailInfo.delivery_managers && emailInfo.delivery_managers.length) {
                sails.log.info('Sending mail to delivery manager.', emailInfo.delivery_managers);
                sails.log.info('fetch employer details of user');
                let employerInfo = await sails.models.userempdetail.findOne({
                    where: {user_ref: emailInfo.user_id},
                    select: ['employer']
                });

                let subject = `Delivery Amendment - ${project_name}`;
                for (let i=0; i < emailInfo.delivery_managers.length; i++) {
                    if(emailInfo.delivery_managers[i].user_ref != req.user.id) {
                        await sendEmailToDeliveryManager(subject, 'amend-gate-booking', emailInfo.delivery_managers[i], user_name, employerInfo.employer, gate_name, project_name, booking_date, booking_slot, booking_details);
                    }

                }
            }
        }
    }

    sails.log.info('update booking successful, id', booking ? booking.id : undefined);
    return ResponseService.successResponse(res, {booking});
};

const deleteBookingFn = async(req, res) => {
    let bookingId = req.param('bookingId');
    if (bookingId) {
        try {
            let deletedBooking = await sails.models.projectgatebooking.destroyOne({
                id: bookingId
            });

            //destroy replicated booking for selected project if available
            let gateSetting = (await sails.models.projectsetting.findOne({where: {project_ref: deletedBooking.project_id, name: 'gate_booking_setting', value: {'!=': null}}, select: ['value']})) || {};
            if (gateSetting && gateSetting.id && gateSetting.value.length) {
                let projectIds = gateSetting.value;
                sails.log.info('Deleting replicated bookings on following project: ', projectIds);

                if (projectIds.length) {
                    await sails.models.projectgatebooking.destroy({
                        where: {
                            project_id: projectIds,
                            identifier: deletedBooking.identifier,
                        }
                    });
                }
            }

            let emailInfo = _.pick((req.body || {}), [
                'user_id',
                'project_name',
                'nominated_manager_name',
                'booking_date',
                'booking_slot',
                'gate_name',
                // 'delivery_managers',
            ]);
            if(deletedBooking) {
                sails.log.info('Booking has been deleted.');
                if(Object.keys(emailInfo).length) {
                    emailInfo.delivery_managers = await getAllDeliveryManager(deletedBooking.project_id);
                    sails.log.info('Sending mail to THE USER WHO MADE THE BOOKING.');
                    let userInfo = await sails.models.user.findOne({
                        where: {id: emailInfo.user_id},
                        select: ['first_name', 'last_name', 'email']
                    });
                    let subject = "Booking has been deleted by Nominated Manager";
                    let user_name = userInfo.first_name+' '+userInfo.last_name;
                    let user_email = userInfo.email;
                    let alert_type = 'deleted';
                    let project_name = emailInfo.project_name;
                    let nominated_manager_name = emailInfo.nominated_manager_name;
                    let booking_date = emailInfo.booking_date;
                    let booking_slot = emailInfo.booking_slot;
                    let gate_name = emailInfo.gate_name;
                    if(emailInfo.user_id != req.user.id) {
                        await sendEmail(subject, user_name, user_email, alert_type, project_name, nominated_manager_name, booking_date, booking_slot);
                    }

                    if (emailInfo.delivery_managers && emailInfo.delivery_managers.length) {
                        sails.log.info('Sending mail to delivery manager.', emailInfo.delivery_managers);
                        sails.log.info('fetch employer details of user');
                        let employerInfo = await sails.models.userempdetail.findOne({
                            where: {user_ref: emailInfo.user_id},
                            select: ['employer']
                        });

                        let subject = `Delivery Booking Deleted - ${project_name}`;
                        for (let i=0; i < emailInfo.delivery_managers.length; i++) {
                            if(emailInfo.delivery_managers[i].user_ref != req.user.id) {
                                await sendEmailToDeliveryManager(subject, 'delete-gate-booking', emailInfo.delivery_managers[i], user_name, employerInfo.employer, gate_name, project_name, booking_date, booking_slot);
                            }
                        }
                    }
                }
                return ResponseService.successResponse(res, { message:'Booking has been deleted.' });
            }
        } catch (failure) {
            sails.log.info('Failed to delete booking', failure);
            return ResponseService.errorResponse(res, sails.__('Failed to delete booking.'), failure);
        }
    }
    return ResponseService.errorResponse(res, sails.__('Failed to delete booking, Booking Id is required.'));
};

const updateBookingStatusFn = async(req, res) => {
    let bookingId = req.param('bookingId');
    sails.log.info('approve booking request', bookingId);
    let rejectRequest = _.pick((req.body || {}), [
        'declined_comment',
        'booking_date',
        'booking_slot',
        'admin_name',
        'supplier',
        'project',
        'userId',
        'status'
    ]);
    let status_details = {
        "comment": rejectRequest.declined_comment || null,
        "admin_name": rejectRequest.admin_name,
        "userId": req.user.id,
        "timestamp": moment().valueOf(),
        "status": buildStatusMessage(rejectRequest.status),
    };
    let booking = await sails.models.projectgatebooking.updateOne({id: bookingId}).set({status: rejectRequest.status, "status_details": status_details});
    //update booking based on identifier if gate setting available
    await updatedReplicatedBookings(booking, {status: rejectRequest.status});
    await sendBookingStatusUpdateEmails(rejectRequest);
    sails.log.info('updated booking status successful, id', bookingId);
    return ResponseService.successResponse(res, {booking});
};

const cleanFields = async (project, data, booking_type) => {
    if(data.co2_emission_gm){
        data.co2_emission_gm = Math.round(+data.co2_emission_gm);
    }

    data.dispatch_distance_matrix = await getDistanceMatrixInfo(project, data.dispatch_post_code);

    if(booking_type === 'delivery' && data.return_postcode) {
        if(data.return_postcode === data.dispatch_post_code) {
            // saving external call
            data.return_distance_matrix = Object.assign({}, (data.dispatch_distance_matrix || {}));
        } else {
            data.return_distance_matrix = await getDistanceMatrixInfo(project, data.return_postcode);
        }
    } else if (booking_type === 'collection' && data.drop_off_address) {
        if(data.drop_off_address === data.dispatch_post_code) {
            // saving external call
            data.return_distance_matrix = Object.assign({}, (data.dispatch_distance_matrix || {}));
        } else {
            data.return_distance_matrix = await getDistanceMatrixInfo(project, data.drop_off_address);
        }
    }
    return data;
};

module.exports = {

    createBooking: createBookingFn,

    createBookingV2: createBookingFn,

    updateBooking: updateBookingFn,

    updateBookingV2: updateBookingFn,

    deleteBooking: deleteBookingFn,

    deleteBookingV2: deleteBookingFn,

    updateProjectGate: async (req, res) => {
        let gateId = req.param('gateId');
        sails.log.info('update project gate request', gateId);
        let updateRequest = _.pick((req.body || {}), [
            'route_map_file_id',
        ]);
        sails.log.info('update project gate with', updateRequest);
        let projectGate = await sails.models.projectgate.updateOne({id: gateId}).set(updateRequest);
        if (projectGate) {
            //update gates based on identifier if gate setting available
            let gateSetting = (await sails.models.projectsetting.findOne({where: {project_ref: projectGate.project_id, name: 'gate_booking_setting', value: {'!=': null}}, select: ['value']})) || {};
            if (gateSetting && gateSetting.id && gateSetting.value.length) {
                let projectIds = gateSetting.value;
                sails.log.info('Update replicated gate on following project: ', projectIds);

                let replicatedGates = await sails.models.projectgate.find({
                    where: {
                        identifier: projectGate.identifier,
                        project_id: projectIds
                    },
                    select: ['id']
                });
                if (replicatedGates.length) {
                    for (let j in replicatedGates) {
                        let gate = replicatedGates[j];
                        await sails.models.projectgate.updateOne({
                            id: gate.id,
                        }).set(updateRequest);
                    }
                }
            }
            sails.log.info('update gate successful, id', projectGate ? projectGate.id : undefined);
            return ResponseService.successResponse(res, {project_gate: projectGate});
        }

        sails.log.info('Failed to update project gate.');
        return ResponseService.errorResponse(res, sails.__('internal server error'), 'Failed to update project gate.');
    },

    exportBookings: async (req, res) => {
        let projectId = +req.param('projectId');
        let selectedDate = req.body.selected_date;
        let fromDate = req.body.from_date;
        let rawRecords = [];
        let start = moment(fromDate, dbDateFormat_YYYY_MM_DD)
        let end = moment(selectedDate, dbDateFormat_YYYY_MM_DD);
        let list = [];
        for (let current = start; current <= end; current.add(1, 'd')) {
            list.push(current.format(dbDateFormat_YYYY_MM_DD))
        }
        let filter = {project_id: projectId, status: 2, gate_id: {'!=': null}, booking_date: {'in': list} }
        rawRecords =  await sails.models.projectgatebooking_reader.find({
                where: filter,
            }).populate('delivery_notes_ref');
        sails.log.info('Found bookings to download', rawRecords.length);

        if (rawRecords.length) {
            //convert time slots to display time slots and sort by it
            rawRecords = (rawRecords || []).map(booking => {
                booking.slots_to_display = bookingSlotsToDisplay(booking.booking_slots);
                booking.deliveryRefNo = booking.delivery_notes_ref?  booking.delivery_notes_ref.delivery_ref_no : '';
                booking.poNumber = booking.delivery_notes_ref? booking.delivery_notes_ref.po_number : '';
                return booking;
            }).sort((a,b) => (a.slots_to_display > b.slots_to_display) ? 1 : ((b.slots_to_display > a.slots_to_display) ? -1 : 0));

            let recordsGroupedByGate = _groupBy(rawRecords, (l) => l.gate_id);
            let recordsGroupedByDate = _groupBy(rawRecords, (l) => l.booking_date);
            recordsGroupedByDate = getSortedBookingsObject(recordsGroupedByDate, list);
            let gateIds = Object.keys(recordsGroupedByGate);
            let gatesInfo = await sails.models.projectgate.find({
                where: {id: gateIds},
                select: ['gate_name']
            });
            sails.log.info('Found gates, ', gatesInfo.length);

            // Format date based on user preference
            recordsGroupedByDate = Object.entries(recordsGroupedByDate).reduce((result, [key, value]) => {
                const formattedDate = moment(key).format(res.locals.__('displayDateFormat_dash_DD_MM_YYYY'));
                result[formattedDate] = value;
                return result;
            }, {});

            //get user ids to prepare submitted by info
            let userIds = (rawRecords || []).reduce((arr, record) => {
                arr.push(record.user_id);
                return arr;
            }, []);

            let usersInfo = await sails.models.user.find({
                where: {id: _uniq(userIds)},
                select: ['first_name', 'middle_name', 'last_name', 'email']
            });
            sails.log.info('Found users as submitted by, ', usersInfo.length);

            let userEmployers = await sails.models.userempdetail.find({
                where: {user_ref: _.uniq(userIds)},
                select: ['employer', 'user_ref']
            });
            let projectInfo = await sails.models.project_reader.findOne({ where: {id: projectId}, select:['is_fors_compliant'] });
            let workbook = await downloadGateBookingsXLSX(recordsGroupedByDate, gatesInfo, usersInfo, userEmployers, selectedDate, projectInfo.is_fors_compliant);
            return streamExcelDownload(res, workbook);
        }
        sails.log.info('No bookings available on the selected date');
        return ResponseService.errorResponse(res, 'No bookings available on the selected date');
    },

    updateBookingStatus: updateBookingStatusFn,

    updateBookingStatusV2: updateBookingStatusFn,

    updateMultipleBookingsStatus: async(req, res) => {
        //let bookingId = req.param('bookingId');
        sails.log.info('update multiple booking status request');
        let updateRequest = _.pick((req.body || {}), [
            'declined_comment',
            'admin_name',
            'project',
            'status',
            'bookingIds'
        ]);
        console.log("updateRequest ", updateRequest)
        let status_details = {
            "comment": updateRequest.declined_comment || null,
            "admin_name": updateRequest.admin_name,
            "userId": req.user.id,
            "timestamp": moment().valueOf(),
            "status": buildStatusMessage(updateRequest.status)
        };
        let bookings = await sails.models.projectgatebooking.update({id: updateRequest.bookingIds}).set({status: updateRequest.status, "status_details": status_details});
       // update booking based on identifier if gate setting available
        for(let booking of bookings) {
            await updatedReplicatedBookings(booking, {status: updateRequest.status});
            let bookingRequest = {
                admin_name: updateRequest.admin_name,
                declined_comment: updateRequest.declined_comment,
                project: updateRequest.project,
                booking_date: booking.booking_date,
                booking_slot: bookingSlotsToDisplay(booking.booking_slots),
                supplier: booking.supplier,
                status: updateRequest.status,
                userId: booking.user_id

            };
            await sendBookingStatusUpdateEmails(bookingRequest);
        }

        sails.log.info('updated multiple booking status request');
        return ResponseService.successResponse(res, {bookings});
    },

    exportBookingsReportPDF: async(req, res) => {
        let projectId = req.param('projectId');
        let selectedDate = req.body.selected_date;
        let fromDate = req.body.from_date;
        let rawRecords = [];
        let start = moment(fromDate, dbDateFormat_YYYY_MM_DD)
        let end = moment(selectedDate, dbDateFormat_YYYY_MM_DD);
        let list = [];
        for (let current = start; current <= end; current.add(1, 'd')) {
            list.push(current.format(dbDateFormat_YYYY_MM_DD))
        }
        let filter = {project_id: projectId, status: 2, gate_id: {'!=': null}, booking_date: {'in': list} }
        rawRecords =  await sails.models.projectgatebooking_reader.find({
                where: filter,
            }).populate('delivery_notes_ref');
        sails.log.info('Found bookings to download', rawRecords.length);

        if (rawRecords.length) {
            let createdByIds = [];
            let users = [];
            let employers = [];
            rawRecords = (rawRecords || []).map(booking => {
                booking.slots_to_display = bookingSlotsToDisplay(booking.booking_slots);
                createdByIds.push(booking.user_id);

                return booking;
            }).sort((a,b) => (a.slots_to_display > b.slots_to_display) ? 1 : ((b.slots_to_display > a.slots_to_display) ? -1 : 0));

            if (createdByIds.length) {
                users = await sails.models.user_reader.find({
                    where: {id: createdByIds},
                    select: ['first_name', 'middle_name', 'last_name']
                });

                employers = await sails.models.userempdetail_reader.find({
                    where: {user_ref: _.uniq(createdByIds)},
                    select: ['employer', 'user_ref']
                });
            }
            rawRecords.map(booking => {
                let user_ref = users.find(user => booking.user_id == user.id);
                if (user_ref && user_ref.id) {
                    booking.created_by_user = user_ref;
                    booking.created_by_user.user_emp = employers.find(employer => user_ref.id == employer.user_ref);
                }
                return booking;
            });
            let bookingsByGate = _groupBy(rawRecords, (l) => l.gate_id);
            let gateIds = Object.keys(bookingsByGate);
            let gatesInfo = await sails.models.projectgate_reader.find({
                where: {id: gateIds},
                select: ['gate_name']
            });
            sails.log.info('Found gates, ', gatesInfo.length);
            let recordsGroupedByDate = _groupBy(rawRecords, (l) => l.booking_date);
            recordsGroupedByDate = getSortedBookingsObject(recordsGroupedByDate, list);
            let projectInfo = await sails.models.project_reader.findOne({
                where: {id: projectId},
                select: ['id', 'name', 'project_number', 'contractor', 'parent_company', 'custom_field']
            });
            let { project_logo_file, companyName } = await getCompanyInfo(projectInfo);
            let title = 'Transport Management Booking List';
            let project_line = `${(projectInfo.project_number != null) ? projectInfo.project_number + ' - ' + projectInfo.name : projectInfo.name} (#${projectInfo.id}): ${projectInfo.contractor}`;
            let date_line = `Report Exported: ${moment().format(res.locals.__('displayFullDateFormat_dash_DD_MM_YYYY_HH_mm_ss'))}`;
            let file_name = `Transport Management Booking List-${selectedDate}`;

            let form_template = `pages/gate-booking/gate-booking`;
            let html = await sails.renderView(form_template, {
                title,
                date_line,
                recordsGroupedByDate: recordsGroupedByDate,
                gatesInfo: gatesInfo,
                dateFormat: res.locals.__('displayDateFormat_dash_DD_MM_YYYY'),
                project_logo_file: project_logo_file,
                getUserFullName(userInfo) {
                    return getUserFullName(userInfo);
                },
                getGateGroupedRecords(records) {
                    return _groupBy(records, (l) => l.gate_id);
                },
                getGateName(gateId) {
                    let gateInfo = (gatesInfo || []).find(gate => (gate.id == gateId));
                    console.log("found gate", gateId ,gateInfo)
                    return gateInfo.gate_name;
                },
                momentT(n, format) {
                    return moment(n).format(format);
                },
                layout: false
            });

            return await downloadPdfViaGenerator({
                req,
                res,
                html,
                tool: 'booking-list',
                file_name,
                heading_line: title,
                project_line,
                date_line,
                logo_file: project_logo_file,
                has_cover: true,
                has_one_page: true,
                responseType: 'url'
            });
        }
        sails.log.info('No bookings available on the selected date');
        return ResponseService.errorResponse(res, 'No bookings available on the selected date');
    },

}
