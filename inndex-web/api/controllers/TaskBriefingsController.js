const moment = require('moment');
const momentTz = require('moment-timezone');
const {DEFAULT_PAGE_SIZE} = sails.config.constants;
const {
    UserRevisionService: {getLatestUserRevision},
    ResponseService,
    DataProcessingService: {
        getUserFullName,
        getProjectTimezone,
        expandToolboxTalks,
        sendMailToNM,
        getShowAlternativeUserList,
        sendToolboxTalksInvitation,
        populateUserRefs,
        populateUserFileRefs,
        populateEmployerRefs,
        populateProjectRefs,
        buildBriefingToolStatusMessage,
        briefingToolTables,
        populateInductionEmployerRef,
        saveToolBriefing,
        convertWordDocToPdf,
    },
    ResponseService: {
        errorResponse,
        successResponse,
        sendResponse
    },
    EmailService: {
        sendMail
    },
    ExcelService: {
        streamExcelDownload,
        taskBriefingReport,
        downloadToolboxTalkXLSX,
        downloadRegisterXLSX,
    },
    TokenUtil: {
        hasOnlySiteManagementAccess,
        getCompanyInfo,
    },
    SharedService: {
        getCountryCodeOfProject,
        downloadPdfViaGenerator,
        mergePdfsViaUrls,
    },
    NotificationService: {
        NOTIFICATION_CATEGORY,
        sendInviteNotification
    },
    HttpService: {
        decodeURIParam
    }
} = require('./../services');
const _uniq = require('lodash/uniq');
const {
    toolBriefingsFn,
    inductionFn: {getUserInductionEmployer, getInductionEmployerByUserIds},
    companyFn:{getTaggedOwnersList},
} = require('./../sql.fn');

const briefingToolModelMaps = {
    'rams': 'projectrams_reader',
    'task_briefings': 'projecttaskbriefings_reader',
    'toolbox_talks': 'toolboxtalks_reader',
    'work_package_plan': 'projectworkpackageplans_reader',
    'take_5s': 'take5s_reader',
};

const getTaskBriefings = async (filter, pageNumber=0, pageSize= DEFAULT_PAGE_SIZE, download = 'false', briefed_by_user_ref = 0, sortKey = 'id', sortDir = 'DESC') => {
    let total_record_count = 0;

    let countFilter = filter;
    if(pageSize && download != 'true') {
        filter = {
            where: filter,
            skip : pageNumber * pageSize,
            limit: pageSize
        }
    }

    let projectTaskBriefings = await sails.models.projecttaskbriefings_reader.find(filter)
        .sort([
            {[sortKey]: sortDir}
        ]);
        // .populate('briefing_file_ref')
        // .populate('tagged_owner');
    projectTaskBriefings = await populateProjectRefs(projectTaskBriefings, 'project_ref', []);
    projectTaskBriefings = await populateUserRefs(projectTaskBriefings, 'user_ref', []);
    projectTaskBriefings = await populateUserFileRefs(projectTaskBriefings, 'briefing_file_ref', []);
    projectTaskBriefings = await populateEmployerRefs(projectTaskBriefings, 'tagged_owner', []);

    if(pageSize && download != 'true') {
        total_record_count = await sails.models.projecttaskbriefings_reader.count(countFilter)
    }
    return await expandToolboxTalks(filter.project_ref, {}, projectTaskBriefings, 'task_briefings', briefed_by_user_ref, total_record_count);
};

const processToDownloadTaskBriefing = async (req, res, whereClause, projectId, type) => {
    let taskBriefing =  await sails.models.projecttaskbriefings.findOne(whereClause)
        .populate('briefing_file_ref')
        .populate('user_ref');
    if (!taskBriefing || !taskBriefing.id) {
        return errorResponse(res, sails.__('Failed to get task briefing.'));
    }

    let project = await sails.models.project.findOne({where: {id: projectId}});
    let tbPhrase = project.custom_field.tb_phrase_singlr;
    sails.log.info('got record, id', taskBriefing.id);
    let { project_logo_file, companyName } = await getCompanyInfo(project);

    let tz = getProjectTimezone(project);
    taskBriefing.project_ref = project;
    let {expanded_briefings, received_briefings} = await expandToolboxTalks(projectId, {},[taskBriefing], 'task_briefings');
    let registers = expanded_briefings[0].register;
    const docName = taskBriefing.briefing_file_ref && taskBriefing.briefing_file_ref.name;
    const docLink = taskBriefing.briefing_file_ref && taskBriefing.briefing_file_ref.file_url;

    let form_template = `pages/toolboxtalk-form-page`;
    let html = await sails.renderView(form_template, {
        title: tbPhrase,
        item_number: taskBriefing.record_id,
        item_title: taskBriefing.briefing_title,
        revision_number: '',
        report_number: `${tbPhrase} No. ${taskBriefing.record_id}`,
        doc_name: docName,
        doc_link: docLink,
        project_logo_file: project_logo_file,
        project: project,
        registers: registers,
        momentTz(n, format) {
            return momentTz(+n).tz(tz).format(format);
        },
        layout: false
    });

    if(type === 'html') {
        sails.log.info('Rendering html view');
        return res.send(html);
    }

    let file_name = `${tbPhrase} Report-${moment().format('YYYY-MM-DD')}`;
    let project_line = `${project.name}`;
    let date_line = `${tbPhrase} No. ${taskBriefing.record_id}`;

    const taskBriefingsPdf = await downloadPdfViaGenerator({
        req,
        res,
        html,
        tool: 'task-briefings',
        file_name,
        heading_line: tbPhrase,
        project_line,
        date_line,
        logo_file: project_logo_file,
        has_cover: true,
        has_one_page: true,
        responseType: 'path'
    });
    const pdfFiles =  [docLink, taskBriefingsPdf.location];
    const mergedFile = await mergePdfsViaUrls(req, res, pdfFiles, file_name);
    return ResponseService.successResponse(res, mergedFile)
};

// get briefing register
const getToolBriefingsList = async (req, res) => {
    let toolKey = req.param('toolKey', null);
    let projectId = +req.param('projectId', 0);
    let pageSize = +req.param('pageSize', 50);
    let pageNumber = +req.param('pageNumber', 0);
    let sortKey = req.param('sortKey', 'briefed_at');
    let sortDir = req.param('sortDir', 'desc');
    let briefingsType = req.param('briefingsType', 'given');

    let hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);

    let defaultResponse = {
        briefings: [],
        totalCount: 0,
        projectId,
        pageNumber,
        pageSize,
        sortKey,
        sortDir,
        toolKey,
        briefingsType,
        hasOnlySiteManagement
    };
    let userEmployer
    if(hasOnlySiteManagement) {
        userEmployer = await getUserInductionEmployer(req.user, projectId);
    }

    let {
        total: totalCount,
        records: briefings
    } = await toolBriefingsFn.getProjectToolBriefings(projectId, pageSize, (pageSize * pageNumber), sortKey, sortDir, {
        briefingsType,
        toolKey,
        ...(toolKey === 'rams' ? (briefingsType !== 'all' ? { userId: req.user.id } : {}) : { userId: req.user.id }),
        toolTable: briefingToolTables[toolKey],
        ...(briefingsType === 'all' && { hasOnlySiteManagement, userEmployer })
      });

    if (!briefings.length) {
        sails.log.info(`No briefing found, for projectId: ${projectId}, tool: ${toolKey}, briefingsType: ${briefingsType} pageNumber: ${pageNumber} pageSize: ${pageSize} totalCount: ${totalCount}`);
        return ResponseService.successResponse(res, {...defaultResponse, totalCount});
    }

    let toolRecordFileRefs = briefings.map(b => b.tool_record_file_ref).filter(id => id);
    let record_file_refs = await toolBriefingsFn.expandToolRecordFileRefs(toolRecordFileRefs);

    for (let i = 0; i < briefings.length; i++) {
        briefings[i].tool_record_file_ref = (record_file_refs || []).find(f => f.id === briefings[i].tool_record_file_ref) || {};
    }

    return ResponseService.successResponse(res, {
        ...defaultResponse,
        briefings,
        totalCount,
    });
};

module.exports = {

    createTaskBriefing: async (req, res) => {
        sails.log.info('Create task briefing for project, by', req.user.id);
        let createRequest = _.pick((req.body || {}), [
            'briefing_title',
            'briefing_file_ref',
            'is_available',
            'tagged_owner',
            'convert_doc_to_pdf'
        ]);

        createRequest.user_ref = req.user.id;
        createRequest.project_ref = +req.param('projectId');

        if(createRequest.user_ref){
            let revision = await getLatestUserRevision(createRequest.user_ref);
            createRequest.user_revision_ref = revision.id;
        }

        if (createRequest.convert_doc_to_pdf) {
            createRequest.briefing_file_ref = await convertWordDocToPdf(+createRequest.briefing_file_ref, req, 'task-briefings');
            if (!createRequest.briefing_file_ref) {
                return errorResponse(res, 'An error occurred during the conversion of the document to PDF.');
            }
        }
        sails.log.info('creating task briefings with', createRequest);
        let taskBriefing = await sails.models.projecttaskbriefings.create(createRequest);
        if(taskBriefing && taskBriefing.id)  {
            let projectInfo = await sails.models.project.findOne({where: {id: taskBriefing.project_ref}, select: ['name', 'custom_field']});
            let tbPhrase = projectInfo.custom_field.tb_phrase_singlr;
            let project_name = projectInfo.name;
            let userInfo = await sails.models.user.findOne({
                select: ['first_name', 'last_name'],
                where:{
                    id: taskBriefing.user_ref
                }
            });

            let taskBriefingUser = userInfo.first_name +' '+ userInfo.last_name;

            let typeTitle = '';
            let mailSubject = tbPhrase+' on '+project_name+ ' project';
            let mailBodyText = `A ${tbPhrase} for project: ${project_name} has been added by ${taskBriefingUser}`;
            await sendMailToNM(taskBriefing, typeTitle, mailSubject, mailBodyText);

            sails.log.info('Successfully created task briefing.')
            return successResponse(res,  {task_briefing: taskBriefing});
        }
        sails.log.error('Failed to create task briefing.')
        return errorResponse(res, 'Failed to create task briefing.');
    },

    updateTaskBriefing: async (req, res) => {
        let id = +req.param('id');
        sails.log.info('Update task briefing request.', id);

        let updateRequest = _.pick((req.body || {}), [
            'briefing_title',
            'briefing_file_ref',
            'is_available',
            'tagged_owner',
        ]);

        sails.log.info('Updating task briefing with: ', updateRequest);
        let taskBriefing = await sails.models.projecttaskbriefings.updateOne({id: id}).set(updateRequest);
        sails.log.info('Updated task briefing successfully, id', taskBriefing.id);
        return successResponse(res, {task_briefing: taskBriefing});
    },

    searchBriefings: async (req, res) => {
        let q = (req.query.q || '').toString().trim();
        let projectId = +req.param('projectId');

        sails.log.info(`Search Task Briefing., q: '${q}'`);

        let filter = { is_available:1, project_ref:projectId, or : [{briefing_title: {contains: q}}]};

        let taskBriefings = await sails.models.projecttaskbriefings.find(filter)
            .sort([
                {briefing_title: 'ASC'},
                {id: 'ASC'},
            ]).limit(50);

        sails.log.info('got task briefing, total', taskBriefings.length);
        return successResponse(res, {task_briefings: taskBriefings});
    },

    getProjectTaskBriefings: async (req, res) => {
        let projectId = +req.param('projectId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let search = req.param('search');
        let tagged_owner = req.param('tagged_owner') ? req.param('tagged_owner').split(',').map(a=>+a): [];
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');
        let sortDir = req.param('sortDir', 'desc');
        let sortKey = req.param('sortKey', 'id');
        let filter = { project_ref:projectId, };
        if(tagged_owner.length){
            filter.tagged_owner = {in:tagged_owner}
        }
        let ids = [];
        if(search && search !== 'null') {
            search = decodeURIParam(search);
            let briefingssSearch = await sails.models.projecttaskbriefings_reader.find({
                where: filter,
                select: ['briefing_title', 'tagged_owner', 'user_ref', 'record_id']
            });//.populate('tagged_owner');

            briefingssSearch = await populateEmployerRefs(briefingssSearch, 'tagged_owner', ['id', 'name']);
            briefingssSearch = await populateUserRefs(briefingssSearch, 'user_ref', []);
            briefingssSearch.map(d => {
                if(
                     (d.briefing_title && d.briefing_title.toLowerCase().indexOf(search) !== -1) ||
                     (d.user_ref && d.user_ref.first_name && d.user_ref.first_name.toLowerCase().indexOf(search) !== -1) ||
                     (d.user_ref && d.user_ref.middle_name && d.user_ref.middle_name.toLowerCase().indexOf(search) !== -1) ||
                     (d.user_ref && d.user_ref.name && d.user_ref.name.toLowerCase().indexOf(search) !== -1) ||
                     (d.tagged_owner && d.tagged_owner.name && d.tagged_owner.name.toLowerCase().indexOf(search) !== -1) ||
                     (d.record_id && d.record_id.includes(search)) || !search

                 ){
                     ids.push(d.id)
                }
             });
             filter = {
                id: ids,
                project_ref: projectId
            };
        }
        let taggedOwnersList = [];
        if(pageNumber == 0 && search == 'null' && !tagged_owner.length){
            taggedOwnersList = await getTaggedOwnersList('project_task_briefings', projectId);
        }

        sails.log.info('fetch all task-briefings with filter:', filter, `is_inherited_project: ${is_inherited_project}`);
        let {expanded_briefings: projectTaskBriefings, total_record_count} = await getTaskBriefings(filter, pageNumber, pageSize, false, 0, sortKey, sortDir);
        // Converting briefing.register structure from [{user_ref: 2}] to [2].
        projectTaskBriefings.map(tb => {
            tb.register.map(briefing => {
                briefing.register = briefing.register.map(r => r.user_ref);
                return briefing;
            });
            return tb;
        });
        return successResponse(res, {
            task_briefings: projectTaskBriefings,
            total_record_count,
            employerList: [],    // key can be removed after June 2024
            taggedOwnersList
        });
    },

    getProjectTaskBriefingsByUser: async (req, res) => {
        let userId = +req.param('userId');
        let projectId = +req.param('projectId');
        let filter = {
            project_ref: projectId,
            is_available: 1,
            briefed_count: { '!=' : null }
        };

        // @todo: this needs to be paginated.
        let {expanded_briefings: taskBriefings, received_briefings} = await getTaskBriefings(filter,0 ,500, false, userId);
        return successResponse(res, {task_briefings: taskBriefings, received_briefings});
    },

    downloadTaskBriefing: async (req, res) => {
        let id = +req.param('id');
        let updatedAt = +req.param('timestamp');
        let type = req.param('type','html');
        let projectId = +req.param('projectId');

        sails.log.info('Render Task Briefing:', id, `type: ${type} updatedAt: ${updatedAt} `);

        let whereClause = {
            id: id,
            updatedAt: updatedAt
        };

        return await processToDownloadTaskBriefing(req, res, whereClause, projectId, type);
    },

    downloadTaskBriefingV1: async (req, res) => {
        let id = +req.param('id');
        let projectId = +req.param('projectId');
        let createdAt = +req.body.createdAt || 0;
        let type = req.body.type;

        sails.log.info('Render Task Briefing:', id, `type: ${type} createdAt: ${createdAt} `);

        let whereClause = {
            id: id,
            createdAt: createdAt
        };

        return await processToDownloadTaskBriefing(req, res, whereClause, projectId, type);
    },

    inviteToTaskBriefing: async(req, res) => {
        sails.log.info('invite task briefing for project, by', req.user.first_name);
        let projectId = +req.param('projectId');
        let inviteRequest = _.pick((req.body || {}), [
            'usersToInvite'
        ]);
        inviteRequest.items = req.body.taskBriefings;
        let user_name = getUserFullName(req.user);
        let project = await sails.models.project_reader.findOne({
            select: ['name', 'custom_field'],
            where: {id: projectId}
        });
        inviteRequest.items = await sails.models.projecttaskbriefings_reader.find({ where: {id: inviteRequest.items}, select: ['id', 'briefing_title']});
        let tbPhrase = project.custom_field.tb_phrase_singlr;
        const category = NOTIFICATION_CATEGORY.TASKBRIEFING_INVITATION;
        let data = {
            category: category,
            project_ref: project.id,
            invitedBy: user_name
        };
        let firebaseMsgData = Object.assign({}, data);
        firebaseMsgData.project_ref = project.id.toString();
        for(let i=0; i<inviteRequest.usersToInvite.length; i++) {
            let { user_ref: userToInvite } = inviteRequest.usersToInvite[i];
            for(let j=0; j<inviteRequest.items.length; j++) {
                const item = inviteRequest.items[j];
                let { briefing_title, id: briefingId} = item;
                data.taskbriefingId = briefingId;
                firebaseMsgData.taskbriefingId = briefingId.toString();
                sails.log.info('Sending push notification for task briefing invite for user ', userToInvite);
                await sendInviteNotification(tbPhrase, briefing_title, project, userToInvite, req.user, category, data, firebaseMsgData);
            }
        }
        let subject = `${tbPhrase} Invite - Project ${project.name}`;
        await sendToolboxTalksInvitation(inviteRequest, project, subject, user_name, tbPhrase);
        return successResponse(res, {status: 'Invites sent successfully!'});
    },

    downloadTaskBriefingReport: async (req, res) => {
        let projectId = +req.param('projectId');
        let fromDate = +(req.body.fromDate);
        let toDate = +(req.body.toDate);
        let download = (req.body.download);
        let is_inherited_project = (req.param('is_inherited_project', false).toString() === 'true');

        sails.log.info('fromDate ', fromDate);
        sails.log.info('toDate ', toDate);

        let filter = { project_ref: projectId };

        if (!fromDate || !toDate) {
            return errorResponse(res, 'from date and to date are required.');
        }
        sails.log.info('download task-briefings with filter:', filter, `is_inherited_project: ${is_inherited_project}`);
        let {expanded_briefings: projectTaskBriefings, received_briefings}= await getTaskBriefings(filter,0, 0, download);
        let project = await sails.models.project.findOne({
            select: ['name', 'custom_field'],
            where: {id: projectId}
        });
        let tbPhrase = project.custom_field.tb_phrase_singlr;
        let workbook = await taskBriefingReport(projectTaskBriefings, fromDate, toDate, tbPhrase);
        return streamExcelDownload(res, workbook);
    },

    downloadTaskBriefingXLSX: async (req, res) => {
        let id = +req.param('id');
        let projectId = +req.body.projectId;
        let timezone = (req.query.tz || 'UTC');

        let record =  await sails.models.projecttaskbriefings.findOne({id: id})
            .populate('briefing_file_ref')
            .populate('user_ref');
        if (record && record.id) {
            let project = await sails.models.project.findOne({
                select: ['name', 'custom_field'],
                where: {id: projectId}
            });
            let tz = getProjectTimezone(project);
            let tbPhrase = project.custom_field.tb_phrase_singlr;
            sails.log.info('got record, id', record.id);
            record.project_ref = project;
            let {expanded_briefings, received_briefings} = await expandToolboxTalks(projectId, {}, [record], 'task_briefings');
            [record] = expanded_briefings;
            let workbook = await downloadToolboxTalkXLSX(record, 'task_briefing', tbPhrase, tz);
            return streamExcelDownload(res, workbook);
        }
        sails.log.info('Task Briefing not found, id', id);
        return ResponseService.errorResponse(res, 'Task Briefing not found.');
    },

    downloadTaskBriefingRegister: async (req, res) => {
        let projectId = +req.param('projectId');
        let records = await sails.models.projecttaskbriefings_reader.find({project_ref: projectId})
            .sort([
                {is_available: 'DESC'},
                {id: 'DESC'}
            ]);
        sails.log.info(`Found ${records.length} TB for project ${projectId}`);
        let {workbook, fileName} = await downloadRegisterXLSX(projectId, 'tb', records, 'task_briefings');
        return streamExcelDownload(res, workbook, fileName);
    },

    addToolBriefing: async (req, res) => {
        let id = +req.param('id');
        let projectId = +req.param('projectId', 0);
        sails.log.info(`Create/Update tool briefing request on project ${projectId}`, id);
        if (!projectId) {
            return errorResponse(res, 'Project id missing.');
        }

        let updateRequest = _.pick((req.body || {}), [
            'tool_key',
            'briefed_at',
            'briefed_by',
            'project_ref',
            'tool_record_ref',
            'register',
            'guest_register',
        ]);

        let briefing = await saveToolBriefing(projectId, id, updateRequest);
        if (!briefing.id) {
            return sendResponse(res, briefing);
        }
        return successResponse(res, briefing);
    },

    // get register
    getToolBriefingsList,

    // get record by id
    getBriefingToolRecord: async (req, res) => {
        let toolKey = req.param('toolKey', null);
        let projectId = +req.param('projectId', 0);
        let recordId = +req.param('recordId', 0);
        let briefingId = +req.param('briefingId', 0);
        let allBriefings = req.param('allBriefings', 'false') === 'true';
        let expandAttendees = (req.param('expand_attendees', 'false') !== 'false');

        let titleClmn = (toolKey === 'toolbox_talks') ? 'talk_title' : 'briefing_title';
        let recNumClmn = (toolKey === 'toolbox_talks') ? 'talk_number' : 'record_id';
        let fileRefClmn = (toolKey === 'toolbox_talks') ? 'talk_file_ref' : 'briefing_file_ref';
        let selectCol = ['id', titleClmn, recNumClmn, fileRefClmn, 'is_available', 'user_ref', 'project_ref', 'tagged_owner', 'createdAt'];
        if(toolKey === 'toolbox_talks'){
            selectCol.push('company_ref');
        }else if(toolKey === toolBriefingsFn.BRIEFING_TOOL_KEY.RAMS){
            selectCol.push(...['reference_number', 'include_during_induction', 'status', 'reject_reason', 'custom_field', 'is_archived', 'auto_approved']);
        }else if(toolKey === 'take_5s'){
            selectCol = ['id', 'user_ref', 'project_ref', 'images', 't5s_number', 'conversation_category', 'employer', 'location_and_task', 'points_and_issues', 'outcomes_and_actions', 'createdAt']
        }
        let toolRecordPromise =  sails.models[briefingToolModelMaps[toolKey]].findOne({
            where: {id: recordId}, select: selectCol
        })

        let toolRecord;
        if(toolKey === 'take_5s'){
            toolRecord = await toolRecordPromise;
            if(toolRecord.images.length){
                sails.log.info(`fetching all images of ${toolKey} id: ${toolRecord.id}`);
                let {images} = await toolBriefingsFn.expandTake5sFiles([toolRecord.id]);
                toolRecord.images = images.flatMap(item => item.images);
            }
        }else{
            toolRecord = await toolRecordPromise.populate(fileRefClmn)
        }

        if (!toolRecord || !toolRecord.id) {
            return errorResponse(res, sails.__('Failed to get the record.'));
        }
        let project = await sails.models.project_reader.findOne({where: {id: projectId}, select: ['id', 'custom_field']});
        //@todo: spatel: merge all briefing vs single briefing logic below.
        if(allBriefings){
            sails.log.info(`fetching all briefings of ${toolKey} id: ${toolRecord.id}`);
            [toolRecord] = await populateUserRefs([toolRecord], 'user_ref', ['first_name', 'last_name']);
            let toolBriefings = await sails.models.toolbriefings_reader.find({
                select: ['register', 'guest_register', 'briefed_at', 'briefed_by', 'briefed_by_name', 'project_ref', 'tool_key', 'tool_record_ref'],
                where : {
                    project_ref: projectId,
                    tool_key: toolKey,
                    tool_record_ref: recordId,
                },
                sort: ['id desc']
            });
            toolBriefings = await populateUserRefs(toolBriefings, 'briefed_by', ['first_name', 'last_name']);
            let userIds = new Set();
            toolBriefings.forEach(item => {
                if(item.briefed_by){
                    userIds.add(item.briefed_by.id);
                }
            });
            userIds = [...userIds];
            let usersEmployer = await getInductionEmployerByUserIds(userIds, projectId, [2,6]);

            // Convert usersEmployer into a lookup Map (faster than array search)
            const inductionMap = new Map(usersEmployer.map(user => [user.user_ref, user.user_employer]));

            // Add induction_company to briefings
            const briefings = toolBriefings.map(briefing => {
                if (briefing.briefed_by && briefing.briefed_by.id) {
                    const company = inductionMap.get(briefing.briefed_by.id) || null;
                    return { ...briefing, induction_company: company };
                }
                return { ...briefing, induction_company: null };
            });

            let briefing_ids = briefings.map(b => b.id);
            let signatures = await sails.models.usersignature_reader.find({
                where: {briefing_ref: briefing_ids, feature_name: 'briefing_tools'}
            });
            //@todo: spatel: eliminate calling below DB call within loop. NOT a high priority as this API is processing single tool record only.
            for (let i = 0; i < briefings.length; i++) {
                sails.log.info(`Processing ${toolKey} briefing row: ${briefings[i].id}`);
                let allRegisters = [...briefings[i].register];
                allRegisters = allRegisters.map(r => { return { ...r, project_ref: projectId }});
                allRegisters = await populateUserRefs(allRegisters, 'user_ref', ['first_name', 'last_name']);
                let showAlternativeUserList = await getShowAlternativeUserList(project, toolKey);
                if( showAlternativeUserList ) {
                    let user_ids = _uniq(allRegisters.map(record => record.user_ref && record.user_ref.id));
                    let employers = await sails.models.userempdetail_reader.find({where: {user_ref: user_ids}, select:['id', 'employer', 'user_ref']});
                    const employerMap = new Map(employers.map(emp => [emp.user_ref, emp]));
                    allRegisters.forEach(register =>{
                        const user_employer = employerMap.get(register.user_ref?.id);
                        if(user_employer) {
                            register["user_employer"] = user_employer;
                        }
                    })
                } else {
                    allRegisters = await populateInductionEmployerRef(allRegisters);
                }
                if(briefings[i].briefed_by && briefings[i].briefed_by.id){
                    briefings[i].briefed_by_name = getUserFullName(briefings[i].briefed_by);
                }
                briefings[i].allattendees = [];
                let register = [];
                for (let j = 0; j < briefings[i].register.length; j++) {
                    let reg = briefings[i].register[j];
                    register.push(reg);
                    let signatureRow = signatures.find(s => (s.briefing_ref === briefings[i].id) && (s.user_ref === reg.user_ref));
                    let u = (allRegisters || []).find(r => r.user_ref.id === reg.user_ref) || {};
                    if (u && u.user_ref) {
                        briefings[i].allattendees.push({
                            user_ref: u.user_ref.id,
                            name: u.user_ref.name,
                            ...(u.user_employer && {employer: u.user_employer.employer}),
                            sign: signatureRow ? signatureRow.sign : null
                        });
                    }

                }
                briefings[i].register = register;
                if (briefings[i].guest_register && briefings[i].guest_register.length) {
                    let guest_register = briefings[i].guest_register.map(gr => {
                        let signature = (signatures || []).find((s) =>  (s.briefing_ref === briefings[i].id) && s.guest_reg_id && (+s.guest_reg_id === gr.id));
                        return {...gr, sign: signature ? signature.sign : null};
                    });
                    briefings[i].allattendees.push(...guest_register);
                }
            }
            return ResponseService.successResponse(res, {
                briefing: {
                    ...toolRecord,
                    record_id: toolRecord[recNumClmn],
                    tool_record_title: toolRecord[titleClmn],
                    tool_record_file_ref: toolRecord[fileRefClmn],
                    register: briefings,
                }
            });
        }

        if (expandAttendees && !briefingId) {
            return errorResponse(res, sails.__('`briefingId` param is required to expand attendees list.'));
        }

        let populatedBriefing = {};
        if(expandAttendees === true && briefingId) {
            let briefing = await sails.models.toolbriefings_reader.findOne({
                select: ['register', 'guest_register', 'briefed_at', 'briefed_by', 'briefed_by_name', 'project_ref', 'tool_key', 'tool_record_ref'],
                where : {id: briefingId, project_ref: projectId}
            });

            if(!briefing) {
                return ResponseService.successResponse(res, { toolRecord, briefing: [] });
            }

            briefing = await populateUserRefs([briefing], 'briefed_by', ['first_name', 'last_name']);
            briefing = await populateInductionEmployerRef(briefing, 'briefed_by');

            let allRegisters = [...briefing[0].register];
            allRegisters = allRegisters.map(r => { return { ...r, project_ref: projectId }});
            allRegisters = await populateUserRefs(allRegisters, 'user_ref', ['first_name', 'last_name']);
            allRegisters = await populateInductionEmployerRef(allRegisters);

            populatedBriefing = briefing[0];
            // Check if briefed_by exists before assigning, for Induction RAMS Briefing where briefed_by is null.
            populatedBriefing.briefed_by && (populatedBriefing.briefed_by.user_employer = briefing[0].user_employer);
            populatedBriefing.allattendees = [];
            for (let j = 0; j < populatedBriefing.register.length; j++) {
                let reg = populatedBriefing.register[j];
                let u = (allRegisters || []).find(r => r.user_ref.id === reg.user_ref) || {};
                if (u && u.user_ref) {
                    populatedBriefing.allattendees.push({
                        user_ref: u.user_ref.id,
                        name: u.user_ref.name,
                        ...(u.user_employer && {employer: u.user_employer.employer})
                    });
                }
            }
            populatedBriefing.allattendees.push(...populatedBriefing.guest_register);
        }
        //Updating generic response keys for talk_title, talk_number and talk_file_ref.
        let tool_record_ref = {
            ...toolRecord,
            record_id: toolRecord[recNumClmn],
            tool_record_title: toolRecord[titleClmn],
            tool_record_file_ref: toolRecord[fileRefClmn]
        };
        //Removed original keys from response object.
        delete tool_record_ref[titleClmn];
        delete tool_record_ref[fileRefClmn];
        delete tool_record_ref[recNumClmn];
        delete populatedBriefing.user_employer;

        return ResponseService.successResponse(res, {
            briefing: {
                ...populatedBriefing,
                tool_record_ref
            }
        });
    },

    // get paged records
    getProjectBriefingToolRecords: async (req, res) => {
        let search = (req.param('search') && req.param('search') !== 'null') ? decodeURIComponent(req.param('search')): '';
        let projectId = +req.param('projectId');
        let sortKey = req.param('sortKey', 'id');
        let toolKey = req.param('toolKey', null);
        let sortDir = req.param('sortDir', 'desc');
        let pageNumber = +req.param('pageNumber', 0);
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let taggedOwnerIds = req.param('tagged_owner', '').split(',').filter(s => s !== '' && !isNaN(+s)).map(s => +s);
        let extraColumns = req.param('extra', '').split(',');
        let companyId = 0;

        let defaultResponse = {
            tool_records: [],
            totalCount: 0,
            projectId,
            pageNumber,
            pageSize,
            sortKey,
            sortDir,
            toolKey,
            search,
        };

        let hasOnlySiteManagement = false;
        let userEmployer = {};
        let isNonInductedSiteManager = false;
        if(toolKey === 'rams'){
            //check if user has only site management access on project
            hasOnlySiteManagement = await hasOnlySiteManagementAccess(req.user, projectId);
            sails.log.info(`user ${req.user.id} hasOnlySiteManagement:`, hasOnlySiteManagement);

            //Site management access level will only be able to access the RAMS that are specific to their company. So if users company = RAMS company
            if (hasOnlySiteManagement) {
                let userEmployerId = await getUserInductionEmployer(req.user, projectId);
                if(userEmployerId){
                    userEmployer = await sails.models.createemployer_reader.findOne({
                        where: {id: userEmployerId},
                        select: ['id', 'name', 'country_code']
                    });
                } else {
                    isNonInductedSiteManager = true;
                }
                taggedOwnerIds = [(userEmployerId || 0)];
            }
        }

        if(toolKey === 'toolbox_talks') {
            /**
             * `companyId` filter will be applied only for Toolbox Talks, As we have company level records for TBTs only.
             * fetching id from project contractor as TBTs are available to all projects of that company.
             * filter: (TBTrecord.project_ref == projectID OR TBTrecord.company_ref == companyID)
             */

            let projectInfo = await sails.models.project_reader.findOne({
                where: {id: projectId},
                select: ['contractor', 'custom_field']
            });
            let country_code = (projectInfo.custom_field && projectInfo.custom_field.country_code) || 'GB';
            let parentCompany = await sails.models.createemployer_reader.findOne({
                where: {name: projectInfo.contractor, country_code},
                select: 'name'
            });
            companyId = (projectInfo.contractor && parentCompany) ? parentCompany.id : 0;
        }

        let {
            total: totalCount,
            records: tool_records
        } = await toolBriefingsFn.getProjectToolRecords(projectId, pageSize, (pageSize * pageNumber), sortKey, sortDir, {
            searchTerm: search, toolKey, toolTable: briefingToolTables[toolKey], companyId, taggedOwnerIds, userId: req.user.id, isNonInductedSiteManager
        });
        if (extraColumns.includes('briefing-count') || extraColumns.includes('attendees-count') && tool_records.length) {
            // fetch briefing counts for this project.
            let attendeesCount = extraColumns.includes('attendees-count');
            let countsByRecords = await toolBriefingsFn.getBriefingCountsOfRecords(projectId, toolKey, tool_records.map(r => r.id), attendeesCount);
            tool_records = tool_records.map(r => {
                let row = countsByRecords.find(c => c.tool_record_ref === r.id);
                r.briefed_count = row ? row.count : 0;

                if(attendeesCount){
                    r.attendees_count = row ? +row.attendees_count : 0;
                }
                if (r.status !== undefined) {
                    r.status_message = buildBriefingToolStatusMessage(r.status);
                }
                return r;
            });
        }



        let taggedOwnersList = [];
        if (extraColumns.includes('tagged-owners')) {
            // fetch tagged owners for filtering.
            taggedOwnersList = await getTaggedOwnersList(briefingToolTables[toolKey], projectId);
        }
        let active_rams = [];
        if(extraColumns.includes('active-rams')){
            active_rams = await sails.models.projectrams_reader.find({
                select: ['id', 'status', 'tagged_owner'],
                where: {project_ref: projectId, is_available: true, status: [2, 3], include_during_induction: true, is_archived: false}
            });
            sails.log.info(`Got ${active_rams.length} active rams on the project ${projectId}.`);
        }

        sails.log.info(`${tool_records.length} briefing tool records found, for projectId: ${projectId}, companyId: ${companyId}, tool: ${toolKey}, q: ${search}, taggedOwnerIds: (${taggedOwnerIds}) page: ${pageNumber},${pageSize} totalCount: ${totalCount}`);

        return ResponseService.successResponse(res, {
            ...defaultResponse,
            tool_records,
            totalCount,
            taggedOwnerIds,
            taggedOwnersList,
            userEmployer,
            ...(extraColumns.includes('active-rams') ? {active_rams}: {}),
        });
    },

    // get all availeble Briefing record list for invite
    getBriefingToolRecordForInvite: async (req, res) => {
        let toolKey = req.param('toolKey');
        let projectId = +req.param('projectId');

        sails.log.info(`Fetching All available Briefing record for invite, [projectId]: ${projectId}, toolkey: ${toolKey}`);
        let project = await sails.models.project.findOne({ where: { id: projectId }, select: ['id', 'parent_company']});
        if( !project ) {
            return ResponseService.successResponse(res, { availableBriefingTools: [] });
        }

        const availableBriefingTools = await toolBriefingsFn.getAvailableBriefingToolsRecords(briefingToolTables[toolKey], toolKey, project);

        return ResponseService.successResponse(res, { availableBriefingTools: availableBriefingTools });
    }

};
