/**
 * Created by spatel on 27/11/23.
 */
const {v4: uuid4} = require('uuid');
const {
    ResponseService: {
        errorResponse,
        successResponse
    },
    SharedService: {
        s3UploaderWithExpiry,
    },
    RekognitoService: {
        searchFacesByImage,
        triggerCreateCollection,
        triggerDeleteFaceFromCollection,
    },
    DataProcessingService: {
        getInductionBlockReason,
    },
    TokenUtil: {
        buildInductionStatusMessage,
    },
} = require('./../services');

const searchEnrolledFace = async (req, res) => {
    let projectId = (req.project && req.project.id) || +req.param('projectId', 0);

    let base64Image = (req.body || {}).base64Image || null;
    if (!projectId || !base64Image || !base64Image.length) {
        return errorResponse(res, 'Image is required');
    }
    let project_has_fr = !!(req.project_optima_setting && req.project_optima_setting.id && req.project_optima_setting.has_fr);
    let has_fr_clock_in_mode = (project_has_fr && [2,3].includes(req.project_optima_setting.clock_in_mode));
    if(!has_fr_clock_in_mode){
        sails.log.info(`[FR]: FR based Clock IN not enabled for Project: ${projectId}`);
        return errorResponse(res, sails.__('facial_recognition_not_enabled'));
    }
    let {custom_field} = req.project || {};
    let result = await searchFacesByImage(projectId, base64Image, 'base64', true);
    if (!result.success || !result.userId) {
        if(result.validPhoto && !result.multiple){
            // Valid photo but no face matched, Could be due to threshold value
            sails.log.info(`[FR]: Valid photo but no face matched, project: ${projectId}`);
            const fileBuffer = new Buffer.from(base64Image.replace(/^data:image\/\w+;base64,/, ""), "base64");
            s3UploaderWithExpiry(`face-search-errors/${projectId}/${uuid4() + '-' + (new Date()).getTime()}.jpg`, fileBuffer, 'image/jpeg', 'public-read', 30).catch(e => sails.log.info(`error while writing file to s3`, e));
        }
        sails.log.info(`[FR]: Nothing matching found under project: ${projectId}, validPhoto?: ${result.validPhoto}, multiple?: ${result.multiple}`);
        return errorResponse(res, sails.__('facial_recognition_search_failed'), result);
    }

    let [last_induction] = await sails.models.inductionrequest_reader.find({
        select: ['id', 'record_id', 'status_code', 'comments', 'additional_data', 'user_ref', 'creator_name', 'fr_face_id'],
        where: {
            project_ref: projectId,
            user_ref: result.userId,
            status_code: [2, 4, 5, 6],
            // As we already matching userId from face result, we don't need redundant check for `fr_face_id`
        },
        sort: ['id DESC'],
        limit: 1,
    });
    // sails.log.info(`[FR]: Matching user found, project: ${projectId}, user_ref: ${result.userId}`, JSON.stringify(result, null, 2));
    if(last_induction && last_induction.id){
        let {additional_data: {user_info, employment_detail}} = last_induction;
        let profile_pic_ref = (user_info ? user_info.profile_pic_ref : null);
        let employment_detail_ref = (employment_detail || {});

        let meta = {
            id: last_induction.id,
            record_id: last_induction.record_id,
            name: last_induction.creator_name,
            user_ref: last_induction.user_ref,
            status_code: last_induction.status_code,
            status_message: buildInductionStatusMessage(last_induction.status_code),

            profile_pic_ref: (profile_pic_ref && profile_pic_ref.file_url) ? profile_pic_ref : null,
            employer: employment_detail_ref.employer || null,
            job_role: employment_detail_ref.job_role || null,
        };
        if([2, 6].includes(last_induction.status_code)){
            // Approved Induction
            sails.log.info(`[FR]: Matching user found, project: ${projectId}, user_ref: ${result.userId} faceId matched? ${(last_induction.fr_face_id === result.faceId)}`);

            return successResponse(res, {
                induction: meta
            });
        } else if([4, 5].includes(last_induction.status_code)){
            // Blocked Induction
            let {header, comment, reason} = getInductionBlockReason(last_induction);
            sails.log.info(`[FR]: User is in blocked state, project: ${projectId}, user_ref: ${result.userId}, status_code: ${last_induction.status_code}, reason: ${reason}`);
            return errorResponse(res, sails.__('facial_recognition_access_denied'), {
                validPhoto: true,
                userId: result.userId,
                success: false,
                induction: meta,
                reason_title: header,
                reason: reason,
                comment,
            });
        }
    }
    sails.log.info(`[FR]: Approved induction not found, project: ${projectId}, user_ref: ${result.userId}`);
    return errorResponse(res, sails.__('facial_recognition_approved_induction_not_found', ((custom_field && custom_field.induction_phrase_singlr) || 'induction')), {
        validPhoto: true,
        userId: result.userId,
        induction: [],
        success: false,
    });

};

const deleteFaceEnrolment = async (req, res) => {
    let projectId = (req.project && req.project.id) || +req.param('projectId', 0);
    let userId = +req.param('userId', 0);
    let faceId = req.param('faceId', null);
    if (!projectId || !faceId) {
        return errorResponse(res, 'All params are required', {projectId, faceId});
    }
    sails.log.info(`[ADMIN]: Delete face, faceId: ${faceId} from project:${projectId}, userId:${userId}`);
    let {success, data} = await triggerDeleteFaceFromCollection(projectId, [faceId]);
    if (!success) {
        return errorResponse(res, ``, data);
    }
    let inductionsCount = 0;
    if(userId){
        // remove induction reference
        sails.log.info(`[FR]: Removing faceId: ${faceId} reference from inductions of user: ${userId}`);
        let updated = await sails.models.inductionrequest.update({
            project_ref: projectId,
            user_ref: userId,
            fr_face_id: faceId,
        }).set({fr_face_id: null});
        sails.log.info(`[FR]: Updated inductions count: ${updated.length}`);
        inductionsCount = updated.length;
    }
    return successResponse(res, {...data, inductionsCount});
};

module.exports = {
    // createCollection: async (req, res) => {
    //     let projectId = +req.param('projectId', 0);
    //     let outcome = await triggerCreateCollection(projectId);
    //     return successResponse(res, outcome);
    // },

    deleteFaceEnrolment: deleteFaceEnrolment,
    // deleteFaceEnrolmentInnTime: deleteFaceEnrolment,

    // searchFacesByImage: searchEnrolledFace,
    searchFacesByImageInnTime: searchEnrolledFace,
};
