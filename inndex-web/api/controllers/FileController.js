/**
 * Created by spatel on 16/9/18.
 */

const ResponseService = require('./../services/ResponseService');
const SharedService = require('./../services/SharedService');
const {INNDEX_SLS_FN_NAME} = sails.config.constants;
const {
    RekognitoService: {
        detectFaces,
    },
    SharedService: {
        mergingPdfs
    },
    HttpService: {
        triggerLambdaFn
    }
} = require('./../services');
// const uuid4 = require('uuid/v4');
const { v4: uuid4 } = require('uuid');
const fs = require('fs');
const path = require('path');


const getVideoThumbnail = async(s3Url, fileN, fileCategory) => {
    sails.log.info('Invoking lambda function to create video thumbnail.');
    return await triggerLambdaFn(sails.config.custom.LAMBDA_VIDEO_THUMBNAIL_FN, {
        s3Url: s3Url,
        filename: fileN,
        file_category: fileCategory
    });
}

const updateVideoCodec = (file_url, file_extension, file_id, mime_type) => {
    sails.log.info('Invoking lambda function to update video codec if needed(decide in lambda function).');
    return triggerLambdaFn(INNDEX_SLS_FN_NAME.LAMBDA_UPDATE_VIDEO_CODEC_FN, {
        file_url,
        file_extension,
        file_id,
        mime_type
    });
}

const getFileType = (mime, defaultType = 0) => {
    // This way we would have single type for images, and would be faster to fetch such records
    let mime_vs_type = {
        'image/png': 1,
        'image/jpeg': 1,
        'image/jpg': 1,

        'image/webp': 2,
        'image/heic': 3,
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': 4,

        'application/msword': 5,
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 5,

        'video/quicktime': 6,
        'video/mp4': 6,

        'application/pdf': 7,
    };

    return mime_vs_type[mime] || defaultType;
}

const fileUploadFn = async (req, res, visitor_id = null) => {

    let user_id = req.user.id;
    let fileCategory = req.param('category', null);
    let fileName = uuid4() + '-' + (new Date()).getTime();
    let actualName = null;
    req.file('file').upload({
        // don't allow the total upload size to exceed ~200MB
        maxBytes: (200 * 1024 * 1024),
        dirname: path.resolve(sails.config.appPath, 'assets/uploads'),
        saveAs: (__newFileStream, cb) => {
            actualName = __newFileStream.filename;
            let fileExtension = path.extname(__newFileStream.filename);
            if(fileExtension.length > 5) {
                sails.log.info('Invalid file extension ', fileExtension);
                return ResponseService.errorResponse(res, `Invalid file extension, ${fileExtension}`);
            }
            fileName = fileName + fileExtension.toLowerCase();
            sails.log.info('File Steam is ', __newFileStream.filename, ' => ', fileName);
            return cb(undefined, fileName)
        }
    }, async function whenDone(err, uploadedFiles) {
        if (err) {
            return ResponseService.errorResponse(res, sails.__('internal server error'), {err});
        }
        if (uploadedFiles.length === 0){
            return ResponseService.errorResponse(res, 'No file was uploaded');
        }
        let uploadedFile = uploadedFiles[0];
        sails.log.info('Uploaded files', uploadedFile);
        if(fileCategory && fileCategory === 'profile-pic'){
            let result = await detectFaces(fs.readFileSync(uploadedFile.fd), 'buffer');
            if(result.error){
                let s3Url = await SharedService.uploadToS3(uploadedFile, fileName, `face-detection-errors/user-${user_id}/${result.code}`);
                // fs.unlinkSync(uploadedFile.fd);
                sails.log.info(`Face detection error for user:${user_id}`, result.message);
                sails.log.info(`Face detection failed file, user:${user_id}`, s3Url);
                return ResponseService.sendResponse(res, result);
            }
        }
        let s3Url = await SharedService.uploadToS3(uploadedFile, fileName, fileCategory);
        let record = {
            file_url : s3Url,
            file_mime : uploadedFile.type,
            file_type: getFileType(uploadedFile.type),
            name: actualName,
            user_id: user_id,
            visitor_ref: visitor_id,
            category: fileCategory
        };
        if(fileCategory && ['message-files', 'permit-mandatory-attachment'].includes(fileCategory) && (uploadedFile.type === 'video/mp4' || uploadedFile.type === 'video/quicktime')) {
            let fileN = uuid4() + '-' + (new Date()).getTime()+ '.png';
            let lambdaResponse = await getVideoThumbnail(s3Url, fileN, 'video-thumbnail');
            sails.log.info('Response received from lambda video thumbnail.',  lambdaResponse.data.Location);
            if (lambdaResponse.statusCode == 200 && lambdaResponse.data.Location) {
                const url = lambdaResponse.data.public_url || lambdaResponse.data.Location;
                record.img_translation = [url];
                sails.log.info('Creating video file record', record);
                let user_file = await sails.models.userfile.create(record);
                req.file_record = user_file;
                return ResponseService.successResponse(res, {user_file});
            }
            sails.log.info('Failed to create thumbnail for uploaded video.');
            return ResponseService.errorResponse(res, 'Failed to create thumbnail for uploaded video.');
        } else {
            sails.log.info('Creating file record', record);
            let user_file = await sails.models.userfile.create(record);
            //update video codec from H265 to H264
            if (user_file.category === 'project-media' && ['video/mp4', 'video/quicktime'].includes(user_file.file_mime)) {
                let fileExtension = path.extname(user_file.name);
                updateVideoCodec(s3Url, fileExtension, user_file.id, user_file.file_mime).catch(sails.log.info);
            }

            sails.log.info('User File record', user_file.id);
            // for now we want to process specific uploads only...!!
            if(['cow-site-drawings-upload'].includes(user_file.category)){
                sails.log.info('Waiting for pdf =>> image translation to get finished');
                user_file = await populateImageTranslation(user_file).catch(sails.log.error);
            }
            else if(['permit-mandatory-attachment', 'user-doc', 'close-out', 'collection-notes-attachment', 'delivery-notes-attachment', 'inspection-docs-attachment', 'close-call', 'good-call', 'observation'].includes(user_file.category)) {
                populateImageTranslation(user_file).catch(sails.log.error);
            }
            req.file_record = user_file;
            return ResponseService.successResponse(res, {user_file});
        }
    });
};

const populateImageTranslation = async (user_file) => {
    let img_translation = await SharedService.translateFileIntoImages(user_file);
    if(img_translation.length){
        return await sails.models.userfile.updateOne({id: user_file.id}).set({
            img_translation,
        });
    }
    return user_file;
};

module.exports = {
    uploadFile: (req, res) => {
        return fileUploadFn(req, res);
    },

    storeInnTimeUploadFile: async (req, res) => {
        let user_id = +req.body.user_id || null;
        let visitor_id = +req.body.visitor_id || null;
        let category = req.param('category', '');
        if(!['vehicle-clock-in', 'inn-time-bulk-clock-in'].includes(category) && !user_id && !visitor_id){
            sails.log.info('Invalid Request, user_id / visitor_id is required', req.body);
            let fileInstance = req.file('file');
            if(fileInstance){
                // Clean upload stream
                fileInstance.upload({noop: true});
            }
            return ResponseService.errorResponse(res, 'user_id / visitor_id is required.');
        }
        req.user = {
            id: user_id,
            // parent_company: req.project.parent_company,
        };
        sails.log.info(`Uploading file for user_id(${user_id}) OR visitor_id(${visitor_id}), Uploader: ${req.project.id}`);
        return await fileUploadFn(req, res, visitor_id);
    },

    deleteFile: async (req, res) => {
        sails.log.info('Delete request for user file', req.body.user_file_id);
        if (!_.has(req.body, 'user_file_id')) {
            sails.log.info('Invalid Request')
            return res.ok({error: true, message: "File id is required."});
        }

        // @todo:Vshal: We should have hard delete for file id which uploaded for new record and was delete before saving the form.
        // Because that file will not get used anywhere.
        let deleted = await SharedService.deleteFileRecord(req.body.user_file_id, req.user.id);
        if(!deleted){
            return ResponseService.errorResponse(res, 'Failed to delete file');
        }
        return ResponseService.successResponse(res, {message: "Deleted record successfully"});
    },

    deleteInnTimeFile: async (req, res) => {
        sails.log.info('Delete request for user file', req.body.user_file_id);
        if (!req.body.user_file_id || isNaN(+req.body.user_file_id)) {
            sails.log.info('Invalid Request for file deletion');
            return ResponseService.errorResponse(res, "File id is required. user_file_id is missing");
        }
        let deleted = await SharedService.deleteFileRecord(req.body.user_file_id, null);
        if (!deleted) {
            return ResponseService.errorResponse(res, 'Failed to delete file');
        }
        return ResponseService.successResponse(res, {message: "Deleted record successfully"});
    },

    seedGenerateFileTranslationById: async (req, res) => {
        let user_id = +req.param('userId', 0);
        let user_file_id = +req.param('userFileId', 0);
        // let updatedAt = +req.param('updatedAt', 0);
        let file_mime = (req.query.any_mime !== 'true') ? 'application/pdf' : undefined;
        let category = (req.query.any_category !== 'true') ? 'user-doc' : undefined;

        let filter = {
            user_id,
            file_mime,
            category
        };
        if(user_file_id){
            filter.id = user_file_id;
        }
        sails.log.info('Generating image translation of file id:', filter);
        if(!filter.user_id){
            sails.log.info('Invalid Request');
            return ResponseService.errorResponse(res, 'user_id is required.');
        }
        let fileInstances = await sails.models.userfile.find(filter);
        for (let i = 0; i < fileInstances.length; i++) {
            fileInstances[i] = await populateImageTranslation(fileInstances[i]);
        }
        return ResponseService.successResponse(res, {message: "processed successfully", fileInstances});
    },

    downloadFile: async (req, res) => {
        // let fileUrl = req.body.file_url;
        // await request.get(fileUrl, function (error, response, body) {
        //     sails.log.info("content-type", response.headers["content-type"]);
        //     if (!error && response.statusCode == 200) {
        //         res.set('Content-Type', response.headers["content-type"]);
        //         sails.log.info("Downloading file.")
        //         return res.send(body);
        //     }
        //
        //     sails.log.info('Failed to download file.', error);
        //     return ResponseService.errorResponse(res, `Something went wrong while downloading file, ${error}`);
        // });
        return ResponseService.errorResponse(res, `downloading file is not supported anymore`);

    },

    mergePDFs: async (req, res) => {
        let user_file = await mergingPdfs(req);
        return ResponseService.successResponse(res, {user_file});
    },

    convertWordDocsToPdf: async (req, res) => {
        let files = req.body.files || [];
        let converted_files = [];
        let promisesArray = [];
        let docsFileCount = 0;
        for (let i = 0; i < files.length; i++) {
            let doc_file = files[i];
            if (!doc_file.doc_file_url) {
                sails.log.info('doc url not found.');
                continue;
            }
            let docFileUrl = doc_file.doc_file_url || '';
            let docFileName = (doc_file.doc_file_name || Date.now()+'.doc');
            let feature_name = doc_file.category;
            let outputFileName = docFileName.split(".")[0];
            let urlParts = docFileUrl.split(".");
            //check if file is valid
            if (!urlParts.length || !['doc', 'docx'].includes((urlParts[urlParts.length-1]).toLowerCase())) {
                sails.log.info('Invalid word doc file.', docFileUrl);
                continue;
            }

            promisesArray.push(((outputFileName, doc_file) => {
                docsFileCount += 1;
                return async () => {
                    let result = await triggerLambdaFn(sails.config.custom.LAMBDA_DOC_TO_PDF_FN, {
                            doc_url: docFileUrl,
                            fromExt: urlParts[urlParts.length - 1],
                            toExt: 'pdf',
                            feature_name: feature_name
                        }
                    );
                    return {
                        ...result,
                        outputFileName,
                        doc_file
                    }
                }
            })(outputFileName, doc_file));
        }
        let outcomes = await Promise.all(promisesArray.map(fn => fn()));
        sails.log.info(`Outcomes of all iteration`, JSON.stringify(outcomes));
        for (let i = 0; i < outcomes.length; i++) {
            let outcome = outcomes[i];
            sails.log.info(`Response received from lambda, doc to pdf.`);
            if(outcome.success && (outcome.data.Location || outcome.data.location)) {
                const public_url = outcome.data.public_url || outcome.data.Location || outcome.data.location;
                sails.log.info(`Received converted PDF from lambda fn, path: ${public_url}`);
                let record = {
                    file_url: public_url,
                    file_mime: 'application/pdf',
                    name: outcome.outputFileName + '.pdf',
                    user_id: req.user.id,
                    category: outcome.doc_file.category
                };

                let user_file = await sails.models.userfile.create(record);
                user_file.doc_file_id = outcome.doc_file.doc_file_id;
                converted_files.push({user_file});
                sails.log.info('User File record', user_file.id);
            } else {
                sails.log.error(`Failed to convert the doc file to pdf, doc_file_url: `, outcome.doc_file.doc_file_url);
            }
        }

        if (docsFileCount > converted_files.length) {
            return ResponseService.errorResponse(res, 'Encountered an issue while trying to convert the uploaded document file to PDF. Please try again',{converted_files});
        }
        return ResponseService.successResponse(res, {converted_files});
    },
};
