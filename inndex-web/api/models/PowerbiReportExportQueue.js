
module.exports = {
    tableName: 'powerbi_report_export_queue',

    attributes: {
        emails: {
            type: 'json',
            defaultsTo: []
        },
        file_location: {
            type: 'string'
        },
        expiration_time: {
            type: 'string',
            columnType: 'bigint'
        },
        status: {
            type: 'number',
            defaultsTo: 0 //0 = pending, 1 = file sent, 2 = expired
        },
        additional_detail: {
            type: 'json',
            defaultsTo: {}
        },
    },

    //attributes methods
    customToJSON: function () {
        return _.omit(this, []);
    }
}
