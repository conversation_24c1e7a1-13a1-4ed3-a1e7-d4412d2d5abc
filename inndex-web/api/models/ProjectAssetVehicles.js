const buildStatus = (code) => {
    if(code === 1){
        return 'Approval Pending';
    } else if(code === 2){
        return 'Approved';
    } else if (code === 3) {
        return 'Declined'
    }
};

module.exports = {
    tableName: 'project_asset_vehicles',

    attributes: {
        vehicle_photos: {
            type: 'json',
            defaultsTo: []
        },
        vehicle_id: {
            type: 'string'
        },
        type_of_vehicle: {
            type: 'string'
        },
        power_output: {
            type: 'number',
            columnType: 'float',
            allowNull: true
        },
        approval_number: {
            type: 'string',
            allowNull: true
        },
        engine_manufacturer: {
            type: 'string',
            allowNull: true
        },
        year_manufactured: {
            type: 'number',
            allowNull: true
        },
        serial_number: {
            type: 'string'
        },
        examination_cert_number: {
            type: 'string',
            allowNull: true
        },
        examination_cert_expiry_date: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        examination_certificates: {
            type: 'json',
            defaultsTo: [],
        },
        service_expiry_date: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        service_certificates: {
            type: 'json',
            defaultsTo: [],
        },
        mot_expiry_date: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        mot_certificates: {
            type: 'json',
            defaultsTo: [],
        },
        project_ref: {
            model: 'project'
        },
        user_ref: {
            model: 'user'
        },
        user_revision_ref: {
            model: 'userrevision'
        },
        is_archived:{
            type: 'boolean',
            defaultsTo: false,
            allowNull: true,
        },
        approval_pending:{
            type: 'number',
            defaultsTo: 1
        },
        activity_logs: {
            type: 'json',
            defaultsTo: []
        },
        arrived_at: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        tagged_owner: {
            type: 'ref',
            columnType: 'integer[]',
            custom: function (value) {
                // Perform any custom validation or serialization logic here
                return value;
            }
        },
        custom_fields: {
            type: 'json',
            defaultsTo: [],
        },
    },

    //attributes methods
    customToJSON: function () {
        if (this.approval_pending == null) {
            this.approval_pending = 1;
        }
        this.approval_pending_message = buildStatus(this.approval_pending);
        return _.omit(this, []);
    },
};
