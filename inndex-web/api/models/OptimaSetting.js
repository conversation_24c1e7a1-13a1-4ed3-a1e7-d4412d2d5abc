/**
 * Created by spatel on 10/3/19.
 */
module.exports = {
    tableName: 'optima_setting',

    attributes: {
        project_ref: {
            model: 'project'
        },
        // host: {
        //     type: 'string',
        //     allowNull: true,
        // },
        // port: {
        //     type: 'string',
        //     allowNull: true,
        // },
        key: {
            type: 'string',
            allowNull: true,
        },
        biometric_source: {
            type: 'string',
            allowNull: true,
        },
        site_id: {
            type: 'string',
            allowNull: true,
        },
        enrolment_type: {
            type: 'string',
            allowNull: true,
            defaultsTo: 'fingerprint',
        },
        allow_cards: {
            type: 'boolean',
            defaultsTo: false,
        },
        has_turnstile: {
            type: 'boolean',
            defaultsTo: false,
        },
        force_onsite_for_an_out: {
            type: 'boolean',
            defaultsTo: true,
        },
        tz: {
            type: 'string',
            defaultsTo: 'Europe/London',
        },
        geo_fence_locations: {
            type: 'json',
            defaultsTo: null
        },
        selfie_required: {
            type: 'boolean',
            defaultsTo: false,
        },
        has_worker_clocking: {
            type: 'boolean',
            defaultsTo: false,
        },
        has_bulk_clocking: {
            type: 'boolean',
            defaultsTo: false,
        },
        has_vehicle_clocking: {
            type: 'boolean',
            defaultsTo: false,
        },
        has_fr: {
            type: 'boolean',
            defaultsTo: false,
        },
        kiosk_mode: {
            type: 'boolean',
            defaultsTo: false,
        },
        clock_in_mode: {
            type: 'number',
            defaultsTo: 1,
        },
        liveness_check: {
            type: 'json',
            defaultsTo: {},
        },
    },

    //attributes methods
    customToJSON: function () {
        let setting = _.omit(this, []);
        // fallback logic for OLD mobile app.
        if (!setting.geo_fencing_acceptable_distance && setting.geo_fence_locations) {
            let [first_location] = setting.geo_fence_locations || [];
            setting.geo_fencing_acceptable_distance = (first_location ? first_location.radius : null);
            setting.site_geo_location = (first_location ? {
                'name': first_location.name,
                'lat': first_location.lat,
                'long': first_location.long
            } : {});
        }
        return setting;
    },
};
