/*
 * @Author: sandeep.patel
 * @Date: 2018-10-04 18:44:21
 * @Last Modified by: sandeep.patel
 * @Last Modified time: 2018-10-04 18:49:05
 */

const moment = require('moment');

let dateToDuration = (job_start_date) => {
    let d = moment(+job_start_date);
    if(d.isValid()){
        let now = moment();
        return moment.duration(now.diff(d, 'months'), 'months');
    }
    return null;
};

module.exports = {
    tableName: 'user_employment_detail',

    attributes: {
        user_ref: {
            model: 'user'
        },
        employer: {
            type: 'string',
            allowNull: true
        },
        reporting_to: {
            type: 'string',
            allowNull: true
        },
        job_role: {
            type: 'string',
            allowNull: true
        },
        type_of_employment: {
            type: 'string',
            allowNull: true
        },
        employment_company: {
            type: 'string',
            allowNull: true
        },
        employee_number: {
            type: 'string',
            allowNull: true
        },
        operative_type: {
            type: 'string',
            allowNull: true
        },
        // @deprecated since 23rd Feb
        // months_with_employer: {
        //     type: 'string',
        //     allowNull: true
        // },
        start_date_with_employer: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true,
        },
        earn_mlw_e783: {
            type: 'number',
            allowNull: true
        },
        /*
        working_arrangement: {
            type: 'string',
            allowNull: true,
        },
        working_pattern: {
            type: 'string',
            allowNull: true,
        },
        bank_name: {
            type: 'string',
            allowNull: true,
        },
        account_number: {
            type: 'string',
            allowNull: true,
            columnType: 'bigint',
        },
        sort_code: {
            type: 'string',
            allowNull: true,
            columnType: 'bigint',
        },
        utr_number: {
            type: 'string',
            allowNull: true,
            columnType: 'bigint',
        },*/
        comment: {
            type: 'string',
            allowNull: true,
        }
    },

    //attributes methods
    customToJSON: function () {
        // if(this.start_date_with_employer){
        //     this.months_with_employer = dateToDuration(this.start_date_with_employer);
        // }
        return _.omit(this, []);
    },
};
