const buildStatus = (code) => {
    if(code === 1){
        return 'Approval Pending';
    } else if(code === 2){
        return 'Approved';
    } else if (code === 3) {
        return 'Declined'
    }
};
module.exports = {
    tableName: 'project_asset_temporary_work',

    attributes: {
        item_id: {
            type: 'string'
        },
        type_of_works: {
            type: 'string'
        },
        item: {
            type: 'string'
        },
        serial_number: {
            type: 'string'
        },
        photos: {
            type: 'json',
            defaultsTo: []
        },

        project_ref: {
            model: 'project'
        },
        user_ref: {
            model: 'user'
        },
        user_revision_ref: {
            model: 'userrevision'
        },
        is_archived:{
            type: 'boolean',
            defaultsTo: false,
            allowNull: true,
        },
        approval_pending:{
            type: 'number',
            defaultsTo: 1
        },
        activity_logs: {
            type: 'json',
            defaultsTo: []
        },
        arrived_at: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        examination_cert_number: {
            type: 'string',
            allowNull: true
        },
        examination_cert_expiry_date: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        examination_certificates: {
            type: 'json',
            defaultsTo: [],
        },
        tagged_owner: {
            type: 'ref',
            columnType: 'integer[]',
            custom: function (value) {
                // Perform any custom validation or serialization logic here
                return value;
            }
        },
        risk_category: {
            type: 'number',
            allowNull: true
        },
        custom_fields: {
            type: 'json',
            defaultsTo: [],
        },
    },

    //attributes methods
    customToJSON: function () {
        if (this.approval_pending == null) {
            this.approval_pending = 1;
        }
        this.approval_pending_message = buildStatus(this.approval_pending);
        return _.omit(this, []);
    },
};