/*
 * RAMS = Risk Assessment & Method Statements.
 */

const buildStatusMessage = TokenUtil.buildRamsStatusMessage;

module.exports = {
    tableName: 'project_rams',

    attributes: {
        record_id: {
            type: 'string',
            allowNull: true,
            columnType: 'bigint'
        },
        briefing_title: {
            type: 'string',
            allowNull: true
        },
        briefing_file_ref: {
            model: 'userfile'
        },
        is_available: {
            type: 'boolean',
            defaultsTo: true
        },
        briefed_count: {
            type: 'string',
            columnType: 'bigint',
            allowNull: true
        },
        user_ref: {
            model: 'user'
        },
        user_revision_ref: {
            model: 'userrevision'
        },
        project_ref: {
            model: 'project'
        },
        tagged_owner: {
            model: 'createemployer'
        },

        status: {
            type: 'number',
            //enum: [1, 2, 3],
            defaultsTo: 1,
        },
        reject_reason: {
            type: 'string',
            allowNull: true
        },
        approved_by: {
            model: 'user'
        },
        approved_at: {
            type: 'string',
            allowNull: true,
            columnType: 'bigint'
        },
        custom_field: {
            type: 'json',
            defaultsTo: null
        },
        include_during_induction: {
            type: 'boolean',
            defaultsTo: false
        },
        is_archived: {
            type: 'boolean',
            defaultsTo: false
        },
        reference_number: {
            type: 'string',
            allowNull: true
        },
        revision_number: {
            type: 'string',
            allowNull: true
        },
        revision_status: {
            type: 'boolean',
            defaultsTo: false
        },
        group_id: {
            type: 'number',
            allowNull: true
        },
        auto_approved: {
            type: 'boolean',
            defaultsTo: false
        },
    },

    beforeCreate: function (values, cb) {
        const baseValue = 0;
        //skipping if creating new revision
        if (!values.record_id) {
            sails.models.projectrams_reader.find({
                where: {project_ref: values.project_ref, revision_status: true},
                select: ['id', 'record_id'],
                limit: 1,
                sort: ['record_id DESC']
            })
                .exec(function(e,rows) {
                    if(e){
                        sails.log.info('Error is', e);
                        return cb(e);
                    }
                    let row = {};
                    if(rows && rows.length){
                        row = rows[0];
                    }
                    let next_record_id =  parseInt(row.record_id ? row.record_id : baseValue) + 1;
                    sails.log.info('next record_id will be', row, next_record_id);
                    values.record_id = next_record_id;
                    cb();
                });
        } else {
            cb();
        }
    },

    //attributes methods
    customToJSON: function () {
        if (this.status == null) {
            this.status = 1;
        }
        this.status_message = buildStatusMessage(this.status);
        return _.omit(this, []);
    },

    afterUpdate: function(values, cb){
        values.status_message = buildStatusMessage(values.status);
        cb();
    }
};
