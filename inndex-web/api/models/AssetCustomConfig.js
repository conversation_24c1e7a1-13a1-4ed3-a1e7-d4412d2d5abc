module.exports = {

    tableName: 'asset_custom_config',

    attributes: {
        name: {
            type: 'string',
        },
        key: {
            type: 'string',
        },
        alternate_phrase: {
            type: 'string',
            allowNull: true,
        },
        // asset_ref: {
        //     type: 'number',
        //     allowNull: false,
        // },
        asset_type: {
            type: 'string',
        },

        default_fields: {
            type: 'json',
            defaultsTo: [],
        },
        custom_fields: {
            type: 'json',
            defaultsTo: [],
        },

        inspection_checklist_type: {
            type: 'string',
        },
        inspection_checklist: {
            type: 'json',
            defaultsTo: [],
        },
        company_ref: {
            model: 'createemployer'
        },
        checklist_version: {
            type: 'number',
            allowNull: false,
        },
        checklist_draft: {
            type: 'json',
            defaultsTo: [],
        },
        parent_key: {
            type: 'string',
            allowNull: true,
        },

    }
};
