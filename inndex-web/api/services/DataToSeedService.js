const { ASSET_TYPES, COMPANY_SETTING_KEY, INNDEX_SETTING_KEY } = sails.config.constants;
const dayjs = require('dayjs');

const meta_supervisor_induction_declarations = [
    {"question":"You must ensure that all people working under your control have received a full site-specific induction and have provided all relevant competencies.","order":1,"id":1,"type":"checkbox"},
    {"question":"You will attend Supervisors briefings and you are responsible for ensuring that information provided and discussed at this meeting is disseminated to your operatives via an activity briefing. ","order":2,"id":2,"type":"checkbox"},
    {"question":"You are expected to take POSITIVE action on all matters relating to the Environment, Health and Safety.","order":3,"id":3,"type":"checkbox"},
    {"question":"You are responsible for ensuring that any person working under your control is wearing correct PPE.  ","order":5,"id":5,"type":"checkbox"},
    {"question":"All of your operations must be covered by a Risk Assessment and Method Statement. All persons involved must be briefed on its contents. You must carry out this briefing and provide written records to this effect. ","order":6,"id":6,"type":"checkbox"},
    {"question":"You must lead by example.","order":7,"id":7,"type":"checkbox"},
    {"question":"You must ensure that you have sufficient labour, plant and equipment to undertake your work correctly and without risk to the environment or the health and safety of any person.","order":8,"id":8,"type":"checkbox"},
    {"question":"Supervisors will be responsible for ensuring all permits are fully completed and in place after having been agreed with the main contractor management, prior to the work referred to being undertaken.","order":9,"id":9,"type":"checkbox"}
];

const meta_operator_induction_declarations = [
    {"question":"Have you received permission from your employer to drive the plant on behalf of them?","id":1,"order":1,"type":"radio"},
    {"question":"Is the Operator’s manual on site and available to you for use?","id":2,"order":2,"type":"radio"},
    {"question":"Have you seen a full copy of the current 6/12 monthly thorough examination certificate for the plant you are expecting to operate?","id":3,"order":3,"type":"radio"},
    {"question":"Have you read, understood and signed the Risk Assessment and Method Statement for the works you are onsite to carry out, understanding that if your works change a new method statement must be in place before works commence?","id":4,"order":4,"type":"radio"},
    {"question":"Do you agree to carrying out daily inspections on the plant & equipment you are to operate, including checking for signs of damage or excessive wear?","id":5,"order":5,"type":"radio"},
    {"question":"When was the last time you operated this specific plant?","id":6,"order":6,"type":"text"},
    {"question":"How long have you been driving this plant for?","id":7,"order":7,"type":"text"},
    {"question":"How do you immobilise/switch off the plant you drive?","id":8,"order":8,"type":"text"}
];

//Last Question Id is 184
const metaInspectionTourCommonChecklist = [
    {
        "category":"General Documentation",
        "question":"Accident Form",
        "display_number":"1.1",
        "question_id":1
    },
    {
        "category":"General Documentation",
        "question":"Method Statements/WPP/TB",
        "display_number":"1.2",
        "question_id":2
    },
    {
        "category":"General Documentation",
        "question":"Permit/s to Work",
        "display_number":"1.3",
        "question_id":3
    },
    {
        "category":"General Documentation",
        "question":"Briefing Records",
        "display_number":"1.4",
        "question_id":4
    },
    {
        "category":"General Documentation",
        "question":"Statutory Inspections",
        "display_number":"1.5",
        "question_id":5
    },
    {
        "category":"General Documentation",
        "question":"Plant Certification",
        "display_number":"1.6",
        "question_id":6
    },
    {
        "category":"General Documentation",
        "question":"Relevant Rule/Hand Book",
        "display_number":"1.7",
        "question_id":7
    },
    {
        "category":"General Documentation",
        "question":"Quality - Records Inspection Checklist/s",
        "display_number":"1.8",
        "question_id":8
    },
    {
        "category":"General Documentation",
        "question":"Quality - Test Records",
        "display_number":"1.9",
        "question_id":9
    },
    {
        "category":"General Documentation",
        "question":"Quality - Concrete",
        "display_number":"1.10",
        "question_id":10
    },
    {
        "category":"General Documentation",
        "question":"Corrective Action Records",
        "display_number":"1.11",
        "question_id":11
    },
    {
        "category":"General Documentation",
        "question":"Daily Supervisor Checklist",
        "display_number":"1.12",
        "question_id":12
    },
    {
        "category":"Safety",
        "question":"Work at height",
        "display_number":"2.1",
        "question_id":13
    },
    {
        "category":"Safety",
        "question":"Excavations",
        "display_number":"2.2",
        "question_id":14
    },
    {
        "category":"Safety",
        "question":"Manual Handling",
        "display_number":"2.3",
        "question_id":15
    },
    {
        "category":"Safety",
        "question":"Lifting Equipment",
        "display_number":"2.4",
        "question_id":16
    },
    {
        "category":"Safety",
        "question":"Mobile Plant, Equipment",
        "display_number":"2.5",
        "question_id":17
    },
    {
        "category":"Safety",
        "question":"Personal Protective Equipment",
        "display_number":"2.6",
        "question_id":18
    },
    {
        "category":"Safety",
        "question":"Electrical Equipment",
        "display_number":"2.7",
        "question_id":19
    },
    {
        "category":"Safety",
        "question":"HAVS",
        "display_number":"2.8",
        "question_id":20
    },
    {
        "category":"Safety",
        "question":"Confined Space",
        "display_number":"2.9",
        "question_id":21
    },
    {
        "category":"Safety",
        "question":"COSHH",
        "display_number":"2.10",
        "question_id":22
    },
    {
        "category":"Safety",
        "question":"Hot Works",
        "display_number":"2.11",
        "question_id":23
    },

    {
        "category":"Safety",
        "question":"Noise",
        "display_number":"2.12",
        "question_id":24
    },
    {
        "category":"General Site Issues",
        "question":"Site Notices/Information",
        "display_number":"3.1",
        "question_id":25
    },
    {
        "category":"General Site Issues",
        "question":"Welfare",
        "display_number":"3.2",
        "question_id":26
    },
    {
        "category":"General Site Issues",
        "question":"Emergency Arrangements",
        "display_number":"3.3",
        "question_id":27
    },
    {
        "category":"General Site Issues",
        "question":"Fire Precautions",
        "display_number":"3.4",
        "question_id":28
    },
    {
        "category":"General Site Issues",
        "question":"Housekeeping",
        "display_number":"3.5",
        "question_id":29
    },
    {
        "category":"General Site Issues",
        "question":"Temporary Lighting",
        "display_number":"3.6",
        "question_id":30
    },
    {
        "category":"General Site Issues",
        "question":"Access & Boundaries",
        "display_number":"3.7",
        "question_id":31
    },
    {
        "category":"General Site Issues",
        "question":"Security (& Storage)",
        "display_number":"3.8",
        "question_id":32
    },
    {
        "category":"General Site Issues",
        "question":"First Aid",
        "display_number":"3.9",
        "question_id":33
    },
    {
        "category":"General Site Issues",
        "question":"Public Safety",
        "display_number":"3.10",
        "question_id":34
    },
    {
        "category":"General Site Issues",
        "question":"Infrastructure Protection",
        "display_number":"3.11",
        "question_id":35
    },
    {
        "category":"General Site Issues",
        "question":"Hazard / Close Call reporting",
        "display_number":"3.12",
        "question_id":36
    },
    {
        "category":"Environment",
        "question":"Emissions to atmosphere",
        "display_number":"4.1",
        "question_id":37
    },
    {
        "category":"Environment",
        "question":"Wildlife - Impact Controls",
        "display_number":"4.2",
        "question_id":38
    },
    {
        "category":"Environment",
        "question":"Nuisance - Operational Control",
        "display_number":"4.3",
        "question_id":39
    },
    {
        "category":"Environment",
        "question":"Waste - Storage/License/Documentation",
        "display_number":"4.4",
        "question_id":40
    },
    {
        "category":"Environment",
        "question":"Spillage/Pollution/Contamination",
        "display_number":"4.5",
        "question_id":41
    },
    {
        "category":"Environment",
        "question":"Noise & Vibration Controls",
        "display_number":"4.6",
        "question_id":42
    },
    {
        "category":"Personnel/Individual",
        "question":"Competency Card Checks",
        "display_number":"5.1",
        "question_id":43
    },
    {
        "category":"Personnel/Individual",
        "question":"Sub-contractor briefing",
        "display_number":"5.2",
        "question_id":44
    },
    {
        "category":"Personnel/Individual",
        "question":"Safety Briefing - Test Knowledge",
        "display_number":"5.3",
        "question_id":45
    },
    {
        "category":"Personnel/Individual",
        "question":"Compliance with Method Statement/WPP/TB",
        "display_number":"5.4",
        "question_id":46
    },
    {
        "category":"Personnel/Individual",
        "question":"Compliance with Risk/COSHH Assessment",
        "display_number":"5.5",
        "question_id":47
    },
    {
        "category":"Personnel/Individual",
        "question":"Correct Manual Handling",
        "display_number":"5.6",
        "question_id":48
    },
    {
        "category":"Personnel/Individual",
        "question":"Compliance with Permits to Work",
        "display_number":"5.7",
        "question_id":49
    },
    {
        "category":"Personnel/Individual",
        "question":"Use of Personal Protective Equipment",
        "display_number":"5.8",
        "question_id":50
    }
];

//Last Question Id is 184
const metaInspectionTourRailChecklist = [
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"Site Access (Signing in etc.)",
        "display_number":"6.1",
        "question_id":51
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"ES (Engineering Supervisor)",
        "display_number":"6.2",
        "question_id":52
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"Overhead Line Permit Arrangements",
        "display_number":"6.3",
        "question_id":53
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"Conductor Rail Permit Arrangements",
        "display_number":"6.4",
        "question_id":54
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"Marker Boards etc.",
        "display_number":"6.5",
        "question_id":55
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"Safeguarded",
        "display_number":"6.6",
        "question_id":56
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"Fenced",
        "display_number":"6.7",
        "question_id":57
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"Site Warden",
        "display_number":"6.8",
        "question_id":58
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"ATWS Equipment",
        "display_number":"6.9",
        "question_id":59
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"TOWS Equipment",
        "display_number":"6.10",
        "question_id":60
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"LOWS Equipment",
        "display_number":"6.11",
        "question_id":61
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"Lookout/s",
        "display_number":"6.12",
        "question_id":62
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"Line blockage arrangements",
        "display_number":"6.13",
        "question_id":63
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"All documentation completed",
        "display_number":"6.14",
        "question_id":64
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"Is SSOW adequate",
        "display_number":"6.15",
        "question_id":65
    },
    {
        "category":"Possessions, Isolations & Protection Arrangements",
        "question":"ALO Working Arrangements",
        "display_number":"6.16",
        "question_id":66
    },
    {
        "category":"LUL Traffic Hour",
        "question":"Green/Amber Zone Authority",
        "display_number":"7.1",
        "question_id":67
    },
    {
        "category":"LUL Traffic Hour",
        "question":"Protection Adequate",
        "display_number":"7.2",
        "question_id":68
    },
    {
        "category":"LUL Engineering Hours",
        "question":"Line Clear - Protection Adequate",
        "display_number":"8.1",
        "question_id":69
    },
    {
        "category":"LUL Engineering Hours",
        "question":"Line Safe - Protection Adequate",
        "display_number":"8.2",
        "question_id":70
    },
    {
        "category":"General Operational",
        "question":"Armbands & ID",
        "display_number":"9.1",
        "question_id":71
    },
    {
        "category":"General Operational",
        "question":"Equipment, e.g. Lookout/Hand/S",
        "display_number":"9.2",
        "question_id":72
    },
    {
        "category":"General Operational",
        "question":"SWL/COSS/SPC/PM/PWT",
        "display_number":"9.3",
        "question_id":73
    },
    {
        "category":"Other",
        "question":"Sample Sentinel card check",
        "display_number":"10.1",
        "question_id":74
    },
    {
        "category":"Other",
        "question":"No.",
        "display_number":"10.1a",
        "question_id":75
    },
    {
        "category":"Other",
        "question":"No.",
        "display_number":"10.1b",
        "question_id":76
    },
    {
        "category":"Other",
        "question":"No.",
        "display_number":"10.1c",
        "question_id":77
    },
    {
        "category":"Other",
        "question":"Work Experience (log) Blue Book Check",
        "display_number":"10.2",
        "question_id":78
    },
    {
        "category":"Other",
        "question":"Name",
        "display_number":"",
        "question_id":79
    }
];

//Last Question Id is 184
const metaInspectionTourIndustrialChecklist = [
    {
        "category": "Documentation",
        "question": "Construction phase plan",
        "display_number": "6.1",
        "question_id": 80
    },
    {
        "category": "Documentation",
        "question": "RAMS",
        "display_number": "6.2",
        "question_id": 81
    },
    {
        "category": "Documentation",
        "question": "RAMS Evaluation",
        "display_number": "6.3",
        "question_id": 82
    },
    {
        "category": "Documentation",
        "question": "Competence",
        "display_number": "6.4",
        "question_id": 83
    },
    {
        "category": "Documentation",
        "question": "Supervision",
        "display_number": "6.5",
        "question_id": 84
    },
    {
        "category": "Documentation",
        "question": "Inductions",
        "display_number": "6.5",
        "question_id": 168
    },
    {
        "category": "Documentation",
        "question": "Prestart H&S Meeting",
        "display_number": "6.5",
        "question_id": 169
    },
    {
        "category": "Documentation",
        "question": "Weekly EHS Return",
        "display_number": "6.5",
        "question_id": 170
    },
    {
        "category": "Documentation",
        "question": "Supervisor Briefings",
        "display_number": "6.5",
        "question_id": 171
    },
    {
        "category": "Good order",
        "question": "Housekeeping",
        "display_number": "7.1",
        "question_id": 86
    },
    {
        "category": "Good order",
        "question": "Material Storage",
        "display_number": "7.2",
        "question_id": 87
    },
    {
        "category": "Good order",
        "question": "Access Routes",
        "display_number": "7.3",
        "question_id": 88
    },
    {
        "category": "Good order",
        "question": "Site Security",
        "display_number": "7.4",
        "question_id": 89
    },
    {
        "category": "Good order",
        "question": "Lighting",
        "display_number": "7.4",
        "question_id": 178
    },
    {
        "category": "Occupational Health",
        "question": "Coronavirus",
        "display_number": "8.1",
        "question_id": 91
    },
    {
        "category": "Occupational Health",
        "question": "PPE",
        "display_number": "8.2",
        "question_id": 92
    },
    {
        "category": "Occupational Health",
        "question": "Manual Handling",
        "display_number": "8.3",
        "question_id": 93
    },
    {
        "category": "Occupational Health",
        "question": "Dust Control",
        "display_number": "8.4",
        "question_id": 94
    },
    {
        "category": "Occupational Health",
        "question": "Noise Control",
        "display_number": "8.5",
        "question_id": 95
    },
    {
        "category": "Occupational Health",
        "question": "Fitness to Work Medicals",
        "display_number": "8.6",
        "question_id": 96
    },
    {
        "category": "Occupational Health",
        "question": "Vibration",
        "display_number": "8.7",
        "question_id": 97
    },
    {
        "category": "Traffic Management",
        "question": "Traffic Management Plan",
        "display_number": "9.1",
        "question_id": 99
    },
    {
        "category": "Traffic Management",
        "question": "Segregation",
        "display_number": "9.2",
        "question_id": 100
    },
    {
        "category": "Traffic Management",
        "question": "Pedestrian Routes",
        "display_number": "9.3",
        "question_id": 101
    },
    {
        "category": "Traffic Management",
        "question": "Competence",
        "display_number": "9.4",
        "question_id": 102
    },
    {
        "category": "Temporary Works",
        "question": "TW Control Register",
        "display_number": "10.1",
        "question_id": 104
    },
    {
        "category": "Temporary Works",
        "question": "TW Design",
        "display_number": "10.2",
        "question_id": 105
    },
    {
        "category": "Temporary Works",
        "question": "TW Coordinator",
        "display_number": "10.3",
        "question_id": 106
    },
    {
        "category": "Temporary Works",
        "question": "TW Controls",
        "display_number": "10.4",
        "question_id": 107
    },
    {
        "category": "Temporary Works",
        "question": "Permits",
        "display_number": "10.5",
        "question_id": 108
    },
    {
        "category": "Plant",
        "question": "Thorough Examinations",
        "display_number": "11.1",
        "question_id": 110
    },
    {
        "category": "Plant",
        "question": "Inspections",
        "display_number": "11.2",
        "question_id": 111
    },
    {
        "category": "Plant",
        "question": "Competence",
        "display_number": "11.3",
        "question_id": 112
    },
    {
        "category": "Plant",
        "question": "Telehandlers",
        "display_number": "11.3",
        "question_id": 180
    },
    {
        "category": "Plant",
        "question": "Bucket Changing Area",
        "display_number": "11.3",
        "question_id": 181
    },
    {
        "category": "Plant",
        "question": "Condition",
        "display_number": "11.4",
        "question_id": 183
    },
    {
        "category": "Plant",
        "question": "Use",
        "display_number": "11.5",
        "question_id": 184
    },
    {
        "category": "Equipment",
        "question": "Condition",
        "display_number": "12.1",
        "question_id": 114
    },
    {
        "category": "Equipment",
        "question": "Inspection",
        "display_number": "12.2",
        "question_id": 115
    },
    {
        "category": "Equipment",
        "question": "Use",
        "display_number": "12.3",
        "question_id": 116
    },
    {
        "category": "Demolition/Asbestos",
        "question": "Documentation",
        "display_number": "13.1",
        "question_id": 118
    },
    {
        "category": "Demolition/Asbestos",
        "question": "Competency",
        "display_number": "13.2",
        "question_id": 119
    },
    {
        "category": "Demolition/Asbestos",
        "question": "Plant",
        "display_number": "13.3",
        "question_id": 120
    },
    {
        "category": "Demolition/Asbestos",
        "question": "Air Monitoring",
        "display_number": "13.4",
        "question_id": 121
    },
    {
        "category": "Demolition/Asbestos",
        "question": "Permits",
        "display_number": "13.5",
        "question_id": 122
    },
    {
        "category": "Fire and Emergency",
        "question": "Fire Plan",
        "display_number": "14.1",
        "question_id": 124
    },
    {
        "category": "Fire and Emergency",
        "question": "Fire Risk Assessment",
        "display_number": "14.2",
        "question_id": 125
    },
    {
        "category": "Fire and Emergency",
        "question": "First Aid",
        "display_number": "14.3",
        "question_id": 126
    },
    {
        "category": "Fire and Emergency",
        "question": "Emergency Rescue",
        "display_number": "14.4",
        "question_id": 127
    },
    {
        "category": "Fire and Emergency",
        "question": "Fire Controls",
        "display_number": "14.5",
        "question_id": 128
    },
    {
        "category": "Fire and Emergency",
        "question": "Flammables",
        "display_number": "14.6",
        "question_id": 129
    },
    {
        "category": "Fire and Emergency",
        "question": "COSHH",
        "display_number": "14.6",
        "question_id": 175
    },
    {
        "category": "Fire and Emergency",
        "question": "Fire Drill",
        "display_number": "14.6",
        "question_id": 176
    },
    {
        "category": "Fire and Emergency",
        "question": "Electrics",
        "display_number": "14.6",
        "question_id": 177
    },
    {
        "category": "Excavations",
        "question": "Trench Support",
        "display_number": "15.1",
        "question_id": 131
    },
    {
        "category": "Excavations",
        "question": "Edge Protection",
        "display_number": "15.2",
        "question_id": 132
    },
    {
        "category": "Excavations",
        "question": "Emergency Rescue",
        "display_number": "15.3",
        "question_id": 133
    },
    {
        "category": "Excavations",
        "question": "Access",
        "display_number": "15.3",
        "question_id": 172
    },
    {
        "category": "Excavations",
        "question": "Signage",
        "display_number": "15.3",
        "question_id": 173
    },
    {
        "category": "Excavations",
        "question": "Manhole Covers",
        "display_number": "15.3",
        "question_id": 174
    },
    {
        "category": "Underground Services",
        "question": "Service Avoidance Plan",
        "display_number": "16.1",
        "question_id": 135
    },
    {
        "category": "Underground Services",
        "question": "Permit to Break Ground",
        "display_number": "16.2",
        "question_id": 136
    },
    {
        "category": "Underground Services",
        "question": "Cable Avoidance Tool",
        "display_number": "16.3",
        "question_id": 137
    },
    {
        "category": "Underground Services",
        "question": "Survey",
        "display_number": "16.4",
        "question_id": 138
    },
    {
        "category": "Underground Services",
        "question": "Insulated Tools",
        "display_number": "16.5",
        "question_id": 139
    },
    {
        "category": "Work at Height",
        "question": "Scaffolding",
        "display_number": "17.1",
        "question_id": 141
    },
    {
        "category": "Work at Height",
        "question": "Edge Protection",
        "display_number": "17.2",
        "question_id": 142
    },
    {
        "category": "Work at Height",
        "question": "MEWP’s",
        "display_number": "17.3",
        "question_id": 143
    },
    {
        "category": "Work at Height",
        "question": "Ladders",
        "display_number": "17.4",
        "question_id": 144
    },
    {
        "category": "Work at Height",
        "question": "Mobile Towers",
        "display_number": "17.5",
        "question_id": 145
    },
    {
        "category": "Work at Height",
        "question": "Podiums",
        "display_number": "17.6",
        "question_id": 146
    },
    {
        "category": "Work at Height",
        "question": "Access Stairs",
        "display_number": "17.7",
        "question_id": 147
    },
    {
        "category": "Work at Height",
        "question": "Loading Bays",
        "display_number": "17.8",
        "question_id": 148
    },
    {
        "category": "Work at Height",
        "question": "Exclusion Zones",
        "display_number": "17.8",
        "question_id": 182
    },
    {
        "category": "Lifting",
        "question": "Lift Plan",
        "display_number": "18.1",
        "question_id": 150
    },
    {
        "category": "Lifting",
        "question": "Cranes",
        "display_number": "18.2",
        "question_id": 151
    },
    {
        "category": "Lifting",
        "question": "Thorough Examination",
        "display_number": "18.3",
        "question_id": 152
    },
    {
        "category": "Lifting",
        "question": "Accessories",
        "display_number": "18.4",
        "question_id": 153
    },
    {
        "category": "Lifting",
        "question": "Appointed Person",
        "display_number": "18.5",
        "question_id": 154
    },
    {
        "category": "Lifting",
        "question": "Slinger/Signaler",
        "display_number": "18.6",
        "question_id": 155
    },
    {
        "category": "Lifting",
        "question": "Crane Supervisor",
        "display_number": "18.7",
        "question_id": 156
    },
    {
        "category": "Lifting",
        "question": "Permit For Lifting",
        "display_number": "18.7",
        "question_id": 179
    },
    {
        "category": "Welfare",
        "question": "Drying Room",
        "display_number": "19.1",
        "question_id": 158
    },
    {
        "category": "Welfare",
        "question": "Toilets",
        "display_number": "19.2",
        "question_id": 159
    },
    {
        "category": "Welfare",
        "question": "Canteen",
        "display_number": "19.3",
        "question_id": 160
    },
    {
        "category": "Welfare",
        "question": "Offices",
        "display_number": "19.4",
        "question_id": 161
    },
    {
        "category": "Environmental",
        "question": "Waste Management",
        "display_number": "25.1",
        "question_id": 163
    },
    {
        "category": "Environmental",
        "question": "Pollution Prevention",
        "display_number": "25.2",
        "question_id": 164
    },
    {
        "category": "Environmental",
        "question": "Water Discharges",
        "display_number": "25.3",
        "question_id": 165
    },
    {
        "category": "Environmental",
        "question": "Concrete Wash Water",
        "display_number": "25.4",
        "question_id": 166
    },
    {
        "category": "Environmental",
        "question": "Ecology",
        "display_number": "25.5",
        "question_id": 167
    }
];

//Last Question Id is 25
const metaAccessEquipmentPodiumStepladderChecklist = [
    {
        "category": "Training",
        "question": "Have all staff who will use podium stepladders been trained in their use?",
        "question_id": 1
    },
    {
        "category": "Site conditions",
        "question": "Is the floor area where the podium stepladders are to be used free from obstacles, debris, trailing cables, etc?",
        "question_id": 2
    },
    {
        "category": "Site conditions",
        "question": "Will the podium stepladder be erected on a flat stable surface?",
        "question_id": 3
    },
    {
        "category": "Pre-assembly checks",
        "question": "Do the podium stepladders have a label fitted containing a company number and a test date as well as a manufacturer’s label?",
        "question_id": 4
    },
    {
        "category": "Pre-assembly checks",
        "question": "Have all welds been checked for cracks or other damage?",
        "question_id": 5
    },
    {
        "category": "Pre-assembly checks",
        "question": "Have all tubes been checked for a sign of physical damage i.e. cracks, distortion or excessive dents?",
        "question_id": 6
    },
    {
        "category": "Pre-assembly checks",
        "question": "Is the working platform free from contamination, cracking, holes or other damage, including any missing or damaged rivet fixings?",
        "question_id": 7
    },
    {
        "category": "Pre-assembly checks",
        "question": "Is there an absence of sharp edges or splinters?",
        "question_id": 8
    },
    {
        "category": "Pre-assembly checks",
        "question": "Is the stepladder free from paint or decoration which could obscure damage?",
        "question_id": 9
    },
    {
        "category": "Pre-assembly checks",
        "question": "Are all required nails, screws, bolts, tie rods and rivets present and firmly fixed?",
        "question_id": 10
    },
    {
        "category": "Pre-assembly checks",
        "question": "If the stepladder has a non-slip base, is it undamaged?",
        "question_id": 11
    },
    {
        "category": "Pre-assembly checks",
        "question": "Are the stepladder feet in good condition?",
        "question_id": 12
    },
    {
        "category": "Pre-assembly checks",
        "question": "Have previous repairs been carried out to a high standard?",
        "question_id": 13
    },
    {
        "category": "Pre-assembly checks",
        "question": "Do all fittings appear to be of an approved type?",
        "question_id": 14
    },
    {
        "category": "Pre-assembly checks",
        "question": "Is there a visible identification number?",
        "question_id": 15
    },
    {
        "category": "Checks during/after assembly and before use",
        "question": "Are all castors secure and their fixing bolts tightened correctly?",
        "question_id": 16
    },
    {
        "category": "Checks during/after assembly and before use",
        "question": "Do all wheel brakes operate and lock the wheels and castors into position?",
        "question_id": 17
    },
    {
        "category": "Checks during/after assembly and before use",
        "question": "Is there free movement of all hinge points of the podium step?",
        "question_id": 18
    },
    {
        "category": "Checks during/after assembly and before use",
        "question": "Will the total load (1 person plus tools and materials) be less than the manufacturer's stated safer working load?",
        "question_id": 19
    },
    {
        "category": "Checks during/after assembly and before use",
        "question": "Are the podium stepladder’s locking mechanisms operating and locking correctly?",
        "question_id": 20
    },
    {
        "category": "Checks during/after assembly and before use",
        "question": "Are the castors pointing outwards and the wheel brakes locked prior to access?",
        "question_id": 21
    },
    {
        "category": "Checks during/after assembly and before use",
        "question": "Is the ladder section located correctly and secure?",
        "question_id": 22
    },
    {
        "category": "Checks during/after assembly and before use",
        "question": "Is the podium stepladder correctly positioned to avoid over-reaching?",
        "question_id": 23
    },
    {
        "category": "Checks during/after assembly and before use",
        "question": "Is the working platform set at the correct height?",
        "question_id": 24
    },
    {
        "category": "Checks during/after assembly and before use",
        "question": "Is the safety gate shut and locked once staff are on the platform?",
        "question_id": 25
    },
];

//Last Question Id is 15
const metaAccessEquipmentLadderChecklist = [
    {
        "category": "Inspection",
        "question": "Is the ladder free from any modification (painted, shortened, etc.)?",
        "question_id": 1
    },
    {
        "category": "Inspection",
        "question": "Are stiles (uprights) free from damage, cracking, corrosion or dents?",
        "question_id": 2
    },
    {
        "category": "Inspection",
        "question": "Are stiles (uprights) free from excessive wearing, splitting or warping?",
        "question_id": 3
    },
    {
        "category": "Inspection",
        "question": "Are rungs (steps) free from wear, looseness or damage?",
        "question_id": 4
    },
    {
        "category": "Inspection",
        "question": "Are rungs (steps) free from damage, cracking, corrosion or dents?",
        "question_id": 5
    },
    {
        "category": "Inspection",
        "question": "Are all treads on rungs (steps) present and free from damage?",
        "question_id": 6
    },
    {
        "category": "Inspection",
        "question": "Are stiles (uprights) or rungs (steps) tight and are dogs in place tight and free from bends?",
        "question_id": 7
    },
    {
        "category": "Inspection",
        "question": "Are all fixings (e.g. bolts or Screws) in place?",
        "question_id": 8
    },
    {
        "category": "Inspection",
        "question": "Are fittings free from excessive wear and all in place?",
        "question_id": 9
    },
    {
        "category": "Inspection",
        "question": "Is the ladder free from distortion and warping?",
        "question_id": 10
    },
    {
        "category": "Inspection",
        "question": "Is ladder identification in place?",
        "question_id": 11
    },
    {
        "category": "Inspection",
        "question": "Are feet free from excessive wearing?",
        "question_id": 12
    },
    {
        "category": "Inspection",
        "question": "If top bar guide is fitted, is it in good condition and secure?",
        "question_id": 13
    },
    {
        "category": "Inspection",
        "question": "If applicable, is reinforcing wire in place and sound?",
        "question_id": 14
    },
    {
        "category": "Inspection",
        "question": "Reinforcing wire (where applicable) check that this is in place and sound",
        "question_id": 15
    }
]

//Last Question Id is 31
const metaAccessEquipmentMobileScaffoldTowerChecklist = [
    {
        "category": "Castors",
        "question": "Are castor housings, wheels and tyres free from damage?",
        "question_id": 1
    },
    {
        "category": "Castors",
        "question": "Do wheels rotate freely?",
        "question_id": 2
    },
    {
        "category": "Castors",
        "question": "Do castors swivel/ rotate properly?",
        "question_id": 3
    },
    {
        "category": "Castors",
        "question": "Do wheel brakes function properly?",
        "question_id": 4
    },
    {
        "category": "Adjustable Legs",
        "question": "Are legs straight and free from any bends?",
        "question_id": 5
    },
    {
        "category": "Adjustable Legs",
        "question": "Are threads free from damage?",
        "question_id": 6
    },
    {
        "category": "Adjustable Legs",
        "question": "Are threads clean and free from debris?",
        "question_id": 7
    },
    {
        "category": "Adjustable Legs",
        "question": "Are devices to stop the leg/s falling out of the frame functioning correctly?",
        "question_id": 8
    },
    {
        "category": "Frames",
        "question": "Are frame members straight and undamaged?",
        "question_id": 9
    },
    {
        "category": "Frames",
        "question": "Are frame members free of extraneous material?",
        "question_id": 10
    },
    {
        "category": "Frames",
        "question": "Are spigots straight and parallel with the axis of the column tube?",
        "question_id": 11
    },
    {
        "category": "Frames",
        "question": "Are devices for locking frames together functioning correctly?",
        "question_id": 12
    },
    {
        "category": "Platforms",
        "question": "Are platforms free from damage?",
        "question_id": 13
    },
    {
        "category": "Platforms",
        "question": "Are frames square and true?",
        "question_id": 14
    },
    {
        "category": "Platforms",
        "question": "Are decks free from warping and splitting?",
        "question_id": 15
    },
    {
        "category": "Platforms",
        "question": "Will the total load (1 person plus tools and materials) be less than the manufacturer's stated safer working load?",
        "question_id": 16
    },
    {
        "category": "Platforms",
        "question": "Are toe board clips/fittings free from damage?",
        "question_id": 17
    },
    {
        "category": "Platforms",
        "question": "Are outriggers, stabilizers, hooks or couplers free from damage?",
        "question_id": 18
    },
    {
        "category": "Pre-User Checks",
        "question": "Is the ground firm and level?",
        "question_id": 19
    },
    {
        "category": "Pre-User Checks",
        "question": "Is the area free from overhead obstructions/hazards?",
        "question_id": 20
    },
    {
        "category": "Pre-User Checks",
        "question": "Do weather conditions permit safe use?",
        "question_id": 21
    },
    {
        "category": "Pre-User Checks",
        "question": "Is the height of the mobile tower less than 3 times the length of the shortest side?",
        "question_id": 22
    },
    {
        "category": "Pre-User Checks",
        "question": "Is the mobile tower vertical and square and are the horizontal braces and platform level?",
        "question_id": 23
    },
    {
        "category": "Pre-User Checks",
        "question": "Are outriggers/stabilisers correctly positioned and secured?",
        "question_id": 24
    },
    {
        "category": "Pre-User Checks",
        "question": "Are all base plates/castor wheels in full contact with the ground, including those on stabilizers/outriggers & have these been properly locked?",
        "question_id": 25
    },
    {
        "category": "Pre-User Checks",
        "question": "Are all spigot and socket joint locks (which hold the frames together) secured?",
        "question_id": 26
    },
    {
        "category": "Pre-User Checks",
        "question": "Have all bracing members been located exactly in accordance with the supplier's instructions?",
        "question_id": 27
    },
    {
        "category": "Pre-User Checks",
        "question": "Are all guardrails and toe boards in position. (Guardrails should be at least 910 mm high and toe boards at least 150 mm high. An intermediate guard rail or suitable alternative should be provided so the unprotected gap does not exceed 470 mm)?",
        "question_id": 28
    },
    {
        "category": "Pre-User Checks",
        "question": "Are access ladders in position and firmly located?",
        "question_id": 29
    },
    {
        "category": "Pre-User Checks",
        "question": "Are barriers in place at ground level to prevent people walking into the tower or straying into the work area?",
        "question_id": 30
    },
    {
        "category": "Pre-User Checks",
        "question": "Has the tower been inspected and tagged by a competent person?",
        "question_id": 31
    },
]

const incident_report_meta_data = {
    "incident_type": [
        "Injury",
        "Health",
        "Road Traffic",
        "Damage or Loss",
        "Environmental",
        "Violence or Abuse",
        "Service Strike"
    ],
    "Injury": {
        "incident_classification": [
            "High Potential Learning Event (HPLE)",
            "Classification Pending",
            "Non-Treatment Injury (NTI)",
            "First Aid Case (FAC)",
            "Medical Treatment Case (MTC)",
            "Modified Work Duty (MWD)",
            "<=7 Day Lost Time Injury (LTI)",
            ">7 Day Lost Time Injury (LTI)",
            "RIDDOR Specified Injury",
            "Fatal"
        ],
        "lighting_conditions": [
            "Daylight",
            "Dark",
            "Artificial Light"
        ],
        "weather_conditions": [
            "Cloudy",
            "Clear",
            "Foggy",
            "Rainy",
            "Sunny",
            "Stormy",
            "Snowy/Icy",
            "Indoors - N/A"
        ],
        "incident_category": [
            "Contact with moving machinery",
            "Hit by moving vehicle",
            "Hit by moving, flying, falling object",
            "Hit something fixed or stationary",
            "Injured while handling, lifting or carrying",
            "Trapped by something collapsing",
            "Slipped, tripped or fell",
            "Fell from height",
            "Exposed to fire",
            "Exposed to explosion",
            "Exposed to / contact with harmful substance",
            "Exposed to / contact with electricity / electrical discharge",
            "Drowned or asphyxiated",
            "Injured by animal",
            "Physically assaulted by person"
        ],
        "gender": [
            'Man',
            'Woman',
            'Non Binary',
            'Other',
            'Prefer not to say'
        ],
        "severity": [
            "Critical (Major)",
            "High Potential",
            "Serious (High)",
            "Moderate",
            "Minor"
        ],
        "person_type": [
            "Employee",
            "Contractor",
            "Member of the public",
            "Delivery Driver",
            "Visitor",
            "Work Experience"
        ],
        "injury_type": [
            "Amputation",
            "Arc Eye",
            "Bruising",
            "Burn / Scald",
            "Chemical Burn",
            "Crush",
            "Cut / Incision",
            "Dermatitis",
            "Electrocution",
            "Foreign Body in Eye",
            "Fracture",
            "Graze",
            "Hand Arm Vibration",
            "Laceration",
            "Loss of Sight (Permanent)",
            "Loss of Sight (Temporary)",
            "Noise",
            "Penetration / Puncture",
            "Strain / Sprain"
        ],
        "parts_of_body_effected": [
            "Ankle",
            "Arm - Lower",
            "Arm - Upper",
            "Back",
            "Chest",
            "Collar Bone",
            "Ear",
            "Elbow",
            "Eye",
            "Face",
            "Finger",
            "Foot",
            "Groin",
            "Hand",
            "Head",
            "Knee",
            "Leg - Lower",
            "Leg - Upper",
            "Neck",
            "Nose",
            "Pelvis",
            "Posterior",
            "Shoulder",
            "Stomach",
            "Thumb",
            "Toe",
            "Wrist"
        ],
        "injured_side": [
            "Both",
            "Left",
            "Right",
            "N/A"
        ],
        "priority": [
            "High",
            "Medium",
            "Low"
        ]
    },
    "Health": {
        "environmental_incident": [
            "Airborne emissions",
            "Protected species",
            "Waste",
            "Breach of permit/license",
            "Fly tip/litter",
            "Land contamination",
            "Oil/Chemical spill",
            "Pests/Vermin (rats, mice etc.)",
            "Water born emissions"
        ],
        "type_of_health_incident": [
            "Existing Medical Condition",
            "Exposure to Asbestos",
            "Expsoure to Biological Agent",
            "Exposure to Dust",
            "Exposure to Hazardous Substance",
            "Exposure to Noise",
            "Exposure to Radiation",
            "Exposure to Vibration"
        ],
        "gender": [
            'Man',
            'Woman',
            'Non Binary',
            'Other',
            'Prefer not to say'
        ],
        "severity": [
            "Critical (Major)",
            "High Potential",
            "Serious (High)",
            "Moderate",
            "Minor"
        ],
        "weather_conditions": [
            "Cloudy",
            "Clear",
            "Foggy",
            "Rainy",
            "Sunny",
            "Stormy",
            "Snowy/Icy",
            "Indoors - N/A"
        ],
        "person_type": [
            "Employee",
            "Contractor",
            "Member of the public",
            "Delivery Driver",
            "Visitor",
            "Work Experience"
        ],
        "lighting_conditions": [
            "Daylight",
            "Dark",
            "Artificial Light"
        ]
    },
    "Road Traffic": {
        "environmental_incident": [
            "Airborne emissions",
            "Protected species",
            "Waste",
            "Breach of permit/license",
            "Fly tip/litter",
            "Land contamination",
            "Oil/Chemical spill",
            "Pests/Vermin (rats, mice etc.)",
            "Water born emissions"
        ],
        "gender": [
            'Man',
            'Woman',
            'Non Binary',
            'Other',
            'Prefer not to say'
        ],
        "weather_conditions": [
            "Cloudy",
            "Clear",
            "Foggy",
            "Rainy",
            "Sunny",
            "Stormy",
            "Snowy/Icy",
            "Indoors - N/A"
        ],
        "location_type": [
            "Carpark",
            "Delivery/Collection Area",
            "Own Site",
            "Unknown",
            "Road",
            "Services",
            "Site Compound",
            "Yard"
        ],
        "road_type": [
            "Motorway",
            "Dual Carriageway",
            "Own Site",
            "Minor Road",
            "Road",
            "Other",
            "Dual Unknown"
        ],
        "lighting_conditions": [
            "Daylight",
            "Dark",
            "Artificial Light"
        ],
        "weather_visibility": [
            "Good",
            "Satisfactory",
            "Poor",
            "Other",
            "Unknown"
        ],
        "vehicle_type": [
            "Dozer",
            "Dumper",
            "Excavator",
            "Forklift",
            "Lorry",
            "Motorcycle",
            "Multiple Occupancy Vehicle",
            "Private Car",
            "Roller",
            "Scooter",
            "Tractor Unit",
            "Van",
            "Other"
        ],
        "severity": [
            "Critical (Major)",
            "High Potential",
            "Serious (High)",
            "Moderate",
            "Minor"
        ],
        "person_type": [
            "Employee",
            "Contractor",
            "Member of the public",
            "Delivery Driver",
            "Visitor",
            "Work Experience"
        ],
        "third_party_category": [
            "Person",
            "Property",
            "Vehicle"
        ],
        "vehicle_status": [
            "Owned",
            "Hired",
            "Leased"
        ]
    },
    "Damage or Loss": {
        "lighting_conditions": [
            "Daylight",
            "Dark",
            "Artificial Light"
        ],
        "type_of_damage_loss": [
            "Building (Third Party Owned)",
            "Building (Company Owned)",
            "Fixed Plant (Third Party Owned)",
            "Fixed Plant (Company Owned)",
            "Mobile Plant (Third Party Owned)",
            "Mobile Plant (Company Owned)",
            "Vehicle (Third Party Owned)",
            "Vehicle (Company Owned)"
        ],
        "severity": [
            "Critical (Major)",
            "High Potential",
            "Serious (High)",
            "Moderate",
            "Minor"
        ],
        "person_type": [
            "Employee",
            "Contractor",
            "Member of the public",
            "Delivery Driver",
            "Visitor",
            "Work Experience"
        ],
        "weather_conditions": [
            "Cloudy",
            "Clear",
            "Foggy",
            "Rainy",
            "Sunny",
            "Stormy",
            "Snowy/Icy",
            "Indoors - N/A"
        ]
    },
    "Environmental": {
        "environmental_incident": [
            "Airborne emissions",
            "Protected species",
            "Waste",
            "Breach of permit/license",
            "Fly tip/litter",
            "Land contamination",
            "Oil/Chemical spill",
            "Pests/Vermin (rats, mice etc.)",
            "Water Pollution"
        ],
        "severity": [
            "Critical (Major)",
            "High Potential",
            "Serious (High)",
            "Moderate",
            "Minor"
        ],
        "person_type": [
            "Employee",
            "Contractor",
            "Member of the public",
            "Delivery Driver",
            "Visitor",
            "Work Experience"
        ],
        "lighting_conditions": [
            "Daylight",
            "Dark",
            "Artificial Light"
        ],
        "weather_conditions": [
            "Cloudy",
            "Clear",
            "Foggy",
            "Rainy",
            "Sunny",
            "Stormy",
            "Snowy/Icy",
            "Indoors - N/A"
        ],
        "expected_root_cause": [
            "Operator error",
            "Permit not issued",
            "Inadequate Risk Assessment",
            "Inadequate supervision",
            "Inadequate training/instruction",
            "Lack of information",
            "Lack of monitoring/inspection",
            "Not in any way work related",
            "N/Poor communication/consultation",
            "Poor design of premises",
            "Poor emergency arrangements",
            "Poor job design",
            "Poor motivation",
            "Poor or wrong equipment",
            "Poor personnel selection",
            "Poor safe working procedures",
            "Poorly allocated responsibilities",
            "Poorly maintained equipment",
            "Poorly maintained premises",
            "Reckless behaviour",
            "Safe working procedure not followed"
        ]
    },
    "Violence or Abuse": {
        "gender": [
            'Man',
            'Woman',
            'Non Binary',
            'Other',
            'Prefer not to say'
        ],
        "severity": [
            "Critical (Major)",
            "High Potential",
            "Serious (High)",
            "Moderate",
            "Minor"
        ],
        "person_type": [
            "Employee",
            "Contractor",
            "Member of the public",
            "Delivery Driver",
            "Visitor",
            "Work Experience"
        ],
        "lighting_conditions": [
            "Daylight",
            "Dark",
            "Artificial Light"
        ],
        "abuse_type": [
            "Physical Abuse",
            "Verbal Abuse"
        ]
    },
    "Service Strike": {
        "weather_conditions": [
            "Cloudy",
            "Clear",
            "Foggy",
            "Rainy",
            "Sunny",
            "Stormy",
            "Snowy/Icy",
            "Indoors - N/A"
        ],
        "utility_strike": [
            "Electric",
            "Gas (Domestic)",
            "Gas (Mains)",
            "Media",
            "Street lighting cable",
            "Telecoms",
            "Water"
        ],
        "severity": [
            "Critical (Major)",
            "High Potential",
            "Serious (High)",
            "Moderate",
            "Minor"
        ],
        "person_type": [
            "Employee",
            "Contractor",
            "Member of the public",
            "Delivery Driver",
            "Visitor",
            "Work Experience"
        ],
        "chartered": [
            "Chartered",
            "Unchartered"
        ],
        "lighting_conditions": [
            "Daylight",
            "Dark",
            "Artificial Light"
        ]
    }
};

const seedVehicleAssetsConfig = async(companyId) => {
    sails.log.info("seedVehicleAssetsConfig: meta config request for company: ", companyId);
    let assetVehicles = await sails.models.inndexsetting.findOne({ name: INNDEX_SETTING_KEY.TYPE_OF_ASSET_VEHICLES_EN_GB });
    let default_fields = [
        {
            title: 'Vehicle ID',
            key: 'vehicle_id',
            type: 'Textbox',
            required: true,
            display: true,
            alwaysDisplay: true,
        },
        {
            title: 'Owner',
            key: 'tagged_owner',
            type: 'Dropdown',
            required: true,
            display: true,
            alwaysDisplay: true,
        },

        {
            title: 'Reg./Serial Number',
            key: 'serial_number',
            type: 'Textbox',
            required: true,
            display: true,
            alwaysDisplay: true,
        },
        {
            title: 'Arrived on site',
            key: 'arrived_at',
            type: 'Date Selector',
            required: false,
            display: true
        },
        {
            title: 'Thorough Examination Certificate',
            key: 'examination_certification',
            type: 'Certification',
            required: false,
            display: true,
            hasCertificateNumber: true,
            document_number_mandatory: false,
            expiry_date_mandatory: true,
            attachment_mandatory: false
        },
        {
            title: 'Service Certificate',
            key: 'service_certification',
            type: 'Certification',
            required: false,
            display: true,
            hasCertificateNumber: false,
            document_number_mandatory: false,
            expiry_date_mandatory: true,
            attachment_mandatory: false
        }
    ];

    let engineRelatedFields = [{
        title: 'Power Output',
        key: 'power_output',
        type: 'Number',
        required: false,
        display: true
    },
    {
        title: 'Engine Manufacturer',
        key: 'engine_manufacturer',
        type: 'Textbox',
        required: false,
        display: true
    },
    {
        title: 'Year Manufactured',
        key: 'year_manufactured',
        type: 'Dropdown',
        required: false,
        display: true
    }];
    
    assetVehicles = assetVehicles.value;
    let inspectionCheckList = await sails.models.inndexsetting.findOne({ name: INNDEX_SETTING_KEY.VEHICLE_ASSET_INSPECTION_CHECKLIST });
    inspectionCheckList = inspectionCheckList.value;
    let meta_data = [];
    for(let i=0; i<assetVehicles.length; i++) {
        let meta_obj = {
            name: assetVehicles[i].value,
            key: assetVehicles[i].key,
            alternate_phrase: assetVehicles[i].value,
            asset_type: ASSET_TYPES.AssetVehicle,
            default_fields: default_fields,
            custom_fields: [],
            inspection_checklist_type: "default",
            inspection_checklist: [],
            checklist_draft: [],
            checklist_version: 2,
            company_ref: companyId,
        };
        if(assetVehicles[i].key != 'van') {
            meta_obj.default_fields = [...default_fields, ...engineRelatedFields];
        }
        if(assetVehicles[i].key === 'van') {
            let motCert = {
                title: 'MOT Certificate',
                key: 'mot_certification',
                type: 'Certification',
                required: false,
                display: true,
                hasCertificateNumber: false,
                document_number_mandatory: false,
                expiry_date_mandatory: true,
                attachment_mandatory: false
            };
            meta_obj.default_fields = [...default_fields, motCert];
        }
        let inspectionList = inspectionCheckList[assetVehicles[i].key+'_checklist'];
        if(inspectionList && Object.keys(inspectionList).length) {
            meta_obj.inspection_checklist_type = 'detailed';
            let checklistItems = [];
            if(assetVehicles[i].key === 'van') {
                checklistItems.push({
                    question_id: getUniqueId(),
                    question: 'Fuel Purchased (Litres)',
                    info: '',
                    type: 'Textbox',
                    required: true,
                    display: true,
                    constant: true
                },
                {
                    question_id: getUniqueId(),
                    question: 'Fuel Purchased (£)',
                    info: '',
                    type: 'Textbox',
                    required: true,
                    display: true,
                    constant: true
                },
                {
                    question_id: getUniqueId(),
                    question: 'Mileage',
                    info: '',
                    type: 'Textbox',
                    required: true,
                    display: true,
                    constant: true
                });
            } else {
                checklistItems.push({
                    question_id: getUniqueId(),
                    question: 'Plant Hours',
                    info: '',
                    type: 'Number',
                    required: false,
                    display: true,
                    constant: true
                });
            }
            let objPropList = Object.keys(inspectionList)
            for(let i=0; i<objPropList.length; i++) {
                if(!['Plant Hours', 'Mileage', 'Fuel Purchased (£)', 'Fuel Purchased (Litres)'].includes(inspectionList[objPropList[i]].title)) {
                    let item = {
                        question_id: getUniqueId(),
                        question: inspectionList[objPropList[i]].title,
                        info: inspectionList[objPropList[i]].info,
                        type: 'Checkbox',
                        required: true,
                    }
                    checklistItems.push(item);
                }
                
            }
            meta_obj.inspection_checklist = checklistItems;
            meta_obj.checklist_draft = checklistItems;
        }
        sails.log.info("adding meta config data for vehilce type: ", meta_obj.name);
        meta_data.push(meta_obj);
    }
    
    await sails.models.assetcustomconfig.destroy({
        asset_type: ASSET_TYPES.AssetVehicle,
        company_ref: companyId
    });
    await sails.models.assetcustomconfig.createEach(meta_data);
    await saveInitialChecklistVersions(companyId, ASSET_TYPES.AssetVehicle);
    return true;
};

const seedEquipmentAssetsConfig = async(companyId) => {
    sails.log.info("seedEquipmentAssetsConfig: meta config request for company: ", companyId);
    let assetTEquipments = await sails.models.inndexsetting.findOne({ name: INNDEX_SETTING_KEY.TYPE_OF_ASSET_EQUIPMENTS_EN_GB });
    
    let default_fields = [
        {
            title: 'Equipment ID',
            key: 'equipment_id',
            type: 'Textbox',
            required: true,
            display: true,
            alwaysDisplay: true
        },
        // {
        //     title: 'Equipment Type',
        //     key: 'equipment_type',
        //     type: 'Dropdown',
        //     required: true,
        //     display: true,
        //     alwaysDisplay: true,
        //     //options: TYPE_OF_EQUIPMENT
        // },
        {
            title: 'Item',
            key: 'item',
            type: 'Textbox',
            required: true,
            display: true,
            alwaysDisplay: true,
            // visibility: assetEquipment.equipment_type === 'other_equipment'
        },
        {
            title: 'Reg./Serial Number',
            key: 'serial_number',
            type: 'Textbox',
            required: true,
            display: true,
            alwaysDisplay: true,
        },
        {
            title: 'Owner',
            key: 'tagged_owner',
            type: 'Dropdown',
            required: true,
            display: true,
            alwaysDisplay: true,
        },
        {
            title: 'Arrived on site',
            key: 'arrived_at',
            type: 'Date Selector',
            required: false,
            display: true,
        },
        // {
        //     title: 'Thorough Examination Certificate Number',
        //     key: 'examination_cert_number',
        //     type: 'Textbox',
        //     required: false,
        //     hide: false,
        // },
        // {
        //     title: 'Thorough Examination Certificate Expiry Date',
        //     key: '_examinationCertExpiryDate',
        //     type: 'Date',
        //     required: false,
        //     hide: false,
        // },
        // {
        //     title: 'Thorough Examination Certificate',
        //     key: 'examination_certification',
        //     type: 'Certification',
        //     required: false,
        //     display: true,
        //     hasCertificateNumber: true,
        //     document_number_mandatory: false,
        //     expiry_date_mandatory: false,
        //     attachment_mandatory: false
        // },
        {
            title: 'Examination Certificate',
            key: 'examination_certification',
            type: 'Certification',
            required: false,
            display: true,
            hasCertificateNumber: true,
            document_number_mandatory: false,
            expiry_date_mandatory: true,
            attachment_mandatory: false
        },
        
    ];

    let accessDefaultFields = default_fields;
    accessDefaultFields = accessDefaultFields.map(field => 
        field.key === 'item' ? { ...field, type: 'Dropdown' } : field
    );

    assetEquipments = assetTEquipments.value;
    //sub types of access and fall arrest systems are added as individual types
    let assetEquipmentItems = [
        {
            key: 'ae_ladder',
            value: 'Ladder',
            has_specific_checklist: true,
            parent_key: 'access_equipment'
        },
        {
            key: 'ae_mobile_scaffold_tower',
            value: 'Mobile Scaffold Tower',
            has_specific_checklist: true,
            parent_key: 'access_equipment'
        },
        {
            key: 'ae_podium_stepladder',
            value: 'Podium Stepladder',
            has_specific_checklist: true,
            parent_key: 'access_equipment'
        },
        {
            key: 'fas_full_body_harness',
            value: 'Full Body Harness',
            has_specific_checklist: true,
            parent_key: 'fall_arrest_systems'
        },
        {
            key: 'fas_lanyard',
            value: 'Lanyard',
            has_specific_checklist: true,
            parent_key: 'fall_arrest_systems',
        }
    ];
    assetEquipments.push(...assetEquipmentItems);
    let fieldsForLiftingEquipment = [{
        title: 'Safe Working Load',
        key: 'safe_working_load',
        type: 'Textbox',
        required: false,
        display: true,
    }];
    let fieldsForPATTest = [{
        title: 'PAT Test',
        key: 'pat_test_expiry_date',
        type: 'Date Selector',
        required: false,
        display: true, 
    }];

    let inspectionCheckList = await sails.models.inndexsetting.findOne({ name: INNDEX_SETTING_KEY.EQUIPMENT_ASSET_INSPECTION_CHECKLIST });
    inspectionCheckList = inspectionCheckList.value;
    let meta_data = [];
    //assetEquipments = assetEquipments.filter(a=> !['access_equipment', 'fall_arrest_systems'].includes(a.key));
    for(let i=0; i<assetEquipments.length; i++) {
        let meta_obj = {
            name: assetEquipments[i].value,
            key: assetEquipments[i].key,
            alternate_phrase: assetEquipments[i].value,
            asset_type: ASSET_TYPES.AssetEquipment,
            custom_fields: [],
            inspection_checklist_type: "default",
            inspection_checklist: [],
            checklist_draft: [],
            checklist_version: 2,
            default_fields: default_fields,
            company_ref: companyId,
            parent_key: assetEquipments[i].parent_key? assetEquipments[i].parent_key: null,
        };
        if(['access_equipment', 'fall_arrest_systems'].includes(assetEquipments[i].key)) {
            meta_obj.default_fields = [];
            meta_data.push(meta_obj);
            continue;
        }
        if(assetEquipments[i].key ===  'lifting_equipment') {
            meta_obj.default_fields = [...default_fields, ...fieldsForLiftingEquipment];
        }
        else if(assetEquipments[i].key == 'electrical_equipment' || assetEquipments[i].key == 'monitoring_equipment' || assetEquipments[i].key == 'engineer_equipment') {
            meta_obj.default_fields = [...default_fields, ...fieldsForPATTest];
        }
        if(assetEquipments[i].key.startsWith('ae') || assetEquipments[i].key.startsWith('fas')) {
            meta_obj.default_fields = accessDefaultFields;
        }
        //adding inspection checklist
        let inspectionList = inspectionCheckList[assetEquipments[i].key+'_checklist'];
        if(inspectionList) {
            meta_obj.inspection_checklist_type = 'detailed';
            let checklistItems = [];
            for(let i=0; i<inspectionList.length; i++) {
                let item = {
                    question_id: getUniqueId(),
                    question: inspectionList[i].question,
                    type: 'Checkbox',
                    required: true,
                }
                checklistItems.push(item);
            }
            meta_obj.inspection_checklist = checklistItems;
            meta_obj.checklist_draft = checklistItems;
        }
        sails.log.info("adding meta config data for equipment type: ", meta_obj.name);
        meta_data.push(meta_obj);

    }
    await sails.models.assetcustomconfig.destroy({
        asset_type: ASSET_TYPES.AssetEquipment,
        company_ref: companyId
    });
    await sails.models.assetcustomconfig.createEach(meta_data);
    await saveInitialChecklistVersions(companyId, ASSET_TYPES.AssetEquipment);
    return true;
};

const seedTemporaryWorksConfig = async(companyId) => {
    sails.log.info("seedTemporaryWorksConfig: meta config request for company: ", companyId);
    let assetTemporaryWorks = await sails.models.inndexsetting.findOne({ name: INNDEX_SETTING_KEY.TYPE_OF_TEMPORARY_WORKS_EN_GB });
    let default_fields = [
        {
            title: 'Item ID',
            key: 'item_id',
            type: 'Textbox',
            required: true,
            display: true,
            alwaysDisplay: true,
        },
        {
            title: 'Item',
            key: 'item',
            type: 'Textbox',
            required: true,
            display: true,
            alwaysDisplay: true,
        },
        {
            title: 'Owner',
            key: 'tagged_owner',
            type: 'Dropdown',
            required: true,
            display: true,
            alwaysDisplay: true,
        },
        {
            title: 'Ref./Serial Number',
            key: 'serial_number',
            type: 'Textbox',
            required: true,
            display: true,
            alwaysDisplay: true,
        },
        {
            title: 'Risk Category',
            key: 'risk_category',
            type: 'Dropdown',
            required: false,
            display: true
        },
        {
            title: 'Date Erected',
            key: 'arrived_at',
            type: 'Date Selector',
            required: false,
            display: true
        },
        {
            title: 'Examination Certificate',
            key: 'examination_certification',
            type: 'Certification',
            required: false,
            display: true,
            hasCertificateNumber: true,
            document_number_mandatory: false,
            expiry_date_mandatory: true,
            attachment_mandatory: false
        },
    ];
    assetTemporaryWorks = assetTemporaryWorks.value;
    let inspectionCheckList = await sails.models.inndexsetting.findOne({ name: INNDEX_SETTING_KEY.TEMPORARY_WORK_ASSET_INSPECTION_CHECKLIST });
    inspectionCheckList = inspectionCheckList.value;
    let meta_data = [];
    for(let i=0; i<assetTemporaryWorks.length; i++) {
        let meta_obj = {
            name: assetTemporaryWorks[i].value,
            key: assetTemporaryWorks[i].key,
            alternate_phrase: assetTemporaryWorks[i].value,
            asset_type: ASSET_TYPES.AssetTemporaryWork,
            default_fields: default_fields,
            custom_fields: [],
            inspection_checklist_type: "default",
            inspection_checklist: [],
            checklist_draft: [],
            company_ref: companyId,
            checklist_version: 2,
        };
        let inspectionList = inspectionCheckList[assetTemporaryWorks[i].key+'_checklist'];
        if(inspectionList) {
            meta_obj.inspection_checklist_type = 'detailed';
            let checklistItems = [];
            for(let i=0; i<inspectionList.length; i++) {
                let item = {
                    question_id: getUniqueId(),
                    question: inspectionList[i].question,
                    type: 'Checkbox',
                    required: true,
                }
                checklistItems.push(item);
            }
            meta_obj.inspection_checklist = checklistItems;
            meta_obj.checklist_draft = checklistItems;
        }
        sails.log.info("adding meta config data for temporary works type: ", meta_obj.name);
        meta_data.push(meta_obj);
    }
    await sails.models.assetcustomconfig.destroy({
        asset_type: ASSET_TYPES.AssetTemporaryWork,
        company_ref: companyId
    });
    await sails.models.assetcustomconfig.createEach(meta_data);
    await saveInitialChecklistVersions(companyId, ASSET_TYPES.AssetTemporaryWork);
    return true;
    
};

const saveInitialChecklistVersions = async(companyId, assetType) => {
    sails.log.info("Save inital checklist versions information for company: ", companyId, ' asset type: ', assetType);
    let assets = await sails.models.assetcustomconfig.find({company_ref:companyId, asset_type: assetType});
    let assetVersions = assets.map(a=> {
        return {
            company_ref: companyId,
            asset_custom_config_ref: a.id,
            key: a.key,
            asset_type: a.asset_type,
            inspection_checklist: a.inspection_checklist,
            checklist_version: a.checklist_version,
            inspection_checklist_type: a.inspection_checklist_type
        }
    });
    await sails.models.assetinspectionchecklists.destroy({
        company_ref: companyId,
        asset_type: assetType,
    });
    await sails.models.assetinspectionchecklists.createEach(assetVersions);
    return;
};

const saveSeedingStatus = async(companyId, successTypes) => {
    let company_additonal_setting  = await sails.models.companysetting_reader.findOne({
        where: {
            company_ref: companyId,
            name: COMPANY_SETTING_KEY.ADDITIONAL_SETTINGS
        }
    });
    company_additonal_setting = company_additonal_setting || { name: COMPANY_SETTING_KEY.ADDITIONAL_SETTINGS, company_ref: companyId, enabled_on: dayjs().valueOf()};

    company_additonal_setting.value = {
        ...company_additonal_setting.value,  
        asset_seeding: {
            ...company_additonal_setting.value?.asset_seeding,  
            [ASSET_TYPES.AssetVehicle]: company_additonal_setting.value?.asset_seeding?.[ASSET_TYPES.AssetVehicle] || successTypes.includes(ASSET_TYPES.AssetVehicle),
            [ASSET_TYPES.AssetEquipment]: company_additonal_setting.value?.asset_seeding?.[ASSET_TYPES.AssetEquipment] || successTypes.includes(ASSET_TYPES.AssetEquipment),
            [ASSET_TYPES.AssetTemporaryWork]: company_additonal_setting.value?.asset_seeding?.[ASSET_TYPES.AssetTemporaryWork] || successTypes.includes(ASSET_TYPES.AssetTemporaryWork)
        }
    };
    if(company_additonal_setting.id) {
        company_additonal_setting = await sails.models.companysetting.updateOne({id: company_additonal_setting.id}).set(company_additonal_setting);
    } else {
        company_additonal_setting = await sails.models.companysetting.create(company_additonal_setting);
    }
    return company_additonal_setting;
}

const getUniqueId = () => {
    let timestamp = Date.now();
    let random = Math.floor(Math.random() * 10000);
    return parseInt(`${timestamp}${random}`);
};

const seedAssetMetaConfig = async(companyId) => {
    sails.log.info('Seeding meta asset config for company:', companyId);
    let assetTypes = ['asset-vehicle', 'asset-equipment', 'asset-temporary'];
    await seedVehicleAssetsConfig(companyId);
    await seedEquipmentAssetsConfig(companyId);
    await seedTemporaryWorksConfig(companyId);
    let company_additonal_setting = await saveSeedingStatus(companyId, assetTypes);
    sails.log.info('Assets meta config updated successfully for all asset types for company:', companyId );
};

const defaultDataForCloseCallCustomField = [
    {
      "is_default": true,
      "label": "Hazard Category",
      "field": "hazard_category",
      "is_active": true
    },
    {
      "is_default": true,
      "label": "Lighting Conditions",
      "field": "lighting_conditions",
      "is_active": true
    },
    {
      "is_default": true,
      "label": "Location Tag(Lat, Long)",
      "field": "location",
      "is_active": true
    },
    {
      "is_default": true,
      "label": "Location",
      "field": "location_and_description",
      "is_active": true
    },
    {
      "is_default": true,
      "label": "Details",
      "field": "additional_detail",
      "is_active": true
    },
    {
      "is_default": true,
      "label": "What could have happend?",
      "field": "cc_detail",
      "is_active": true
    }
];

module.exports = {
    meta_supervisor_induction_declarations,
    meta_operator_induction_declarations,
    metaInspectionTourCommonChecklist,
    metaInspectionTourRailChecklist,
    metaInspectionTourIndustrialChecklist,
    defaultDataForCloseCallCustomField,
    metaAccessEquipmentPodiumStepladderChecklist,
    metaAccessEquipmentLadderChecklist,
    metaAccessEquipmentMobileScaffoldTowerChecklist,
    incident_report_meta_data,
    seedAssetMetaConfig,
    seedVehicleAssetsConfig,
    seedEquipmentAssetsConfig,
    seedTemporaryWorksConfig,
}
