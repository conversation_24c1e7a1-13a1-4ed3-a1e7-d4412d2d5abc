/**
 * Created by <PERSON><PERSON><PERSON> on 20/5/19.
 */
const {
    coreFn: {runReadQuery},
    companyFn: {getUserInductionsIntoCompany},
} = require('./../sql.fn');
const {getUserFullName, populateEmployerRefs, getProjectTimezone,} = require('./DataProcessingService');
const {callOptimaBoxGateway, updateBadge} = require('./OptimaSyncService');
const HttpService = require('./HttpService');
const {sendMail, queueEmailNotifications} = require('./EmailService');
const {allProjectAdminsByOneOfDesignations, allResourceAdminsWithOneOfDesignations, filterProjectUsersEmailEligibility, ROLES} = require('./TokenUtil');
const moment = require('moment');
const momentTz = require('moment-timezone');
const _groupBy = require('lodash/groupBy');
const {
    EMAIL_NOTIFICATION_FEATURE_CODES,
    fall_back_timezone,
} = sails.config.constants;

const getMandatoryCompetencies = async () => {
    sails.log.info('get mandatory competencies');
    let records = await runReadQuery(`SELECT id, name, country_code FROM meta_competencies WHERE tags ?& array [$1];`, ['mandatory-competency']);
    sails.log.info('total competencies', records.length);
    let groups = records.reduce((list, row) => {
        if(!list[row.country_code]){
            list[row.country_code] = [];
        }
        list[row.country_code].push(row.name);
        return list;
    }, {});
    sails.log.info('total countries with mandatory competencies', Object.keys(groups));
    return groups;
};

const getExpiryDate = (date, tz) => {
    sails.log.info(`getExpiryDate fn called with date: ${date}, tz:${tz}`);
    let timeZone = tz ? tz : fall_back_timezone;
    return date ? momentTz(+parseInt(date)).tz(timeZone).format('D/MMM/Y') : '';
};

const processNextMonthExpiringCompetencies = async () => {
    /*
    + @DB: get all non-deleted docs expiring in coming month
    + group by `doc_owner_id`
    + @DB: get all users: {id, email, name, tz, parent_company: {id, name}}
    + @DB: get list of parent_companies, get list of managers of each parent_company
    + @Loop: each user group
        + @DB: get user's blacklisted status into his parent_company
        + @Loop: each user doc
            + @ses: send mail to user
            + @DB: get inductions having this document
            + @Loop: each induction
                + IF-exists: get project info from cache
                + ELSE:
                    + @DB: get active project
                    + if: active,
                        + @DB: get nominated site-admins of project, filter one having notification enabled,
                        + prepare list of mandatory competencies per project (global + other_doc_required)
                    + store data into cache
                + IF: not `blocked/rejected` induction state,
                    + add record to list for project_level_notification
            + @DB: update doc with `is_notified`
            + @lambda: if not company blacklisted, notify all managers
    + @Loop: each project_level_notification
        + @lambda: send email notification to project site-admin managers of given project
    */
    let epoch_after_30_days = momentTz().tz(fall_back_timezone).add(30, 'days').startOf('day').valueOf();
    let eod_epoch_after_30_days = momentTz().tz(fall_back_timezone).add(30, 'days').endOf('day').valueOf();

    const sql = `
        SELECT id, name, doc_number, expiry_date, doc_owner_id, parent_doc_ref
        FROM user_doc WHERE is_notified = 0 AND is_deleted = 0 AND expiry_date BETWEEN $1 AND $2;`;
    sails.log.info(`Executing DB query: ${sql}`, [epoch_after_30_days, eod_epoch_after_30_days]);
    let rawResult = await runReadQuery(sql, [epoch_after_30_days, eod_epoch_after_30_days]);
    if (!rawResult.length) {
        sails.log.info(`No records to process for expire after a month cron.`);
        return true;
    }

    let user_documents = _groupBy((rawResult || []), (row) => row.doc_owner_id);
    let unique_users = Object.keys(user_documents);
    sails.log.info(`Total ${rawResult.length} documents to be expired next month, owned by ${unique_users.length} users`);
    let users = await _getUsersMetaForCron(unique_users);

    let managers_per_company = await _getManagersPerParentCompany(users);

    let projectCache = {};
    let project_level_notification = {};

    for (let i = 0; i < users.length; i++) {
        let user = users[i];
        user.name = getUserFullName(user);
        let company = (user.parent_company && user.parent_company.id) ? user.parent_company : {};
        sails.log.info(`processing user:${user.id}, parent_company ${company.id || ''}, has_company_portal: ${company.has_company_portal || ''}`);
        if (company.id && company.has_company_portal) {
            let rows = await getUserInductionsIntoCompany(company.id, user.id, 5);
            if (rows.length) {
                user._isCompanyBlacklisted = true;
            }
        }

        let my_documents = user_documents[user.id] || [];
        for (let j = 0; j < my_documents.length; j++) {
            let doc = my_documents[j];

            sails.log.info(`processing doc:${doc.id}`);
            await _notifyDocOwner(user, doc);

            let document_inductions = await _getInductionsOfDocumentId(doc.doc_owner_id, doc);
            for (let k = 0, len = document_inductions.length; k < len; k++) {
                let ir = document_inductions[k];

                if (!projectCache[ir.project_ref]) {
                    projectCache[ir.project_ref] = await _getProjectMetaWithManagers(ir.project_ref, false);

                } else {
                    sails.log.info(`Using cache data for project: ${ir.project_ref}`);
                }

                let p = projectCache[ir.project_ref].project;
                if (!p) {
                    sails.log.info(`Project not found Or is not active, id: ${ir.project_ref}, skipping notification / induction update`);
                    continue;
                }
                if (![0, 4, 5].includes(ir.status_code)) {
                    // induction is not in blocked/rejected state, notify project managers
                    if (!project_level_notification[ir.project_ref]) {
                        project_level_notification[ir.project_ref] = [];
                    }
                    project_level_notification[ir.project_ref].push({
                        doc,
                        owner: user,
                        employer: ir.employer,
                    });
                }
            }
            try {
                // no need to filter for parent docs, as we are just marking for notification.
                await sails.models.userdoc.updateOne({id: doc.id}).set({is_notified: 1});
            } catch (e) {
                sails.log.info('Unable to update document record.', e);
                continue;
            }
            if (!company.id || !(managers_per_company[company.id] || []).length || user._isCompanyBlacklisted) {
                sails.log.info(`Skipping, ${(managers_per_company[company.id] || []).length} company nominated managers to notify, for company ${company.id}, or user is blacklisted`);
                continue;
            }

            await _notifyCompanyManagers(managers_per_company[company.id], user, doc);
        }
    }
    sails.log.info(`All users are processed for next month expiring documents`);

    let projects_to_notify = Object.keys(project_level_notification);
    sails.log.info(`Total ${projects_to_notify.length} projects to notify for next month expiry`);
    for (let i = 0; i < projects_to_notify.length; i++) {
        const project_ref = projects_to_notify[i];
        const entries = project_level_notification[project_ref];
        sails.log.info(`Notify project ${project_ref} for ${entries.length} entries expiring next month.`);
        // sails.log.info(entries);
        _notifyProjectManagersV2(projectCache[project_ref], entries, false).catch(sails.log.info);
    }

    return true;
};

/**
 *
 * @param user_ids
 * @returns {Promise<[{id, email, first_name, last_name, parent_company: {id, name}, timezone}]>}
 * @private
 */
const _getUsersMetaForCron = async (user_ids) => {
    let users = await sails.models.user_reader.find({
        where: {id: user_ids},
        select: ['id', 'email', 'first_name', 'middle_name', 'last_name', 'parent_company', 'timezone', 'is_active'],
    });

    users = await populateEmployerRefs(users, 'parent_company', ['id', 'name', 'country_code', 'has_company_portal']);

    return users;
};

/**
 *
 * @param users [{id, email, first_name, last_name, parent_company, timezone}]
 * @returns {Promise<{}>}
 * @private
 */
const _getManagersPerParentCompany = async (users) => {
    let parent_companies = Object.values(users.reduce((list, user) => {
        let company = (user.parent_company && user.parent_company.id) ? user.parent_company : {};
        if (!company.id || !company.has_company_portal) {
            return list;
        }
        list[company.id] = company;
        return list;
    }, {}));

    sails.log.info(`Total ${parent_companies.length} companies with portal enabled, fetch nominated managers`);
    let company_managers = {};
    for (let i = 0; i < parent_companies.length; i++) {
        let companyNominatedManagers = await allResourceAdminsWithOneOfDesignations(parent_companies[i].id, ['nominated'], true, ROLES.COMPANY_ADMIN);
        sails.log.info(`company: ${parent_companies[i].id}, total company nominated managers, count:`, companyNominatedManagers.length);

        company_managers[parent_companies[i].id] = (companyNominatedManagers || []).reduce((list, m) => {
            if (m.user_ref) {
                list.push({id: m.user_ref.id, name: getUserFullName(m.user_ref), email: m.user_ref.email});
            }
            return list;
        }, []);
    }
    return company_managers;
};

/**
 *
 * @param owner {{id, name, email, timezone, is_active}}
 * @param doc {{name, expiry_date}}
 * @param today
 * @returns {Promise<void>|Boolean}
 * @private
 */
const _notifyDocOwner = async (owner, doc, today = false) => {
    if(!owner.is_active){
        sails.log.info(`Skipping expiry notification to inactive user: ${owner.id}`);
        return true;
    }
    let subject = `Competency/Certification Expiry Notification`;
    if(today){
        let html = await sails.renderView('pages/mail/competency-expire-today', {
            title: subject,
            userDet: owner,
            factor: doc,
            isNomManager: false,
            type: 'html',
            expiryDate: getExpiryDate(doc.expiry_date, owner.timezone),
            layout: false
        });
        return await sendMail(subject, [owner.email], html);
    }

    let html = await sails.renderView('pages/mail/competency-expire-in-month', {
        title: subject,
        userDet: owner,
        factor: doc,
        type: 'html',
        expiryDate: getExpiryDate(doc.expiry_date, owner.timezone),
        layout: false
    });
    return await sendMail(subject, [owner.email], html);
};

/**
 *
 * @param managers [{id,name,email}]
 * @param owner {{name, first_name, timezone}}
 * @param doc {{name, expiry_date}}
 * @param expiring_today
 * @returns {Promise<boolean>}
 * @private
 */
const _notifyCompanyManagers = async (managers, owner, doc, expiring_today = false) => {
    sails.log.info(`Sending mail to company nominated managers, count: ${managers.length}`);
    return await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.COMPETENCY_EXPIRY_ALERT_FOR_MANAGER, managers, {
        "messageInfo": {
            mail_title: "Competency/Certification Expiry Notification",
            doc: {
                "expiring_today": expiring_today,
                "name": doc.name,
                "expiry_date": getExpiryDate(doc.expiry_date, owner.timezone),
                "owner_full_name": owner.name,
                "owner_first_name": (owner.first_name),
            },
        },
    });
};



const _notifyProjectManagersV2 = async ({project, managers}, entries, expiring_today = true) => {
    let template_file_ref = expiring_today ? 'pages/sls-email-partials/competency-expiring-today-project-n-manager' : 'pages/sls-email-partials/competency-expiring-30d-project-n-manager';
    let partial_html = await sails.renderView(template_file_ref, {
        entries,
        showExpiryDateFn: (epoch_string) => {
            return getExpiryDate(epoch_string, project.tz);
        },
        project,
        layout: false
    });
    sails.log.info(`Send email to project: ${project.id} nominated managers, expiring_today type: ${expiring_today}, count:`, managers.length);
    return await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.PLAIN_MESSAGE_ALERT, managers, {
        "messageInfo": {
            mail_title: `Competency/Certification Expiry: ${project.name}`,
            html_message: partial_html,
            origin_action: `competency-expiry-cron`,
        },
        project: {id: project.id, name: project.name},
    });
};

const _getInductionsOfDocumentId = async (doc_owner_id, doc) => {
    const documentInductionsQuery = `SELECT id,
                                           record_id,
                                           project_ref,
                                           comments,
                                           optima_badge_number::numeric,
                                           status_code,
                                           additional_data -> 'employment_detail' ->> 'employer' as employer
                                    from induction_request
                                    where user_doc_ids @> ($1)::jsonb
                                      and user_ref = $2 ;`;
    let document_inductions = await runReadQuery(documentInductionsQuery, [`${doc.id}`, doc_owner_id]);
    sails.log.info(`got total ${document_inductions.length} inductions with expired doc: ${doc.id}, owner: ${doc_owner_id}`);
    return document_inductions;
};

const _getProjectMetaWithManagers = async (project_ref, prefill_enabled_competency = false, mandatory_competencies_per_country = {}) => {
    sails.log.info(`fetching project: ${project_ref} meta and managers info`);
    let projectData = await sails.models.project_reader.findOne({
        where: {id: project_ref, is_active: 1},
        select: ['name', 'is_cscs_require', 'other_doc_required', 'blacklist_user_on_expiry', 'custom_field']
    });
    if(!projectData || !projectData.id){
        sails.log.info('Project not found Or is not active, id:', project_ref);
        return {
            project: null,
            managers: []
        }
    }

    projectData.tz = getProjectTimezone(projectData);

    if(prefill_enabled_competency){
        let cc = (projectData.custom_field && projectData.custom_field.country_code);
        projectData.enabled_competencies = [];
        if(projectData.is_cscs_require && cc){
            projectData.enabled_competencies.push(...(mandatory_competencies_per_country[cc] || []));
        }
        if(projectData.other_doc_required && HttpService.typeOf(projectData.other_doc_required, 'array')){
            projectData.enabled_competencies.push(...projectData.other_doc_required);
        }

        sails.log.info(`project:${project_ref}, blacklist_user_on_expiry: ${projectData.blacklist_user_on_expiry} cscs_required: ${(projectData.is_cscs_require && cc)}, other_doc_required:`, projectData.other_doc_required);
    }

    let projUsrResult = await allProjectAdminsByOneOfDesignations(projectData.id, ['nominated', 'custom']);
    projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, 'competency_expiry');

    let managers = (projUsrResult || []).reduce((list, m) => {
        if (m.user_ref) {
            list.push({id: m.user_ref.id, name: getUserFullName(m.user_ref), email: m.user_ref.email});
        }
        return list;
    }, []);

    return {
        project: projectData,
        managers: managers
    };
};

const processTodayExpiringCompetencies = async () => {
    /*
    + @DB: get all owner non-deleted docs, expiring today
    + group by `doc_owner_id`
    + @DB: get all users: {id, email, name, tz, parent_company: {id, name}}
    + @DB: get list of parent_companies, get list of managers of each parent_company
    + @DB: prepare list of mandatory competencies per country
    + @Loop: each user group
        + @DB: get user's blacklisted status into his parent_company
        + @Loop: each user doc
            + @ses: if doc not deleted manually, send mail to user.
            + @DB: get inductions having this document
            + @Loop: each induction
                + IF-exists: get project info from cache
                + ELSE:
                    + @DB: get active project
                    + if: active,
                        + @DB: get nominated site-admins of project, filter one having notification enabled,
                        + prepare list of mandatory competencies per project (global + other_doc_required)
                    + store data into cache
                + IF: not `blocked/rejected` induction state,
                    + add record to list for project_level_notification
                    // + @ses: send mail to all project site-admin managers
                + IF: `approve/in-review/change-request` AND project blacklisting enabled AND competency name is among required one.
                    + @DB: update induction to blacklisted state.
            + @DB: update if is a child doc, unlink it from parent
            + @DB: update document with `is_deleted_manually: 2?, is_notified: 2`
            + @lambda: if not company blacklisted, notify all managers
    + @Loop: each project_level_notification
        + @api: send email notification to project site-admin managers of given project
    + @DB: get optima settings of all project, who's inductions are updated to blocked state.
    + @Loop: each project with optima setting enabled.
        + @api: get Deny Access group details.
        + @Loop: each updated induction of given project.
            + @lambda: update badge state to Deny Access group.
    */
    let todayEpoch = momentTz().tz(fall_back_timezone).startOf('day').add(10, 'minutes').valueOf();

    const sql = `
        SELECT id, name, expiry_date, doc_number, doc_owner_id, parent_doc_ref, is_deleted, is_deleted_manually
        FROM user_doc WHERE (is_deleted_manually IN (0, 1)) AND is_notified IN (0, 1) AND expiry_date < $1;`;
    sails.log.info(`Executing DB query: ${sql}`, [todayEpoch]);
    let rawResult = await runReadQuery(sql, [todayEpoch]);
    if (!rawResult.length) {
        sails.log.info(`No document records expiring today.`);
        return true;
    }

    let user_documents = _groupBy((rawResult || []), (row) => row.doc_owner_id);
    let unique_users = Object.keys(user_documents);
    sails.log.info(`Total ${rawResult.length} documents to be expired today, owned by ${unique_users.length} users`);
    let users = await _getUsersMetaForCron(unique_users);

    let managers_per_company = await _getManagersPerParentCompany(users);

    const mandatory_competencies_per_country = await getMandatoryCompetencies();

    let projectCache = {};
    let project_level_notification = {};
    let updated_induction_groups = {};
    for (let i = 0; i < users.length; i++) {
        let user = users[i];
        user.name = getUserFullName(user);
        let company = (user.parent_company && user.parent_company.id) ? user.parent_company : {};
        sails.log.info(`processing user:${user.id}, parent_company ${company.id || ''}, has_company_portal: ${company.has_company_portal || ''}`);
        if (company.id && company.has_company_portal) {
            let rows = await getUserInductionsIntoCompany(company.id, user.id, 5);
            if (rows.length) {
                user._isCompanyBlacklisted = true;
            }
        }

        let my_documents = user_documents[user.id] || [];
        for (let j = 0; j < my_documents.length; j++) {
            let doc = my_documents[j];

            sails.log.info(`processing doc:${doc.id} of user:${user.id}, is_deleted_manually: ${doc.is_deleted_manually}`);
            if(doc.is_deleted_manually !== 1) {
                await _notifyDocOwner(user, doc, true);
            }

            let document_inductions = await _getInductionsOfDocumentId(doc.doc_owner_id, doc);
            for (let k = 0, len = document_inductions.length; k < len; k++) {
                let ir = document_inductions[k];

                if(!projectCache[ir.project_ref]){
                    projectCache[ir.project_ref] = await _getProjectMetaWithManagers(ir.project_ref, true, mandatory_competencies_per_country);

                }else{
                    sails.log.info(`Using cache data for project: ${ir.project_ref}`);
                }

                let p = projectCache[ir.project_ref].project;
                if (!p) {
                    sails.log.info(`Project not found Or is not active, id: ${ir.project_ref}, skipping notification / induction update`);
                    continue;
                }
                sails.log.info(`Processing induction id:${ir.id} for project: ${ir.project_ref} status_code: ${ir.status_code}`);
                if (![0, 4, 5].includes(ir.status_code)) {
                    // induction is not in blocked/rejected state, notify project managers
                    if(!project_level_notification[ir.project_ref]){
                        project_level_notification[ir.project_ref] = [];
                    }
                    project_level_notification[ir.project_ref].push({
                        doc,
                        owner: user,
                        employer: ir.employer,
                    });
                }

                if([2, 6].includes(ir.status_code) && p.blacklist_user_on_expiry !== null && p.enabled_competencies.includes(doc.name)){
                    // induction is approved like, and project have blacklisting enabled on expiry.
                    sails.log.info(`Mandatory doc "${doc.name}[${doc.id}]" is getting expired, project: ${p.id} have blacklisting enabled`);
                    let target_status = p.blacklist_user_on_expiry;
                    let comment = {
                        timestamp: moment().valueOf(),
                        note: `competency/ID (${doc.name}) expired`,
                        user_id: 0,
                        name: 'Admin',
                        origin: 'system',
                        module: 'competency-expiry',
                        state: {
                            from: ir.status_code,
                            to: target_status
                        }
                    };
                    let updated_induction = await sails.models.inductionrequest.updateOne({id: ir.id})
                        .set({
                            comments: [
                                ...(ir.comments || []),
                                comment
                            ],
                            status_code: target_status
                        });
                    sails.log.info(`Updated induction id: ${updated_induction.id} to blocked status from: ${ir.status_code} to: ${target_status}, after document expiry`);

                    if(!updated_induction.optima_badge_number){
                        sails.log.info(`Induction id: ${updated_induction.id} doesn't have optima badge, skipping optima badge update call`);
                        continue;
                    }
                    if(!updated_induction_groups[updated_induction.project_ref]){
                        updated_induction_groups[updated_induction.project_ref] = [];
                    }
                    updated_induction_groups[updated_induction.project_ref].push(updated_induction);
                }
            }


            try {
                if(!doc.parent_doc_ref){
                    // self is parent, so Unlink all children
                    await sails.models.userdoc.update({parent_doc_ref : doc.id}).set({parent_doc_ref : null});
                }else{
                    // self is child, so Unlink from parent
                    // No action needed.
                }
                // Only move non-deleted one INTO system-deleted state.
                const new_delete_state = (doc.is_deleted_manually === 0) ? 2 : doc.is_deleted_manually;
                await sails.models.userdoc.updateOne({id: doc.id}).set({is_deleted_manually: new_delete_state, is_notified: 2});
            } catch (e) {
                sails.log.info('Unable to delete document record.', e);
                continue;
            }
            if (!company.id || !(managers_per_company[company.id] || []).length || user._isCompanyBlacklisted) {
                sails.log.info(`Skipping, ${(managers_per_company[company.id] || []).length} company nominated managers notification, for company ${company.id}, could be blacklisted? ${user._isCompanyBlacklisted}`);
                continue;
            }

            await _notifyCompanyManagers(managers_per_company[company.id], user, doc, true);

        }
    }

    let projects_to_notify = Object.keys(project_level_notification);
    sails.log.info(`Total ${projects_to_notify.length} projects to notify`);
    for (let i = 0; i < projects_to_notify.length; i++) {
        const project_ref = projects_to_notify[i];
        const entries = project_level_notification[project_ref];
        sails.log.info(`Notify project ${project_ref} for ${entries.length} entries expiring`);
        // sails.log.info(entries);
        _notifyProjectManagersV2(projectCache[project_ref], entries).catch(sails.log.info);
    }

    let project_to_check_optima = Object.keys(updated_induction_groups);
    sails.log.info(`Total ${project_to_check_optima.length} projects to update optima for blocking`);
    if (!project_to_check_optima.length) {
        return true;
    }
    let optima_enabled_projects = await sails.models.optimasetting_reader.find({
        project_ref: project_to_check_optima,
        biometric_source: 'optima',
        site_id: {'!=': null}
    });

    sails.log.info(`Optima enabled projects are: ${optima_enabled_projects.length}`);
    for (let i = 0; i < optima_enabled_projects.length; i++) {
        let os_of_project = optima_enabled_projects[i];

        // this project have optima setting enabled, and have inductions blocked.
        // Updated all badges to Blocked Access group
        sails.log.info(`fetching access groups of ${os_of_project.project_ref}, site_id: ${os_of_project.site_id}`);
        let {success, status, data} = await callOptimaBoxGateway(os_of_project, {
            endpoint: 'Groups',
            method: 'GET',
        }, {});
        let deny_access_group = {};
        let label = `Deny Access`;
        if (success && status === 200) {
            deny_access_group = (data.groups || []).find((access_group => (access_group.libelle === label))) || {};
        }
        if (!deny_access_group.id) {
            sails.log.info(`Deny access groups of ${os_of_project.project_ref}, site_id: ${os_of_project.site_id} NOT FOUND`);
            continue;
        }

        // update all induction badges to blocked.
        let records = updated_induction_groups[os_of_project.project_ref];
        for (let k = 0; k < records.length; k++) {
            let ir = records[k];
            let updated = await updateBadge(os_of_project, ir.optima_badge_number, {
                "groupId": deny_access_group.id,
            });
        }
    }

    return true;
};

module.exports = {
    // Send expiry notification one month before and on expiry date and delete document.

    competencyExpire: async (finalCallbackFn) => {
        try{
            await processNextMonthExpiringCompetencies();
            sails.log.info(`now processing today expiring documents`);
            await processTodayExpiringCompetencies();
            finalCallbackFn(null, []);
        } catch (syncEventError) {
            sails.log.error(`Something went wrong in cron job`, syncEventError);
            finalCallbackFn(syncEventError)
        }
    },
};
