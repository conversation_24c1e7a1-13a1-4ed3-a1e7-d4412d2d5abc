/**
 * Created by spatel on 20/6/19.
 */
const HttpService = require('./HttpService');
const moment = require('moment');
const DEFAULT_COUNTY_CODE_GB = 'GB';

const isNonUKProject = (country_code) => {
    return (country_code !== DEFAULT_COUNTY_CODE_GB);
};

const postcodeToLatLong = async (postcode, countryCode = DEFAULT_COUNTY_CODE_GB) => {
    let distance_matrix_key = sails.config.custom.DISTANCE_METRIX_KEY || '';
    try {
        const response = await HttpService.makeGET(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${postcode}.json`, {
                access_token: sails.config.custom.MAPBOX_SECRET_ACCESS_KEY,
                country: countryCode,
                postcode:postcode,
                autocomplete: false,
                fuzzyMatch: false,
            }
        ); 
        const data = response.data;

        if (data && data.features.length) {
            // Sorts the array by relevance in descending order to ensure the most relevant option gets find and evaluated first for place_type
            let postcodeFeatureIndex = data.features
                .sort((a, b) => (+b.relevance) - (+a.relevance))
                .findIndex(a => (a.place_type && a.place_type.includes('postcode')));
            if(postcodeFeatureIndex === -1 && countryCode.toLowerCase() !== "ie") {
                sails.log.error(`invalid postcode - ${postcode} `);
                return null;
            }
            const {center, context, place_name, relevance} = data.features[postcodeFeatureIndex];
            // relevance to match exactness of matches from postcode and address.
            if(relevance === 1 ||  (countryCode.toLowerCase() === "ie")){
                let districtObj = (context || []).find(a => a.id.toLowerCase().includes("district"))
                let countryObj = (context || []).find(a => a.id.toLowerCase().includes("country"))
                return {
                    lat: (center && center[1]),
                    long: (center && center[0]),
                    region: place_name || null,
                    admin_district: districtObj ? districtObj.text : null,
                    country: countryObj ? countryObj.text : null,
                }
            }
            //additional api call for guatamela and australian postcodes because of data unavailability on mapbox
        } else if (['gt', 'au'].includes(countryCode.toLowerCase())) {
            let api_response = await HttpService.makeGET(`https://maps.googleapis.com/maps/api/geocode/json`, {
                address: `${postcode} ${countryCode}`,
                componentRestrictions: `country:${countryCode}`,
                key: distance_matrix_key
            }, {}, true, 15000);
            sails.log.info('Google Geocode response', (api_response.data && api_response.data.status));
            if(api_response.status !== 200) {
                return null;
            }
            let [first_result] = ((api_response.data && api_response.data.results) || []);
            if(!first_result || !first_result.geometry){
                sails.log.info('Google Geocode response, No valid result found');
                return null;
            }
            let {address_components, formatted_address, geometry: {location}} = first_result;
            let admin_district = (address_components || []).filter((add) => add.types.includes('administrative_area_level_1')).map(a => a.long_name).join(', ');
            let country = (address_components || []).filter((add) => add.types.includes('country')).map(a => a.long_name).join(', ');
            return {
                lat: (location && location.lat),
                long: (location && location.lng),
                region: formatted_address,
                admin_district,
                country
            }
        } else {
            sails.log.error(`invalid postcode - ${postcode} `);
        }
    } catch (e) {
        sails.log.error('Failed to query mapbox/google maps for geocode details', e);
    }
    return null;
};

const getLocationKey = async (postcode, countryCode = DEFAULT_COUNTY_CODE_GB) => {
    postcode = (postcode || '').toString().trim().replace(/\s/g, '')
    sails.log.info(`weather-sync-service get location key for postcode: ${postcode}`);
    if (!postcode.length) {
        return false;
    }

    let accuweather_key = (sails.config.custom.ACCU_WEATHER_API_KEY || '');
    let accuResponse = await HttpService.makeGET(`http://dataservice.accuweather.com/locations/v1/postalcodes/${countryCode}/search`, {
        apikey: accuweather_key,
        q: postcode
    }, {}, true, 20000);

    if (accuResponse.success && HttpService.typeOf(accuResponse.data, 'array') && accuResponse.data.length) {
        let firstLocation = accuResponse.data[0];
        if (firstLocation && firstLocation.Key) {

            sails.log.info(`location_key: ${firstLocation.Key} postcode: ${postcode} `);

            return {
                postcode,
                weather_location_key: firstLocation.Key
            };
        }
    } else {
        sails.log.info(`postcode: ${postcode} not found`);
    }
    return false;
};

const fetchLocationKeyByGeoPosition = async (lat,long) => {
    if (!lat || !long) {
        return false;
    }

    let accuweather_key = (sails.config.custom.ACCU_WEATHER_API_KEY || '');
    let accuResponse = await HttpService.makeGET(`http://dataservice.accuweather.com/locations/v1/cities/geoposition/search`, {
        apikey: accuweather_key,
        q: `${lat},${long}`
    }, {}, true, 20000);

    if (accuResponse.success && HttpService.typeOf(accuResponse.data, 'object') && accuResponse.data) {
        let firstLocation = accuResponse.data;
        if (firstLocation && firstLocation.Key) {

            sails.log.info(`location_key: ${firstLocation.Key} lat: ${lat}, long: ${long}`);

            return {
                weather_location_key: firstLocation.Key
            };
        }
    } else {
        sails.log.info(`location not found for lat: ${lat}, long: ${long}`);
    }
    return false;
};

const updateOrCreateWeatherLog = async (key, forecastDate, dailyForecast, type = 'future-forecast') => {
    let weather_log = {};
    let existingOne = await sails.models.weatherlog.findOne({
        location_key: key,
        day: forecastDate,
        type: type,
        source: 'accuweather',
    });

    if (!existingOne) {
        let inserted = await sails.models.weatherlog.create({
            location_key: key,
            source: 'accuweather',
            type,
            day: forecastDate,
            forecast: dailyForecast
        });
        sails.log.info(`Inserted id: ${inserted.id} Location key: ${key} day: ${forecastDate}`);
        weather_log = inserted;
    } else if (existingOne && existingOne.type !== type) {
        let updated = await sails.models.weatherlog
            .updateOne({id: existingOne.id})
            .set({
                location_key: key,
                source: 'accuweather',
                type,
                day: forecastDate,
                forecast: dailyForecast
            });
        sails.log.info(`Updated existing id: ${updated.id} Location key: ${key} day: ${forecastDate}`);
        weather_log = updated;
    } else {
        sails.log.info(`Record already exists, Location key: ${key} day: ${forecastDate}`);
        weather_log = existingOne;
    }

    return weather_log;
};

// Fetch hourly weather report for next 24Hrs for given location keys
const fetchHourlyWeather = async (location_keys, projects) => {
    let accuweather_key = (sails.config.custom.HOURLY_ACCU_WEATHER_API_KEY || '');
    let resultsOfAll = await Promise.all(Object.keys(location_keys).map(async (key) => {
        sails.log.info(`get hourly weather forecast for "${key}"`);
        try {
            let response = await HttpService.makeGET(`http://dataservice.accuweather.com/forecasts/v1/hourly/12hour/${key}`, {
                apikey: accuweather_key,
                details: true,
                metric: true // for celcius in response
            }, {}, true, 20000);

            sails.log.info('response status', response.status);
            if (response.success && response.data.length) {
                sails.log.info(`found hrourly forecast ${key}`);
                let project_record = location_keys[key];
                const recPromises = response.data.map(async f => {
                    let forecastDateTime = moment(f.DateTime, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM-DDTH');
                    return await updateOrCreateWeatherLog(key, forecastDateTime, f,'hourly-forecast');
                });
                return await Promise.all(recPromises);
            } else {
                sails.log.info('Forecast not found', key, ' HTTP Status: ', response.status);
            }
        } catch (processError) {
            sails.log.error(`Failed to sync hourly weather for ${key}`, processError);
            return false;
        }
    }));
    return resultsOfAll;
};

//remove old records of weather logs which are not being used
const removeHistoricalWeatherLogs = async () => {
    let today = moment(new Date());
    let day5Before = moment(today).subtract(5, 'days').format('YYYY-MM-DD');
    let type = 'future-forecast';
    let beforeHourlyDate = moment(today).subtract(1, 'months').format('YYYY-MM-DD');
    sails.log.info("About to delete future forecast weather logs");
    let rawResult = await sails.sendNativeQuery(`Delete from weather_log where TO_DATE(day, 'YYYY-MM-DD') < $1 and type = $2`,
    [day5Before, type]
    );
    sails.log.info("Deleted future forecast count: ", rawResult.rowCount, " before date: ", day5Before);
    sails.log.info("Now deleting the hourly weather logs");
    let sql = `Delete from weather_log where TO_DATE(day, 'YYYY-MM-DD') < $1 and type = $2 and id NOT IN (
        SELECT DISTINCT jsonb_array_elements_text(weather_logs)::int
        FROM public.project_daily_activities
        WHERE weather_logs::text <> '[]'
    )`;
    let hourlyResult = await sails.sendNativeQuery(sql, [beforeHourlyDate, 'hourly-forecast']);
    sails.log.info("Deleted hourly forecast count: ", hourlyResult.rowCount, " before date: ", beforeHourlyDate);
    return;
};

module.exports = {
    // fetch all active projects with postcode, location id
    // get unique location ids
    // get their daily forecast
    // store same into DB, after checking if one already does not exists for same data & location key
    sync: async () => {

        try {
            let accuweather_key = (sails.config.custom.ACCU_WEATHER_API_KEY || '');
            let projects = await sails.models.project.find({
                select: ['id', 'is_active', 'weather_location_key', 'postcode', 'custom_field'],
                where: {
                    is_active: 1,
                    disabled_on: null,
                    weather_location_key: {'!=': null},
                    project_category: 'default', // We don't need DAILY weather info of Company projects for now.
                },
            });

            if (projects && projects.length) {
                let location_keys = projects.reduce((obj, p) => {
                    obj[p.weather_location_key] = p;
                    return obj;
                }, {});
                sails.log.info(`Found ${projects.length} project records to sync weather, having ${Object.keys(location_keys).length} unique keys`);

                let resultsOfAll = await Promise.all(Object.keys(location_keys).map(async (key) => {
                    sails.log.info(`get weather forecast for "${key}"`);
                    try {
                        let response = await HttpService.makeGET(`http://dataservice.accuweather.com/forecasts/v1/daily/5day/${key}`, {
                            apikey: accuweather_key,
                            details: true,
                            metric: true // for celcius in response
                        }, {}, true, 20000);
                        if (response.success && response.data.DailyForecasts && response.data.DailyForecasts.length) {
                            sails.log.info(`found future-forecast ${key}, days`, response.data.DailyForecasts.map(d => d.Date));
                            let dates = [];
                            for (let i = 0; i < response.data.DailyForecasts.length; i++) {
                                let dailyForecast = response.data.DailyForecasts[i];
                                let project_record = location_keys[key];
                                let country_code = (project_record.custom_field && project_record.custom_field.country_code) || DEFAULT_COUNTY_CODE_GB;
                                let forecastDate = moment(dailyForecast.Date, isNonUKProject(country_code) ? 'YYYY-MM-DDTHH:mm:ss' : 'YYYY-MM-DDTHH:mm:ssZ').format('YYYY-MM-DD');
                                dates.push(forecastDate);
                                await updateOrCreateWeatherLog(key, forecastDate, dailyForecast, 'future-forecast');
                            }
                            return dates;
                        } else {
                            sails.log.info('Forecast not found', key, ' HTTP Status: ', response.status);
                        }

                    } catch (processError) {
                        sails.log.error(`Failed to sync weather for ${key}`, processError);
                        return false;
                    }
                }));
                sails.log.info('Weather sync complete.Running removeHistoricalWeatherLogs now!');
                await removeHistoricalWeatherLogs();
                return resultsOfAll;
            }

            return true;

        } catch (e) {
            sails.log.info('Failed to sync weather data', e);
            return false;
        }
    },

    syncHourly: async () => {

        try {
            //Add Daily Activities filter as we are using these weather logs for the feature only
            let rawResult = await sails.sendNativeQuery(`SELECT id, is_active, weather_location_key, postcode
            FROM project
            WHERE (project_section_access ->>'daily_activities' = 'true' OR company_additional_project_section_access->>'daily_activities' = 'true')
            AND weather_location_key != '' AND is_active=1`
            );

            sails.log.info(`total search results: ${rawResult.rows.length}`);

            let projects = [];
            if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
                projects = rawResult.rows;
            }

            if (projects && projects.length) {
                let location_keys = projects.reduce((obj, p) => {
                    obj[p.weather_location_key] = p;
                    return obj;
                }, {});
                sails.log.info(`Found ${projects.length} project records to sync hourly weather, having ${Object.keys(location_keys).length} unique keys`);

                let hourlyResults =  await fetchHourlyWeather(location_keys, projects);

                return hourlyResults;
            }

            return true;

        } catch (e) {
            sails.log.info('Failed to sync hourly weather data', e);
            return false;
        }
    },

    // Attach Location key for given project,
    // for later reference in cron job
    attachLocationKey: async (project, attach_weather_key = true) => {
        try {
            let custom_field = project.custom_field || {};
            let locationInfo = {};
            let countryCode = (custom_field.country_code) || DEFAULT_COUNTY_CODE_GB;
            if(attach_weather_key){
                if(project.custom_field.country_code === 'AE'){
                    locationInfo = await fetchLocationKeyByGeoPosition(custom_field.location.lat, custom_field.location.long);
                }else {
                    locationInfo = await getLocationKey(project.postcode, countryCode);
                }
            }
            let updateSet = {
            ...((locationInfo && locationInfo.weather_location_key) ? locationInfo : {}),
            };
            if (Object.keys(updateSet).length) {
                let updatedProject = await sails.models.project.updateOne({id: project.id}).set(updateSet);
                sails.log.info('Attached location key & location with project successful, id', updatedProject.id);
                return updatedProject;
            }

            return false;
        } catch (updateError) {
            sails.log.info('Failed to update location key info', updateError);
            return false;
        }

    },

    updateOrCreateWeatherLog,
    isNonUKProject,
    DEFAULT_COUNTY_CODE_GB,
    postcodeToLatLong
};
