// https://github.com/AntonioRecaldeRusso/s3-url-parser/blob/master/index.js

const {
    S3_BUCKET,
    INNDEX_S3_BUCKET,
    CDN_DOMAIN_HOST,
    TMP_CF_DOMAIN_HOST,
} = sails.config.custom;

const fromUrl = (url) => {
    let _decodedUrl = decodeURIComponent(url);

    let _result = null;

    // http://s3.amazonaws.com/bucket/key1/key2
    let _match = _decodedUrl.match(/^https?:\/\/s3.amazonaws.com\/([^\/]+)\/?(.*?)$/);
    if (_match) {
        _result = {
            bucket: _match[1],
            Bucket: _match[1],
            key: _match[2],
            Key: _match[2],
            region: '',
        };
    }

    // http://s3-aws-region.amazonaws.com/bucket/key1/key2
    _match = _decodedUrl.match(/^https?:\/\/s3-([^.]+).amazonaws.com\/([^\/]+)\/?(.*?)$/);
    if (_match) {
        _result = {
            bucket: _match[2],
            Bucket: _match[2],
            key: _match[3],
            Key: _match[3],
            region: _match[1],
        };
    }

    // http://bucket.s3.amazonaws.com/key1/key2
    _match = _decodedUrl.match(/^https?:\/\/([^.]+).s3.amazonaws.com\/?(.*?)$/);
    if (_match) {
        _result = {
            bucket: _match[1],
            Bucket: _match[1],
            key: _match[2],
            Key: _match[2],
            region: '',
        };
    }

    // http://bucket.s3-aws-region.amazonaws.com/key1/key2 or,
    // http://bucket.s3.aws-region.amazonaws.com/key1/key2
    _match = _decodedUrl.match(/^https?:\/\/([^.]+).(?:s3-|s3\.)([^.]+).amazonaws.com\/?(.*?)$/);
    if (_match) {
        _result = {
            bucket: _match[1],
            Bucket: _match[1],
            key: _match[3],
            Key: _match[3],
            region: _match[2],
        };
    }

    return _result;
};

const fromCDNUrl = (url) => {
    let _decodedUrl = decodeURIComponent(url);

    let _result = null;

    // https://cdn.inndex.co.uk/key1/key2
    let _match = _decodedUrl.match(new RegExp(`^https?:\/\/${CDN_DOMAIN_HOST}\/?(.*?)$`));
    if (_match) {
        _result = {
            bucket: S3_BUCKET,
            Bucket: S3_BUCKET,
            key: _match[1],
            Key: _match[1],
            region: '',
        };
        return _result;
    }
    // https://tmp.inndex.co.uk/key1/key2
    _match = _decodedUrl.match(new RegExp(`^https?:\/\/${TMP_CF_DOMAIN_HOST}\/?(.*?)$`));
    if (_match) {
        _result = {
            bucket: INNDEX_S3_BUCKET,
            key: _match[1],
            region: '',
        };
    }

    return _result;
};

module.exports = {
    fromUrl,

    valid: (url) => {
        const params = fromUrl(url);
        return (params && params.bucket && params.key);
    },

    fromCDNUrl,
};
