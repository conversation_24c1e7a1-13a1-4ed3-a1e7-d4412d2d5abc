/**
 * Created by spatel on 09/07/20.
 */
const COLORS = {
    VERY_LIGHT_BLUE_BG: 'E5F4FD',
    VERY_LIGHT_GRAY_BG: 'ededed',
};
const ExcelJS = require('exceljs');
const axios = require('axios');
const sizeOf = require('image-size');
const XLS_MIME_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
const IMAGE_MIMEs = ['image/jpeg', 'image/png', 'image/heic'];
const moment = require('moment');
const momentTz = require('moment-timezone');
const {toolBriefingsFn, inductionFn: { getUserInductionEmployer, getInductionEmployerByUserIds, },} = require('./../sql.fn');
const { getUserFullName, getProjectTimezone, showDurationAsHours, getActiveTravelTime, getTotalTravelDuration, getTotalTravelDistance, replaceAll, getProjectDistrict,getToolBriefings} = require('./DataProcessingService');
const _uniq = require('lodash/uniq');
const {
    dbDateFormat_YYYY_MM_DD,
    displayDateFormat_DD_MM_YYYY_HH_mm_ss,
    displayDateFormat_DD_MM_YYYY,
    kmToMiles,
    fall_back_timezone
} = sails.config.constants;
const formatTimeInTimezone = (n, format, timezone) => {
    return timezone ? momentTz(+n).tz(timezone).format(format) : (+n) ? moment(+n).format(format) : moment().format(format);
};
const HttpService = require('./HttpService');

const _increaseRowHeightIfNeeded = (worksheetRow, imageHeight, maxHeightNeeded = 0) => {
    let existingHeight = worksheetRow.height;
    // Adding extra 2, so that medium border can also be covered.
    let targetHeight = (imageHeight / 1.3) + 2;
    if (!existingHeight) {
        worksheetRow.height = targetHeight;
        maxHeightNeeded = targetHeight;
    } else if (!maxHeightNeeded || maxHeightNeeded < targetHeight) {
        worksheetRow.height = targetHeight;
        maxHeightNeeded = targetHeight;
    }
    return maxHeightNeeded;
};

const _getCorrectImageSize = ({width, height}) => {
    let maxWidth = 135, maxHeight = 135, ratio = 0;

    if (width > maxWidth) {
        ratio = maxWidth / width;
        height = height * ratio;
        width = width * ratio;
    }

    if (height > maxHeight) {
        ratio = maxHeight / height;
        width = width * ratio;
        height = height * ratio;
    }
    return {width: width, height: height};
};

// @todo: spatel: Make this parallel with limit.
const _addImagesToWorkbook = async (workbook, rowId, images = [], rowIdKey = 'rowId') => {
    let addedImages = [];
    for (let i = 0, len = (images || []).length; i < len; i++) {
        let {file_url, file_mime, sm_url, img_translation} = images[i];
        if (file_url && IMAGE_MIMEs.includes(file_mime)) {
            try {
                let imageBuffer;
                let f_url = sm_url || file_url;
                const url = new URL(f_url);
                if (url.hostname && url.hostname.includes("localhost")) {
                    imageBuffer = await axios.get(f_url, {
                        responseType: 'arraybuffer'
                    });
                } else {
                    imageBuffer = await HttpService.fetchS3UrlAsBuffer(f_url);
                }
                let imageDetails = sizeOf(imageBuffer.data);
                sails.log.info('imageDetails', imageDetails);
                const imageId = workbook.addImage({
                    buffer: imageBuffer.data,
                    extension: imageDetails.type,
                });
                addedImages.push({
                    'id': imageId,
                    [rowIdKey]: rowId,
                    ext: _getCorrectImageSize(imageDetails)
                });
            } catch (e) {
                sails.log.info(`Image loading failed, url: ${sm_url || file_url} status: ${e.response ? e.response.status : e.message}`)
            }
        } else if(file_mime == 'application/pdf') {
            for (let k = 0; k < img_translation.length; k++) {
                let pdfPage = img_translation[k];
                try {
                    let imageBuffer = await HttpService.fetchS3UrlAsBuffer(pdfPage);
                    let imageDetails = sizeOf(imageBuffer.data);
                    sails.log.info('imageDetails PDF', imageDetails);
                    const imageId = workbook.addImage({
                        buffer: imageBuffer.data,
                        extension: imageDetails.type,
                    });
                    addedImages.push({
                        'id': imageId,
                        [rowIdKey]: rowId,
                        ext: _getCorrectImageSize(imageDetails)
                    });
                } catch (e) {
                    sails.log.info(`Image loading failed, url: ${pdfPage} status: ${e.response ? e.response.status : e.message}`)
                }
            }
        }
    }
    return addedImages;
}

const _createOuterBorder = (worksheet, start = {row: 1, col: 1}, end = {row: 1, col: 1}, borderWidth = 'medium') => {

    const borderStyle = {
        style: borderWidth
    };
    for (let i = start.row; i <= end.row; i++) {
        const leftBorderCell = worksheet.getCell(i, start.col);
        const rightBorderCell = worksheet.getCell(i, end.col);
        leftBorderCell.border = {
            ...leftBorderCell.border,
            left: borderStyle
        };
        rightBorderCell.border = {
            ...rightBorderCell.border,
            right: borderStyle
        };
    }

    for (let i = start.col; i <= end.col; i++) {
        const topBorderCell = worksheet.getCell(start.row, i);
        const bottomBorderCell = worksheet.getCell(end.row, i);
        topBorderCell.border = {
            ...topBorderCell.border,
            top: borderStyle
        };
        bottomBorderCell.border = {
            ...bottomBorderCell.border,
            bottom: borderStyle
        };
    }
};

const _applyStyleOnAllCellsOfRow = (worksheet, rowNumber, {fill, alignment, border}) => {
    return worksheet.getRow(rowNumber).eachCell(cell => {
        cell.fill = Object.assign(cell.fill || {}, fill);
        cell.alignment = Object.assign(cell.alignment || {}, alignment);
        cell.border = Object.assign(cell.border || {}, border);
    });
};

const _getFillPattern = ({fgColor}) => {
    return Object.assign({
        type: 'pattern',
        pattern: 'solid',
    }, {
        ...(fgColor ? {fgColor: {argb: fgColor}} : {}),
    });
};

/**
 * read XLSX file as JSON
 *
 * @param filePath
 * @param rawRows
 * @param sheetIndexOrName
 * @returns {Promise<[]>}
 */
const readExcelFileAsObjects = async (filePath, rawRows = false, sheetIndexOrName = 1) => {
    sails.log.info('reading excel file:', filePath);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);

    let records = [];
    let worksheet = workbook.getWorksheet(sheetIndexOrName);
    let headers = [];
    worksheet.eachRow((row, index) => {
        let rowValues = (row.values || []).slice(1);
        if (rawRows) {
            records.push(rowValues);
            return true;
        }

        if (index === 1) {
            headers = rowValues;
        } else {
            let rowObject = headers.reduce((out, key, i) => {
                out[key] = rowValues[i] ? rowValues[i] : null;
                return out;
            }, {});
            records.push(rowObject);
        }
    });
    sails.log.info('total records', records.length);
    return records;
};

const truncateTextIf = (text, maxLength = 30) => {
    return (text.length > maxLength) ? text.substring(0, maxLength) : text;
}

const getWorksheetName = (name) => {
    // Excel doesn't support more than 31 characters for worksheet name
    name = truncateTextIf(name, 30);
    // Worksheet name "ABC" cannot include any of the following characters: * ? : \ / [ ]
    return name.replace(/([^A-Za-z0-9\s\-_&])/gim, '-');
};

const streamExcelDownload = async (httpResponse, workbook, fullFileName = 'report.xlsx', mime = XLS_MIME_TYPE) => {
    httpResponse.setHeader('Content-Type', mime + ';charset=UTF-8');
    httpResponse.setHeader("Content-Disposition", "attachment; filename=" + encodeURIComponent(fullFileName));
    // httpResponse.setHeader('Content-Length', stream.length);
    await workbook.xlsx.write(httpResponse);
    sails.log.info('Finished streaming file', fullFileName);
    httpResponse.end();
    try {
        if(process.env.SKIP_LOADING_ENV_CONF !== 'true'){ (workbook.xlsx.writeFile(require('path').resolve(process.cwd(), `../${(new Date()).getTime()}-${fullFileName.replace(/\//g, '-')}`))).then(out => sails.log.info('Finished writing workbook file', fullFileName));}
    } catch (e) {
        console.error('Writing dump', e);
    }
    return 'Finished';
};

const exportCompanyStatsReport = async (records) => {
    sails.log.info('creating company stats export', (new Date()));
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('tool-usage-data', {
        views: [{zoomScale: 80}],
    });
    worksheet.columns = [
        { header: `Active`, key: 'active', width: 8 },
        { header: `Project Ref`, key: 'project_ref', width: 12 },
        { header: `Project Name`, key: 'name', width: 30 },
        { header: 'Start Date', key: 'start_date', width: 13 },
        { header: 'End Date', key: 'end_date', width: 13 },
        { header: 'Inductions', key: 'inductionCounts', width: 13 },
        { header: 'Time Management (hr)', key: 'totalSiteTime', width: 22 },
        { header: 'Progress Photos', key: 'progressPhotoCounts', width: 18 },
        { header: 'Delivery Notes', key: 'deliveryNoteCounts', width: 18 },
        { header: 'Daily Diaries', key: 'dailyActivityCounts', width: 18 },
        { header: 'Toolbox Talks - No. Added', key: 'toolboxTalksCounts', width: 30 },
        { header: 'Toolbox Talks - No. People Briefed', key: 'toolboxTalksBriefingCounts', width: 35 },
        { header: 'Good Calls - Raised', key: 'goodCallCounts', width: 20 },
        { header: 'Close Calls - Raised', key: 'closeCallCounts', width: 20 },
        { header: 'Close Calls - Open', key: 'closeCallOpenCounts', width: 20 },
        { header: 'Observations - Raised', key: 'observationCounts', width: 24 },
        { header: 'Observations - Open', key: 'observationOpenCounts', width: 24 },
        { header: 'Incident Reports', key: 'incidentReportCounts', width: 18 },
        { header: 'Inspections', key: 'inspectionTourCounts', width: 18 },
        { header: 'Inspections - Items scored', key: 'i_s', width: 5 },
        { header: 'Inspections - Items open', key: 'i_o', width: 5 },
        { header: 'Task Briefings - No. Added', key: 'taskBriefingCounts', width: 30 },
        { header: 'Task Briefings - No. People Briefed', key: 'taskBriefingBriefingCounts', width: 30 },
        { header: 'WPPs - No. Added', key: 'workPackagePlanCounts', width: 20 },
        { header: 'WPPs - No. People Briefed', key: 'wppBriefingCounts', width: 27 },
        { header: 'RAMS - No. Added', key: 'ramsCounts', width: 20 },
        { header: 'RAMS - No. People Briefed', key: 'ramsBriefingCounts', width: 27 },
        { header: 'Asset Management Vehicles - Total', key: 'assetVehicleCounts', width: 35 },
        { header: 'Asset Management Vehicles - Archived', key: 'assetVehicleArchivedCounts', width: 35 },
        { header: 'Asset Management Equipment - Total', key: 'assetEquipmentCounts', width: 35 },
        { header: 'Asset Management Equipment - Archived', key: 'assetEquipmentArchivedCounts', width: 35 },
        { header: 'Powra', key: 'powraCounts', width: 18 },
        { header: 'Clerk Of Works - Total', key: 'clerkOfWorksCounts', width: 22 },
        { header: 'Clerk Of Works - Open', key: 'clerkOfWorksOpenCounts', width: 22 },
        { header: 'Take 5s - Total', key: 'take5sCounts', width: 22 },
        { header: 'Take 5s - Total Attendees', key: 'take5sAttendeeCounts', width: 25 },
    ];
    worksheet.getRow(1).font = {bold: true, size: 11};
    worksheet.addRows(records);
    worksheet.autoFilter = 'A1:AH1';
    sails.log.info('created workbook', (new Date()));
    return workbook;
};

const exportBiometricSettingsReport = async (records) => {
    sails.log.info('[ADMIN] creating biometric settings export', (new Date()));
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('biometric-data', {
        views: [{zoomScale: 80}],
    });
    worksheet.columns = [
        { header: `Project ID`, key: 'project_ref', width: 12 },
        { header: `Project Name`, key: 'name', width: 30 },
        { header: `Company`, key: 'company', width: 30 },
        { header: `Company ID`, key: 'parent_company', width: 15 },
        { header: `Active`, key: 'is_active', width: 10 },
        { header: `Site ID`, key: 'site_id', width: 10 },
        { header: `Biometric source`, key: 'biometric_source', width: 8 },
        { header: `Geo-fence locations`, key: 'geo_fence_location_count', width: 15 },
        { header: `Timezone`, key: 'tz', width: 15 },
    ];
    worksheet.getRow(1).font = {bold: true, size: 11};
    worksheet.addRows(records);
    worksheet.autoFilter = 'A1:H1';
    sails.log.info('created biometric setting workbook', (new Date()));
    return workbook;
};

const exportTimesheets = async (records, timesheet_week_end, current_symbol) => {
    sails.log.info('creating timesheets export', (new Date()), 'timesheet_week_end', timesheet_week_end);
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('timesheets', {
        views: [{zoomScale: 80}],
    });

    let now = moment().day(timesheet_week_end);
    let weeks = [];
    for (let i = 0; i < 7; i++) {
        weeks.push(...[
            { header: `${now.format('ddd')} Hours \n (Overtime)`, key: `${now.format('ddd')}_overtime`, width: 18},
            { header: `${now.format('ddd')} Hours \n (Night)`, key: `${now.format('ddd')}_night`, width: 18},
            { header: `${now.format('ddd')} Hours \n (Day)`, key: `${now.format('ddd')}_day`, width: 18}
        ]);
        now = now.subtract(1, 'day');
    }
    weeks = weeks.reverse();
    // sails.log.info('Weeks are', JSON.stringify(weeks, null, 2));
    worksheet.columns = [
        { header: 'Project ID', key: 'project_id', width: 18},
        { header: 'Project Name', key: 'project_name', width: 18},
        { header: 'Project Post/Zip Code', key: 'project_postcode', width: 18},
        { header: 'Project District', key: 'district', width: 18},
        { header: 'Induction Number', key: 'record_id', width: 18},
        { header: 'innDex ID', key: 'user_ref', width: 15},
        { header: 'First Name', key: 'first_name', width: 18},
        { header: 'Last Name', key: 'last_name', width: 18},
        { header: 'Email', key: 'email', width: 24},
        { header: 'National Insurance Number', key: 'nin', width: 22},
        { header: 'Company', key: 'employer', width: 18},
        { header: 'Job Role', key: 'job_role', width: 18},
        { header: 'Type of Employment', key: 'type_of_employment', width: 22},
        { header: 'Week Ending', key: 'week_end_on', width: 18},
        ...weeks,
        { header: 'Total Day Hours', key: 'total_day_hr_label', width: 18},
        { header: 'Total Night Hours', key: 'total_night_hr_label', width: 18},
        { header: 'Total Basic Hours', key: 'total_basic_hr_label', width: 18},
        { header: 'Total Overtime Hours', key: 'total_overtime_hr_label', width: 18},
        { header: 'Total Driving Hours', key: 'total_driving_hr_label', width: 18},
        { header: 'Total Training Hours', key: 'total_training_hr_label', width: 18},
        { header: 'Total Manager Auth. Hours', key: 'total_manager_a_hr_label', width: 18},
        { header: 'Total Combined Hours', key: 'total_combined_hr_label', width: 18},
        { header: `Total Price Work${current_symbol ? `\n(${current_symbol})`: ''}`, key: 'total_price_work_label', width: 18},
    ];
    worksheet.getRow(1).font = {bold: true, size: 11};
    _applyStyleOnAllCellsOfRow(worksheet, 1, {
        fill: _getFillPattern({fgColor: COLORS.VERY_LIGHT_GRAY_BG}),
        alignment: {vertical: 'middle', horizontal: 'center',  wrapText: true},
        border: {
            right: {style: 'thin'},
            bottom: {style: 'thin'},
        }
    });
    worksheet.getColumn('N').numFmt = 'dd/mmm/yyyy;@';
    worksheet.getRow(1).height = 40;
    worksheet.addRows(records);
    worksheet.autoFilter = 'A1:AJ1';
    sails.log.info('created workbook', (new Date()));
    return workbook;
};

const exportDailyDeclarationOfUser = async (records, {full_name, employer}, timezone = 'UTC') => {
    sails.log.info('creating declaration answers export', (new Date()), 'timezone:', timezone);

    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('declaration', {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 9}
    });
    worksheet.addRow([{
        richText: [
            { 'font': {'bold': true, size: 14}, 'text': `Daily Declaration Report: ${full_name} \n` },
            { 'font': {'bold': true, size: 14}, 'text': `(${employer})` }
        ]
    }]).font = {size: 14, bold: true};
    worksheet.getRow(1).height = 45;
    _applyStyleOnAllCellsOfRow(worksheet, 1, {
        fill: _getFillPattern({fgColor: COLORS.VERY_LIGHT_BLUE_BG})
    });
    worksheet.getColumn('A').width = 30;
    worksheet.getColumn('A').alignment = {wrapText: true};
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('B').alignment = {wrapText: true};
    worksheet.mergeCells('A1:B1');
    _createOuterBorder(worksheet, {row: 1, col: 1}, {row: 1, col: 2}, 'thin');
    for (let i = 0; i < records.length; i++) {
        worksheet.addRow([]).commit();
        let event = records[i];
        worksheet.addRow([`Date: ${momentTz.unix(+event.event_date_time).tz(timezone).format('DD-MM-YYYY HH:mm:ss')}`]).font = {bold: true};
        let lastRow = worksheet.addRow(['Question', 'Answer']);
        _createOuterBorder(worksheet, {row: lastRow.number, col: 1}, {row: lastRow.number, col: 2}, 'thin');
        _applyStyleOnAllCellsOfRow(worksheet, lastRow.number, {
            fill: _getFillPattern({fgColor: COLORS.VERY_LIGHT_GRAY_BG})
        });
        if(event.extras && event.extras.declarations){
            worksheet.addRows((event.extras.declarations || []).map(declaration => {
                return [
                    declaration.message,
                    declaration.answer,
                ];
            }));
        }
    }
    sails.log.info('created workbook', (new Date()));
    return workbook;
};

const exportInductionInviteListReport = async (records, {name}, timezone = 'UTC') => {
    sails.log.info('creating workbook export', (new Date()), 'timezone:', timezone);

    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('invites-list', {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70}
    });

    worksheet.addRow([`Previous Induction Invites: ${name}`]).font = {size: 14, bold: true};
    worksheet.getRow(1).height = 25;
    worksheet.getRow(1).eachCell(cell => {
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: COLORS.VERY_LIGHT_GRAY_BG}
        };
    });
    worksheet.addRow([]).commit();
    worksheet.mergeCells('A1:B1');
    _createOuterBorder(worksheet, {row: 1, col: 1}, {row: 1, col: 2}, 'thin');

    worksheet.addRow(['Date/Time Invite Sent', 'Sent to']).eachCell(cell => {
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'cccccc'}
        };
        cell.border = {
            right: {style: 'thin'},
        }
    });
    worksheet.getRow(3).font = {size: 14};
    _createOuterBorder(worksheet, {row: 3, col: 1}, {row: 3, col: 2}, 'thin');
    worksheet.getColumn('A').width = 25;
    worksheet.getColumn('B').width = 35;
    worksheet.addRows(records.map(row => {
        return [
            momentTz(+row.createdAt).tz(timezone).format('DD-MM-YYYY HH:mm:ss'),
            row.email || row.mobile_no,
        ];
    }));

    sails.log.info('created workbook', (new Date()));
    return workbook;
};

const exportTotalTimeReportOfProject = async (projectId, records, timezone = 'UTC', addTotalsRow = true, includeBreaks = false, separateContractors = false, includeEmployeeNumber = false) => {
    sails.log.info('creating total time workbook export', (new Date()), 'timezone:', timezone, 'includeEmployeeNumber', includeEmployeeNumber);
    let projectDistricts = await getProjectDistrict(+projectId)

    let companies = ['Overall'];
    let workbook = new ExcelJS.Workbook();
    if(separateContractors) {
        let list = [];
        records.forEach(r => {
            if(r.employer) {
                list.push(r.employer);
            }
        });
        companies = ['Overall', ...new Set(list)];
    }


    let sheetCounter = 0;
    let sheetRecords = [];
    for(let company of companies) {
        let worksheet = workbook.addWorksheet(getWorksheetName(company), {
            views: [{zoomScale: 67}],
            pageSetup: {paperSize: 5, orientation: 'landscape', scale: 63}
        });
        let columns = [
            ...([
                { header: "Date", key: "date", width:15},
                { header: "Project/Contract Number", key: "project_number", width: 25},
                { header: "Project", key: "project_name", width: 20},
                { header: "First Name", key: "first_name", width:15},
                { header: "Last Name", key: "last_name", width:15},
                ...includeEmployeeNumber ? [{ header: "Employee Number", key: "employee_number", width:20}]: [],
                { header: "Company", key: "company", width:20},
                { header: "Type of Employment", key: "type_of_employment", width:20},
                { header: "Agency", key: "employment_company", width:20},
                { header: "Job Role", key: "job_role", width:20},
                { header: "innDex ID", key: "inndex_id", width:12},
                { header: "In Time", key: "in", width:12},
                { header: "Out Time ", key: "out", width:12},
                { header: "Total Time (hours)", key: "total", width:20},
            ]),

            ...(includeBreaks ? [
                { header: "Total Break Time", key: "total_break", width:18},
                { header: "Total Time - Break(s)", key: "total_wo_break", width:20, alignment: {  horizontal: 'right'}}
            ] : []),

            ...([
                { header: "Travel Time (hours)", key: "travel_time", width:12},
                ...(projectDistricts.length ? [
                    { header: "District", key: "district", width:20},
                ] : []),
                { header: "Distance Traveled (km)", key: "distance_traveled", width:12},
                { header: "Method of Travel", key: "travel_method", width:20},
                ...(projectDistricts.length ? [
                    { header: "Local Worker", key: "local_worker", width:20},
                ] : []),
                { header: "Total Time inc. Travel (hours)", key: "total_with_travel_time", width:20},
                { header: "SSSTS/SMSTS", key: "has_SMSTS_or_SSSTS", width:10},
                { header: "First Aid", key: "is_first_adider", width:10},
                { header: "Fire Marshall", key: "has_fire_marshal", width:15},
                { header: "PTS Number", key: "pts_number", width:10},
                { header: "CSCS Number", key: "cscs_number", width:10},
                { header: "Adjusted Minutes", key: "adjusted_min", width:15},

                { header: "Adjusted Minutes Comments", key: "adjusted_min_comment", width:30},
            ]),
            // { header: "Declarations", key: "clock_in_declarations", width:40},
            { header: "Add Time Log Comments", key: "notes", width:40},
        ];

        let firstDocPosition = columns.findIndex(column => column.key === 'has_SMSTS_or_SSSTS');
        let lastDocPosition = columns.findIndex(column => column.key === 'cscs_number');
        let docStartFrom = String.fromCharCode((firstDocPosition + 1) + 64);
        let docEndFrom = String.fromCharCode((lastDocPosition + 1) + 64);
        worksheet.columns = columns;
        worksheet.autoFilter = {
            from: 'A1',
            to: {
                row: 1,
                column: columns.length
            }
        };
        worksheet.getRow(1).height = 20;
        worksheet.getRow(1).eachCell({ includeEmpty: true }, function(cell, colNumber) {
            cell.font = {
                bold: true,
            };
            cell.fill = {
                type: 'pattern',
                pattern:'solid',
                fgColor: {argb: 'dedede'}
            };
            cell.border = {
                top: {style:'thin'},
                left: {style:'thin'},
                bottom: {style:'thin'},
                right: {style:'thin'}
            };
        });
        if(sheetCounter === 0) {
            sheetRecords = records;
        }
        else if(separateContractors && sheetCounter > 0) {
            sheetRecords = records.filter(r=> r.employer === company);
        }
        let total_break_time = 0;
        let total_time = 0;
        let total_travel_time = 0;
        let total_travel_distance = 0;
        let total_time_wo_break = 0;
        let total_time_included_duration = moment.duration();
        let total_has_SMSTS_or_SSSTS = {};
        let total_is_first_adider = {};
        let total_has_fire_marshal = {};
        sheetRecords.map((row, index) => {
            if(separateContractors && sheetCounter > 0 && addTotalsRow) {
                total_break_time = (total_break_time + (row.total_break_sec ? (+row.total_break_sec) : 0));
                total_time = (total_time + (row.total_sec ? (+row.total_sec) : 0));
                total_travel_time = (total_travel_time + (row.travel_time ? +row.travel_time : 0));
                total_travel_distance = (total_travel_distance + (row.distance_traveled ? (+row.distance_traveled) : 0));
                total_time_wo_break = (total_time_wo_break + (row.total_wo_break_sec ? (+row.total_wo_break_sec) : 0));
                total_time_included_duration = row.total_with_travel_time_sec ? total_time_included_duration.add(row.total_with_travel_time_sec) : total_time_included_duration;
                if (row.has_SMSTS_or_SSSTS === 'Yes') {
                    total_has_SMSTS_or_SSSTS[index] = 'Yes';
                }
                if (row.is_first_adider === 'Yes') {
                    total_is_first_adider[index] = 'Yes';
                }
                if (row.has_fire_marshal === 'Yes') {
                    total_has_fire_marshal[index] = 'Yes';
                }
            }

            /*
            // Disabled this column
            if (row.raw_clock_in_declarations) {
                row.clock_in_declarations = (row.raw_clock_in_declarations || []).reduce((string, declaration, i) => {
                    string += `${(i === 0) ? '\n' : ''} Q. ${declaration.message} \n A. ${declaration.answer}`;
                    return string;
                }, '');
            }
            */
           if (row.manual_time_comments) {
                row.notes = (row.manual_time_comments).reduce((string, comment, i) => {
                    string += `${(comment.event_type === 'IN') ? 'Manual in: ' : 'Manual out: '} ${comment.note}\n`;
                    return string;
                }, '');
            }
            row.date = row.date ? new Date(row.date) : '';
            const newRow = worksheet.addRow(row);
            newRow.getCell('A').numFmt = 'dd/mmm/yyyy;@';
            // newRow.getCell('A').numFmt = '[$-409]d-mmm-yyyy;@';  // alternative way of doing the same.
            // sails.log.info('Added row', newRow.getCell('A').value)
            if(!row.is_user){
                let cellIndex = firstDocPosition + 1;
                while (cellIndex <= lastDocPosition + 1){
                    let columnName = String.fromCharCode(cellIndex+64);
                    let cell = worksheet.getCell(`${columnName}${index+2}`);
                    cell.value = 'VISITOR';
                    cell.font = { bold: true, color: {'argb': 'edb61d'}};
                    cell.alignment = {  horizontal: 'center'};
                    cellIndex++;
                }
            }
        });

        if(separateContractors && sheetCounter > 0 && addTotalsRow) {
            let lastrow = {'out': 'Total:',
                'total_break': showDurationAsHours(total_break_time),
                'total': showDurationAsHours(total_time),
                'total_wo_break': showDurationAsHours(total_time_wo_break),
                'travel_time': `${total_travel_time} minutes`,
                'distance_traveled': `${total_travel_distance} Km`,
                'total_with_travel_time': showDurationAsHours(total_time_included_duration.asSeconds()),
                'has_SMSTS_or_SSSTS': Object.keys(total_has_SMSTS_or_SSSTS).length.toString(),
                'is_first_adider': Object.keys(total_is_first_adider).length.toString(),
                'has_fire_marshal': Object.keys(total_has_fire_marshal).length.toString(),
                is_user: true,
            };
            worksheet.addRow(lastrow);
        }
        if(sheetRecords.length && addTotalsRow){
            let total_column_alphabet = ('P'); // includeBreaks ? 'J' : 'I'
            worksheet.getColumn('O').width = 15;
            worksheet.getColumn(total_column_alphabet).width = 18;
            worksheet.getColumn(total_column_alphabet).alignment = {  horizontal: 'right'};
            worksheet.getCell(`${total_column_alphabet}1`).alignment = {  horizontal: 'left'}; // reset for first row
            if(includeBreaks){
                // worksheet.getColumn('I').width = 18;
                worksheet.getColumn('R').alignment = {  horizontal: 'right'};
                worksheet.getCell(`R1`).alignment = {  horizontal: 'left'}; // reset for first row
            }
            worksheet.getRow(worksheet.rowCount).font = {size: 12, bold: true};
            _createOuterBorder(worksheet, {row: worksheet.rowCount, col: 8}, {row: worksheet.rowCount, col: (includeBreaks ? 18 : 16)}, 'thin');
        }
        worksheet.getColumn('AA').alignment = {wrapText: true};
        sheetCounter++;
    }
    sails.log.info('created workbook', (new Date()));
    return workbook;
}

const exportProjectResourcePlans = async (project, records) => {
    sails.log.info('creating resource plan export', (new Date()));
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('resource-plans', {
        views: [{zoomScale: 80}],
    });
    worksheet.columns = [
        // { header: `User Id`, key: 'resource_ref', width: 15 },
        { header: `Date`, key: 'day_of_yr', width: 11 },
        { header: `Name`, key: 'name', width: 30 },
        { header: 'Start', key: 'planned_start', width: 10 },
        { header: 'End', key: 'planned_end', width: 10 },
        { header: 'Travel', key: 'travel_minutes', width: 12 },
        { header: 'Total', key: 'planned_total', width: 15 },
        { header: 'Actual Start', key: 'actual_start', width: 12 },
        { header: 'Actual End', key: 'actual_end', width: 12 },
        { header: 'Travel', key: 'travel_minutes2', width: 14 },
        { header: 'Actual Total', key: 'actual_total', width: 20 },
    ];
    worksheet.getRow(1).font = {bold: true, size: 11};
    /*worksheet.getColumn(`F1:F${worksheet.rowCount}`).border = {
        right: {style: 'medium'}
    };*/
    records.map(r => {
        let row = worksheet.addRow(r);
        if(r.violation){
            row.getCell(7).font = {'color': {'argb': 'dc3545'}}
            row.getCell(8).font = {'color': {'argb': 'dc3545'}}
            row.getCell(9).font = {'color': {'argb': 'dc3545'}}
            row.getCell(10).font = {'color': {'argb': 'dc3545'}}
        }
    });
    sails.log.info('created workbook', (new Date()));
    return workbook;
};

const exportVehicleDailyLogs = async (record_groups) => {
    sails.log.info('creating vehicle daily logs export', (new Date()));
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('Overall', {
        views: [{ zoomScale: 67}],
        pageSetup:{ orientation:'landscape', scale: 38}
    });
    worksheet.columns = [
        { header: 'Date', key: 'day_of_yr', width: 12 },
        { header: 'Time on site', key: 'time_on_site', width: 15 },
        { header: 'Time off site', key: 'time_off_site', width: 15 },
        { header: 'Total time on site', key: 'total_time', width: 20 },
        { header: 'Type', key: 'interaction_type', width: 12 },
        { header: 'Vehicle Registration Number', key: 'registration_no', width: 22 },
        { header: 'Vehicle Type', key: 'type', width: 18 },
        { header: 'Laden %', key: 'laden_percent', width: 18 },
        { header: 'Vehicle Make', key: 'make', width: 18 },
        { header: 'Vehicle Fuel Type', key: 'fuel_type', width: 15 },
        { header: 'Vehicle CO2 Emissions (g/Km)', key: 'co2_emission_gm', width: 20 },
        { header: 'Driver Name', key: 'driver_name', width: 18 },
        { header: 'Haulage Company', key: 'haulage_company', width: 22 },
        { header: 'Supplier', key: 'supplier', width: 15 },
        { header: 'Dispatch Postcode', key: 'dispatch_postcode', width: 20 },
        { header: 'Return Postcode', key: 'return_postcode', width: 20 },
        { header: 'Distance to Site (km)', key: 'distance_travelled_to_site', width: 24 },
        { header: 'Distance from Site (km)', key: 'distance_travelled_from_site', width: 24 },
        { header: 'Total Distance Travel (km)', key: 'total_distance_travelled', width: 24 },
        { header: 'Total CO2 Emissions (kg)', key: 'total_co2_kg', width: 25 },
    ];
    worksheet.autoFilter = 'A1:O1';
    _applyStyleOnAllCellsOfRow(worksheet, 1, {
        fill: _getFillPattern({fgColor: COLORS.VERY_LIGHT_GRAY_BG})
    });

    worksheet.getRow(1).font = {bold: true};
    worksheet.getRow(1).height = 20;
    /*
    worksheet.getColumn(`A`).alignment = { vertical: 'middle', horizontal: 'center' };
    worksheet.getColumn(`B`).alignment = { vertical: 'middle', horizontal: 'center' };
    worksheet.getColumn(`C`).alignment = { vertical: 'middle', horizontal: 'center' };
    worksheet.getColumn(`D`).alignment = { vertical: 'middle', horizontal: 'center' };
    worksheet.getColumn(`E`).alignment = { vertical: 'middle', horizontal: 'center' };
    worksheet.getColumn(`F`).alignment = { vertical: 'middle', horizontal: 'center' };
    */
    for (let i = 0; i < record_groups.length; i++) {
        worksheet.addRows((record_groups[i] || []));
        /*
        // Keeping vehicle row merging logic disabled for now

        let added_row_number = (record_groups[i] || []).map(record => {
            return worksheet.addRow(record).number;
        });

        if(added_row_number.length > 1){
            // have more than one row per vehicle, do cell merge
            sails.log.info('Merging row numbers', added_row_number);
            let start = added_row_number.shift();
            let end = added_row_number.pop();
            worksheet.mergeCells(`A${start}:A${end}`);
            worksheet.mergeCells(`B${start}:B${end}`);
            worksheet.mergeCells(`C${start}:C${end}`);
            worksheet.mergeCells(`D${start}:D${end}`);
            worksheet.mergeCells(`E${start}:E${end}`);
            worksheet.mergeCells(`F${start}:F${end}`);
        }
        */
    }

    sails.log.info('created workbook', (new Date()));
    return workbook;
};


const progressPhotosReport = async(progressPhotos) => {
    sails.log.info('creating workbook export', (new Date()));

    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('project-progress-photos', {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70}
    });
    let styleFilter = {alignment: {  vertical: 'middle',horizontal: 'center',  wrapText: true}};
    let maxColumns = 1;

    progressPhotos.map(p=>{
        let currColumns = 0;
        for(const photo of p.pp_images) {
            currColumns += (photo.img_translation.length) ? photo.img_translation.length : 1;
        }
        maxColumns = (currColumns > maxColumns) ? currColumns : maxColumns;
    })
    let columns = [
        { header: "Date Submitted", key: "Date Submitted", width:20, ...styleFilter},
        { header: "Submitted By", key: "Submitted By", width:15, ...styleFilter},
        { header: "Title", key: "Title", width:20, ...styleFilter},
        { header: "Location", key: "Location", width:15, ...styleFilter},
        { header: "Location Tag (Lat, Long)", key: "Location Tag (Lat, Long)", width:25, ...styleFilter},
        { header: "Description", key: "Description",alignment: {  vertical: 'middle',horizontal: 'center'}, width:30, ...styleFilter},
        { header: "Owner", key: "Owner", width:15, ...styleFilter},
        { header: "Photos", key: "Photos", width:20, ...styleFilter}
    ];
    if(maxColumns > 1) {
        for(let i=1; i<maxColumns; i++) {
            columns.push({ header: "Photo "+i, key: "Photos"+i, width:20, ...styleFilter});
        }
    }

    worksheet.columns = columns;

    let records = [];
    progressPhotos.map(r => {
        let result = {
            "Date Submitted": r.createdAt ? moment(+r.createdAt).format('DD-MM-YYYY') : null,
            "Submitted By": DataProcessingService.getUserFullName(r.user_ref),
            "Title": r.pp_title,
            "Location": r.pp_location,
            "Location Tag (Lat, Long)": (r.location && r.location.lat && r.location.long) ? `${r.location.lat}, ${r.location.long}` : "",
            "Description": (r.pp_description && r.pp_description.replace(/<br\s*\/?>/ig, "\r\n")),
            "Owner": r.tagged_owner? r.tagged_owner.name : '',
            "Photos": null
        };
        records.push(result);
    });
    worksheet.addRows(records);
    let lastColNumber = 7+maxColumns;
    let endColumn = worksheet.getRow(1).getCell(worksheet.getColumn(lastColNumber)._key);
    if(maxColumns > 1) {
        let colToMerge = 'H1:'+endColumn._address;
        worksheet.mergeCells(colToMerge);
    }
    let r = 1;
    for(const p of progressPhotos) {
        let c = 7;
        worksheet.getRow(r).height = 110;
        for(const i of p.pp_images) {
            let fileRecords = await _addImagesToWorkbook(workbook, 'img'+i.id, [i]);
            if (fileRecords.length) {
                for(const fileRecord of fileRecords) {
                    worksheet.addImage(fileRecord.id, {
                        tl: {col: c+0.05, row: r+0.25},
                        ext: fileRecord.ext,
                    });
                    c++;
                }
            }
        }
        r++;
    }
    worksheet.lastRow.height = 110;
    worksheet.eachRow(function(row, rowNumber) {
        // Iterate over all (including empty) cells in a row
        row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
            if (rowNumber === 1) {
                row.height = 20;
                cell.font = {
                bold: true,
                };
                cell.fill = {
                    type: 'pattern',
                    pattern:'solid',
                    fgColor: {argb: 'dedede'}
                }
                cell.border = {
                    top: {style:'thin'},
                    left: {style:'thin'},
                    bottom: {style:'thin'},
                    right: {style:'thin'}
                };

            }
            cell.alignment = {  vertical: 'middle',horizontal: 'center',  wrapText: true};
        });
    });
    return workbook;
};

const parseDistanceTravelled = (record) => {
    let output = '';
    if (record.dn_load_type == 'Part load' && record.dn_total_distance_travelled) {
        output = `${Number(record.dn_total_distance_travelled / 1000).toFixed(2)} km (${Number((record.dn_distance_travelled*2) / 1000).toFixed(2)} km total, including return journey)`;
    } else if (record.dn_load_type == 'Full load' && record.dn_total_distance_travelled) {
        output = `${Number(record.dn_total_distance_travelled / 1000).toFixed(2)} km (Including return journey)`;
    }
    return output;
}

const deliveryNotesReport = async(deliveryNotes, timezone, featureName='delivery-note') => {
    sails.log.info('creating workbook export', (new Date()));

    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet(getWorksheetName(featureName), {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70}
    });

    let maxColumns = 1;
    let ftLabel = (featureName == 'delivery-note') ? 'Delivery' : 'Collection';

    deliveryNotes.map(n=>{
        let currColumns = 0;
        for(const photo of n.dn_images) {
            currColumns += (photo.img_translation.length) ? photo.img_translation.length : 1;
        }
        maxColumns = (currColumns > maxColumns) ? currColumns : maxColumns;
    })

    let columns = [
        { header: "Submitted By", key: "Submitted By", width:15},
        { header: "Submitted", key: "Submitted", width:20},
        { header: `${ftLabel} Date`, key: "Delivery Date", width:15},
        { header: "PO Number", key: "PO Number", width:15},
        { header: `${ftLabel} Ref./No`, key: "Delivery Ref./No", width:31},
        { header: "Supplier", key: "Supplier", width:15},
        { header: "Location", key: "Location", width:15},
        { header: "Dispatch Postcode", key: "Dispatch Postcode", width:15},
        { header: "Full/Part Load", key: "Full/Part Load", width:15},
        { header: "Distance Travelled", key: "Distance Travelled", width:15},
        { header: "Description", key: "Description", width:20},
        { header: "Photos", key: "Photos", width:20},
    ];
    if(maxColumns > 1) {
        for(let i=1; i<maxColumns; i++) {
            columns.push({ header: "Photos"+i, key: "Photos"+i, width:20});
        }
    }

    worksheet.columns = columns;

    let records = [];
    deliveryNotes.map(r => {
        console.log(r.user_ref.first_name, r.pp_description)
        let result = {
            "Submitted By": DataProcessingService.getUserFullName(r.user_ref),
            "Submitted": r.createdAt ? moment(+r.createdAt).format('DD-MM-YYYY') : null,
            "Delivery Date":r.dn_delivered_on ? momentTz(+r.dn_delivered_on).tz(timezone).format('DD-MM-YYYY') : null,
            "PO Number": r.po_number,
            "Delivery Ref./No": r.delivery_ref_no,
            "Supplier": r.dn_supplier,
            "Location": r.dn_location,
            "Dispatch Postcode": r.dn_dispatch_postcode,
            "Full/Part Load": r.dn_load_type + ((r.dn_load_type === 'Part load') ?  ` (${r.dn_load_percentage}%)` : ''),
            "Distance Travelled": parseDistanceTravelled(r),
            "Description": r.dn_description,
            "Photos": null
        };
        records.push(result);
    });
    let photoColumn = 'L';
    let endColumn = String.fromCharCode(photoColumn.charCodeAt(0) + maxColumns - 1);
    worksheet.addRows(records);
    if(maxColumns > 1) {
        let colToMerge = 'L1:'+endColumn+'1';
        worksheet.mergeCells(colToMerge);
    }
    let r = 1;
    for(const n of deliveryNotes) {
        let c = 11;
        worksheet.getRow(r).height = 110;
        for(const i of n.dn_images) {
            let fileRecords = await _addImagesToWorkbook(workbook, 'img'+i.id, [i]);
            if (fileRecords.length) {
                for(const fileRecord of fileRecords) {
                    worksheet.addImage(fileRecord.id, {
                        tl: {col: c+0.05, row: r+0.25},
                        ext: fileRecord.ext,
                    });
                    c++;
                }
            }
        }
        r++;
    }
    worksheet.lastRow.height = 110;
    worksheet.eachRow(function(row, rowNumber) {
        // Iterate over all (including empty) cells in a row
        row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
            if (rowNumber === 1) {
                row.height = 20;
                cell.font = {
                bold: true,
                };
                cell.fill = {
                    type: 'pattern',
                    pattern:'solid',
                    fgColor: {argb: 'dedede'}
                }
                cell.border = {
                    top: {style:'thin'},
                    left: {style:'thin'},
                    bottom: {style:'thin'},
                    right: {style:'thin'}
                };

            }
            cell.alignment = {  vertical: 'middle',horizontal: 'center',  wrapText: true};
        });
    });
    return workbook;
};

//For Close Call Report
const getCustomFields = (closeCalls, imageHeaderStyle) => {
    const ccCustomFields = [];

    closeCalls.forEach(call => {
        (call.custom_fields || []).forEach(customField => {
            const isExist = ccCustomFields.find(field => field.label === customField.label);
            if (!isExist && !customField.is_default) {
                ccCustomFields.push(customField);
            }
        });
    });
    sails.log.info("ccCustomFields: ", ccCustomFields);

    let customFields = [];
    let defaultFields = [
        { header: "Hazard Category", key: "Hazard Category", width:31.5, ...imageHeaderStyle, identifier: 'hazard_category'},
        { header: "Lighting Conditions", key: "Lighting Conditions", width:31.5, ...imageHeaderStyle, identifier: 'lighting_conditions'},
        { header: "Location", key: "Location", width:43, ...imageHeaderStyle, identifier: 'location_and_description'},
        { header: "Details", key: "Details", width:43, ...imageHeaderStyle, identifier: 'additional_detail'},
        { header: "Location Tag (Lat, Long)", key: "Location Tag (Lat, Long)", width:30, ...imageHeaderStyle, identifier: 'location'},
        { header: "What could have happened?", key: "What could have happened?", width:31.5, ...imageHeaderStyle, identifier: 'cc_detail'},
    ];
    if (ccCustomFields.length) {
        (ccCustomFields).map(field => {
            let fieldForColumn = {
                header: field.label,
                key: field.label,
                width:28.6,
                ...imageHeaderStyle
            };
            customFields.push(fieldForColumn);
        });
    }

    return {defaultFields, customFields};
};

//For Close Call Report
const getCustomFieldsValue = (record, defaultFields, customFields) => {
    let possibleDefaultFieldsValue = {
        "Hazard Category": record.hazard_category,
        "Lighting Conditions": record.lighting_conditions,
        "Location": record.location_and_description,
        "Details": record.additional_detail,
        "Location Tag (Lat, Long)": (record.location && record.location.lat && record.location.long) ? `${record.location.lat}, ${record.location.long}` : "",
        "What could have happened?": record.cc_detail
    };

    let defaultFieldsValue = {};
    defaultFields.map(field => {
        if (possibleDefaultFieldsValue[field.header]) {
            defaultFieldsValue[field.header] = possibleDefaultFieldsValue[field.header];
        }
        return field;
    });

    let customFieldsValue = {};
    customFields.map(field => {
        let fieldWithValue = (record.custom_fields || []).find(cField => cField.label == field.header) || {'value': ''};
        customFieldsValue[field.header] = fieldWithValue.value;
        return field;
    });

    return {defaultFieldsValue, customFieldsValue};
};

//For Close Call Report
const getColumnPositions = (columns, alphabet) => {
    let photos = "Photos";
    let photos2 = "Photos2";
    let photos3 = "Photos3";
    let ccPhoto = "Close Out Photos";
    let ccPhoto2 = "Close Out Photos2";
    let ccPhoto3 = "Close Out Photos3";
    let status = "Status";
    let photosColumnNumber = [];
    let photosColumnAlphabet = [];
    let photoStartNum = 0;
    let closeoutPhotoStartNum = 0;
    let statusColNum = 0;

    let photoStartAlpha = "A";
    let photoEndAlpha = "A";
    let closeoutPhotoStartAlpha = "A";
    let closeoutPhotoEndAlpha = "A";

    photosColumnNumber = columns.reduce((arr, column, index) => {
        if ([photos, photos2, photos3, ccPhoto, ccPhoto2, ccPhoto3, status].includes(column.header)) {

            if (column.header == photos) {
                photoStartNum = index + 1;
                photoStartAlpha = alphabet[index];
            }

            if (column.header == ccPhoto) {
                closeoutPhotoStartNum = index + 1;
                closeoutPhotoStartAlpha = alphabet[index];
            }

            if (column.header == photos3) {
                photoEndAlpha = alphabet[index];
            }

            if (column.header == ccPhoto3) {
                closeoutPhotoEndAlpha = alphabet[index];
            }

            if (column.header == status) {
                statusColNum = index + 1;
            } else {
                arr.push(index+1);
                photosColumnAlphabet.push(alphabet[index]);
            }
        }
        return arr;
    }, []);

    return { photosColumnNumber, photosColumnAlphabet, photoStartNum, closeoutPhotoStartNum, photoStartAlpha, photoEndAlpha, closeoutPhotoStartAlpha, closeoutPhotoEndAlpha, statusColNum };
}

const closeCallReport = async (closeCalls, projectInfo, timezone, isSiteAdmin) => {
    sails.log.info('creating workbook export', (new Date()));

    let ccSglrPhrase = projectInfo.custom_field.cc_phrase_singlr;

    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet(getWorksheetName(ccSglrPhrase), {
        views: [{zoomScale: 60}],
        pageSetup:{paperSize: 5, orientation:'landscape', scale: 86}
    });

    let col1 = ccSglrPhrase+" no";
    let imageHeaderStyle = {style: {alignment: {  vertical: 'middle',horizontal: 'center',  wrapText: true}}};

    let {defaultFields, customFields} = getCustomFields(closeCalls, imageHeaderStyle);
    let alphabet = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U,", "V", "W", "X", "Y", "Z","AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU,", "AV", "AW", "AX", "AY", "AZ"];
    if (isSiteAdmin) {
        let columns = [
            { header: col1, key: col1, width:21.5, ...imageHeaderStyle},
            { header: "Date/Time", key: "Date & Time", width:28.6, ...imageHeaderStyle},
            { header: "Project", key: "Project", width:31.5, ...imageHeaderStyle},
            { header: "Raised By", key: "Raised By", width:31.5, ...imageHeaderStyle},
            { header: "Employer", key: "Employer", width:31.5, ...imageHeaderStyle},
            ...defaultFields,
            { header: "Owner", key: "Owner", width:37.6, ...imageHeaderStyle},
            { header: "Assigned To", key: "Assigned To", width:37.6, ...imageHeaderStyle},
            { header: "Photos", key: "Photos", width:20, ...imageHeaderStyle},
            { header: "Photos2", key: "Photos2", width:20, ...imageHeaderStyle},
            { header: "Photos3", key: "Photos3", width:20, ...imageHeaderStyle},
            ...customFields,
            { header: "Status", key: "Status", width:31.5, ...imageHeaderStyle},
            { header: "Action taken to close out", key: "Action taken to close out", width:36, ...imageHeaderStyle},
            { header: "Close Out Photos", key: "Close Out Photos", width:20, ...imageHeaderStyle},
            { header: "Close Out Photos2", key: "Close Out Photos2", width:20, ...imageHeaderStyle},
            { header: "Close Out Photos3", key: "Close Out Photos3", width:20, ...imageHeaderStyle},
            { header: "Closed Out By", key: "Closed out by", width:36, ...imageHeaderStyle},
            { header: "Close Out Date/Time", key: "Close out Date", width:31.5, ...imageHeaderStyle}
        ];

        worksheet.columns = columns;

        let {
            photosColumnNumber,
            photosColumnAlphabet,
            photoStartNum,
            closeoutPhotoStartNum,
            photoStartAlpha,
            photoEndAlpha,
            closeoutPhotoStartAlpha,
            closeoutPhotoEndAlpha,
            statusColNum
        } = getColumnPositions(columns, alphabet);
        let closeCallImagesWorkspaceId = [];
        let closedOutImagesWorkspaceId = [];

        for (let i = 0, len = closeCalls.length; i < len; i++){
            // Add All images into workbook, and save their IDs
            closeCallImagesWorkspaceId.push(...(await _addImagesToWorkbook(workbook, closeCalls[i].cc_number, closeCalls[i].cc_images, 'rowId')));
            closedOutImagesWorkspaceId.push(...(await _addImagesToWorkbook(workbook, closeCalls[i].cc_number, closeCalls[i].corrective_images, 'rowId')));
        }

        let records = closeCalls.map(r => {
            let userEmployer = 'Anonymous';
            if (!r.is_anonymous) {
                if (r.user_employer && r.user_employer.employer) {
                    userEmployer = r.user_employer.employer;
                }
            }

            let userName = 'Anonymous';
            if (!r.is_anonymous) {
                userName = getUserFullName(r.user_ref)
            }

            let textEnterByAdmin = '';
            textEnterByAdmin = (r.closed_out_by ? r.closed_out_by+': ' : '') + (r.corrective_detail ? r.corrective_detail : '');

            let {defaultFieldsValue, customFieldsValue} = getCustomFieldsValue(r, defaultFields, customFields);
            return {
                [col1]: r.cc_number,
                "Date & Time": r.createdAt ? moment(+r.createdAt).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : null,
                "Project": (projectInfo && projectInfo.project_number !=null ? projectInfo.project_number +' - ' + projectInfo.name: projectInfo.name || ''),
                "Raised By": userName,
                "Employer": userEmployer,
                ...defaultFieldsValue,
                "Assigned To": (r.assigned_to && r.assigned_to.id) ? getUserFullName(r.assigned_to) : '',
                "Owner": (r.tagged_owner && r.tagged_owner.name) ? r.tagged_owner.name : 'N/A',
                "Photo": null,
                "Photo2": null,
                "Photo3": null,
                ...customFieldsValue,
                "Status": r.status_message,
                "Action taken to close out": textEnterByAdmin,
                "Close Out Photos": null,
                "Close Out Photos2": null,
                "Close Out Photos3": null,
                "Closed out by": r.closed_out_by,
                "Close out Date": r.closed_out_date ? moment(+r.closed_out_date).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : null
            };
        });

        worksheet.addRows(records);
        console.log('Merging heading column');
        worksheet.mergeCells(`${photoStartAlpha}1:${photoEndAlpha}1`);
        worksheet.mergeCells(`${closeoutPhotoStartAlpha}1:${closeoutPhotoEndAlpha}1`);
        let lastRowIndex = 0;
        worksheet.eachRow(function(row, rowNumber) {
            lastRowIndex++;
            let actualRow = worksheet.getRow(lastRowIndex);

            // Iterate over all (including empty) cells in a row
            actualRow.eachCell({ includeEmpty: true }, function(cell, colNumber) {
                // Styling first ROW of Heading
                if (rowNumber === 1) {
                    actualRow.height = 20;
                    cell.font = {
                        bold: true,
                    };
                    cell.fill = {
                        type: 'pattern',
                        pattern:'solid',
                        fgColor: {argb: 'dedede'}
                    }
                }

                if(!photosColumnNumber.includes(colNumber)) {
                    cell.border = {
                        top: {style:'thin'},
                        left: {style:'thin'},
                        bottom: {style:'thin'},
                        right: {style:'thin'}
                    };
                } else {
                    cell.border = {
                        top: {style:'thin'},
                    };
                }
                if(colNumber === statusColNum){
                    let v = cell.value, o = 'Open', c = 'Closed Out';
                    if (v === o || v === c) {
                        cell.fill = {
                            type: 'pattern',
                            pattern:'solid',
                            fgColor:{argb: (v===o) ? 'dc3545' : '28a745'}
                        };
                    }
                }
            });
            if(rowNumber <= 1){
                return;
            }
            console.log('Processing old row #', rowNumber, 'last row index was', lastRowIndex);

            let maxHeightNeeded = 0;
            let mergeStartAt = lastRowIndex;
            let mergeEndAt = lastRowIndex;
            let imgs = (closeCallImagesWorkspaceId || []).filter(i => i.rowId == actualRow.getCell(col1).value) || [];
            imgs.map(async (img, loopIndex) => {
                console.log('Adding image in row', lastRowIndex);
                let tlCol = (photoStartNum - 1) + 0.10 + (loopIndex % 3);
                let tlRow = (lastRowIndex-1) + 0.1;
                let tl = { col: tlCol, row: tlRow };
                worksheet.addImage(img.id, {
                    tl: tl,
                    ext: img.ext,
                });
                maxHeightNeeded = _increaseRowHeightIfNeeded(worksheet.getRow(lastRowIndex), img.ext.height, maxHeightNeeded);
                // Just before it need, add an empty row
                if(loopIndex > 0 && (loopIndex % 3) === 2 && loopIndex < (imgs.length-1)){
                    console.log('Adding empty row');
                    worksheet.spliceRows(lastRowIndex+1, 0, []);
                    mergeEndAt++;
                    lastRowIndex++;
                }
            });


            let lastClosedOutRowIndex = mergeStartAt;
            //console.log('Closed out will start from ', lastClosedOutRowIndex, 'while current index is', lastRowIndex);
            let img_of_closed_out = (closedOutImagesWorkspaceId || []).filter(i => i.rowId == actualRow.getCell(col1).value) || [];
            img_of_closed_out.map((img, loopIndex) => {
                console.log('Adding closed out image in row', lastClosedOutRowIndex);
                let tlCol = (closeoutPhotoStartNum - 1) + 0.10 + (loopIndex%3);
                let tlRow = (lastClosedOutRowIndex-1) + 0.1;
                let tl = { col: tlCol, row: tlRow };
                worksheet.addImage(img.id, {
                    tl: tl,
                    ext: img.ext,
                });
                maxHeightNeeded = _increaseRowHeightIfNeeded(worksheet.getRow(lastClosedOutRowIndex), img.ext.height, maxHeightNeeded);
                // Just before it need, add an empty row
                if(loopIndex > 0 && (loopIndex % 3) === 2 && loopIndex < (img_of_closed_out.length-1)){
                    if(lastClosedOutRowIndex >= lastRowIndex){
                        console.log('Adding empty row for closed out');
                        worksheet.spliceRows(lastClosedOutRowIndex+1, 0, []);
                        mergeEndAt++;
                        lastRowIndex++;
                    }
                    lastClosedOutRowIndex++;
                }
            });
            worksheet.getRow(lastRowIndex).eachCell({includeEmpty: true}, function(cell, colNumber) {
                cell.border = {
                    ...(cell.border ? cell.border : {}),
                    bottom: {style:'thin'},
                };
            });

            // Merge if extra rows were added above
            if(mergeEndAt > mergeStartAt){
                console.log(`Merging cells vertically ${mergeStartAt} to ${mergeEndAt}`);
                for (let index in columns) {
                    if (alphabet[index] && !photosColumnAlphabet.includes(alphabet[index])) {
                        worksheet.mergeCells(`${alphabet[index]}${mergeStartAt}:${alphabet[index]}${mergeEndAt}`);
                    }
                }
            }
            //console.log('Last row index is', lastRowIndex);
        });
    } else {
        let columns = [
            { header: col1, key: col1, width:21.5, ...imageHeaderStyle},
            { header: "Date/Time", key: "Date & Time", width:28.6, ...imageHeaderStyle},
            { header: "Project", key: "Project", width:31.5, ...imageHeaderStyle},
            { header: "Raised By", key: "Raised By", width:31.5, ...imageHeaderStyle},
            { header: "Employer", key: "Employer", width:31.5, ...imageHeaderStyle},
            ...defaultFields,
            { header: "Assigned To", key: "Assigned To", width:37.6, ...imageHeaderStyle},
            { header: "Photos", key: "Photos", width:20, ...imageHeaderStyle},
            { header: "Photos2", key: "Photos2", width:20, ...imageHeaderStyle},
            { header: "Photos3", key: "Photos3", width:20, ...imageHeaderStyle},
            ...customFields,
            { header: "Status", key: "Status", width:31.5, ...imageHeaderStyle},
            { header: "Action taken to close out", key: "Action taken to close out", width:36, ...imageHeaderStyle},
            { header: "Close Out Photos", key: "Close Out Photos", width:20, ...imageHeaderStyle},
            { header: "Close Out Photos2", key: "Close Out Photos2", width:20, ...imageHeaderStyle},
            { header: "Close Out Photos3", key: "Close Out Photos3", width:20, ...imageHeaderStyle},
            { header: "Closed Out By", key: "Closed out by", width:36, ...imageHeaderStyle},
            { header: "Close Out Date/Time", key: "Close out Date", width:31.5, ...imageHeaderStyle}
        ];
        worksheet.columns = columns;

        let {
            photosColumnNumber,
            photosColumnAlphabet,
            photoStartNum,
            closeoutPhotoStartNum,
            photoStartAlpha,
            photoEndAlpha,
            closeoutPhotoStartAlpha,
            closeoutPhotoEndAlpha,
            statusColNum
        } = getColumnPositions(columns, alphabet);

        let closeCallImagesWorkspaceId = [];
        let closedOutImagesWorkspaceId = [];

        for (let i = 0, len = closeCalls.length; i < len; i++){
            // Add All images into workbook, and save their IDs
            closeCallImagesWorkspaceId.push(...(await _addImagesToWorkbook(workbook, closeCalls[i].cc_number, closeCalls[i].cc_images, 'rowId')));
            closedOutImagesWorkspaceId.push(...(await _addImagesToWorkbook(workbook, closeCalls[i].cc_number, closeCalls[i].corrective_images, 'rowId')));
        }

        let records = closeCalls.map(r => {
            let userEmployer = 'Anonymous';
            if (!r.is_anonymous) {
                if (r.user_employer && r.user_employer.employer) {
                    userEmployer = r.user_employer.employer;
                }
            }

            let userName = 'Anonymous';
            if (!r.is_anonymous) {
                userName = r.user_ref.name;
            }

            let textEnterByAdmin = '';


            textEnterByAdmin = (r.closed_out_by ? r.closed_out_by+': ' : '') + (r.corrective_detail ? r.corrective_detail : '');

            let {defaultFieldsValue, customFieldsValue} = getCustomFieldsValue(r, defaultFields, customFields);
            return {
                [col1]: r.cc_number,
                "Date & Time": r.createdAt ? moment(+r.createdAt).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : null,
                "Project": (projectInfo && projectInfo.project_number !=null ? projectInfo.project_number +' - ' + projectInfo.name: projectInfo.name || ''),
                "Raised By": userName,
                "Employer": userEmployer,
                ...defaultFieldsValue,
                "Assigned To": (r.assigned_to && r.assigned_to.id) ? getUserFullName(r.assigned_to) : '',
                "Photo": null,
                "Photo2": null,
                "Photo3": null,
                ...customFieldsValue,
                "Status": r.status_message,
                "Action taken to close out": textEnterByAdmin,
                "Close Out Photos": null,
                "Close Out Photos2": null,
                "Close Out Photos3": null,
                "Closed out by": r.closed_out_by,
                "Close out Date": r.closed_out_date ? moment(+r.closed_out_date).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : null
            };
        });

        worksheet.addRows(records);
        console.log('Merging heading column');
        worksheet.mergeCells(`${photoStartAlpha}1:${photoEndAlpha}1`);
        worksheet.mergeCells(`${closeoutPhotoStartAlpha}1:${closeoutPhotoEndAlpha}1`);
        let lastRowIndex = 0;
        worksheet.eachRow(function(row, rowNumber) {
            lastRowIndex++;
            let actualRow = worksheet.getRow(lastRowIndex);

            // Iterate over all (including empty) cells in a row
            actualRow.eachCell({ includeEmpty: true }, function(cell, colNumber) {
                // Styling first ROW of Heading
                if (rowNumber === 1) {
                    actualRow.height = 20;
                    cell.font = {
                        bold: true,
                    };
                    cell.fill = {
                        type: 'pattern',
                        pattern:'solid',
                        fgColor: {argb: 'dedede'}
                    }
                }
                if(!photosColumnNumber.includes(colNumber)){
                    cell.border = {
                        top: {style:'thin'},
                        left: {style:'thin'},
                        bottom: {style:'thin'},
                        right: {style:'thin'}
                    };
                } else {
                    cell.border = {
                        top: {style:'thin'},
                    };
                }
                if(colNumber === statusColNum) {
                    let v = cell.value, o = 'Open', c = 'Closed Out';
                    if (v === o || v === c) {
                        cell.fill = {
                            type: 'pattern',
                            pattern:'solid',
                            fgColor:{argb: (v===o) ? 'dc3545' : '28a745'}
                        };
                    }
                }
            });
            if(rowNumber <= 1){
                return;
            }
            //console.log('Processing old row #', rowNumber, 'last row index was', lastRowIndex);

            let maxHeightNeeded = 0;
            let mergeStartAt = lastRowIndex;
            let mergeEndAt = lastRowIndex;
            let imgs = (closeCallImagesWorkspaceId || []).filter(i => i.rowId == actualRow.getCell(col1).value) || [];
            imgs.map(async (img, loopIndex) => {
                console.log('Adding image in row', lastRowIndex);
                let tlCol = (photoStartNum - 1) + 0.10 + (loopIndex % 3);
                let tlRow = (lastRowIndex-1) + 0.1;
                let tl = { col: tlCol, row: tlRow };
                worksheet.addImage(img.id, {
                    tl: tl,
                    ext: img.ext,
                });
                maxHeightNeeded = _increaseRowHeightIfNeeded(worksheet.getRow(lastRowIndex), img.ext.height, maxHeightNeeded);
                // Just before it need, add an empty row
                if(loopIndex > 0 && (loopIndex % 3) === 2 && loopIndex < (imgs.length-1)){
                    console.log('Adding empty row');
                    worksheet.spliceRows(lastRowIndex+1, 0, []);
                    mergeEndAt++;
                    lastRowIndex++;
                }
            });


            let lastClosedOutRowIndex = mergeStartAt;
            console.log('Closed out will start from ', lastClosedOutRowIndex, 'while current index is', lastRowIndex);
            let img_of_closed_out = (closedOutImagesWorkspaceId || []).filter(i => i.rowId == actualRow.getCell(col1).value) || [];
            img_of_closed_out.map((img, loopIndex) => {
                console.log('Adding closed out image in row', lastClosedOutRowIndex);
                let tlCol = (closeoutPhotoStartNum - 1) + 0.10 + (loopIndex%3);
                let tlRow = (lastClosedOutRowIndex-1) + 0.1;
                let tl = { col: tlCol, row: tlRow };
                worksheet.addImage(img.id, {
                    tl: tl,
                    ext: img.ext,
                });
                maxHeightNeeded = _increaseRowHeightIfNeeded(worksheet.getRow(lastClosedOutRowIndex), img.ext.height, maxHeightNeeded);
                // Just before it need, add an empty row
                if(loopIndex > 0 && (loopIndex % 3) === 2 && loopIndex < (img_of_closed_out.length-1)){
                    if(lastClosedOutRowIndex >= lastRowIndex){
                        console.log('Adding empty row for closed out');
                        worksheet.spliceRows(lastClosedOutRowIndex+1, 0, []);
                        mergeEndAt++;
                        lastRowIndex++;
                    }
                    lastClosedOutRowIndex++;
                }
            });
            worksheet.getRow(lastRowIndex).eachCell({includeEmpty: true}, function(cell, colNumber) {
                cell.border = {
                    ...(cell.border ? cell.border : {}),
                    bottom: {style:'thin'},
                };
            });

            // Merge if extra rows were added above
            if(mergeEndAt > mergeStartAt){
                console.log(`Merging cells vertically ${mergeStartAt} to ${mergeEndAt}`);
                for (let index in columns) {
                    if (alphabet[index] && !photosColumnAlphabet.includes(alphabet[index])) {
                        worksheet.mergeCells(`${alphabet[index]}${mergeStartAt}:${alphabet[index]}${mergeEndAt}`);
                    }
                }
            }
            console.log('Last row index is', lastRowIndex);
        });
    }

    return workbook;
};
const companyProjectTimeSheetReport = async ({
                                                 visible_to_date,
                                                 project = {},
                                                 company_logo,
                                                 records = [],
                                                 comment,
                                                 orderNumber,
                                                 departmentNumber,
                                                 signature,

                                                 user,
                                                 user_employment,
                                             }, fn) => {
    sails.log.info('creating workbook export', (new Date()));

    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('project-timesheet', {
        views: [{zoomScale: 75}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 60}
    });

    let [logoFileRecord] = await _addImagesToWorkbook(workbook, 'logo', [company_logo]);
    sails.log.info('logoFileRecord', logoFileRecord)
    if (logoFileRecord) {
        // Add Logo only when there is data.
        worksheet.addImage(logoFileRecord.id, {
            tl: {col: 0.1, row: 0.1},
            ext: logoFileRecord.ext,
        });
        _increaseRowHeightIfNeeded(worksheet.getRow(1), logoFileRecord.ext.height);
    }

    worksheet.getColumn('A').width = 35;
    worksheet.getColumn('A').style = {alignment: {vertical: 'middle', wrapText: true}};
    worksheet.getColumn('B').width = 25;
    worksheet.getColumn('B').style = {alignment: {vertical: 'middle', wrapText: true}};
    worksheet.getColumn('C').width = 25;
    worksheet.getColumn('C').style = {alignment: {vertical: 'middle', wrapText: true}};
    worksheet.getColumn('D').width = 10;
    worksheet.getColumn('K').width = 15;
    // worksheet.getColumn('J').style = {alignment: {horizontal: 'center'}};
    worksheet.getRow(1).commit();
    worksheet.addRow([(project.parent_company && project.parent_company.name) || '', project.name]).font = {size: 14, bold: true};
    worksheet.getRow(2).commit();
    worksheet.addRow(['Week Ending:', visible_to_date]).commit();
    worksheet.addRow(['Client:', project.client]).commit();
    worksheet.addRow(['Client Contact:', project.main_contact_name]).commit();
    worksheet.addRow(['Client Number:', project.main_contact_number]).commit();

    _createOuterBorder(worksheet, {row: 4, col: 1}, {row: 6, col: 2}, 'thin');
    worksheet.addRow([]).commit();
    worksheet.addRow(["Name", "Employer", "Trade", "innDex ID", "Mon", "Tues", "Weds", "Thur", "Fri", "Sat", "Sun", "Total"]);
    worksheet.getRow(8).eachCell(cell => {
        cell.font = {bold: true};
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'd9d9d9'}
        };
        cell.border = {
            top: {style: 'medium'},
            left: {style: 'thin'},
            bottom: {style: 'thin'},
            right: {style: 'thin'}
        }
    });

    worksheet.addRows(records.map(row => {
        return [
            fn.getUserFullName(row.user_ref),
            fn.showEmploymentCompany(row),
            row.job_role,
            row.user_ref.id,
            row['Mon'] ? fn.moment().startOf('day').add(fn.duration(+row['Mon'], 'seconds')).format('HH:mm:ss') : '-',
            row['Tue'] ? fn.moment().startOf('day').add(fn.duration(+row['Tue'], 'seconds')).format('HH:mm:ss') : '-',
            row['Wed'] ? fn.moment().startOf('day').add(fn.duration(+row['Wed'], 'seconds')).format('HH:mm:ss') : '-',
            row['Thu'] ? fn.moment().startOf('day').add(fn.duration(+row['Thu'], 'seconds')).format('HH:mm:ss') : '-',
            row['Fri'] ? fn.moment().startOf('day').add(fn.duration(+row['Fri'], 'seconds')).format('HH:mm:ss') : '-',
            row['Sat'] ? fn.moment().startOf('day').add(fn.duration(+row['Sat'], 'seconds')).format('HH:mm:ss') : '-',
            row['Sun'] ? fn.moment().startOf('day').add(fn.duration(+row['Sun'], 'seconds')).format('HH:mm:ss') : '-',
            row['Total'] ? row['total_string'] : '-'
        ];
    }));
    // Adding 1 row extra space.
    let lastDataRowCount = (8 + records.length + 1);

    _createOuterBorder(worksheet, {row: 8, col: 1}, {row: lastDataRowCount, col: 12});

    worksheet.addRow([]).commit();
    worksheet.addRow(['Comments:', comment]).commit();
    worksheet.addRow(['Order No.:', orderNumber]).commit();
    worksheet.addRow(['Department No.:', departmentNumber]).commit();
    worksheet.addRow(['Name:', fn.getUserFullName(user)]).commit();
    worksheet.addRow(['Position:', user_employment && user_employment.job_role]).commit();
    worksheet.addRow(['Signed:', '']).commit();

    if (signature) {
        sails.log.info('Adding signature into excel');
        const signatureImageId = workbook.addImage({
            base64: signature,
            extension: 'png',
        });
        let dimensions = sizeOf(Buffer.from(signature.split(';base64,').pop(), 'base64'));
        let corrected_dimension = _getCorrectImageSize(dimensions);
        worksheet.addImage(signatureImageId, {
            tl: {col: 1, row: (worksheet.rowCount - 1)},
            ext: corrected_dimension,
        });
        _increaseRowHeightIfNeeded(worksheet.getRow(worksheet.rowCount), corrected_dimension.height);
    }

    _createOuterBorder(worksheet, {row: lastDataRowCount + 2, col: 1}, {row: worksheet.rowCount, col: 2}, 'thin');

    sails.log.info('created workbook', (new Date()));
    return workbook;
};

const taskBriefingReport = async  (records, fromDate, toDate, tbPhrase) => {
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet(getWorksheetName(tbPhrase), {
        properties: {
            defaultRowHeight: 30
        }
    });
    worksheet.columns = [
        { header: 'Project', key: 'project', width: 27 },
        { header: tbPhrase+' #', key: 'task_briefing_no', width: 27 },
        { header: 'Title', key: 'title', width: 27 },
        { header: 'No. Times Briefed', key: 'briefed_count', width: 22 },
        { header: 'No. Operatives Briefed', key: 'total_operatives_briefed', width: 22 },
    ];
    let rows = records.filter(function(r) {
        if((+r.createdAt >= fromDate && +r.createdAt <= toDate)) {
            return true;
        }

        let register = (r.register || []).filter(regObj => {
            if((+regObj.briefed_at >= fromDate && +regObj.briefed_at <= toDate)) {
                return true;
            }
        });

        if(register.length) {
            return true;
        }

        return false;
    }).map(r => {
        //let userEmployer = r.user_employer.employer;
        //let userName = r.user_ref.name;
        let timesBriefedCount = 0;
        let operativesBriefedCount = 0;
        let registerBetweenRange = r.register.filter(reg => {
            if(+reg.briefed_at >= fromDate && +reg.briefed_at <= toDate) {
                timesBriefedCount = timesBriefedCount + 1;
                operativesBriefedCount += reg.allattendees.length;
            }
        })

        return {
            "project": (r.project_ref && r.project_ref.project_number !=null ? r.project_ref.project_number +' - ' + r.project_ref.name: r.project_ref.name || ''),
            "task_briefing_no": r.record_id,
            "title": r.briefing_title,
            "briefed_count": timesBriefedCount,
            "total_operatives_briefed": operativesBriefedCount,
        };
    });
    worksheet.addRows(rows);
    worksheet.eachRow(function(row, rowNumber) {
        if(rowNumber == 1) {
            row.height = 20;
            row.eachCell(function(cell) {
                cell.fill = {
                    type: 'pattern',
                    pattern:'solid',
                    fgColor: {argb: 'dedede'}
                }
                cell.border = {
                    top: { style: "thin" },
                    left: { style: "thin" },
                    bottom: { style: "thin" },
                    right: { style: "thin" }
                };
            });
        }
    });

    return workbook;
};

/*
 * NOTE: Change in this method will affect following features:
 * - Work Package Plan(WPP)
 * - Risk Assessment & Method Statements(RAMs)
 *
 * Recommend: It will require to test all above features on change anything in the method.
 * */
const workPackagePlanReport = async  (records, fromDate, toDate, wppPhrase) => {
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet(getWorksheetName(wppPhrase), {
        properties: {
            defaultRowHeight: 30
        }
    });
    worksheet.columns = [
        { header: `${wppPhrase} #`, key: 'work_plan_no', width: 27 },
        { header: 'Title', key: 'title', width: 27 },
        { header: 'No. Times Briefed', key: 'briefed_count', width: 22 },
        { header: 'No. Operatives Briefed', key: 'total_operatives_briefed', width: 22 },
    ];
    let rows = records.filter(function(r) {
        if((+r.createdAt >= fromDate && +r.createdAt <= toDate)) {
            return true;
        }

        let register = (r.register || []).filter(regObj => {
            if((+regObj.briefed_at >= fromDate && +regObj.briefed_at <= toDate)) {
                return true;
            }
        });

        if(register.length) {
            return true;
        }

        return false;
    }).map(r => {
        //let userEmployer = r.user_employer.employer;
        //let userName = r.user_ref.name;
        let timesBriefedCount = 0;
        let operativesBriefedCount = 0;
        let registerBetweenRange = r.register.filter(reg => {
            if(+reg.briefed_at >= fromDate && +reg.briefed_at <= toDate) {
                timesBriefedCount = timesBriefedCount + 1;
                operativesBriefedCount += reg.allattendees.length;
            }
        })

        return {
            "work_plan_no": r.record_id,
            "title": r.briefing_title,
            "briefed_count": timesBriefedCount,
            "total_operatives_briefed": operativesBriefedCount,
        };
    });
    worksheet.addRows(rows);
    worksheet.eachRow(function(row, rowNumber) {
        if(rowNumber == 1) {
            row.height = 20;
            row.eachCell(function(cell) {
                cell.fill = {
                    type: 'pattern',
                    pattern:'solid',
                    fgColor: {argb: 'dedede'}
                }
                cell.border = {
                    top: { style: "thin" },
                    left: { style: "thin" },
                    bottom: { style: "thin" },
                    right: { style: "thin" }
                };
            });
        }
    });

    return workbook;
};

/*
 * NOTE: Change in this method will affect following features:
 * - Toolbox Talks,
 * - Task Briefings,
 * - Work Package Plan(WPP)
 * - Risk Assessment & Method Statements(RAMs)
 *
 * Recommend: It will require to test all above features on change anything in the method.
 * */
const downloadToolboxTalkXLSX = async(record, toolKey, featureName, tz = fall_back_timezone) => {
    sails.log.info(`creating workbook export for ${featureName}`, (new Date()));
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet(getWorksheetName(`${featureName} Report`), {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70}
    });

    worksheet.properties.defaultRowHeight = 20;

    worksheet.getColumn('A').width = 30;
    worksheet.getColumn('A').style = {alignment: {wrapText: true}};
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('B').style = {alignment: {wrapText: true}};
    worksheet.getColumn('C').width = 30;

    let rowIndex = 1;
    let primaryRows = [1];
    if (toolKey === 'toolbox_talk') {
        worksheet.addRow([`${featureName}: ${record.talk_title}`, ' ', ' ']).commit();
    } else if (toolKey === 'rams') {
        worksheet.addRow([`${record.reference_number} - ${record.briefing_title}`, ' ', ' ']).commit();
        worksheet.addRow([`Revision: ${record.revision_number}`, ' ', ' ']).commit();
        primaryRows.push(2);
        rowIndex = 2;
    } else {
        worksheet.addRow([`${featureName}: ${record.briefing_title}`, ' ', ' ']).commit();
    }

    for (let i = 0; i < primaryRows.length; i++) {
        worksheet.getRow(primaryRows[i]).eachCell(cell => {
            cell.font = {bold: true, size: 14};
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {argb: 'e9eff9'}
            };
            cell.border = {
                top: {style: 'medium'},
                left: {style: 'thin'},
                bottom: {style: 'thin'},
                right: {style: 'thin'}
            }
        });
        worksheet.mergeCells(`A${primaryRows[i]}:C${primaryRows[i]}`);
    }

    (record.register || []).map(reg => {
        if (reg.allattendees.length) {
            tz = (reg.project_ref && reg.project_ref.custom_field && reg.project_ref.custom_field.timezone) ? reg.project_ref.custom_field.timezone : tz;
            let briefedAt = reg.briefed_at ? momentTz(+reg.briefed_at).tz(tz).format('DD-MM-YYYY HH:mm:ss') : null;
            worksheet.addRow([`Briefed By: ${reg.briefed_by_name}`, `Briefing: ${briefedAt}`]).eachCell(cell => {
                cell.font = {bold: true};
            });
            worksheet.addRow(["Name", "Employer", "Signature"]).eachCell(cell => {
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: {argb: 'D3D3D3'}
                };
                cell.border = {
                    top: {style: 'thin'},
                    bottom: {style: 'thin'},
                    right: {style: 'thin'}
                }
            });
            rowIndex += 2;
            let signatureArr = [];
            (reg.allattendees || []).reduce((array, attendee) => {
                if (attendee.name) {
                    worksheet.addRow([attendee.name, attendee.emp, null]).height = 30;
                    rowIndex += 1;
                    if (attendee.sign) {
                        signatureArr[rowIndex] = attendee.sign;
                    }
                }
                return array;
            }, []);


            signatureArr.map((signature, index) => {
                let imageId = workbook.addImage({
                    base64: signature,
                    extension: 'png',
                });
                worksheet.addImage(imageId, {
                    tl: { col: 2+0.15, row: (index-1)+0.25 },
                    ext: { width: 130, height: 30 }
                });
            });

            worksheet.addRow([' ', ' ', ' ']).eachCell(cell => {
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: {argb: 'EDB61D'}
                };
                cell.border = {
                    top: {style: 'thin'},
                    bottom: {style: 'thin'},
                }
            });
            rowIndex += 1;
        }
    });

    return workbook;
};

/*
 * NOTE: Change in this method will affect following features:
 * - Toolbox Talks,
 * - Task Briefings,
 * - Work Package Plan(WPP)
 * - Risk Assessment & Method Statements(RAMs)
 *
 * Recommend: It will require to test all above features on change anything in the method.
 * */
const downloadRegisterXLSX = async (projectId, feature, records, toolKey) => {
    let project = await sails.models.project.findOne({
        select: ['name', 'custom_field', 'contractor'],
        where: {id: projectId}
    });

    sails.log.info(`Found project for project Id ${projectId}: ${project.name}`);
    let alternatPhrase = '';
    if (feature == 'rams') {
        alternatPhrase = project.custom_field.rams_phrase_singlr;
    } else if (feature == 'tbt') {
        alternatPhrase = 'Toolbox Talk';
    } else if (feature == 'tb') {
        alternatPhrase = project.custom_field.tb_phrase_singlr;
    } else if (feature == 'wpp') {
        alternatPhrase = project.custom_field.wpp_phrase_singlr;
    }

    sails.log.info(`Downloading register for feature ${alternatPhrase} toolKey: ${toolKey}`);
    if (toolKey) {
        // fetch briefing counts for this project.
        let countsByRecords = await toolBriefingsFn.getBriefingCountsOfRecords(projectId, toolKey, records.map(r => r.id), true);

        // fetch briefings
        let recordIds = records.map(item => item.id);
        let briefings = await getToolBriefings(projectId, toolKey, recordIds);
        let userIds = new Set();
        briefings.forEach(item => {
            if(item.briefed_by){
                userIds.add(item.briefed_by.id);
            }
        });
        userIds = [...userIds];
        let usersEmployer = await getInductionEmployerByUserIds(userIds, projectId, [2,6]);

        // Convert usersEmployer into a lookup Map (faster than array search)
        const inductionMap = new Map(usersEmployer.map(user => [user.user_ref, user.user_employer]));

        // Add induction_company to briefings
        const updatedBriefings = briefings.map(briefing => {
            if (briefing.briefed_by && briefing.briefed_by.id) {
                const company = inductionMap.get(briefing.briefed_by.id) || null;
                return { ...briefing, induction_company: company };
            }
            return { ...briefing, induction_company: null };
        });

        records = records.map(r => {
            let row = countsByRecords.find(c => c.tool_record_ref === r.id);
            let briefingRow = updatedBriefings.find(b => b.tool_record_ref === r.id);
            r.briefed_count = row ? row.count : 0;
            r.attendees_count = row ? +row.attendees_count : 0;
            r.last_briefing = briefingRow;
            return r;
        });

    }

    //expand users
    let userIds = [];
    let taggedCompanyIds = []
    records = (records || []).map(record => {
        userIds.push(record.user_ref);
        if (record.approved_by) {
            userIds.push(record.approved_by);
        }

        if (record.tagged_owner) {
            taggedCompanyIds.push(record.tagged_owner);
        }
        return record;
    });

    sails.log.info('User Ids of Items, ', userIds);
    let usersInfo = await sails.models.user_reader.find({
        where: {id: _uniq(userIds)},
        select: ['first_name', 'middle_name', 'last_name', 'email', 'timezone']
    });

    sails.log.info('Tagged Company Ids of Items, ', taggedCompanyIds);
    let companiesInfo = await sails.models.createemployer.find({
        where: {id: _uniq(taggedCompanyIds)},
        select: ['name']
    });

    records = (records || []).map(record => {
        record.user_ref = usersInfo.find(user => user.id == record.user_ref);
        record.approved_by = usersInfo.find(user => user.id == record.approved_by);
        record.tagged_owner = companiesInfo.find(company => company.id == record.tagged_owner);
        return record;
    });

    let title = `${alternatPhrase}-register-${project.contractor}-${project.name}`.replace(/[*?\\/:[\]]/g, '_');

    let workbook = new ExcelJS.Workbook();

    if (feature === 'rams') {
        let activeRams = records.filter(record => !record.is_archived && record.revision_status);
        let archivedRams = records.filter(record => (record.is_archived || !record.revision_status));
        prepareDownloadRegisterXlsx(workbook, 'Active', activeRams, project, alternatPhrase, feature);
        prepareDownloadRegisterXlsx(workbook, 'Archived', archivedRams, project, alternatPhrase, feature);
    } else {
        prepareDownloadRegisterXlsx(workbook, title, records, project, alternatPhrase, feature);
    }

    let fileName = `${title}.xlsx`;
    return {workbook, fileName};
};

const prepareDownloadRegisterXlsx = (workbook, workSheetName, records, project, alternatPhrase, feature) => {
    let worksheet = workbook.addWorksheet(getWorksheetName(workSheetName), {
        properties: {
            defaultRowHeight: 30
        }
    });

    if (feature == 'rams') {
        worksheet.columns = [
            { header: `${alternatPhrase} #`, key: 's_no', width: 27 },
            { header: 'Ref. Number', key: 'reference_number', width: 22 },
            { header: 'Title', key: 'title', width: 27 },
            { header: `${alternatPhrase} Revision`, key: 'revision_number', width: 22 },
            { header: 'Company', key: 'company', width: 22 },
            { header: 'Uploaded By', key: 'uploaded_by', width: 22 },
            { header: 'Uploaded', key: 'uploaded', width: 22 },
            { header: 'Status', key: 'status', width: 22 },
            { header: 'Approved By', key: 'approved_by', width: 22 },
            { header: 'Approved', key: 'approved_at', width: 22 },
            { header: 'No. Times Briefed', key: 'times_briefed', width: 22 },
            { header: 'No. Operatives Briefed', key: 'operatives_briefed', width: 22},
            { header: 'Last briefed', key: 'last_briefed', width: 22},
            { header: 'Last Briefed by', key: 'last_briefed_by', width: 35},

        ];
    } else {
        worksheet.columns = [
            { header: `${alternatPhrase} #`, key: 's_no', width: 27 },
            { header: 'Title', key: 'title', width: 27 },
            { header: 'Company', key: 'company', width: 22 },
            { header: 'Uploaded By', key: 'uploaded_by', width: 22 },
            { header: 'Uploaded', key: 'uploaded', width: 22 },
            { header: 'Status', key: 'status', width: 22 },
            { header: 'No. Times Briefed', key: 'times_briefed', width: 22 },
            { header: 'No. Operatives Briefed', key: 'operatives_briefed', width: 22 },
            { header: 'Last briefed', key: 'last_briefed', width: 22},
            { header: 'Last Briefed by', key: 'last_briefed_by', width: 35},
        ];
    }

    let tz = getProjectTimezone(project);
    let rows = [];
    records.filter((r, index) => {
        if (feature == 'rams') {
            let last_briefed_by = r.last_briefing?.briefed_by_name || r.last_briefing?.briefed_by?.name;
            rows.push({
                "s_no": r.record_id,
                "reference_number":  r.reference_number,
                "title": r.briefing_title,
                "revision_number": r.revision_number,
                "company": (r.tagged_owner && r.tagged_owner.name) ? r.tagged_owner.name : '',
                "uploaded_by": getUserFullName(r.user_ref),
                "uploaded": r.createdAt ? moment(+r.createdAt).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : '',
                "status": (r.status == 1) ? 'Pending' : ((r.status == 2) ? ((r.is_available == 0) ? 'Not Available' : 'Accepted') : "Rejected"),
                "approved_by": getUserFullName(r.approved_by),
                "approved_at": r.approved_at ? moment(+r.approved_at).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : '',
                "times_briefed": r.briefed_count, // getBriefedCountByRegister(r, project.id),
                "operatives_briefed": r.attendees_count,
                "last_briefed":r.last_briefing ? moment(+r.last_briefing.briefed_at).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : '',
                "last_briefed_by": (last_briefed_by ? last_briefed_by : '')  +  (r.last_briefing?.induction_company ? ` (${r.last_briefing?.induction_company})` : '')
            });

        } else {
            let last_briefed_by = r.last_briefing?.briefed_by_name || r.last_briefing?.briefed_by?.name;
            rows.push({
                "s_no": (feature == 'tbt') ? r.talk_number : r.record_id,
                "company": (r.tagged_owner && r.tagged_owner.name) ? r.tagged_owner.name : '',
                "title": (feature == 'tbt') ? r.talk_title : r.briefing_title,
                "uploaded_by": getUserFullName(r.user_ref),
                "uploaded": r.createdAt ? moment(+r.createdAt).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : '',
                "status": (r.is_available == 0) ? 'Not Available' : 'Available',
                "times_briefed": r.briefed_count, // getBriefedCountByRegister(r, project.id),
                "operatives_briefed": r.attendees_count,
                "last_briefed":r.last_briefing ? moment(+r.last_briefing.briefed_at).tz(tz).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : '',
                "last_briefed_by": (last_briefed_by ? last_briefed_by : '')  +  (r.last_briefing?.induction_company ? ` (${r.last_briefing?.induction_company})` : '')
            });
        }
    });
    worksheet.addRows(rows);
    worksheet.getRow(1).eachCell(cell => {
        cell.font = {bold: true};
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style: 'medium'},
            left: {style: 'thin'},
            bottom: {style: 'thin'},
            right: {style: 'thin'}
        };
    });

    //for Status column
    const statusColumn = worksheet.getColumn(6);
    statusColumn.eachCell(function(cell, rowNumber) {

        //Text colour for 'Available' in green, 'Pending' in orange, 'Not Available' in black, Rejected in red.
        if(rowNumber > 1 && cell.value) {
            if (cell.value == 'Available' || cell.value == 'Approved') {
                cell.font = {
                    color: { argb: '00B050' },
                };
            } else if (cell.value == 'Pending') {
                cell.font = {
                    color: { argb: 'FFC000' },
                };
            } else if(cell.value == 'Declined') {
                cell.font = {
                    color: { argb: 'FF0000' },
                };
            }
        }
    });

    return workbook;
}

const _groupBy = require('lodash/groupBy');

const downloadGateBookingsXLSX = async(recordsByDate, gatesInfo, usersInfo, userEmployers, selectedDate, is_fors_compliant) => {

    sails.log.info(`creating workbook export for gate bookings`);
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet(`Gate Booking Report`, {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70}
    });
    worksheet.properties.defaultRowHeight = 25;
    worksheet.getColumn('A').width = 30;
    worksheet.getColumn('A').style = {alignment: {wrapText: true}};
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('B').style = {alignment: {wrapText: true}};
    worksheet.getColumn('C').width = 30;
    worksheet.getColumn('C').style = {alignment: {wrapText: true}};
    worksheet.getColumn('D').width = 30;
    worksheet.getColumn('D').style = {alignment: {wrapText: true}};
    worksheet.getColumn('E').width = 30;
    worksheet.getColumn('E').style = {alignment: {wrapText: true}};

    let rowIndex = 0;
    for(let bookingDate in recordsByDate) {
        worksheet.addRow([`Date`, `${bookingDate}`]);
        rowIndex += 1;
        worksheet.getRow(rowIndex).eachCell(cell => {
            cell.font = {bold: true, size: 14};
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {argb: 'bfbfbf'}
            };
            cell.border = {
                top: {style: 'thin'},
                bottom: {style: 'thin'},
            }
        });
        worksheet.addRow([]);
        rowIndex += 1;
        let records =  _groupBy(recordsByDate[bookingDate], (l) => l.gate_id);
        for(let gateId in records) {
            sails.log.info(`Processing gate's Id, ${gateId}`);
            let gateInfo = (gatesInfo || []).find(gate => (gate.id == gateId)) || {};
            sails.log.info('gateInfo ', gateInfo);
            let bookings = records[gateId];
            sails.log.info(`Bookings count, `, bookings.length);
            worksheet.addRow([`Gate`, `${gateInfo.gate_name}`]);
            rowIndex += 1;
            worksheet.getRow(rowIndex).eachCell(cell => {
                cell.font = {bold: true, size: 14};
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: {argb: 'bfbfbf'}
                };
                cell.border = {
                    top: {style: 'thin'},
                    bottom: {style: 'thin'},
                }
            });

            let maxMaterialColumns = 0;
            bookings.map(p=>{
                let currColumns = 0;
                currColumns = p.materials.length;
                maxMaterialColumns = (currColumns > maxMaterialColumns) ? currColumns : maxMaterialColumns;
            })

            let forsColumns = [];
            if(is_fors_compliant) {
                forsColumns = [
                    'FORS No',
                    'FORS Badge'
                ];
            }

            let colms = [
                ...[
                    'Time Slot',
                    'Booked By',
                    'Booking Details',
                    'Supplier',
                    'Supplier Contact Number',
                    'Haulage Company',
                    'Delivery Ref./No.',
                    'PO Number',
                    'Booking Ref',
                    'Supplier Contact Name',
                    'Supplier Contact Number'
                ],
                ...forsColumns,
                ...[
                    'Vehicle Reg',
                    'Vehicle Type',
                    'Vehicle Make & Model',
                    'Vehicle CO2 Emissions (g/Km)',
                    'Waste Collection Company',
                    'Driver Name',
                    'Driver Contact Number',
                    'Dispatch Post Code',
                    'Return Post Code',
                    'Distance to Site (km)',
                    'Distance from Site (km)',
                    'Total Distance Travel (km)',
                    'Total CO2 Emissions (kg)',
                    'Handling Equipment',
                    'Drop-off Post Code'
                ]
            ]
            if(maxMaterialColumns >= 1) {
                for(let i=1; i<=maxMaterialColumns; i++) {
                    colms.push("Material "+i);
                    colms.push("Quantity "+i);
                    colms.push("Unit "+i);
                }
            }

            worksheet.addRow(colms);
            for(let i= 1; i<= colms.length; i++) {
                worksheet.getColumn(i).width = 30;
                worksheet.getColumn(i).style = {alignment: {wrapText: true}};
            }

            rowIndex += 1;

            worksheet.getRow(rowIndex).eachCell(cell => {
                cell.font = {bold: true, size: 14};
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: {argb: 'd9d9d9'}
                };
                cell.border = {
                    top: {style: 'thin'},
                    left: {style: 'thin'},
                    right: {style: 'thin'},
                    bottom: {style: 'thin'}
                }
            });
            (bookings || []).map(booking => {
                let submittedByUser = (usersInfo || []).find(user => (user.id == booking.user_id));
                let userFullname = getUserFullName(submittedByUser);
                let userEmployer = (userEmployers || []).find(ue => (ue.user_ref == booking.user_id));
                let userEmployerName = (userEmployer && userEmployer.employer) ? userEmployer.employer: 'N/A';
                let materialsData = [];
                for(let i=0; i< booking.materials.length; i++) {
                    materialsData.push(booking.materials[i].material, booking.materials[i].quantity, (booking.materials[i].unit || ''))
                }
                let dispatch_distance_travelled = (booking.dispatch_distance_matrix && booking.dispatch_distance_matrix.distance && booking.dispatch_distance_matrix.distance.value) ? (+booking.dispatch_distance_matrix.distance.value / 1000) : 0;
                let return_distance_travelled = (booking.return_distance_matrix && booking.return_distance_matrix.distance && booking.return_distance_matrix.distance.value) ? (+booking.return_distance_matrix.distance.value / 1000) : 0;

                let total_co2_kg = null;
                if (booking.co2_emission_gm) {
                    let a = (dispatch_distance_travelled) ? Number((dispatch_distance_travelled * (+booking.co2_emission_gm)) / 1000) : null;
                    let b = (return_distance_travelled) ? Number((return_distance_travelled * (+booking.co2_emission_gm)) / 1000) : null;

                    total_co2_kg = ((a || 0) + (b || 0));
                }

                let forsColumnData = [];
                if(is_fors_compliant) {
                    forsColumnData = [
                        booking.fors_no? `${booking.fors_no}` : '',
                        booking.fors_no_badge? `${booking.fors_no_badge}` : '',
                    ]
                }

                let rowColumnData = [
                    ...[
                        `${booking.slots_to_display}`,
                        `${userFullname} (${userEmployerName})`,
                        booking.booking_details? `${booking.booking_details}` : '',
                        booking.supplier? `${booking.supplier}` : '',
                        booking.contact_number? `${booking.contact_number}` : '',
                        booking.haulage_company? `${booking.haulage_company}` : '',
                        `${booking.deliveryRefNo}`,
                        `${booking.poNumber}`,
                        booking.booking_ref? `${booking.booking_ref}` : '',
                        booking.contact_name? `${booking.contact_name}` : '',
                        booking.contact_number? `${booking.contact_number}` : '',
                    ],
                    ...forsColumnData,
                    ...[
                        booking.vehicle_reg? `${booking.vehicle_reg}` : '',
                        booking.vehicle_type? `${booking.vehicle_type}` : '',
                        booking.vehicle_make_model? `${booking.vehicle_make_model}` : '',
                        booking.co2_emission_gm? `${booking.co2_emission_gm}` : '',
                        booking.waste_collection_company? `${booking.waste_collection_company}` : '',
                        booking.driver_name? `${booking.driver_name}` : '',
                        booking.driver_number? `${booking.driver_number}` : '',
                        booking.dispatch_post_code? `${booking.dispatch_post_code}` : '',
                        booking.return_postcode? `${booking.return_postcode}` : '',
                        `${(dispatch_distance_travelled || null)}`,
                        `${(return_distance_travelled || null)}`,
                        `${(( dispatch_distance_travelled + return_distance_travelled ) || null)}`,
                        `${total_co2_kg}`,
                        booking.handling_equipment? `${booking.handling_equipment}` : '',
                        booking.drop_off_address? `${booking.drop_off_address}` : '',
                        ...materialsData
                    ]
                ];

                worksheet.addRow(rowColumnData);
                rowIndex += 1;
            });

            worksheet.addRow([]);
            rowIndex += 1;
        }
    }


    return workbook;
};


const showDurationInHours = (duration_in_sec) => {
    if(duration_in_sec && !isNaN(+duration_in_sec)){
        return PdfUtil.showDurationAsHours(duration_in_sec);
    }
    return duration_in_sec;
}

const getPercentage = (record, activity) => {
    if (record.progress[activity.key] ) {
        return record.progress[activity.key] + '%'
    }
}

const getTotalHours = (record, activity) => {
    let total_hours = 0;
    let hours;
    record.operatives.forEach(operative => {
        hours = operative.hours[activity.key];
        if(hours) {
            total_hours += Number(hours);
        }
    });
    if(total_hours > 0) {
        return showDurationInHours(total_hours);
    }

}

const getPlantHours = (record, activity) => {
    let total = 0;
    let hours;
    record.plants.forEach(p => {
        hours = p.hours[activity.key];
        if(hours) {
            total += Number(hours);
        }
    });
    if(total > 0) {
        return showDurationInHours(total);
    }
}

const timeConvert = (numOfHours) => {
    let mins = numOfHours * 60;
    let hours = (mins / 60);
    let rhours = Math.floor(hours);
    let minutes = (hours - rhours) * 60;
    let rminutes = Math.round(minutes);
    return rhours + " hour(s) " + rminutes + " minute(s)";
}

const getFatigueType = (row) => {
    if(row.category === 'Fatigue_PeriodOfDuty') {
        return 'Consecutive days in a 14 day period';
    } else if(row.category === 'Fatigue_ShiftGap') {
        return 'Minimum rest period';
    } else if(row.category === 'Fatigue_WeeklyHours') {
        return 'Site Time in a 7 day period';
    } else if(row.category === 'Fatigue_DailyTotalHours') {
        return 'Daily Site & Travel Time';
    } else if(row.category === 'Fatigue_DailySiteHours') {
        return 'Daily Site Time';
    }
}

const getFatigueDeetails = (row) => {
    if(row.category === 'Fatigue_PeriodOfDuty') {
        return 'Day ' + row.details.spentDays + ' ( ' + moment.unix(row.event_timestamp).format('DD-MM-YYYY') + ' )';
    } else if(row.category === 'Fatigue_ShiftGap') {
        return timeConvert(row.details.shiftGap);
    } else if(row.category === 'Fatigue_WeeklyHours') {
        return timeConvert(row.details.totalHours);
    } else if(row.category === 'Fatigue_DailyTotalHours') {
        return timeConvert(row.details.totalHours);
    } else if(row.category === 'Fatigue_DailySiteHours') {
        return timeConvert(row.details.siteHours);
    }
}

const getFatigueComments = (row) => {
    let commentsData = '';
    let str = '';
    for(const r of row.comments) {
        str = '';
        str = str.concat(r.name , ' ' , '(' , r.timestamp? moment.unix(r.timestamp).format('DD-MM-YYYY'): '' ,') : ' , r.note , ' \n');
        commentsData = commentsData.concat(str);
    }
    return commentsData;
}

const getDownloadDailyReportWorkbook = async (records) => {
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('Daily Activity Report', {
        properties: {
            defaultRowHeight: 20
        }
    });

    worksheet.columns = [
        { header: 'Shift Date', key: 'shift_date', width: 27 },
        { header: 'Shift Time', key: 'shift_time', width: 27 },
        { header: 'Submitted By', key: 'submitted_by', width: 27 },
        { header: 'Activity', key: 'activity', width: 27 },
        { header: '% Complete', key: 'complete', width: 22 },
        { header: 'Workforce Hours', key: 'workforce_hours', width: 22 },
        { header: 'Plant Hours', key: 'plant_hours', width: 22 },
    ];
    let rows = [];
    records.filter(r => {
        for(const activity of r.activities) {
            rows.push({
                "shift_date": moment(r.shift_date, 'YYYY-MM-DD').format('DD-MM-YYYY'),
                "shift_time": r.shift_time.from + ' - ' + r.shift_time.to,
                "submitted_by": r.submitted_by,
                "activity": activity.title,
                "complete": getPercentage(r, activity),
                "workforce_hours": getTotalHours(r, activity),
                "plant_hours": getPlantHours(r, activity),
            });
        }
    });
    worksheet.addRows(rows);
    worksheet.getRow(1).eachCell(cell => {
        cell.font = {bold: true};
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style: 'medium'},
            left: {style: 'thin'},
            bottom: {style: 'thin'},
            right: {style: 'thin'}
        }
    });

    return workbook;
}

const getWorkforceHoursReportWorkbook = async (records) => {
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('Daily Workforce Hours', {
        properties: {
            defaultRowHeight: 20
        }
    });

    let dates = (records || []).reduce((arr, record) => {
        arr.push(moment(record.shift_date, 'YYYY-MM-DD').format('DD/MM/YYYY'));
        return arr;
    },[]);
    dates = _uniq(dates);

    /*TITLE*/
    let lastColumnName = String.fromCharCode(2 + dates.length + 1 + 64);
    worksheet.mergeCells('C1', `${lastColumnName}1`);
    worksheet.getCell('C1').value = 'Hours worked (Accumulation of all daily activity reports)';
    worksheet.getCell('C1').alignment = { vertical: 'middle', horizontal: 'center' };


    worksheet.getRow(2).values = ['Name', 'Job Role', ...dates , 'Total'];
    let columns = [
        { key: 'name', width: 27 },
        { key: 'trade', width: 27 },
    ];
    dates.map(date => {
        let item = { key: date, width: 27 };
        columns.push(item);
    });
    columns.push({ key: 'total', width: 22 });

    worksheet.columns = columns;

    let rows = [];
    records.filter(r => {
        let recordDate = moment(r.shift_date).format('DD/MM/YYYY');
        for(const operative of r.operatives) {
            let existingItem = rows.find(data => data.name == operative.name && data.trade == operative.trade);
            let existingItemIndex = rows.findIndex(data => data.name == operative.name && data.trade == operative.trade);

            let hoursArr = Object.values(operative.hours);
            let hoursSum = hoursArr.reduce(function(a, b){
                return Number(a) + Number(b);
            }, 0);

            if (existingItem) {
                existingItem[recordDate] = (existingItem[recordDate]) ?  +existingItem[recordDate] + hoursSum : hoursSum;
                existingItem.total = +existingItem.total + +hoursSum;

                rows[existingItemIndex] = existingItem;
            } else {
                let item = {
                    "name": operative.name,
                    "trade": operative.trade
                };
                item[recordDate] = hoursSum;
                item.total = hoursSum;

                rows.push(item);
            }
        }
    });

    for (let index in dates) {
        rows = rows.map(row => {
            row.total = (row.total && !isNaN(row.total)) ? showDurationInHours(row.total) : row.total;
            row[dates[index]] = (row[dates[index]] && !isNaN(row[dates[index]])) ? showDurationInHours(row[dates[index]]) : row[dates[index]];
            return row;
        });
    }

    //sails.log.info(JSON.stringify(rows));

    worksheet.addRows(rows);
    worksheet.getRow(1).eachCell(cell => {
        cell.font = {bold: true};
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'b9b6b6'}
        };
        cell.border = {
            top: {style: 'thin'},
            left: {style: 'medium'},
            bottom: {style: 'medium'},
            right: {style: 'medium'}
        }
    });

    worksheet.getRow(2).eachCell(cell => {
        cell.font = {bold: true};
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style: 'medium'},
            left: {style: 'thin'},
            bottom: {style: 'thin'},
            right: {style: 'thin'}
        }
    });

    return workbook;
}

const getWorkforceHoursComparisonReportWorkbook = async (records, timeLogs) => {
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('Workforce Actual vs Reported Hours Report', {
        properties: {
            defaultRowHeight: 20
        }
    });

    let dates = (records || []).reduce((arr, record) => {
        arr.push(moment(record.shift_date, 'YYYY-MM-DD').format('DD/MM/YYYY'));
        return arr;
    },[]);
    dates = _uniq(dates);

    let lastColumnName = String.fromCharCode(2 + dates.length + 64);
    worksheet.mergeCells('C1', `${lastColumnName}1`);
    worksheet.getCell('C1').value = 'Comparison between actual clocked hours vs hours in daily activity report';
    worksheet.getCell('C1').alignment = { vertical: 'middle', horizontal: 'center' };
    worksheet.getRow(2).values = ['Name', 'Job Role', ...dates];
    let columns = [
        { key: 'name', width: 27 },
        { key: 'trade', width: 27 },
    ];
    dates.map(date => {
        let item = { key: date, width: 27 };
        columns.push(item);
    });

    worksheet.columns = columns;

    let rows = [];
    records.filter(r => {
        let recordDate = moment(r.shift_date).format('DD/MM/YYYY');
        for(const operative of r.operatives) {
            let existingItem = rows.find(data => data.name == operative.name && data.trade == operative.trade);
            let existingItemIndex = rows.findIndex(data => data.name == operative.name && data.trade == operative.trade);
            let actualHours = null;
            if(operative.user_ref) {
                let a = timeLogs.find(t => t.user_ref === operative.user_ref && moment(t.day_of_yr).format(dbDateFormat_YYYY_MM_DD) === moment(r.shift_date).format('YYYY-MM-DD'));
                if(a && a.total_in_sec) {
                    actualHours = Number(a.total_in_sec);
                }
                if(a && a.adjustment_minutes) {
                    actualHours = actualHours + (a.adjustment_minutes*60);
                }
            }
            let hoursArr = Object.values(operative.hours);
            let hoursSum = hoursArr.reduce(function(a, b){
                return Number(a) + Number(b);
            }, 0);

            if (existingItem && actualHours) {
                existingItem[recordDate] =  existingItem[recordDate] ? existingItem[recordDate] + hoursSum : (hoursSum - actualHours);
                rows[existingItemIndex] = existingItem;
            } else {
                let item = {
                    "name": operative.name,
                    "trade": operative.trade
                };
                if(actualHours) {
                    item[recordDate] = hoursSum - actualHours;
                }
                rows.push(item);
            }
        }
    });

    let i = 3;
    worksheet.addRows(rows);
    for (let index in dates) {
        const dobCol = worksheet.getColumn(i);
        dobCol.eachCell(function(cell, rowNumber) {
            if(cell._row._number > 2 && rows[rowNumber-3][dates[index]] && !isNaN(rows[rowNumber-3][dates[index]])) {
                if(rows[rowNumber-3][dates[index]] > 0) {
                    cell.font = {
                        color: {argb: '28a745'}
                    };
                    cell.value = '+' + showDurationInHours(rows[rowNumber-3][dates[index]])
                }else if(rows[rowNumber-3][dates[index]] === 0) {
                    cell.font = {
                        color: {argb: '28a745'}
                    };
                    cell.value = showDurationInHours(rows[rowNumber-3][dates[index]])
                } else {
                    cell.font = {
                        color: {argb: 'df5966'}
                    };
                    cell.value = '-' + showDurationInHours(Math.abs(rows[rowNumber-3][dates[index]]));
                }
            }
        });
        i++;
    }

    worksheet.getRow(1).eachCell(cell => {
        cell.font = {bold: true};
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'b9b6b6'}
        };
        cell.border = {
            top: {style: 'thin'},
            left: {style: 'medium'},
            bottom: {style: 'medium'},
            right: {style: 'medium'}
        }
    });

    worksheet.getRow(2).eachCell(cell => {
        cell.font = {bold: true};
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style: 'medium'},
            left: {style: 'thin'},
            bottom: {style: 'thin'},
            right: {style: 'thin'}
        }
    });

    return workbook;
}

const getActivityHoursBreakdownWorkbook = async (records, projectInfo) => {
    let workbook = new ExcelJS.Workbook();
    let pivotWorksheet = workbook.addWorksheet('Pivot Tables', {
        properties: {
            defaultRowHeight: 20
        }
    });
    let worksheet = workbook.addWorksheet('Data', {
        properties: {
            defaultRowHeight: 20
        }
    });

    let tz = getProjectTimezone(projectInfo);
    worksheet.columns = [
        { header: 'Shift Date', key: 'shift_date', width: 27 },
        { header: 'Shift Time', key: 'shift_time', width: 27 },
        { header: 'Submitted By', key: 'submitted_by', width: 27 },
        { header: 'Activity', key: 'activity', width: 27 },
        { header: 'Location', key: 'location', width: 27 },
        { header: '% Complete', key: 'complete', width: 22 },
        { header: 'Workforce Hours', key: 'workforce_time', width: 22 },
        { header: 'Plant Hours', key: 'plant_time', width: 22 },
        { header: 'Name', key: 'name', width: 22 },
        { header: 'Job Role', key: 'job_role', width: 22 },
        { header: 'Operator', key: 'operator', width: 22 },
        { header: 'Plant', key: 'plant', width: 22 },
        { header: 'Work Hours', key: 'workforce_hours', width: 22 },
        { header: 'Plant Hours', key: 'plant_hours', width: 22 },
    ];
    let rows = [];
    records.filter(r => {
        for(const activity of r.activities) {
            let activityData = {
                "shift_date": momentTz(r.shift_date, 'YYYY-MM-DD').tz(tz).format('DD-MM-YYYY'),
                "shift_time": r.shift_time.from + ' - ' + r.shift_time.to,
                "submitted_by": r.submitted_by,
                "activity": activity.title,
                "complete": getPercentage(r, activity),
                "location": r.activities_location[activity.key] || '',
            };
            for(const operative of r.operatives) {
                let activity_hours = operative.hours[activity.key];
                let operativeInfo = {
                    "workforce_time": showDurationInHours(activity_hours),
                    "name": operative.name,
                    "job_role": operative.trade,
                    "workforce_hours": activity_hours? Number((activity_hours/3600).toFixed(2)): null,
                    "plant_hours": null,
                };
                rows.push({...activityData, ...operativeInfo});
            }
            for(const plant of r.plants) {
                let plant_hours = plant.hours[activity.key];
                let operativeInfo = {
                    "operator": plant.operator,
                    "plant_time": showDurationInHours(plant_hours),
                    "workforce_hours": null,
                    "plant_hours": plant_hours? Number((plant_hours/3600).toFixed(2)): null,
                    "plant": plant.plant
                };
                rows.push({...activityData, ...operativeInfo});
            }
        }
    });

    worksheet.addRows(rows);
    worksheet.getRow(1).eachCell(cell => {
        cell.font = {bold: true};
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style: 'medium'},
            left: {style: 'thin'},
            bottom: {style: 'thin'},
            right: {style: 'thin'}
        }
    });

    pivotWorksheet.addRow([]);
    pivotWorksheet.columns = [
        { header: '',},
        { header: '', width: 23 },
        { header: '', width: 18 },
        { header: '', width: 18 },
        { header: '' },
        { header: '', width: 23 },
        { header: '', width: 18 },
        { header: '', width: 18 },
        { header: '' },
        { header: '', width: 23 },
        { header: '', width: 18 },
        { header: '', width: 18 },
        { header: '' },
        { header: '', width: 23 },
        { header: '', width: 18 },
        { header: '', width: 18 },
        { header: '' },
        { header: '', width: 23 },
        { header: '', width: 18 },
        { header: '', width: 18 },
        { header: '' },
        { header: '', width: 23 },
        { header: '', width: 18 },
        { header: '', width: 18 },
    ];
    pivotWorksheet.addRow(['', 'Workforce Hours / Job Role', '', '', '', 'Plant Hours / Operator', '', '', '', 'Workforce Hours / Activity', '', '', '', 'Plant Hours / Activity', '', '', '', 'Location / Activity', '', '', '', 'Location / Plant']);
    pivotWorksheet.addRow([]);
    console.log(rows[1]);
    let jobRoleHours = getPivotResult(rows, "job_role", "name", "workforce_hours");
    let jobRoleHoursTableData = getTable(jobRoleHours);
    let jobRoleTable = getTableStruct("jobRoleTable", 'B4', getColumnsArray('Name', 'Job Role','Sum of Work hours'), jobRoleHoursTableData);
    pivotWorksheet.addTable(jobRoleTable);
    _createOuterBorder(pivotWorksheet, { row: 4, col: 2 },{ row: 4+jobRoleHoursTableData.length, col: 4 }, 'thin');

    let plantOperatorHours = getPivotResult(rows, "plant", "operator", "plant_hours");
    let plantOperatorHoursTableData = getTable(plantOperatorHours);
    let plantOperatorTable = getTableStruct("jobRoleTable", 'F4', getColumnsArray('Plant', 'Operator','Sum of Plant hours'), plantOperatorHoursTableData);
    pivotWorksheet.addTable(plantOperatorTable);
    _createOuterBorder(pivotWorksheet, { row: 4, col: 6 }, { row: 4+plantOperatorHoursTableData.length, col: 8 }, 'thin');

    let activityHours = getPivotResult(rows, "activity", "name", "workforce_hours");
    let activityHoursTableData = getTable(activityHours);
    let activityHoursTable = getTableStruct("jobRoleTable", 'J4', getColumnsArray('Activity', 'Name','Sum of Work hours'), activityHoursTableData);
    pivotWorksheet.addTable(activityHoursTable);
    _createOuterBorder(pivotWorksheet, { row: 4, col: 10 }, { row: 4+activityHoursTableData.length, col: 12 }, 'thin'
    );

    let plantActivityHours = getPivotResult(rows, "activity", "plant", "plant_hours");
    let plantActivityHoursTableData = getTable(plantActivityHours);
    let plantActivityHoursTable = getTableStruct("jobRoleTable", 'N4', getColumnsArray('Activity', 'Name','Sum of Plant hours'), plantActivityHoursTableData);
    pivotWorksheet.addTable(plantActivityHoursTable);
    _createOuterBorder(pivotWorksheet, { row: 4, col: 14 }, { row: 4+plantActivityHoursTableData.length, col: 16 }, 'thin'
    );

    let locationActivityHours = getPivotResult(rows, "location", "activity", "workforce_hours");
    let locationActivityHoursTableData = getTable(locationActivityHours);
    let locationActivityHoursTable = getTableStruct("jobRoleTable", 'R4', getColumnsArray('Location', 'Activity','Sum of Work hours'), locationActivityHoursTableData);
    pivotWorksheet.addTable(locationActivityHoursTable);
    _createOuterBorder(pivotWorksheet, { row: 4, col: 18 }, { row: 4+locationActivityHoursTableData.length, col: 20 }, 'thin'
    );

    let locationPlantHours = getPivotResult(rows, "location", "activity", "plant_hours");
    let locationPlantHoursTableData = getTable(locationPlantHours);
    let locationPlantHoursTable = getTableStruct("jobRoleTable", 'V4', getColumnsArray('Location', 'Activity','Sum of Plant hours'), locationPlantHoursTableData);
    pivotWorksheet.addTable(locationPlantHoursTable);
    _createOuterBorder(pivotWorksheet,
        { row: 4, col: 22 }, { row: 4+locationPlantHoursTableData.length, col: 24 }, 'thin');

    pivotWorksheet.getRow(2).eachCell(cell => {
        cell.font = {bold: true};
    });
    pivotWorksheet.getRow(4).eachCell(cell => {
        cell.font = {bold: true};
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style: 'thin'},
            left: {style: 'thin'},
            bottom: {style: 'thin'},
            right: {style: 'thin'}
        }
    });
    return workbook;
}

const getColumnsArray = (colA, colB, colC) => {
    return [
        {name: colA, filterButton: true},
        {name: colB, filterButton: true},
        {name: colC, filterButton: true},
    ]
};

const getTableStruct = (name, ref, columns, rows) => {
    let tableObj = {
        name: name,
        ref: ref,
        headerRow: true,
        style: {
          theme: 'TableStyleDark11',
        },
        columns: columns,
        rows: rows,
    };
    return tableObj;
}

const getTable = (data) => {
    let resultArray = [];
    let pivotData = data.pivot;
    let sumsData = data.rowSums;
    let keysArray = Object.keys(pivotData);
    let sum = 0;
    for(let key of keysArray) {
        resultArray.push([key, null, sumsData[key]]);
        Object.keys(pivotData[key]).forEach(item => {
            resultArray.push([null, item, pivotData[key][item]]);
        });
        sum += sumsData[key];
    }
    resultArray.push(['Total Result', null, sum]);
    return resultArray;
}

const getPivotResult = (data, rowKey, columnKey, valueKey) => {
    const pivot = {};
    const rowSums = {};
    data.forEach(item => {
        if(item[valueKey]) {
            const rowValue = item[rowKey];
            const columnValue = item[columnKey];
            const value = item[valueKey];
            pivot[rowValue] = pivot[rowValue] || {};
            pivot[rowValue][columnValue] = pivot[rowValue][columnValue] || 0;
            pivot[rowValue][columnValue] += value;
            rowSums[rowValue] = rowSums[rowValue] || 0;
            rowSums[rowValue] += value;
        }
    });
    return { pivot, rowSums };
}

const getFatigueReportWorkbook = async(records) => {
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('Fatigue Records', {

    });
    let stylefilter = {style: { alignment: { vertical: 'middle', horizontal: 'left', wrapText: true}}};
    worksheet.columns = [
        { header: 'Date of breach', key: 'date', width: 15, ...stylefilter },
        { header: 'Name', key: 'name', width: 20 ,...stylefilter},
        { header: 'Employer', key: 'employer', width: 20, ...stylefilter },
        { header: 'Type of breach', key: 'type', width: 35, ...stylefilter },
        { header: 'Details', key: 'details', width: 30, ...stylefilter },
        { header: 'Closeout Details', key: 'comments', width: 40, style: { alignment: { vertical: 'middle', horizontal: 'left', wrapText: true } }},
        { header: 'Attachment', key: 'attachment', width: 40, ...stylefilter },
    ];
    let rows = [];
    records.filter(r => {
        rows.push({
            "date": r.event_timestamp? moment.unix(r.event_timestamp).format('DD-MM-YYYY'): '',
            "name": getUserFullName(r.user_ref),
            "employer": r.employer ? r.employer : '',
            "type": getFatigueType(r),
            "details": getFatigueDeetails(r),
            "comments": r.closed_out_details,
            "attachment": '',
        });
    });
    worksheet.addRows(rows);
    worksheet.getRow(1).eachCell(cell => {
        cell.font = {bold: true};
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style: 'medium'},
            left: {style: 'thin'},
            bottom: {style: 'thin'},
            right: {style: 'thin'}
        }
    });
    let hyperLinkCell = null;
    for(let k=0; k<records.length; k++) {
        if(records[k].closeout_file_id) {
            hyperLinkCell = worksheet.getCell(`G${k+2}`);
            hyperLinkCell.value = {
                text: records[k].closeout_file_id.name,
                hyperlink: records[k].closeout_file_id.file_url,
                tooltip: records[k].closeout_file_id.file_url
            };
            hyperLinkCell.font = { underline: true, color: {argb: '0099FF'} };
        }
    }

    return workbook;

}

const getInspectionParticipants = async (participants, companyInfo, report_from, report_to) => {
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('Inspection Participants', {
        properties: {
            defaultRowHeight: 20
        }
    });

    /*TITLE*/
    let columnHeadings = ['First Name', 'Last Name', 'First Inspection Date', 'Last Inspection Date', 'Total Inspections Participated', 'Total Number of projects', 'Best Inspection Rating', 'Worst Inspection Rating', 'Average Inspection Rating'];
    let lastColumnName = String.fromCharCode(columnHeadings.length + 64);
    worksheet.mergeCells('A1', `${lastColumnName}1`);
    worksheet.getCell('A1').value = `Inspection Participants Report - ${companyInfo.name} - (${report_from} - ${report_to})`;
    worksheet.getCell('A1').alignment = { vertical: 'middle' };

    worksheet.getRow(2).values = columnHeadings;
    worksheet.getRow(2).height = 42;

    let columns = [
        { key: 'first_name', width: 20 },
        { key: 'last_name', width: 20 },
        { key: 'first_inspection_date', width: 15 },
        { key: 'last_inspection_date', width: 15 },
        { key: 'total_inspections_participated', width: 15 },
        { key: 'total_number_of_projects', width: 15 },
        { key: 'best_inspection_rating', width: 20 },
        { key: 'worst_inspection_rating', width: 20 },
        { key: 'average_inspection_rating', width: 15 }
    ];

    worksheet.columns = columns;
    worksheet.addRows(participants);

    worksheet.getRow(2).eachCell(cell => {
        cell.font = {
            bold: true,
            size: 10,
        };
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style: 'medium'},
            left: {style: 'thin'},
            bottom: {style: 'thin'},
            right: {style: 'thin'}
        };
        cell.alignment = {
            horizontal: 'center',
            vertical: 'middle',
            wrapText: true
        }
    });

    worksheet.eachRow(function(row, rowNumber) {
        if(rowNumber > 2) {
            row.font = { size: 8 };
        }
    });

    const avgRatingCol = worksheet.getColumn(columnHeadings.length);
    avgRatingCol.eachCell(function(cell, rowNumber) {
        if(rowNumber > 2 && cell.value) {
            let valArr = cell.value.split('%');
            let percent = Math.round(valArr[0]);
            if (percent < 50) {
                cell.font = {
                    color: { argb: 'FF0000' },
                    size: 8
                };
            } else if (percent >= 50 && percent <= 75) {
                cell.font = {
                    color: { argb: 'FFC000' },
                    size: 8
                };
            } else if(percent > 75) {
                cell.font = {
                    color: { argb: '00B050' },
                    size: 8
                };
            }
        }
    });

    return workbook;
}

const cellBoldStyle = function(cell) {
    cell.font = {bold: true};
}

const rowHeadingStyle = function(cell) {
    cell.font = {bold: true};
    cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'd9d9d9'}
    };
    cell.border = {
        top: {style: 'medium'},
        left: {style: 'thin'},
        bottom: {style: 'thin'},
        right: {style: 'thin'}
    }
};

const getProjectEmissionsReportWorkbook = async (projectData) => {
    const meterToMile = 0.*********;
    let workbook = new ExcelJS.Workbook();
    let carbonWorksheet = workbook.addWorksheet('Carbon Footprint', {});
    let mileageWorksheet = workbook.addWorksheet('Mileage', {});
    let companies = Object.keys(projectData);
    let totalEntries = projectData[companies[0]].length;
    let columnHeadings = ['',];
    let styleFilter = {alignment: {  vertical: 'middle',horizontal: 'center',  wrapText: true}};
    let columns = [
        { key: "company", width:20, ...styleFilter},

    ];
    for(let i=0; i<totalEntries; i++) {
        columns.push({ key: "date"+i, width:20, ...styleFilter});
        columnHeadings.push(moment(projectData[companies[0]][i].day_of_yr, 'YYYY-MM-DD').format('DD-MM-YYYY'))
    }

    columnHeadings.push('Total');
    columns.push({ key: "total", width:30, ...styleFilter})
    carbonWorksheet.columns = columns;
    mileageWorksheet.columns = columns;
    carbonWorksheet.getRow(2).values = columnHeadings;
    mileageWorksheet.getRow(2).values = columnHeadings;
    carbonWorksheet.mergeCells('A1', `A2`);
    carbonWorksheet.getCell('A1').value = `Company`;
    carbonWorksheet.getCell('B1').value = `Workers Travel Carbon Footprint (kg)`;
    carbonWorksheet.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };

    mileageWorksheet.mergeCells('A1', `A2`);
    mileageWorksheet.getCell('A1').value = `Company`;
    mileageWorksheet.getCell('B1').value = `Workers Mileage Travelled (miles)`;
    mileageWorksheet.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };

    let lastColNumber = totalEntries + 2;
    let endColumn = carbonWorksheet.getRow(1).getCell(carbonWorksheet.getColumn(lastColNumber)._key);
    let colToMerge = 'B1:'+endColumn._address;
    mileageWorksheet.mergeCells(colToMerge);
    carbonWorksheet.mergeCells(colToMerge);
    let rowsDataCarbonFootprint = [];
    let mileageRows = [];
    let ind = null;
    let rowDataCarbonFootprint = {};
    let rowDataMileage = {};
    let carbonSheetOverallRow = ['Overall', ...Array(totalEntries).fill(undefined)];
    let mileageSheetOverallRow = ['Overall', ...Array(totalEntries).fill(undefined)];
    for(let company of companies) {
        rowDataCarbonFootprint = {'company': company};
        rowDataMileage = {'company': company};
        for(let i=0; i<projectData[company].length; i++) {
            ind = `date${i}`;
            rowDataCarbonFootprint[ind] = projectData[company][i].daily_sum;
            rowDataMileage[ind] = projectData[company][i].daily_distance * meterToMile;
            if(carbonSheetOverallRow[i+1]) {
                carbonSheetOverallRow[i+1] += projectData[company][i].daily_sum;
            } else {
                carbonSheetOverallRow[i+1] = projectData[company][i].daily_sum;
            }
            if(mileageSheetOverallRow[i+1]) {
                mileageSheetOverallRow[i+1] += projectData[company][i].daily_distance * meterToMile;
            } else {
                mileageSheetOverallRow[i+1] = projectData[company][i].daily_distance * meterToMile;
            }
        }
        rowDataCarbonFootprint['total'] = projectData[company][projectData[company].length-1].incremental_sum;
        rowsDataCarbonFootprint.push(rowDataCarbonFootprint);
        rowDataMileage['total'] = projectData[company][projectData[company].length-1].incremental_distance * meterToMile;
        mileageRows.push(rowDataMileage);
        if(carbonSheetOverallRow[columnHeadings.length-1]) {
            carbonSheetOverallRow[columnHeadings.length-1] += projectData[company][projectData[company].length-1].incremental_sum;
        } else {
            carbonSheetOverallRow[columnHeadings.length-1] = projectData[company][projectData[company].length-1].incremental_sum;
        }
        if(mileageSheetOverallRow[columnHeadings.length-1]) {
            mileageSheetOverallRow[columnHeadings.length-1] += projectData[company][projectData[company].length-1].incremental_distance * meterToMile;
        } else {
            mileageSheetOverallRow[columnHeadings.length-1] = projectData[company][projectData[company].length-1].incremental_distance * meterToMile;
        }

    }
    carbonWorksheet.getRow(3).values = carbonSheetOverallRow;
    mileageWorksheet.getRow(3).values = mileageSheetOverallRow;
    carbonWorksheet.addRows(rowsDataCarbonFootprint);
    mileageWorksheet.addRows(mileageRows);
    carbonWorksheet.getRow(1).eachCell(cell => rowHeadingStyle(cell));
    carbonWorksheet.getRow(2).eachCell(cell => rowHeadingStyle(cell));
    mileageWorksheet.getRow(1).eachCell(cell => rowHeadingStyle(cell));
    mileageWorksheet.getRow(2).eachCell(cell => rowHeadingStyle(cell));
    mileageWorksheet.getRow(3).eachCell(cell => cellBoldStyle(cell));
    carbonWorksheet.getRow(3).eachCell(cell =>cellBoldStyle(cell));
    return workbook;
};

const assetRegisterReport = async (liveAssetRecords, archivedAssetRecords, assetType, projectInfo) => {
    sails.log.info('creating workbook export', (new Date()));

    let workbook = new ExcelJS.Workbook();
    prepareAssetRecordsWorkSheet(workbook, 'On-site', liveAssetRecords, assetType, projectInfo);
    prepareAssetRecordsWorkSheet(workbook, 'Archived', archivedAssetRecords, assetType, projectInfo);
    return workbook;
};

const {

    ASSET_TYPES,
} = sails.config.constants;
const prepareAssetRecordsWorkSheet = function (workbook, workSheetName, assetRecords, assetType, projectInfo) {
    let worksheet = workbook.addWorksheet(getWorksheetName(workSheetName), {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70}
    });

    let timezone = (projectInfo && projectInfo.custom_field && projectInfo.custom_field.timezone) || fall_back_timezone;

    let columns = [];
    let records = [];
    if (assetType == 'vehicle') {
        columns = [
            {header: "Vehicle ID", key: "Vehicle ID", width: 20},
            {header: "Type of Vehicle", key: "Type of Vehicle", width: 25},
            {header: "Reg./Serial Number", key: "Reg./Serial Number", width: 25},
            {header: "Owner(s)", key: "Owner", width: 40},
            {header: "Arrived on Site", key: "Arrived on Site", width: 20},
            {header: "Thorough Examination Expiry", key: "Thorough Examination Expiry", width: 20},
            {header: "Service Expiry", key: "Service Expiry", width: 20},
            {header: "MOT Expiry", key: "MOT Expiry", width: 20},
            {header: "Plant Hours", key: "plant_hours", width: 20},
            {header: "Last Daily Inspection", key: "Last Daily Inspection", width: 20},
            {header: "Open Faults", key: "Open Faults", width: 20},
        ];
        if(workSheetName === 'Archived'){
            columns.push({header: "Archive Date", key: "Archive Date", width: 20},{header: "Archive By", key: "Archive By", width: 40});
        }

        assetRecords.map(r => {
            let archived_logs = null;
            if(r.is_archived){
                archived_logs = r.activity_logs.pop();
            }
            let result = {
                "Vehicle ID": r.vehicle_id,
                "Type of Vehicle": r.alternate_phrase,
                "Reg./Serial Number": r.serial_number,
                "Owner": (r.tagged_owner.length) ? (r.tagged_owner || []).reduce((arr, owner) => { arr.push(owner.name); return arr;  },[]).join(',') : "",
                "Arrived on Site": r.arrived_at ? formatTimeInTimezone(+r.arrived_at, 'DD-MMM-YY', timezone) : null,
                "Thorough Examination Expiry": r.examination_cert_expiry_date ? formatTimeInTimezone(+r.examination_cert_expiry_date, 'DD-MMM-YY', timezone) : null,
                "Service Expiry": r.service_expiry_date ? formatTimeInTimezone(+r.service_expiry_date, 'DD-MMM-YY', timezone) : null,
                "MOT Expiry": r.mot_expiry_date ? formatTimeInTimezone(+r.mot_expiry_date, 'DD-MMM-YY', timezone) : null,
                "plant_hours": r.plant_hours,
                "Last Daily Inspection": r.lastInspectionCreatedAt ? formatTimeInTimezone(+r.lastInspectionCreatedAt, 'DD-MMM-YY', timezone) : null,
                "Open Faults": r.faultCount,
                "Archive Date": archived_logs != null ? formatTimeInTimezone(+archived_logs.timestamp, 'DD-MMM-YY', timezone) : null,
                "Archive By": archived_logs != null ? archived_logs.name : null
            };
            records.push(result);
        });
    } else if (assetType == 'equipment') {
        columns = [
            {header: "Equipment ID", key: "Equipment ID", width: 20},
            {header: "Equipment Type", key: "Equipment Type", width: 25},
            {header: "Item", key: "Item", width: 25},
            {header: "Reg./Serial Number", key: "Reg./Serial Number", width: 25},
            {header: "Owner", key: "Owner", width: 40},
            {header: "Arrived on Site", key: "Arrived on Site", width: 20},
            {header: "Thorough Examination Expiry", key: "Thorough Examination Expiry", width: 20},
            {header: "PAT Test Expiry", key: "PAT Test Expiry", width: 20},
            {header: "Last Daily Inspection", key: "Last Daily Inspection", width: 20},
            {header: "Open Faults", key: "Open Faults", width: 20},
        ];

        if(workSheetName === 'Archived'){
            columns.push({header: "Archive Date", key: "Archive Date", width: 20},{header: "Archive By", key: "Archive By", width: 40});
        }

        assetRecords.map(r => {
            let archived_logs = null;
            if(r.is_archived){
                archived_logs = r.activity_logs.pop();
            }
            let result = {
                "Equipment ID": r.equipment_id,
                "Equipment Type": r.alternate_phrase,
                "Item": r.item,
                "Reg./Serial Number": r.serial_number,
                "Owner": (r.tagged_owner.length) ? (r.tagged_owner || []).reduce((arr, owner) => { arr.push(owner.name); return arr;  },[]).join(',') : "",
                "Arrived on Site": r.arrived_at ? formatTimeInTimezone(+r.arrived_at, 'DD-MMM-YY', timezone) : null,
                "Thorough Examination Expiry":r.examination_cert_expiry_date ? formatTimeInTimezone(+r.examination_cert_expiry_date, 'DD-MMM-YY', timezone) : null,
                "PAT Test Expiry": r.pat_test_expiry_date ? formatTimeInTimezone(+r.pat_test_expiry_date, 'DD-MMM-YY', timezone) : null,
                "Last Daily Inspection": r.lastInspectionCreatedAt ? formatTimeInTimezone(+r.lastInspectionCreatedAt, 'DD-MMM-YY', timezone) : null,
                "Open Faults": r.faultCount,
                "Archive Date": archived_logs != null ? formatTimeInTimezone(+archived_logs.timestamp, 'DD-MMM-YY', timezone) : null,
                "Archive By": archived_logs != null ? archived_logs.name : null
            };
            records.push(result);
        });
    } else if (assetType == ASSET_TYPES.AssetTemporaryWork) {
        columns = [
            {header: "Item ID", key: "item_id", width: 20},
            {header: "Temporary Work Type", key: "work_type", width: 25},
            {header: "Item", key: "Item", width: 25},
            {header: "Reg./Serial Number", key: "Reg./Serial Number", width: 25},
            {header: "Owner(s)", key: "Owner", width: 40},
            {header: "Date Erected", key: "Date Erected", width: 20},
            {header: "Risk Category", key: "risk_category", width: 20},
            {header: "Last Daily Inspection", key: "Last Daily Inspection", width: 20},
            {header: "Open Faults", key: "Open Faults", width: 20},
        ];

        if(workSheetName === 'Archived'){
            columns.push({header: "Archive Date", key: "Archive Date", width: 20},{header: "Archive By", key: "Archive By", width: 40});
        }

        assetRecords.map(r => {
            let archived_logs = null;
            if(r.is_archived){
                archived_logs = r.activity_logs.pop();
            }
            let result = {
                "item_id": r.item_id,
                "work_type": r.alternate_phrase,
                "Item": r.item,
                "Reg./Serial Number": r.serial_number,
                "Owner": (r.tagged_owner.length) ? (r.tagged_owner || []).reduce((arr, owner) => { arr.push(owner.name); return arr;  },[]).join(',') : "",
                "Date Erected": r.arrived_at ? formatTimeInTimezone(+r.arrived_at, 'DD-MMM-YY', timezone) : null,
                "risk_category": r.risk_category,
                "Last Daily Inspection": r.lastInspectionCreatedAt ? formatTimeInTimezone(+r.lastInspectionCreatedAt, 'DD-MMM-YY', timezone) : null,
                "Open Faults": r.faultCount,
                "Archive Date": archived_logs != null ? formatTimeInTimezone(+archived_logs.timestamp, 'DD-MMM-YY', timezone) : null,
                "Archive By": archived_logs != null ? archived_logs.name : null
            };
            records.push(result);
        });
    }

    worksheet.columns = columns;
    worksheet.autoFilter = {
        from :"A1",
        to:{
            row: +worksheet.rowCount,
            column: +worksheet.columnCount
        }
    };
    worksheet.addRows(records);

    worksheet.eachRow(function(row, rowNumber) {
        // Iterate over all (including empty) cells in a row
        row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
            row.height = 18;
            if (rowNumber === 1) {
                row.height = 30;
                cell.font = {
                    bold: true,
                };
                cell.fill = {
                    type: 'pattern',
                    pattern:'solid',
                    fgColor: {argb: 'dedede'}
                }
                cell.border = {
                    top: {style:'thin'},
                    left: {style:'thin'},
                    bottom: {style:'thin'},
                    right: {style:'thin'}
                };
            } else {
                let color = '';
                if (cell.value && assetType == 'vehicle' && [6,7,8].includes(colNumber)) {
                    let expiryStatus = checkCertificateExpiry(moment(cell.value, 'DD-MMM-YY').valueOf());
                    color = expiryStatus === 0 ? 'ff0000' : expiryStatus === 1 ? 'ff7f00' : '0aa532';
                }

                if (cell.value && assetType == 'equipment' && [7,8].includes(colNumber)) {
                    let expiryStatus = checkCertificateExpiry(moment(cell.value, 'DD-MMM-YY').valueOf());
                    color = expiryStatus === 0 ? 'ff0000' : expiryStatus === 1 ? 'ff7f00' : '0aa532';
                }

                if (color) {
                    cell.font = { color: {'argb': color}};
                }
            }

            cell.alignment = {  vertical: 'middle',horizontal: 'center',  wrapText: true};
        });
    });
    return worksheet;
}

//Asset vehicles and equipment
const checkCertificateExpiry = function (expiry_date) {
    const today = moment().unix() * 1000;
    let priorDateInTimestamp = moment().add(30, 'day').unix() * 1000;
    if(expiry_date < today) { //let than current date
        return 0;
    } else if (expiry_date > today && expiry_date <= priorDateInTimestamp) { //between 1-30 days of current date
        return 1;
    } else if (expiry_date > priorDateInTimestamp) { //more than 30 days
        return 2;
    }
}

const badgeEventExcel = async (biometricEvents, geoFenceEvents) => {
    sails.log.info('creating workbook export', (new Date()));

    let events = ([...biometricEvents, ...geoFenceEvents]).sort((a,b) => (a.event_epoch < b.event_epoch) ? 1 : ((b.event_epoch < a.event_epoch) ? -1 : 0));

    let workbook = new ExcelJS.Workbook();
    prepareBadgeEventWorkSheet(workbook, 'Events', events);
    return workbook;
};

const prepareBadgeEventWorkSheet = function (workbook, workSheetName, events) {
    let worksheet = workbook.addWorksheet(getWorksheetName(workSheetName), {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70}
    });

    let styleFilter = {alignment: {  vertical: 'left',horizontal: 'center',  wrapText: true}};
    let columns = [
        {header: "Date", key: "Date", width: 12, ...styleFilter},
        {header: "Time", key: "Time", width: 12, ...styleFilter},
        {header: "First Name", key: "First Name", width: 20, ...styleFilter},
        {header: "Last Name", key: "Last Name", width: 20, ...styleFilter},
        {header: "innDex ID", key: "innDex ID", width: 12, ...styleFilter},
        {header: "Company", key: "Company", width: 20, ...styleFilter},
        {header: "Job Role", key: "Job Role", width: 22, ...styleFilter},
        {header: "Event Type", key: "Event Type", width: 12, ...styleFilter},
        {header: "Location Name", key: "Location Name", width: 28, ...styleFilter},
        {header: "Event", key: "Event", width: 10, ...styleFilter}
    ];
    let records = [];
    events.map(r => {
        let result = {
            "Date": r.event_date,
            "Time": r.event_time,
            "First Name": r.first_name,
            "Last Name":  r.last_name,
            "innDex ID":  r.inndex_id,
            "Company":  r.company,
            "Job Role":  r.job_role,
            "Event Type": r.event_from,
            'Location Name': r.location_name,
            "Event": r.event_type
        };

        records.push(result);
    });
    worksheet.columns = columns;
    worksheet.addRows(records);

    worksheet.getRow(1).height = 20;
    worksheet.getRow(1).eachCell({ includeEmpty: true }, function(cell, colNumber) {
        cell.font = {
            bold: true,
        };
        cell.fill = {
            type: 'pattern',
            pattern:'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style:'thin'},
            left: {style:'thin'},
            bottom: {style:'thin'},
            right: {style:'thin'}
        };
    });

    return worksheet;
}

const take5sReport = async (take5s, take5SglrPhrase, timezone) => {
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('Take5s', {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70, horizontalCentered: true,
        verticalCentered: true,}
    });
    let styleFilter = {alignment: {  vertical: 'middle',horizontal: 'center',  wrapText: true}};
    let maxColumns = 1;

    take5s.map(p=>{
        let currColumns = 0;
        for(const photo of p.images) {
            currColumns += (photo.img_translation.length) ? photo.img_translation.length : 1;
        }
        maxColumns = (currColumns > maxColumns) ? currColumns : maxColumns;
    })
    let columns = [
        { header: take5SglrPhrase+" #", key: "Take 5 No.", width:15, ...styleFilter},
        { header: "Date & Time", key: "Date & Time", width:15, ...styleFilter},
        { header: "Employer", key: "Employer", width:20, ...styleFilter},
        { header: "Submitted By", key: "Submitted By", width:20, ...styleFilter},
        { header: "Conversation Category", key: "Conversation Category", width:30, ...styleFilter},
        { header: "Location & Task", key: "Location & Task", width:20, ...styleFilter},
        { header: "Discussion Points & Issues", key: "Discussion Points & Issues", width:30, ...styleFilter},
        { header: "Outcomes & Actions", key: "Outcomes & Actions", width:20, ...styleFilter},
        { header: "Attended", key: "Attended", width:15, ...styleFilter},
        { header: "Photos", key: "photos", width:20, ...styleFilter}

    ];
    if(maxColumns > 1) {
        for(let i=1; i<maxColumns; i++) {
            columns.push({ header: "Photo "+i, key: "Photos"+i, width:20, ...styleFilter});
        }
    }

    worksheet.columns = columns;

    let records = [];
    take5s.map(r => {
        let userEmployer = r.employer ? r.employer: '';
        let userName = r.user_ref.name;
        let result = {
            "Take 5 No.": r.t5s_number,
            "Date & Time": momentTz(+r.createdAt).tz(timezone).format('DD-MM-YYYY HH:mm:ss'),
            "Submitted By": userName,
            "Employer": userEmployer,
            "Conversation Category": r.conversation_category,
            "Location & Task": r.location_and_task,
            "Discussion Points & Issues": r.points_and_issues,
            "Outcomes & Actions": r.outcomes_and_actions,
            "Attended": r.attendees_count,
            "Photos": null
        };

        records.push(result);
    });
    worksheet.addRows(records);
    let lastColNumber = 9+maxColumns;
    let endColumn = worksheet.getRow(1).getCell(worksheet.getColumn(lastColNumber)._key);
    if(maxColumns > 1) {
        let colToMerge = 'J1:'+endColumn._address;
        worksheet.mergeCells(colToMerge);
    }
    let r = 1;
    let imgs = [];
    let img = null;
    for(let i=0; i< take5s.length; i++) {
        let c = 9;
        worksheet.getRow(r).height = 110;
        imgs = take5s[i].images;
        for(let j=0; j< imgs.length; j++) {
            img = imgs[j];
            let fileRecords = await _addImagesToWorkbook(workbook, 'img'+take5s[i].id, [img]);
            if (fileRecords.length) {
                for(const fileRecord of fileRecords) {
                    worksheet.addImage(fileRecord.id, {
                        tl: {col: c+0.05, row: r+0.25},
                        ext: fileRecord.ext,
                    });
                    c++;
                }
            }
        }
        r++;
    }
    worksheet.lastRow.height = 110;
    worksheet.eachRow(function(row, rowNumber) {
        row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
            if (rowNumber === 1) {
                row.height = 20;
                cell.font = {
                bold: true,
                };
                cell.fill = {
                    type: 'pattern',
                    pattern:'solid',
                    fgColor: {argb: 'dedede'}
                }
                cell.border = {
                    top: {style:'thin'},
                    left: {style:'thin'},
                    bottom: {style:'thin'},
                    right: {style:'thin'}
                };
            }
            cell.alignment = {  vertical: 'middle',horizontal: 'center',  wrapText: true};
        });
    });
    return workbook;
};

const groupBy = (array, key) => {
    return array.reduce((acc, obj) => {
        const property = obj.question_ref[key];
        acc[property] = acc[property] || [];
        acc[property].push(obj);
        return acc;
    }, {});
}

const hasHealthIssue = (induction, projectInfo) => {
        induction.health_issues = null;
        induction.medical_issues = [];
        if (induction.additional_data.health_assessment_answers && induction.additional_data.health_assessment_answers.length) {
            const health_issues = induction.additional_data.health_assessment_answers.filter(healthAss => Number(healthAss.answer) === 1);
            induction.health_issues = health_issues.length ? groupBy(health_issues, 'category') : null;
        }
        if (induction.additional_data.medical_assessments_answers && induction.additional_data.medical_assessments_answers.length) {
            const medical_issues = induction.additional_data.medical_assessments_answers.filter(medicalAss => Number(medicalAss.answer) === 1);
            induction.medical_issues = medical_issues.length ? medical_issues : [];
        }

    return ((!projectInfo.custom_field.disable || (projectInfo.custom_field.disable && !projectInfo.custom_field.disable.view_medication_modal)) && (induction.medical_issues.length || induction.health_issues || induction.reportable_medical_conditions === 'yes' || induction.on_long_medication === 'yes')) ? 'Yes' : 'No';
}

const getInductionRecordsWorkbook = async(records, projectInfo, timezone) => {
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet(getWorksheetName(`${projectInfo.custom_field.induction_phrase_singlr}`), {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70}
    });

    let projectDistricts = await getProjectDistrict(+projectInfo.id);

    let localWorkForceColumns = [];
    if (projectDistricts.length) {
        localWorkForceColumns = [
            { header: "District", key: "district", width:20},
            { header: "Local Worker", key: "local_worker", width:15},
        ]
    }
    let additionalColumns = [];
    (records || []).map(item => {
        if(item.induction_question_answers && item.induction_question_answers.additional_qa && item.induction_question_answers.additional_qa.length) {
            (item.induction_question_answers.additional_qa || []).map((qa, i) => {
                let existingIndex = (additionalColumns).findIndex(item => item.header === qa.question);
                if(existingIndex == -1) {
                    additionalColumns.push({
                        header: qa.question, key: `${qa.question}_${i}`, width:20
                    })
                }

                if (qa.ans_field_type == 'radio_yn' && qa.sub_questions && qa.sub_questions.length) {
                    (qa.sub_questions || []).map((sub_qa, j) => {
                        let key = `${replaceAll(qa.question, ' ', '')}_${replaceAll(sub_qa.question, ' ', '')}`;
                        let existingSubIndex = (additionalColumns).findIndex(item => item.key === key);
                        if(existingSubIndex == -1) {
                            additionalColumns.push({
                                header: sub_qa.question,
                                key: `${key}`,
                                width: 20
                            })
                        }
                        return sub_qa;
                    });
                }
                return qa;
            });
        }

        item.local_worker = (projectDistricts.length && item.district && projectDistricts.includes(item.district)) ? 'Yes' : 'No';
        //sails.log.info(item.district, projectDistricts, item.local_worker);
        return item;
    });

    let columns = [
        { header: "Project ID", key: "Project ID", width:10},
        { header: `${projectInfo.custom_field.induction_phrase_singlr} Number`, key: "Induction No", width:10},
        { header: "innDex ID", key: "InnDex ID", width:10},
        { header: "First Name", key: "First Name", width:15},
        { header: "Last Name", key: "Last Name", width:15},
        { header: "Email", key: "Email", width:30},
        { header: "Date of Birth", key: "Date of Birth", width:15},
        { header: "Nationality", key: "Nationality", width:15},
        { header: "Gender", key: "Gender", width:15},
        { header: "Phone Number", key: "Phone Number", width:15},
        { header: "Company", key: "company", width:20},
        { header: "Type of Employment", key: "employment_type", width:20},
        { header: "Employment Start Date", key: "employment_start", width:20},
        { header: "Agency", key: "agency", width:20},
        { header: "Job Role", key: "job_role", width:20},
        { header: "Health/Medical Issue(s)", key: "health_issue", width:20},
        { header: "Post Code", key: "Postcode", width:15},
        ...localWorkForceColumns,
        { header: "Method of Travel", key: "Travel Method", width:15},
        { header: "Vehicle Reg. No.", key: "Vehicle Reg", width:20},
        { header: "Total Travel Time (mins.)", key: "Total Travel Time (mins)", width:13},
        { header: "Distance Travelled (miles)", key: "Distance Traveled (miles)", width:10},
        { header: "Last on Site", key: "Last on Site", width:15},
        { header: "Total Working Hours", key: "Total Working Hours", width:20},
        { header: "Total Working Days", key: "Total Working Days", width:20},
        { header: "CSCS No.", key: "CSCS No.", width:10},
        { header: "CPCS No.", key: "CPCS No.", width:10},
        { header: "PTS No.", key: "PTS No.", width:10},
        { header: "First Aider", key: "First Aid", width:8},
        { header: "SMSTS", key: "SMSTS", width:8},
        { header: "SSSTS", key: "SSSTS", width:8},
        { header: `Date of ${projectInfo.custom_field.induction_phrase_singlr}`, key: "Date of Induction", width:12},
        { header: "Induction Status", key: "Status Message", width:10},
        { header: "Induction Approver", key: "Inductor", width:15},
        ...additionalColumns
    ];

    worksheet.columns = columns;
    worksheet.autoFilter = 'A1:Z1';
    worksheet.getRow(1).height = 40;
    worksheet.getRow(1).eachCell({ includeEmpty: true }, function(cell, colNumber) {
        cell.font = {
            bold: true,
        };
        cell.fill = {
            type: 'pattern',
            pattern:'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style:'thin'},
            left: {style:'thin'},
            bottom: {style:'thin'},
            right: {style:'thin'}
        };
        cell.alignment = {  vertical: 'middle',horizontal: 'center',  wrapText: true};
    });

    records = records.map(r => {
        let additionalColumnValues = {};
        if(r.induction_question_answers && r.induction_question_answers.additional_qa && r.induction_question_answers.additional_qa.length) {
            (r.induction_question_answers.additional_qa || []).map((qa, i) => {
                additionalColumnValues[`${qa.question}_${i}`] = (qa.ans_field_type === 'date' && qa.ans_epoch) ? momentTz(+qa.ans_epoch).tz(timezone).format(displayDateFormat_DD_MM_YYYY) : qa.answer;
                if (qa.ans_field_type == 'radio_yn' && qa.sub_questions && qa.sub_questions.length) {
                    (qa.sub_questions || []).map((sub_qa, j) => {
                        additionalColumnValues[`${replaceAll(qa.question, ' ', '')}_${replaceAll(sub_qa.question, ' ', '')}`] = (sub_qa.ans_field_type === 'date' && sub_qa.ans_epoch) ? momentTz(+sub_qa.ans_epoch).tz(timezone).format(displayDateFormat_DD_MM_YYYY) : sub_qa.answer;
                        return sub_qa;
                    });
                }
                return qa
            });
        }

        let travelTimeOverride = getActiveTravelTime(r, momentTz().tz(timezone));
        let totalTime = getTotalTravelDuration(travelTimeOverride.travel_time);
        let totalDistance = getTotalTravelDistance(travelTimeOverride.travel_time);
        let cscs_no = null;
        let cpcs_no = null;
        let pts_no = null;
        let first_aid_competency = `No`;
        let smsts_competency = `No`;
        let sssts_competency = `No`;

        let health_issue = hasHealthIssue(r, projectInfo);
        let user_docs = ((r.additional_data || {}).user_docs || []);
        if (user_docs) {
            let first_aid_doc_names = ['first aid', 'emergency first aid'];
            if (user_docs.findIndex(document => document && first_aid_doc_names.includes((document.name || '').toString().toLowerCase().trim())) !== -1) {
                first_aid_competency = `Yes`;
            }
            if (user_docs.findIndex(document => document && (document.name || '').toString().toLowerCase().trim() === `smsts`) !== -1) {
                smsts_competency = `Yes`;
            }
            if (user_docs.findIndex(document => document && (document.name || '').toString().toLowerCase().trim() === `sssts`) !== -1) {
                sssts_competency = `Yes`;
            }
            let cscs = user_docs.find(document => document && ['cscs (front)', 'cscs'].includes((document.name || '').toString().toLowerCase().trim()));
            if (cscs) {
                cscs_no = cscs.doc_number;
            }

            let cpcs = user_docs.find(document => document && ['cpcs (front)', 'cpcs'].includes((document.name || '').toString().toLowerCase().trim()));
            if (cpcs) {
                cpcs_no = cpcs.doc_number;
            }

            let pts = user_docs.find(document => document && (document.name || '').toString().toLowerCase().trim() === `pts`);
            if (pts) {
                pts_no = pts.doc_number;
            }
        }

        let localWorkForceColumnValue = {};
        if (projectDistricts.length) {
            localWorkForceColumnValue.district = r.district;
            localWorkForceColumnValue.local_worker = r.local_worker;
        }

        let employment_company = (r.additional_data && r.additional_data.employment_detail && r.additional_data.employment_detail.employment_company) ? `${r.additional_data.employment_detail.employment_company}` : '';
        return {
            "Project ID": r.project_ref,
            "Induction No": r.record_id,
            "InnDex ID": r.additional_data.user_info && r.additional_data.user_info.id && r.additional_data.user_info.id,
            "First Name": r.additional_data.user_info && r.additional_data.user_info.id && r.additional_data.user_info.first_name,
            "Last Name": r.additional_data.user_info && r.additional_data.user_info.id && r.additional_data.user_info.last_name,
            "Email": r.additional_data.user_info && r.additional_data.user_info.id && r.additional_data.user_info.email,
            "Date of Birth": momentTz(r.additional_data.user_info.dob).tz(timezone).format(displayDateFormat_DD_MM_YYYY),
            "Nationality": r.additional_data.user_info && r.additional_data.user_info.country,
            "Gender": r.additional_data && r.additional_data.user_info && r.additional_data.user_info.gender,
            "Phone Number": (r.additional_data && r.additional_data.contact_detail && r.additional_data.contact_detail.mobile_no),
            "company": (r.additional_data && r.additional_data.employment_detail && r.additional_data.employment_detail.employer),
            "employment_type": r.additional_data && r.additional_data.employment_detail && r.additional_data.employment_detail.type_of_employment,
            "employment_start": (r.additional_data && r.additional_data.employment_detail && r.additional_data.employment_detail.start_date_with_employer) ? momentTz(+r.additional_data.employment_detail.start_date_with_employer).tz(timezone).format(displayDateFormat_DD_MM_YYYY) : null,
            "agency": (r.additional_data && r.additional_data.employment_detail && r.additional_data.employment_detail.type_of_employment === 'Agency') ? employment_company : 'NA',
            "job_role": r.additional_data && r.additional_data.employment_detail && r.additional_data.employment_detail.job_role,
            "health_issue": health_issue,
            "Postcode": (r.additional_data && r.additional_data.contact_detail && r.additional_data.contact_detail.post_code),
            ...localWorkForceColumnValue,
            "Travel Method": r.travel_method,
            "Vehicle Reg": (r.vehicle_reg_number) ? r.vehicle_reg_number : 'NA',
            "Total Travel Time (mins)": totalTime,
            "Distance Traveled (miles)": totalDistance ? Number(kmToMiles(totalDistance).toFixed(2)) : null,
            "Last on Site": (r.user_last_on_site) ? momentTz(+(r.user_last_on_site) * 1000).tz(timezone).format(displayDateFormat_DD_MM_YYYY_HH_mm_ss) : null,
            "Total Working Hours": r.total_working_hours,
            "Total Working Days": r.total_working_days,
            "CSCS No.": cscs_no,
            "CPCS No.": cpcs_no,
            "PTS No.": pts_no,
            "First Aid": first_aid_competency,
            "SMSTS": smsts_competency,
            "SSSTS": sssts_competency,
            "Date of Induction": r.createdAt ? momentTz(+r.createdAt).tz(timezone).format(displayDateFormat_DD_MM_YYYY) : null,
            "Status Message": r.status_message,
            "Inductor": r.inductor_ref && r.inductor_ref.id && (r.inductor_ref.name || r.inductor_ref.first_name),
            ...additionalColumnValues
        };
    }).sort((a, b) => (+b['Induction No'] - +a['Induction No']));

    worksheet.addRows(records);
    return workbook;
};

const getCompanyInductionsWorkbook = async(records, phrasing, timezone) => {
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet(getWorksheetName(phrasing), {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70}
    });
    let additionalColumns = [];
    (records || []).map(item => {
        if(item.additional_iq && item.additional_iq.induction_questions && item.additional_iq.induction_questions.length) {
            (item.additional_iq.induction_questions || []).map((qa, i) => {
                let existingIndex = (additionalColumns).findIndex(item => item.header === qa.question);
                if(existingIndex == -1) {
                    additionalColumns.push({
                        header: qa.question, key: `${qa.question}_${i}`, width:20
                    })
                }

                if (qa.ans_field_type == 'radio_yn' && qa.sub_questions && qa.sub_questions.length) {
                    (qa.sub_questions || []).map((sub_qa, j) => {
                        let key = `${replaceAll(qa.question, ' ', '')}_${replaceAll(sub_qa.question, ' ', '')}`;
                        let existingSubIndex = (additionalColumns).findIndex(item => item.key === key);
                        if(existingSubIndex == -1) {
                            additionalColumns.push({
                                header: sub_qa.question,
                                key: `${key}`,
                                width: 20
                            })
                        }
                        return sub_qa;
                    });
                }
                return qa;
            });
        }

        if(item.quiz && item.quiz.induction_answers && item.quiz.induction_answers.length) {
            (item.quiz.induction_answers || []).map((qa, i) => {
                let quizQue = (item.quiz.induction_questions || []).find(que => que.id == qa.question_id);
                let existingIndex = (additionalColumns).findIndex(item => item.header == quizQue.question);
                if(existingIndex == -1) {
                    additionalColumns.push({
                        header: quizQue.question, key: `${qa.question_id}_${i}`, width:20
                    })
                }
                return qa;
            });
        }
        return item;
    });

    let columns = [
        { header: `${phrasing} Number`, key: "Induction No", width:10},
        { header: "innDex ID", key: "InnDex ID", width:10},
        { header: "First Name", key: "First Name", width:15},
        { header: "Last Name", key: "Last Name", width:15},
        { header: "Email", key: "Email", width:30},
        { header: "Date of Birth", key: "Date of Birth", width:15},
        { header: "Nationality", key: "Nationality", width:15},
        { header: `Date of Induction`, key: "Date of Induction", width:12},
        { header: `Date of Induction Expiration`, key: "Date of Induction Expiration", width:12},
        { header: "Induction Status", key: "Status Message", width:10},
        ...additionalColumns
    ];

    worksheet.columns = columns;
    worksheet.autoFilter = 'A1:Z1';
    worksheet.getRow(1).height = 40;
    worksheet.getRow(1).eachCell({ includeEmpty: true }, function(cell, colNumber) {
        cell.font = {
            bold: true,
        };
        cell.fill = {
            type: 'pattern',
            pattern:'solid',
            fgColor: {argb: 'dedede'}
        };
        cell.border = {
            top: {style:'thin'},
            left: {style:'thin'},
            bottom: {style:'thin'},
            right: {style:'thin'}
        };
        cell.alignment = {  vertical: 'middle',horizontal: 'center',  wrapText: true};
    });

    records = records.map(r => {
        let additionalColumnValues = {};
        if(r.additional_iq && r.additional_iq.induction_questions && r.additional_iq.induction_questions.length) {
            (r.additional_iq.induction_questions || []).map((qa, i) => {
                additionalColumnValues[`${qa.question}_${i}`] = (qa.ans_field_type === 'date' && qa.ans_epoch) ? momentTz(+qa.ans_epoch).tz(timezone).format(displayDateFormat_DD_MM_YYYY) : qa.answer;
                if (qa.ans_field_type == 'radio_yn' && qa.sub_questions && qa.sub_questions.length) {
                    (qa.sub_questions || []).map((sub_qa, j) => {
                        additionalColumnValues[`${replaceAll(qa.question, ' ', '')}_${replaceAll(sub_qa.question, ' ', '')}`] = (sub_qa.ans_field_type === 'date' && sub_qa.ans_epoch) ? momentTz(+sub_qa.ans_epoch).tz(timezone).format(displayDateFormat_DD_MM_YYYY) : sub_qa.answer;
                        return sub_qa;
                    });
                }
                return qa
            });
        }

        if(r.quiz && r.quiz.induction_answers && r.quiz.induction_answers.length) {
            (r.quiz.induction_answers || []).map((qa, i) => {
                additionalColumnValues[`${qa.question_id}_${i}`] = (qa.answers || []).join(', ');
                return qa
            });
        }

        return {
            "Induction No": r.record_id,
            "InnDex ID": r.user_ref && r.user_ref.id && r.user_ref.id,
            "First Name": r.user_ref && r.user_ref.id && r.user_ref.first_name,
            "Last Name": r.user_ref && r.user_ref.id && r.user_ref.last_name,
            "Email": r.user_ref && r.user_ref.id && r.user_ref.email,
            "Date of Birth": momentTz(r.user_ref.dob).tz(timezone).format(displayDateFormat_DD_MM_YYYY),
            "Nationality": r.user_ref && r.user_ref.country,
            "Date of Induction": r.createdAt ? momentTz(+r.createdAt).tz(timezone).format(displayDateFormat_DD_MM_YYYY) : null,
            "Date of Induction Expiration": r.expire_on ? momentTz(+r.expire_on).tz(timezone).format(displayDateFormat_DD_MM_YYYY) : null,
            "Status Message": r.status_message,
            ...additionalColumnValues
        };
    }).sort((a, b) => (+b['Induction No'] - +a['Induction No']));

    worksheet.addRows(records);
    return workbook;
};

const exportPermitRequestsReport = async (records) => {
    sails.log.info('creating project permit requests export', (new Date()));
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('register', {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70, horizontalCentered: true, verticalCentered: true }
    });
    worksheet.columns = [
        { header: 'Permit #', key: 'record_id', width: 15 },
        { header: 'Permit Type', key: 'permit_type', width: 30 },
        { header: 'Requested', key: 'requested_at', width: 20 },
        { header: 'Requested By', key: 'requested_by', width: 20 },
        { header: 'Requestor Company', key: 'requestor_company', width: 20 },
        { header: 'Status', key: 'status', width: 20 },
        { header: 'Start Date', key: 'start_date', width: 20 },
        { header: 'Expiry Date', key: 'expiry_date', width: 20 },
        { header: 'Closeout Date', key: 'closeout_date', width: 20 },
        { header: 'Closeout By', key: 'closeout_by', width: 20 }
    ];
    worksheet.getRow(1).font = {bold: true, size: 11};
    worksheet.getRow(1).height = 25;
    worksheet.getRow(1).eachCell(cell => {
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: COLORS.VERY_LIGHT_GRAY_BG}
        };
    });
    worksheet.addRows(records);
    worksheet.autoFilter = 'A1:AH1';
    sails.log.info('created workbook', (new Date()));
    return workbook;
};

// Utility function to get a column number to Excel column letters
function getExcelColumnLetter(colNum) {
    let letter = '';
    while (colNum > 0) {
        let rem = (colNum - 1) % 26;
        letter = String.fromCharCode(65 + rem) + letter;
        colNum = Math.floor((colNum - 1) / 26);
    }
    return letter;
}

const downloadAssignedConductCard = async (records, include = []) => {
    sails.log.info('Creating project conduct card requests excel export', (new Date()));
    let workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet('Conduct cards', {
        views: [{zoomScale: 80}],
        pageSetup: {paperSize: 5, orientation: 'landscape', scale: 70, horizontalCentered: true, verticalCentered: true }
    });
    worksheet.columns = [
        ...(include.includes('ucc_ref') ? [{ header: 'Card Ref No.', key: 'ucc_ref', width: 20 }] : []),
        ...(!include.includes('ucc_ref') ? [{ header: 'Induction Record #', key: 'induction_record_id', width: 15 }] : []),
        { header: 'Date/Time of Issue', key: 'issue_on', width: 20 },
        { header: 'Card Name', key: 'card_name', width: 20 },
        { header: 'Card Type', key: 'card_type', width: 20 },
        { header: 'Name', key: 'name', width: 20 },
        { header: 'Company', key: 'company_name', width: 20 },
        ...(include.includes('ucc_ref') ? [{ header: 'Induction Record #', key: 'induction_record_id', width: 15 }] : []),
        ...(include.includes('project_name') ? [{ header: 'Project', key: 'project_name', width: 20 }] : []),
        { header: 'Issued By', key: 'issued_by', width: 20 },
        { header: 'Action', key: 'card_action', width: 20 },
        { header: 'Expiry', key: 'expire_on', width: 20 },
        { header: 'Comments', key: 'comment', width: 20 }
    ];
    worksheet.getRow(1).font = {bold: true, size: 11};
    worksheet.getRow(1).height = 25;
    worksheet.getRow(1).eachCell(cell => {
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: COLORS.VERY_LIGHT_GRAY_BG}
        };
    });
    worksheet.addRows(records);
    const columnCount = worksheet.columns.length;
    worksheet.autoFilter = `A1:${getExcelColumnLetter(columnCount)}1`;
    sails.log.info('Created workbook', (new Date()));
    return workbook;
};

module.exports = {
    streamExcelDownload,
    readExcelFileAsObjects,
    exportBiometricSettingsReport,
    exportCompanyStatsReport,
    exportTimesheets,
    exportDailyDeclarationOfUser,
    exportInductionInviteListReport,
    exportTotalTimeReportOfProject,
    exportProjectResourcePlans,
    exportVehicleDailyLogs,
    companyProjectTimeSheetReport,
    progressPhotosReport,
    deliveryNotesReport,
    parseDistanceTravelled,
    taskBriefingReport,
    downloadToolboxTalkXLSX,
    downloadGateBookingsXLSX,
    workPackagePlanReport,
    getDownloadDailyReportWorkbook,
    getFatigueReportWorkbook,
    getWorkforceHoursReportWorkbook,
    getWorkforceHoursComparisonReportWorkbook,
    getInspectionParticipants,
    getProjectEmissionsReportWorkbook,
    assetRegisterReport,
    downloadRegisterXLSX,
    closeCallReport,
    badgeEventExcel,
    getActivityHoursBreakdownWorkbook,
    take5sReport,
    getInductionRecordsWorkbook,
    getCompanyInductionsWorkbook,
    exportPermitRequestsReport,
    downloadAssignedConductCard,
};
