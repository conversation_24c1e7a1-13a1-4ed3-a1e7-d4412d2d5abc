/**
 * Created by spatel on 19/9/18.
 * https://docs.aws.amazon.com/AWSJavaScriptSDK/latest/AWS/S3.html#getSignedUrl-property
 */

const path = require('path');
const { Upload } = require('@aws-sdk/lib-storage');
const { S3, S3Client } = require('@aws-sdk/client-s3');
const s3 = new S3();
const fs = require('fs');
const os = require('os');
const s3urls = require('./s3UrlParser');
const { v4: uuid4 } = require('uuid');
const dayjs = require('dayjs');
const { downloadUrlAt, fetchUrlAs, fetchS3UrlAsBuffer, typeOf, parseJson, triggerLambdaFn} = require('./HttpService');
const { Poppler } = require("node-poppler");
const {
    APP_ENV,
    S3_BUCKET,
    POPPLER_BIN_PATH,
    INNDEX_S3_BUCKET,
    CDN_DOMAIN_HOST,
    TMP_CF_DOMAIN_HOST,
    AUTO_CLEAN_S3_BUCKET,
    LAMBDA_EB_CRON_SCHEDULER,
    LAMBDA_EB_CRON_DELETE,
    LAMBDA_ONE_TIME_EVENT_SCHEDULER,
    AWS_REGION_SLS,
} = sails.config.custom;

const s3Client = new S3Client({region: AWS_REGION_SLS});

const moment = require("moment");
let poppler_bin_path = null;
if(os.platform() === 'linux'){
    poppler_bin_path = POPPLER_BIN_PATH;
}
const poppler = new Poppler(poppler_bin_path);

let tempDirPath = (featurePath = 'inndex') => {
    return fs.mkdtempSync(path.join(os.tmpdir(), `${featurePath}`));
}

let deleteS3File = async (fileUrl) => {

    if (s3urls.valid(fileUrl)) {

        let params = s3urls.fromUrl(fileUrl);
        sails.log.info('Is a valid URL.', fileUrl, params);
        try{
            let result = await s3.deleteObject(params);
            sails.log.info('S3 File deleted successfully', result);
            return true;
        }catch(e){
            sails.log.error('Failed to delete S3 file', e);
            return false;
        }
    }
    let cdnURL = s3urls.fromCDNUrl(fileUrl);
    if(cdnURL){
        sails.log.info('Is a valid CDN URL.', fileUrl, cdnURL);
        try{
            let result = await s3.deleteObject(cdnURL);
            sails.log.info('S3 File deleted successfully', result);
            return true;
        }catch(e){
            sails.log.error('Failed to delete CDN file', e);
            return false;
        }
    }
    else{
        sails.log.info('Not a valid S3 URL?', fileUrl);
        return true;
    }
};

/**
 * Asynchronous function to upload a file to an S3 bucket.
 *
 * @param {string} fileName - The name of the file to be uploaded.
 * @param {Buffer|string|Uint8Array|Blob|ReadStream} body - The content of the file to be uploaded.
 * @param {string} contentType - The MIME type of the file being uploaded.
 * @param {string} [bucketType='default'] - The type of the S3 bucket to upload the file to. Defaults to 'default'.
 *
 * @returns {Promise<Object>} A promise that resolves with an object containing upload details and the file's public URL.
 *
 * @throws {Error} Throws an error if the upload to S3 fails.
 */
const s3Uploader = async (fileName, body, contentType, bucketType = 'default') => {
    sails.log.info(`Uploading file to S3(${APP_ENV})`, fileName);
    let tags = [
        // {Key: "APP_ENV", Value: APP_ENV},
    ];
    let uploadOutput = await new Upload({
        client: s3Client,
        tags: tags,
        params: {
            Body: body,
            Bucket: (bucketType == 'default') ? S3_BUCKET : INNDEX_S3_BUCKET,
            Key: fileName,
            ACL: 'public-read',
            ContentType: contentType,
            // @todo: spatel: Add some tag for identifications
        },

    }).done();
    uploadOutput.public_url = generateS3ObjectPublicUrl(uploadOutput);
    return uploadOutput;
};

/**
 *  Upload file to S3 with tagged information to match with Lifecycle Expiry policy
 * @param filePathAndName
 * @param body
 * @param contentType
 * @param ACL
 * @param expireInDays
 * @returns {Promise<Object>} A promise that resolves with an object containing upload details and the file's public URL.
 */
const s3UploaderWithExpiry = async (filePathAndName, body, contentType, ACL, expireInDays = 1) => {
    filePathAndName = `${AUTO_CLEAN_S3_BUCKET}/${filePathAndName}`;
    sails.log.info(`Uploading to S3(${APP_ENV}) expiry: "${expireInDays}" path:`, filePathAndName);
    let tags = [
        { Key: 'APP_ENV', Value: APP_ENV },
        { Key: 'FILE_EXPIRY', Value: expireInDays.toString() },
    ];
    let uploadOutput = await new Upload({
        client: s3Client,
        params: {
            Bucket: INNDEX_S3_BUCKET,
            Key: filePathAndName,
            Body: body,
            ACL,
            ContentType: contentType,
        },
        tags: tags,
    }).done();
    uploadOutput.public_url = generateS3ObjectPublicUrl(uploadOutput);
    return uploadOutput;
};

const generateS3ObjectPublicUrl = ({Location, Key}, bucketName = '-') => {
    if (bucketName === S3_BUCKET && Key){
        return `https://${CDN_DOMAIN_HOST}/${Key}`;
    }
    else if (bucketName === INNDEX_S3_BUCKET && Key){
        return `https://${TMP_CF_DOMAIN_HOST}/${Key}`;
    }
    let {bucket, key} = s3urls.fromUrl(Location);
    if (bucket === S3_BUCKET && key){
        return `https://${CDN_DOMAIN_HOST}/${key}`;
    }
    else if (bucket === INNDEX_S3_BUCKET && key){
        return `https://${TMP_CF_DOMAIN_HOST}/${key}`;
    }
    sails.log.info(`falling back to S3 URL(${bucketName}) Key: ${Key}`);
    return Location;
};

/**
 *  Generate write path for an S3 Upload.
 *
 * @param ext
 * @param folderPrefix
 * @param category
 * @returns {{path: [], name: string}}
 */
const getWritePath = (ext, folderPrefix = null, category = null) => {
    let path = [];
    let name = `${uuid4()}-${(new Date()).getTime()}.${ext}`;
    if (APP_ENV !== 'production') {
        // not prod.
        path.push(APP_ENV);
    }
    if (folderPrefix) {
        path.push(folderPrefix.toString());
    }
    if (category) {
        path.push(category.toString());
    }
    path.push(name);
    return {
        name,
        path
    };
};

/**
 *  get list of all files into given Directory path.
 * @param dirPath
 * @returns {[]}
 */
const listDirFiles = (dirPath = '.') => {
    let files = fs.readdirSync(dirPath);
    return files.reduce((list, subPath) => {
        let fullPath = `${dirPath}/${subPath}`;
        if (fs.statSync(fullPath).isFile()) {
            list.push(fullPath);
        }
        return list;
    }, []);
};

/**
 *  get Image translations of given user_file
 *
 *  download file to fs if not exists,
 *  create tmp output folder, covert pdf into images,
 *  read output folder, and upload each image to S3,
 *  respond with uploaded URLs list.
 * @param {id, file_mime, fd, file_url, category}
 * @param defaultValue
 * @returns {Promise<[]>}
 */
const translateFileIntoImages = async ({id, file_mime, fd, file_url, category}, defaultValue = []) => {
    if(file_mime !== 'application/pdf' || (!fd && !file_url)){
        return defaultValue;
    }
    let downloaded = false;
    let dir = fs.mkdtempSync(path.join(os.tmpdir(), `inndex-${dayjs().format('YYYY-MM-DD')}-${id}-`));
    let file_path = fd;
    if(file_url){
        // get file onto tmp file system, for further processing
        let writePath = `${dir}/src-file.pdf`;
        if (!(await downloadUrlAt(writePath, file_url))) {
            // unlink tmp folder
            fs.rmSync(dir, {recursive: true});
            return defaultValue;
        }
        downloaded = true;
        file_path = writePath;
    }

    sails.log.info(`Generating image translation of(id:${id}): ${file_path}`);
    let stdOut = await poppler.pdfToCairo(file_path, `${dir}/${id}`, {
        // pngFile: true,
        jpegFile: true,
    });

    if(downloaded){
        fs.unlinkSync(file_path);
        sails.log.info(`Removed file(id:${id}): ${file_path} after processing`);
    }
    let files = listDirFiles(dir);
    sails.log.info(`Total ${files.length} image translation generated at: ${dir}`);

    // read output folder, and upload each image to S3
    let result = defaultValue;
    // @todo: spatel: below upload can be done in parallel
    for (let i = 0; i < files.length; i++) {
        let s3_path = getWritePath('jpg', `translations/${id}`, category);
        let body = fs.createReadStream(files[i]);
        let outcome = await s3Uploader(s3_path.path.join('/'), body, 'image/jpeg');
        result.push(outcome.public_url);
        fs.unlinkSync(files[i]);
    }

    // unlink tmp folder
    fs.rmSync(dir, {recursive: true});

    return result;
};

/**
 * get Country code of given project ID
 *
 * @param projectId
 * @param defaultValue
 * @returns {Promise<string|undefined>}
 */
const getCountryCodeOfProject = async (projectId, defaultValue = undefined) => {
    let projectInfo = await sails.models.project_reader.findOne({where: {id: projectId}, select: ['custom_field']});
    return (projectInfo && projectInfo.custom_field && projectInfo.custom_field.country_code) || defaultValue;
};
/**
 * list of all companies & given user's company as well
 * @deprecated: Instead of this use sql fn based: getUserInductionEmployer
 * @param requestedUserId
 * @param userCountryCode
 * @param country_code
 * @param projectId
 * @returns {Promise<{employerList: *, userEmployer: T}>}
 */
/*
    const getActiveEmployers = async ({id: requestedUserId, country_code: userCountryCode}, country_code, projectId=null) => {
    let userEmpInfo = null;
    if (projectId) {
        sails.log.info(`Get user employer for user ${requestedUserId} from induction of project ${projectId}`)
        let rawResult = await sails.sendNativeQuery(
            `SELECT
            additional_data->'employment_detail'->'employer' as user_employer
        FROM induction_request
        WHERE
             status_code = 2 AND
             project_ref = $1 AND
             user_ref = $2
             ORDER BY id DESC LIMIT 1`,
            [projectId, requestedUserId]
        );
        if (typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            userEmpInfo = {employer: rawResult.rows[0].user_employer};
        }

        sails.log.info(`User employer for user ${requestedUserId} from induction of project ${projectId}: ${(userEmpInfo && userEmpInfo.employer) ? userEmpInfo.employer : 'Not found'}`)
    }

    //if projectId not available or employer not available induction
    if(!userEmpInfo) {
        sails.log.info(`Get user employer for user ${requestedUserId} from user profile.`)

        userEmpInfo = await sails.models.userempdetail_reader.findOne({
            where: {
                user_ref: requestedUserId
            },
            select: ['employer']
        });
    }

    let employerList = await sails.models.createemployer_reader.find({
        where: (country_code ? {country_code} : {}),
        select: ['id', 'name', 'country_code']
    }).sort('name ASC');

    let userEmployer = (userEmpInfo && userEmpInfo.employer) ? (employerList || []).find(emp => (emp.name === userEmpInfo.employer) && (emp.country_code === userCountryCode)) : {};
    return {employerList, userEmployer};
};
*/
const mergePdfLambda = async (req, pdf_files, merged_file_name) => {
    try {
        const lambdaResult = await triggerLambdaFn(sails.config.custom.LAMBDA_MERGE_PDF_FN, {
            pdf_files,
            merged_file_name
        });
        sails.log.info("Lambda function invoked to merge PDFs.");
        return lambdaResult;
    } catch(failure) {
        sails.log.info('Failed to merge the pdf files.', failure);
        return ResponseService.errorObject('Failed to merge the pdf files.');
    }
}

const instantPdfGenerator = async (req, res, html, feature_name, filename, userAgent, chrome_setting = {format: 'A4'}, responseType='pdf', has_cover=false, has_one_page=false) => {
    sails.log.info(`Instant pdf generation started for feature: ${feature_name}, Has cover? ${has_cover}, responseType: ${responseType}`);
    sails.log.info(`Caller userAgent: ${userAgent}`);

    let uniqueFileName = `${uuid4()}-${(new Date()).getTime()}.pdf`;
    let s3UploadResponse = await s3UploaderWithExpiry(`${feature_name}/instant-pdf-${dayjs().valueOf()}.html`, html, 'text/html', 'public-read', 1);
    sails.log.info(`Uploaded file ${s3UploadResponse.key || s3UploadResponse.Key} on s3 in bucket ${s3UploadResponse.Bucket}.`);
    let htmlFilePath = (s3UploadResponse.Location || s3UploadResponse.location);
    try {
        let lambdaResult = await triggerLambdaFn(sails.config.custom.LAMBDA_SINGAL_PDF_FN, {
            s3_path: htmlFilePath,
            filename: (['url', 'path', 'pdfBuffer', 'pdf'].includes(responseType)) ? uniqueFileName : filename,
            feature_name,
            chrome_setting,
            has_cover,
            has_one_page
        });
        sails.log.info(`Instant pdf lambda result: ${feature_name}, `, lambdaResult.data);
        if(!lambdaResult.statusCode || lambdaResult.statusCode != 200) {
            if (responseType == 'pdfBuffer') {
                return ResponseService.errorObject(`${feature_name.toUpperCase()}: Failed to download the requested report. Please try again after sometime.`);
            }

            sails.log.info(`${feature_name.toUpperCase()}: Failed to download the requested report. Please try again after sometime.`);
            return ResponseService.errorResponse(res, `${feature_name.toUpperCase()}: Failed to download the requested report. Please try again after sometime.`);
        }

        //return pdf buffer
        if (responseType == 'pdfBuffer') {
            let pdfBuffer = await fetchS3UrlAsBuffer(lambdaResult.data.Location);
            return (pdfBuffer && pdfBuffer.data) ? pdfBuffer.data : '';
        }

        if (responseType == 'url') {
            return ResponseService.successResponse(res, { "location": lambdaResult.data.public_url, "name": `${filename}.pdf` })
        }

        if (responseType == 'path') {
            return { "location": lambdaResult.data.public_url, "name": `${filename}.pdf` }
        }

        let streamResponse = await fetchUrlAs(lambdaResult.data.Location, 'stream');
        if(!streamResponse.success){
            sails.log.info(`${feature_name.toUpperCase()}: Failed to fetch file using url as stream.`);
            return ResponseService.errorResponse(res, `${feature_name.toUpperCase()}: Failed to download the requested report. Please try again after sometime.`);
        };

        res.set('Content-Disposition', `attachment; filename=${encodeURIComponent(filename)}.pdf`);
        res.set('Content-Type', 'application/pdf');
        sails.log.info(`${feature_name}: Instant pdf has been generated.`);
        return streamResponse.data.pipe(res);
    } catch(failure) {
        sails.log.info('Failed to download the requested report.', failure);
        return ResponseService.errorResponse(res, 'Failed to find download the report.', failure);
    }
};

module.exports = {
    //To extract days and hours from ISO 8601 string
    extractDayAndHour: (isoDuration) => {
        // Regular expressions to match the day and hour components
        const dayRegex = /(\d+)D/;
        const hourRegex = /(\d+)H/;
        // Use RegExp.exec() to extract the values
        const dayMatch = dayRegex.exec(isoDuration);
        const hourMatch = hourRegex.exec(isoDuration);
        // Check if matches were found
        const day = dayMatch ? parseInt(dayMatch[1]) : 0;
        const hour = hourMatch ? parseInt(hourMatch[1]) : 0;
        return { day, hour };
    },

    uploadToS3: async (uploadedFile, fileName, fileCategory) => {
        return new Promise(async(resolve, reject) => {
            if(!['production', 'staging'].includes(APP_ENV)){
                if(!uploadedFile.fd){
                    return resolve(null);
                }
                // Copying file in .tmp folder to avoid need of sync task
                // Coping so that after service restarts same files can be preserved.
                let tmp_path = path.resolve(sails.config.appPath, '.tmp/public/uploads') + '/' + fileName;
                sails.log.info('Coping file', uploadedFile.fd);
                sails.log.info('To path', tmp_path);
                fs.copyFileSync(uploadedFile.fd, tmp_path);
                let baseUrl = sails.config.custom.baseUrl || '';
                let url = require('util').format('%s/uploads/%s', baseUrl, fileName);
                return resolve(url);
            }
            if(APP_ENV !== 'production'){
                fileCategory = `${APP_ENV}${fileCategory ? `/${fileCategory}` : ''}`;
            }
            if(fileCategory){
                fileName = `${fileCategory}/${fileName}`;
            }

            let body = '';
            if(uploadedFile.imageData) {
                let dataStr = uploadedFile.imageData.replace(/^data:image\/\w+;base64,/, "");
                body = Buffer.from(dataStr,'base64');
            } else {
                body = fs.createReadStream(uploadedFile.fd);
            }
            s3Uploader(fileName, body, uploadedFile.type).then(data => {
                try{
                    if(uploadedFile.fd) {
                        fs.unlinkSync(uploadedFile.fd);
                    }
                }catch(e){
                    sails.log.error('Failed to unlink file after s3 upload', e);
                }
                sails.log.info('Uploaded :', data.public_url);
                resolve(data.public_url);
            }).catch(err => {
                sails.log.info('S3 Upload failed ', err);
                reject(err);
            });
        });

    },

    deleteFileRecord: async (user_file_id, user_id, forceDelete = false) => {
        if (!forceDelete) {
            sails.log.info('Say, Deleted record..');
            return true;
        }
        sails.log.info('Delete file with force delete option.');

        let record = await sails.models.userfile.findOne({id: user_file_id});
        if(!record){
            sails.log.info('Failed to find file record')
            return false;
        }
        sails.log.info('File Record found',record);
        let deleteS3 = await deleteS3File(record.file_url);

        sails.log.info('Deleting record of User file',{user_file_id, user_id})
        try{
            await sails.models.userfile.destroy({
                id: user_file_id,
                //user_id
            })
        }catch(e){
            sails.log.info('Failed to delete User File record', e);
            return false;
        }
        sails.log.info('Deleted record')
        return true;
    },

    downloadZippedImages: async (req, res, fileUrls, fileName) => {
        sails.log.info('Invoking lambda fn to download zipped IMGs.')
        try {
            let lambdaResult = await triggerLambdaFn(sails.config.custom.LAMBDA_ZIPPED_IMGS_FN, {
                "S3fileUrls" : fileUrls,
                "fileName": fileName
            });
            sails.log.info(`Instant zip has been generated.`);
            return ResponseService.successResponse(res, {file_url: (lambdaResult.data.zipResult.public_url || lambdaResult.data.zipResult.Location)})
        } catch(failure) {
            sails.log.info('Something went wrong while calling zip lambda function.', failure);
            return ResponseService.errorResponse(res, 'Something went wrong while calling zip lambda function.', failure);
        }
    },

    mergingPdfs: async (req) => {
        let pdf_files = req.body.pdf_files;
        sails.log.info(`Merging pdf start for category "${req.body.file_category}" with files count "${pdf_files.length}"`);
        let merged_file_name = (req.body.merged_file_name).replace(/[^a-zA-Z0-9.]/g, '-');
        const uniqueFileName = `${uuid4()}-${(new Date()).getTime()}.pdf`;
        const resultObject = await mergePdfLambda(req, pdf_files, uniqueFileName);
        if(!resultObject.statusCode || resultObject.statusCode !== 200 || resultObject.errorType || !(resultObject.data.Location || resultObject.data.location)) {
            sails.log.info(`Failed to merge the PDFs. Uploaded pdfs should be valid one. ${resultObject.errorMessage || ''}`);
            return ResponseService.errorObject(`Failed to merge the PDFs. Uploaded pdfs should be valid one.`);
        }
        let responseBody = resultObject.data;
        const public_url = responseBody.public_url || responseBody.Location || resultObject.data.location;
        sails.log.info(`Received merged PDF from lambda fn, path: ${public_url}`);
        let record = {
            file_url : public_url,
            file_mime : 'application/pdf',
            name: merged_file_name,
            user_id: req.user.id,
            category: req.body.file_category
        };

        let user_file = await sails.models.userfile.create(record);
        sails.log.info('User File record', user_file.id);
        return user_file;
    },
    mergePdfsViaUrls: async (req, res, pdf_files, merged_file_name) => {
        sails.log.info(`Merging pdf start with files count "${pdf_files.length}"`);
        sails.log.info(`Merging pdf files ${pdf_files}`);
        const uniqueFileName = `${uuid4()}-${(new Date()).getTime()}.pdf`;
        // TODO: @rugved change later for custom file name support
        const resultObject = await mergePdfLambda(req, pdf_files, uniqueFileName );
        if(!resultObject.statusCode || resultObject.statusCode !== 200 || resultObject.errorType || !(resultObject.data.Location)) {
            sails.log.info(`Failed to merge the PDFs. Uploaded pdfs should be valid one. ${resultObject.errorMessage || ''}`);
            return ResponseService.errorObject(`Failed to merge the PDFs. Uploaded pdfs should be valid one.`);
        }
        let responseBody = resultObject.data;
        const public_url = responseBody.public_url || responseBody.Location;
        sails.log.info(`Received merged PDF from lambda fn, path: ${public_url}`);
        return { "location": public_url, "name": `${merged_file_name}.pdf` };
    },

    downloadPdfViaGenerator: async ({req, res, html, tool, file_name, heading_line, project_line, date_line, logo_file, use_inndex_logo = false, additional_line='', has_cover=false, has_one_page=false, responseType='', ellipses_heading=false}) => {
        sails.log.info(`Preparing chrome setting to call instantPdfGenerator() fn.`);
        let settings = {
            format: 'A4',
            displayHeaderFooter: true,
            headerTemplate: await DataProcessingService.getPdfHeaderTemplate(heading_line, project_line, date_line, logo_file, additional_line, ellipses_heading, use_inndex_logo),
            footerTemplate: await sails.renderView(`pages/form/v2/global-pdf-footer`, { layout: false }),
            scale: 0.9,
            margin:{
                top: "100px",
                bottom: "30px"
            }
        };
        return await instantPdfGenerator(req, res, html, tool, file_name, req.headers['user-agent'], settings, responseType, has_cover, has_one_page);
    },

    instantPdfGenerator,
    s3Uploader,
    getWritePath,
    translateFileIntoImages,
    deleteS3File,
    tempDirPath,
    s3UploaderWithExpiry,
    getCountryCodeOfProject,

    rectifyDateFormat: (input_date) => {
        const BOOKING_DATE_FORMAT = 'DD-MM-YYYY';
        let selectedDate = (input_date || '').toString().trim();
        let request_date = moment(selectedDate, BOOKING_DATE_FORMAT, true);
        sails.log.info(`provided date is ${selectedDate}`);
        let fallback_formats = ['DD-MM-YY', 'DD/MM/YYYY', 'DD/MM/YY'];
        if(request_date.isValid()){
            return {
                error: false,
                input_date: selectedDate,
            }
        }
        // Workaround fix to support existing mobile app.
        // @deprecated: This IF block logic can be removed after 1st Sept.
        for (let i = 0; i < fallback_formats.length; i++) {
            // sails.log.info(`matching ${selectedDate} with format ${fallback_formats[i]}`);
            let fallback_format = moment(selectedDate, fallback_formats[i], true);
            if (fallback_format.isValid()) {
                sails.log.warn(`Fallback logic for existing app user, payload had: ${selectedDate}, translated to : ${fallback_format.format()}`);
                request_date = fallback_format;
                selectedDate = fallback_format.format(BOOKING_DATE_FORMAT);
                return {
                    error: false,
                    input_date: selectedDate,
                }
            }
        }


        return {
            error: true,
            input_date: selectedDate,
        }
    },

    scheduleEventBridgeCron: async (id, tool_key, target, expression, timezone, sch_name) => {
        sails.log.info(`Triggering ${LAMBDA_EB_CRON_SCHEDULER} to schedule ${sch_name} ${tool_key} event. record id ${id} expression ${expression}`);
        // "feature_name": "rams",
        // "target_payload": { "record_id": 22 }, //Project ID
        // "sch_name": "asite_project",
        // "sch_expression": "rate(5 minutes)", // cron(* * * * * *)
        // "sch_timezone": "Europe/London",
        // "target_lambda_fn_arn" : "LAMBDA_TEST_EMAIL_FN"
        let payload = {
            "feature_name": tool_key,
            "target_payload": { "record_id": +id, timezone, tool_key },
            "sch_name": sch_name,
            "sch_expression": expression,
            "sch_timezone": timezone,
            "target_lambda_fn_arn" : target,
            "update": false,
            "enabled": true
        };

        return  {
            success,
            statusCode,
            data
        } = await triggerLambdaFn(LAMBDA_EB_CRON_SCHEDULER, payload);
    },

    deleteEventBridgeScheduleCron: async (id, feature_name, sch_name) => {
        sails.log.info(`Triggering ${LAMBDA_EB_CRON_DELETE} to delete ${sch_name} ${feature_name} event. record id ${id}`);
        // "feature_name": "rams",
        // "target_payload": { "record_id": 22 }, //Project ID
        // "sch_name": "asite_project",

        // `${requestBody.sch_name}_${requestBody.target_payload.record_id}_${requestBody.feature_name}_${APP_ENV}`
        let payload = {
            "feature_name": feature_name,
            "target_payload": { "record_id": +id },
            "sch_name": sch_name
        };

        return  {
            success,
            statusCode,
            data
        } = await triggerLambdaFn(LAMBDA_EB_CRON_DELETE, payload);
    },

    scheduleEvent: async (id, feature_name ,target, dueDate, action, updateExisting=false) => {
        sails.log.info(`Triggering ${LAMBDA_ONE_TIME_EVENT_SCHEDULER} to schedule ${action} ${feature_name} event. record id: ${id}`);

        let payload = {
            "feature_name": feature_name,
            "target_payload": { "record_id": +id },
            "action": action,
            "due_date_epoch": +dueDate,
            "target_lambda_fn_arn" : target
        }
        if (updateExisting) {
            payload.updateExistingEvent = true;
        }
        return ({
            success,
            statusCode,
            data
        } = await triggerLambdaFn(LAMBDA_ONE_TIME_EVENT_SCHEDULER, payload));
    },

    extractPlatformVersion: (platform, versionPattern) => {
        if (!platform) return false; // Handle missing values
    
        // Extract version from the string
        const versionMatch = platform.match(/\b(\d+\.\d+\.\d+)\b/);
        if (!versionMatch) return false; // Return false if no version is found
    
        const version = versionMatch[1]; // Extracted version string
        sails.log.info("Extracted Version:", version);
    
        // Regex to validate versions 
        return versionPattern.test(version);
    }
};
