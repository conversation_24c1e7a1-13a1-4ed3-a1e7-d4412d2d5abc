/**
 * Created by spatel on 9/4/19.
 */
const displayDateFormat = 'DD-MM-YYYY';
const dbDateFormat = 'YYYY-MM-DD';
const meterToMile = 0.000621371;
const dayjs = require('dayjs');
const { v4: uuid4 } = require('uuid');
const moment = require('moment');
const momentTz = require('moment-timezone');
const HttpService = require('./HttpService');
const nth = require('lodash/nth');
const get = require('lodash/get');
const _groupBy = require('lodash/groupBy');
const _uniq = require('lodash/uniq');
const {
    EVENT_TYPE,
    VALID_SOURCES,
} = require('./ServiceConstants');

const {
    fall_back_timezone,
    dbDateFormat_YYYY_MM_DD,
    dbDateFormat_YYYY_MM_DD_HH_mm_ss,
    EMAIL_NOTIFICATION_FEATURE_CODES,
    COMPANY_SETTING_KEY,
    PROJECT_SETTING_KEY: {INDUCTION_SLOTS_INFO, MANDATORY_COMPETENCY_EXCEPTION_USERS},
    FEATURES: { PERMIT_REGISTER }
} = sails.config.constants;
const {
    ToolBriefingsValidator: {
        validateToolBriefing
    }
} = require('../validators');
const {
    errorObject,
} = require('./ResponseService');
const {
    companyFn,
    inductionFn: {
        getInductionEmployerByUserIds,
    },
    toolBriefingsFn: {
        BRIEFING_TOOL_KEY
    }
} = require('./../sql.fn');

const {allProjectAdminsByOneOfDesignations, allResourceAdminsWithOneOfDesignations, filterProjectUsersEmailEligibility} = require('./TokenUtil');
const {sendMail, sendRawEmail, queueEmailNotifications,} = require('./EmailService');
const {instantPdfGenerator, downloadPdfViaGenerator} = require('./SharedService');
const {getCardsInfo} = require('./CSCSService');
const {updateOrCreateWeatherLog,DEFAULT_COUNTY_CODE_GB} = require('./WeatherSyncService');
const { getInspectionToursFn } = require('../sql.fn/inspection.fn');
const { profileHealthAssessmentExcluded, profileMedAssessmentExcluded } = require('./FeatureExclusionUtil');
const _get = require("lodash/get");

const buildCloseCallStatus = (code) => {
    if(code === 1) {
        return 'Open';
    } else if(code === 2){
        return 'Closed Out';
    }
};

const getUserFullName = (user = {}, withoutMiddleName = false) => {
    if(withoutMiddleName){
        return `${user.first_name ? user.first_name : ''}${user.last_name ? ' ' + user.last_name : ''}`.trim();
    }
    return `${user.first_name ? user.first_name : ''}${user.middle_name ? ' ' + user.middle_name : ''}${user.last_name ? ' ' + user.last_name : ''}`.trim();
};

const getUserFirstName = (user = {}) => {
    return `${user.first_name ? user.first_name : ''}`.trim();
};

const buildCompanyInductionStatus = (_this) => {
    const code = _this.status_code;
    if(code === 3){
        _this.status_message = 'Rejected';
    }
    else if(code === 2 || (_this.expire_on && (new Date()).getTime() > +_this.expire_on)){
        _this.status_code = 2;
        _this.status_message = 'Expired';
    }
    else if(code === 1){
        _this.status_message = 'Active';
    }
    return _this;
};

const buildRecordRef = (values) => {
    return `${(values.project_ref && values.project_ref.id) || values.project_ref}-${values.record_id}`
};

/**
 * Pass `project_info` object in input params, that it will parse timezone from custom_field
*/

const getProjectTimezone = (project) => {
    return (project && project.custom_field && project.custom_field.timezone) || fall_back_timezone;
};

const getFilteredInductionBySearch = async (projectId, text = '') => {
    if(!text){
        // no filter needed
        return [];
    }
    sails.log.info(`search inductions, project: ${projectId} text: ${text}`);
    let rawResult = await sails.sendNativeQuery(`SELECT
       id,
       creator_name,
       optima_badge_number::numeric,
       user_ref
    FROM induction_request
    WHERE project_ref = $1 AND
          (
              creator_name ilike $2
              );`, [projectId, `%${text}%`]);
    let induction_requests = (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];

    sails.log.info(`got search result for inductions, project: ${projectId} text: ${text}, count:`, induction_requests.length);

    return induction_requests;
};


const attachProfilePicWithUserRefInfo = async (data_list, reference_key = 'user_ref') => {

    let usersProfilePicIds = Object.values((data_list || []).reduce((ids, record) => {
        if (record[reference_key] && record[reference_key].profile_pic_ref) {
            ids[record[reference_key].id] = record[reference_key].profile_pic_ref;
        }
        return ids;
    }, {}));
    sails.log.info('profile pics to expand, count:', usersProfilePicIds.length);
    if(usersProfilePicIds.length){
        let usersProfilePicSrc = await sails.models.userfile_reader.find({
            where: {id: usersProfilePicIds},
            select: ['id', 'file_url', 'sm_url']
        });

        let usersProfilePicSrcSet = (usersProfilePicSrc || []).reduce((obj, record) => {
            obj[record.id] = record;
            return obj;
        }, {});
        sails.log.info('attaching profile pics');

        return (data_list || []).map(record => {
            if (record[reference_key] && record[reference_key].profile_pic_ref) {
                let pic_file_id = record[reference_key].profile_pic_ref;
                record[reference_key].profile_pic_ref = usersProfilePicSrcSet[pic_file_id] || null;
            }
            return record;
        });
    }
    return data_list;
};

const attachProfilePicWithUsersInfo = async (users = [], usersProfilePicIds = [], extractIds = false) => {
    if(extractIds){
        usersProfilePicIds = Object.values((users || []).reduce((ids, record) => {
            if (record.profile_pic_ref) {
                ids[record.id] = record.profile_pic_ref;
            }
            return ids;
        }, {}));
    }
    if(!usersProfilePicIds || !usersProfilePicIds.length){
        return users;
    }
    let usersProfilePicSrc = await sails.models.userfile_reader.find({
        where: {id: usersProfilePicIds},
        select: ['id', 'file_url', 'sm_url']
    });

    sails.log.info('Number of user profile pic found ', usersProfilePicSrc.length);

    (users || []).map(user => {
        if (user.profile_pic_ref) {
            user.profile_pic_ref = usersProfilePicSrc.find(file => file.id === user.profile_pic_ref);
        }
        return user;
    });
    return users;
};

const convertDecimalHrToDuration = (duration_hrs, return_duration = true, defaultValue = null) => {
    if(duration_hrs){
        let hrs = parseInt(duration_hrs);
        let min = Math.round((Number(duration_hrs)-hrs) * 60);
        if(return_duration){
            return (hrs*60*60) + (min*60);
        }
        return hrs+':'+min;
    }
    return defaultValue !== undefined ? defaultValue : duration_hrs;
}

const sortBadgeLogsByDay = (logs = [], columnName = 'day_of_yr') => {
    return logs.sort((a, b) => (moment(b[columnName], dbDateFormat).diff(moment(a[columnName], dbDateFormat))));
};

/**
 *
 * @param day_of_yr_today
 * @param logs
 * @param now
 * @returns {*|null|{clock_in}}
 */
const eventOfToday = (day_of_yr_today, logs = [], now = moment()) => {
    let log_of_today = logs.find(l => l.day_of_yr === day_of_yr_today);
    if (!log_of_today) {
        let previous_day = moment(day_of_yr_today, dbDateFormat).subtract(1, 'd').format(dbDateFormat);
        let log_of_previous_day = logs.find(l => l.day_of_yr === previous_day);
        if (log_of_previous_day && log_of_previous_day.clock_in) {
            let hrs_since_started = now.diff(moment.unix(+log_of_previous_day.clock_in), 'h');
            if (hrs_since_started <= 16) {
                return log_of_previous_day;
            }
        }
    }
    return log_of_today || null;
};

const isFirstAider = (user_docs) => {
    let first_aid_doc_names = ['first aid', 'emergency first aid'];
    return (user_docs || []).find(doc => doc && doc.id && doc.name && first_aid_doc_names.includes((doc.name || '').toString().toLowerCase().trim()));
};

const isMentalHealthFirstAider = (user_docs) => {
    return (user_docs || []).find(doc => doc && doc.id && doc.name && doc.name.toString().toLowerCase().trim() === 'mental health - first aid');
};

const haveSMSTSOrSSSTSDoc = (user_docs) => {
    return (user_docs || []).find(doc =>
        doc &&
        doc.id &&
        doc.name &&
        (
            doc.name.toString().toLowerCase().trim() === 'sssts' ||
            doc.name.toString().toLowerCase().trim() === 'smsts'
        )
    );
};

const haveFireMarshal = (user_docs) => {
    let file_marshals = ['fire marshal', 'fire marshal / fire warden'];
    return (user_docs || []).find(doc =>
        doc &&
        doc.id &&
        doc.name &&
        (
            file_marshals.includes(doc.name.toString().toLowerCase().trim())
        )
    );
};

const havePts = (user_docs) => {
    return (user_docs || []).find(doc =>
        doc &&
        doc.id &&
        doc.name &&
        (
            doc.name.toString().toLowerCase().trim() === 'pts'
        )
    );
};

const haveCSCS = (user_docs) => {
    return (user_docs || []).find(doc =>
        doc &&
        doc.id &&
        doc.name &&
        (
            doc.name.toString().toLowerCase().trim() === 'cscs'
        )
    );
};

const getTotalTravelDuration = (travel_time = {}, sufix = null) => {
    let minutes = [];
    if(travel_time && travel_time.to_work){
        let d = moment.duration(travel_time.to_work);
        if(moment.isDuration(d)) {
            minutes.push(d.asMinutes())
        };
    }
    if(travel_time && travel_time.to_home){
        let d = moment.duration(travel_time.to_home);
        if(moment.isDuration(d)) {
            minutes.push(d.asMinutes())
        };
    }
    let t = minutes.length ? minutes.reduce((sum, m) => sum + m, 0) : null;
    if(sufix && t !== null){
        return `${t} ${sufix}`;
    }
    return t;
};

const getTotalTime = (duration_in_sec, travel_time, return_duration = false) => {
    let d = [];
    if(duration_in_sec){
        d.push(moment.duration(+duration_in_sec, 'seconds'));
    }
    let travelMinutes = getTotalTravelDuration(travel_time);
    if(travelMinutes){
        d.push(moment.duration(+travelMinutes, 'minutes'));
    }
    if(d.length){
        let total = d.reduce((sumDuration, d) => sumDuration.add(d), moment.duration());
        if(moment.isDuration(total)){
            return return_duration ? total : (moment().startOf('day').add(total).format('HH:mm:ss'));
        }
    }

    return null;
};

const getTotalTravelDistance = (active_travel_time = {}) => {
    let a = ((active_travel_time.to_work_dm || {}).distance) || {};
    let b = ((active_travel_time.to_home_dm || {}).distance) || {};
    if(a.value && b.value){
        return +(((+a.value + +b.value) / 1000 ).toFixed(2));
    }
    return null;
};

const showDuration = (duration_in_sec) => {
    return moment().startOf('day').add(moment.duration(+duration_in_sec, 'seconds')).format('HH:mm:ss')
};

/**
 * @param seconds
 * @param unit
 */
const secondsToHuman = (seconds = 0, unit = false) => {
    let duration = dayjs.duration(seconds, 'seconds');
    let n = duration.asHours().toFixed(2).replace(/\.00$/, '');
    return unit ? `${n}h` : Number(n);
};

const showDurationAsHours = (duration_in_sec, hours_only = false, minuteSuffix = 'minutes') => {
    let d = moment.duration(+duration_in_sec, 'seconds');
    let str = [];
    if(moment.isDuration(d)){
        let m = d.minutes();
        d = d.subtract(m, 'minutes');
        let h = Math.round(d.asHours());
        if(h){
            if(hours_only){
                return `${h}`;
            }
            str.push(`${h} hr`);
        }
        if(m){
            str.push(`${m} ${minuteSuffix}`);
        }
    }
    return str.join(' ');
};

const getUserVerifiableUserDocuments = async (user_docs, user_ref, country_code, only_unverified = false) => {
    let names = _uniq(user_docs.filter(d => (!only_unverified || (only_unverified && !d.is_verified))).map(d => d.name));
    sails.log.info(`Looking for meta data for scheme-id of competencies from user: ${user_ref}, names`, names);
    if (!names.length) {
        sails.log.info(`user: ${user_ref}, got 0 non-verified competencies, skipping`);
        return [];
    }
    let meta_competencies = await sails.models.competencies_reader.find({
        select: ['id', 'name', 'tags', 'scheme_id'],
        where: {
            name: names,
            country_code: country_code,
            scheme_id: { '!=': null},
        }
    });
    let verifiable_user_documents = meta_competencies.reduce((list, meta) => {
        let documents = user_docs.filter(d => d.name === meta.name);
        documents.map(d => {
            list.push({
                ...d,
                scheme_id: meta.scheme_id,
                _extra_set: {
                    _id_ref: d.id,  // just for reverse mapping into bulkValidateUserDocuments
                }
            });
        })
        return list;
    }, []);
    sails.log.info(`Total ${verifiable_user_documents.length} verification enabled competencies into user: "${user_ref}" profile`);
    return verifiable_user_documents;
};

const bulkValidateUserDocuments = async (user_docs, user, verifiable_documents, parent_companies, update_back_profile = true) => {
    sails.log.info(`[CSCS] validating via parent companies: ${parent_companies}, if CITB config exists`);
    let [active_company_setting] = await companyFn.getCompanySettingsByName(parent_companies, COMPANY_SETTING_KEY.CSCS_CONFIG);
    if(!active_company_setting || !active_company_setting.company_ref){
        return user_docs;
    }
    sails.log.info(`[CSCS] validating total:${verifiable_documents.length} documents of user:${user.id} using ${active_company_setting.company_ref} CITB configs`);

    try {
        let verification_results = await getCardsInfo(active_company_setting.company_ref, verifiable_documents, user.last_name);
        // sails.log.info(`verification results are:`, JSON.stringify(verification_results, null, 2));

        let update_back_user_docs = [];
        // Need to use Loop -> Loop, to support multiple competencies of same name, by different document # case.
        for (let i = 0; i < verification_results.length; i++) {
            let {success: api_call_success, cardInfo, doc_number, _id_ref} = verification_results[i] || {};
            if(!api_call_success){
                sails.log.info(`[CSCS] response was NOT successful, skipping index:${i}, _id_ref: ${_id_ref}, doc_number: ${doc_number}`);
                continue;
            }
            // _id_ref is being attached via `_extra_set`
            let doc_index = user_docs.findIndex(document_row => (api_call_success && _id_ref === document_row.id && doc_number === document_row.doc_number));
            if(doc_index > -1){
                user_docs[doc_index].verification_info = cardInfo;
                user_docs[doc_index].is_verified = (cardInfo && cardInfo.isValid) ? 1 : 0;
                if (cardInfo && cardInfo.dateOfExpiry) {
                    user_docs[doc_index].expiry_date = dayjs(cardInfo.dateOfExpiry, dbDateFormat_YYYY_MM_DD).valueOf();
                }
                sails.log.info(`[CSCS] updating user document "${user_docs[doc_index].name}" id: ${user_docs[doc_index].id} number:"${user_docs[doc_index].doc_number}" with validation result:`, user_docs[doc_index].is_verified);
                let document_row = user_docs[doc_index];
                update_back_user_docs.push({
                    id: document_row.id,
                    verification_info: document_row.verification_info,
                    is_verified: document_row.is_verified,
                    expiry_date: document_row.expiry_date,
                });
            }

        }

        if (update_back_profile && update_back_user_docs.length) {
            for (let i = 0; i < update_back_user_docs.length; i++) {
                let {verification_info, is_verified, expiry_date} = update_back_user_docs[i];
                await sails.models.userdoc.updateOne({
                    id: update_back_user_docs[i].id,
                }).set({
                    verification_info,
                    is_verified,
                    expiry_date,
                });
            }
            sails.log.info(`updated back ${update_back_user_docs.length} user documents with validation result`);
        }
    }catch (e) {
        sails.log.info(`[CSCS] bulk user document validation failed, user:${user.id} error:`, e);
    }
        return user_docs;
};

const attachUserDocument = async (induction, {user_ref, country_code, last_name, parent_company}, validate = false, update_back_profile = false, update = false, exclude_expired = true) => {
    sails.log.info(`Attaching user doc got validate:${validate}, update_back_profile: ${update_back_profile} inputs:`, {user_ref, country_code, last_name, parent_company});
    if(induction.user_doc_ids && induction.additional_data){
        let doc_ids = _uniq((induction.user_doc_ids || []).map(id => HttpService.typeOf(id, 'object') ? id.id : (id && +id)));
        sails.log.info(`Attaching user doc to induction, ids: ${doc_ids}, exclude_expired: ${exclude_expired}`);
        let expired_filter = {
            expiry_date: {'>': moment().valueOf()},
            is_deleted: {'!=': 1}
        };
        let user_docs = await sails.models.userdoc_reader.find({
            id: doc_ids,
            // parent_doc_ref: null, // get only parent docs
            ...(exclude_expired ? expired_filter : {})
        });
        user_docs = await populateDocumentChildren(user_docs, (exclude_expired ? expired_filter : {}));
        user_docs = await (expandUserDocFiles(user_docs, (expandErr, expandedList) =>  expandedList || user_docs));

        if (validate) {
            let verifiable_user_documents = await getUserVerifiableUserDocuments(user_docs, user_ref, country_code);
            if (verifiable_user_documents.length) {
                user_docs = await bulkValidateUserDocuments(user_docs, {id: user_ref, last_name}, verifiable_user_documents, [parent_company], update_back_profile);
            }
        }

        let additional_data = {
            ...(induction.additional_data || {}),
            user_docs,
        };
        if(update){
            return await sails.models.inductionrequest.updateOne({id: induction.id}).set({additional_data});
        }
        induction.additional_data = additional_data;
    }else{
        sails.log.info('No user doc ids Or additional data to attach a copy', induction.user_doc_ids);
    }
    return induction;
};

const documentNotExpired = (document, now_ms) => (document.expiry_date && +document.expiry_date > now_ms);

const documentNotDeleted = document => (document.is_deleted !== 1);

const expandUserDocs = async (induction_requests = []) => {
    // reduce array to get user doc ids
    // get those user docs
    // process all records to replace user doc id reference with actual user doc model
    sails.log.info(`expanding user doc, from existing clone`);
    return induction_requests.map(ir => {
        let saved_docs = (ir.additional_data && ir.additional_data.user_docs) || ir.user_docs || [];
        let now_ms = moment().valueOf();
        ir.user_doc_ids = saved_docs.reduce((list, document) => {
            if(
                document &&
                documentNotExpired(document, now_ms) &&
                (documentNotDeleted(document))
            ){
                list.push(document);
                let children = (document.children || []).filter(c => documentNotExpired(c, now_ms) && documentNotDeleted(c));
                if(children.length){
                    list.push(...children);
                }
            }
            return list;
        }, []);
        return ir;
    })
/*
    let all_user_doc_ids = induction_requests.reduce((ids, record) => {
        if (record.user_doc_ids && record.user_doc_ids.length) {
            ids.push(...record.user_doc_ids);
        }
        return ids;
    }, []);
    all_user_doc_ids = _.uniq(all_user_doc_ids);
    sails.log.info(`Fetching user doc ids, list count: ${all_user_doc_ids.length}`);

    try {
        if (all_user_doc_ids.length) {
            // no need to query if no document exists

            let user_docs = await sails.models.userdoc.find({
                id: all_user_doc_ids,
                // parent_doc_ref: null, // get only parent docs
                is_deleted: {'!=': 1},
                expiry_date: {'>': moment().valueOf()}
            }).populate('children', {
                where: {
                    is_deleted: {'!=': 1},
                    expiry_date: {'>': moment().valueOf()}
                }
            });
            // All children should be attached at linear level to work with existing consumers fo this fn
            induction_requests = induction_requests.map(record => {
                if (record.user_doc_ids) {
                    //sails.log.info(`Expanding user doc ids for ${record.id}: `, record.user_doc_ids);
                    record.user_doc_ids = record.user_doc_ids.reduce((list, user_doc_id) => {
                        let document = user_docs.find(doc => doc.id === user_doc_id);
                        if(document){
                            let children = (document.children || []);
                            list.push(document);
                            if(children.length){
                                list.push(...children);
                            }
                        }
                        return list;
                    }, []);
                }
                return record;
            });
        }
    } catch (e) {
        sails.log.info('Failed to fetch document records', e);
        return cb(e, null);
    }
    return cb(null, induction_requests);*/
};

const getInductionBlockReason = ({comments = []}) => {

    let blocking_comments = (comments || []).filter(comment => (
        comment.module && (comment.origin === 'system' || comment.origin === 'admin') &&
        ['conduct-card-assign', 'competency-expiry', 'access-change'].includes(comment.module)
    )).sort((a, b) => a.timestamp - b.timestamp);
    // sails.log.info(comments, JSON.stringify(blocking_comments, null,2))
    let comment = (blocking_comments.length) ? blocking_comments.pop() : null;

    let header = 'No reason available of blocking';
    let reason = '-';
    if(comment){
        header = `${comment.note} ${dayjs(comment.timestamp).format(displayDateFormat)}`;
        reason = comment.module;
    }

    return {
        reason,
        header,
        comment,
    };
};

const stringCompare = (str1, str2) => {
    return (str1 || '').toString().toLowerCase().trim() !== (str2 || '').toString().toLowerCase().trim();
};

const numberCompare = (num1, num2) => {
    return (+num1 || 0) !== (+num2 || 0);
};

const compareObjectFields = (old_details, new_details, strings_to_compare, number_to_compare, objects_to_compare, contact_number_to_compare) => {
    let changed = [];
    for (let i = 0; i < (strings_to_compare || []).length; i++) {
        if (stringCompare(old_details[strings_to_compare[i]], new_details[strings_to_compare[i]])) {
            changed.push({
                key: strings_to_compare[i],
                from: old_details[strings_to_compare[i]],
                to: new_details[strings_to_compare[i]]
            });
        }
    }
    for (let i = 0; i < (number_to_compare || []).length; i++) {
        if (numberCompare(old_details[number_to_compare[i]], new_details[number_to_compare[i]])) {
            changed.push({
                key: number_to_compare[i],
                from: old_details[number_to_compare[i]],
                to: new_details[number_to_compare[i]]
            });
        }
    }
    for (let i = 0; i < (objects_to_compare || []).length; i++) {
        let old_id = (old_details[objects_to_compare[i]] && old_details[objects_to_compare[i]].id ? old_details[objects_to_compare[i]].id : old_details[objects_to_compare[i]]);
        let new_id = (new_details[objects_to_compare[i]] && new_details[objects_to_compare[i]].id ? new_details[objects_to_compare[i]].id : new_details[objects_to_compare[i]]);
        if (numberCompare(old_id, new_id)) {
            changed.push({
                is_object: true,
                key: objects_to_compare[i],
                from: old_details[objects_to_compare[i]],
                to: new_details[objects_to_compare[i]]
            });
        }
    }
    for (let i = 0; i < (contact_number_to_compare || []).length; i++) {
        if (old_details[contact_number_to_compare[i]] && new_details[contact_number_to_compare[i]] && (stringCompare(old_details[contact_number_to_compare[i]].code, new_details[contact_number_to_compare[i]].code) || stringCompare(old_details[contact_number_to_compare[i]].number, new_details[contact_number_to_compare[i]].number))) {
            changed.push({
                is_object: true,
                key: contact_number_to_compare[i],
                from: old_details[contact_number_to_compare[i]],
                to: new_details[contact_number_to_compare[i]]
            });
        }
    }

    return changed;
};

const compareListOfAssessmentAnswers = (old_details, new_details, ref_key, compare_key) => {
    let old = (old_details || []).reduce((out, k) => {
        let key = (k[ref_key] && k[ref_key].id) ? k[ref_key].id : k[ref_key];
        out[key] = k[compare_key];
        return out;
    }, {});
    let current = (new_details || []).reduce((out, k) => {
        let key = (k[ref_key] && k[ref_key].id) ? k[ref_key].id : k[ref_key];
        out[key] = k[compare_key];
        return out;
    }, {});

    let outcome = Object.keys(current).reduce((result, key) => {
        if(old[key] === undefined || old[key] === null){
            result.push({
                key: key,
                from: null,
                to: current[key],
                newly_added: true,
            })
        }
        if(stringCompare(old[key], current[key])){
            result.push({
                key: key,
                from: old[key],
                to: current[key]
            });
        }
        return result;
    }, []);
    Object.keys(old).map(key => {
        if(current[key] === undefined || current[key] === null){
            outcome.push({
                key: key,
                from: old[key],
                to: null,
                removed: true,
            })
        }
    });

    return outcome;
};

const modifiedPersonalDetails = (old_details, new_details, strings_to_compare = ['title', 'first_name', 'middle_name', 'last_name', 'dob', 'country', 'gender', 'nin'], objects_to_compare = ['profile_pic_ref']) => {
    let changed = compareObjectFields(old_details, new_details, strings_to_compare, [], objects_to_compare);
    if (changed.length) {
        sails.log.info('personal info modified', changed);
    }
    return changed;
};

const modifiedEmploymentDetails = (old_details, new_details, strings_to_compare = ['employer', 'job_role', 'type_of_employment', 'employment_company'], number_to_compare = ['start_date_with_employer', 'earn_mlw_e783']) => {
    let changed = compareObjectFields(old_details, new_details, strings_to_compare, number_to_compare);
    if (changed.length) {
        sails.log.info('employment info modified', changed);
    }
    return changed;
};

const modifiedContactDetails = (old_details, new_details, strings_to_compare = ['house_no', 'street', 'city', 'post_code', 'country', 'emergency_contact'], contact_number_to_compare = ['home_number', 'mobile_number', 'emergency_contact_number']) => {
    let changed = compareObjectFields(old_details, new_details, strings_to_compare, [], [], contact_number_to_compare);
    if (changed.length) {
        sails.log.info('contact info modified', changed);
    }
    return changed;
};

const isDocModifiedByUser = (old_documents, new_document) => {
    let old_doc = (old_documents || []).find(d => d && d.id === new_document.id);
    if(!old_doc){
        return [];
    }
    let doc_changes = compareObjectFields(old_doc, new_document, ['name', 'doc_number', 'expiry_date', 'description'], []);

    if((old_doc.children || []).length !== (new_document.children || []).length){
        doc_changes.push({
            key: `Sentinel Competency count`,
            from: old_doc.children.length,
            to: new_document.children.length
        });
    }
    // if((old_doc.user_files || []).length !== (new_document.user_files || []).length){
    //     doc_changes.push({
    //         key: `Image count`,
    //         from: old_doc.user_files.length,
    //         to: new_document.user_files.length
    //     });
    // }
    return doc_changes;
};

// Below constants should be in sync with `FE app/modules/common/induction-change-log/induction-change-log.component.ts`
const CHANGE_TYPES = {
    'profile.personal:updated': 'Personal Detail',
    'profile.contact:updated': 'Contact Detail',
    'profile.employment:updated': 'Employment Detail',
    'profile.health:updated': 'Health Assessment',
    'profile.medical:updated': 'Medical Assessment',
    'profile.doc:updated': 'Competency',
};

const CONTACT_DETAIL_LABELS = {
    'house_no': 'House No.',
    'street': 'Address Line',
    'city': 'City',
    'post_code': 'Postal Code',
    'country': 'Country',
    'home_no': 'Home Phone No.',
    'home_number': 'Home Phone No.',
    'mobile_no': 'Mobile No.',
    'mobile_number': 'Mobile No.',
    'emergency_contact': 'Emergency Contact',
    'emergency_contact_no': 'Emergency Contact No.',
    'emergency_contact_number': 'Emergency Contact No.',
};

const COMPETENCY_LABELS = {
    'name': 'Name',
    'doc_number': 'Document No.',
    'expiry_date': 'Expiry Date',
    'description': 'Description',
};

const notifyManagersAboutCompetencyChanges = async (user_name, {id, name: project_name}, induction, change_log) => {
    let project_managers = await allProjectAdminsByOneOfDesignations(id, ['nominated', 'custom']);
    project_managers = filterProjectUsersEmailEligibility(project_managers, 'induction');
    sails.log.info(`Sending email to nom managers, induction profile info update (${change_log.type}), count:`, project_managers.length);
    let induction_phrase_singlr = (induction.additional_data && induction.additional_data.project && induction.additional_data.project.custom_field && induction.additional_data.project.custom_field.induction_phrase_singlr) || '';
    let project_tz = getProjectTimezone(induction.additional_data && induction.additional_data.project);

    let subject = `${induction_phrase_singlr} info. updated: ${user_name} (${induction_phrase_singlr} Record: ${induction.record_id}) @ ${project_name}`;

    for (let i = 0; i < project_managers.length; i++) {
        let manager_ref = (project_managers[i] && project_managers[i].user_ref) || {};

        let emailHtml = await sails.renderView('pages/mail/mail-content', {
            title: subject,
            mail_body: 'induction-documents-updated',
            user_name,
            induction_phrase_singlr,
            record_id: induction.record_id,
            project_name: project_name || '',
            receiver_name: manager_ref.first_name,
            change_log,
            COMPETENCY_LABELS,
            CONTACT_DETAIL_LABELS,
            toDate: (n) => {
                return momentTz(+n).tz(project_tz).format(displayDateFormat);
            },
            isDocumentChange: (type) => {
                return type === 'profile.doc:updated';
            },
            is_in_review: induction.status_code === 6,
            changeTypeToLabel: (type) => {
                return CHANGE_TYPES[type] || type;
            },
            numberToYesNo: (num) => {
                if (+num === 1) {
                    return 'Yes';
                } else if (+num === 0) {
                    return 'No';
                } else if (num === null) {
                    return '-';
                }
                return num;
            },
            layout: false
        });
        sails.log.info('Sending mail to', manager_ref.email);
        await sendMail(subject, [manager_ref.email], emailHtml);
        sails.log.info('induction profile info updated email has been sent');
    }
};

const checkIfHeathOrMedicalEnabled = (contractor, exclusion_setting, project_country) => {
    let validatorList = {};
    if(exclusion_setting && project_country) {
        validatorList.activeExclusions = (exclusion_setting[project_country] || {}).exclude || [];
        validatorList.globalExclusions = (exclusion_setting['ALL'] || {}).exclude || [];
        validatorList.activeMandatoryItems = (exclusion_setting[project_country] || {}).mandatory || [];
        validatorList.globalMandatoryItems = (exclusion_setting['ALL'] || {}).mandatory || [];
    }

    let {enabled} = getPendingAssessmentStatus({}, contractor.features_status);
    sails.log.info(`[checkIfHeathOrMedicalEnabled] Check if feature status enabled health_answers_required: ${enabled.health_answers_required} medical_answers_required: ${enabled.medical_answers_required}`);

    let is_health_allowed = !(profileHealthAssessmentExcluded(validatorList)) && enabled.health_answers_required;
    let is_medical_allowed = !(profileMedAssessmentExcluded(validatorList)) && enabled.medical_answers_required;

    return { is_health_allowed, is_medical_allowed };
};

const updateUserInductionsWithProfileChanges = async (user, target = ['doc'], {
    user_docs,
    new_user,
    employment_detail,
    contact_detail,
    health_questions,
    health_assessments,
    medical_questions,
    medical_assessments,
} = {user_docs: []}, {verifiable_documents} = {verifiable_documents: []}) => {
    const user_ref = user.id;
    const user_name = getUserFullName(user);

    // get all inductions of user which are approved, pending
    let user_inductions = await sails.models.inductionrequest_reader.find({
        select: ['project_ref', 'id', 'record_id', 'status_code', 'additional_data', 'user_doc_ids', 'recent_changes', 're_review_logs'],
        where: {
            user_ref,
            status_code: [1, 2, 3, 4, 5, 6],
        }
    });
    user_inductions = await populateProjectRefs(user_inductions, 'project_ref', ['id', 'name', 'disabled_on', 'is_active', 'parent_company', 'custom_field']);
    let projectContractorIDs = _.uniq(user_inductions.map(i => +i.project_ref.parent_company));

    let projectContractors = await sails.models.createemployer_reader.find({
        where: { id: projectContractorIDs },
        select: ['features_status']
    });

    let record = await sails.models.inndexsetting_reader.findOne({ name: 'exclusion_by_country_code' });
    let exclusion_setting = (record && record.value) || {};

    // filter all active projects from them
    let active_inductions = user_inductions.filter(induction => (
        induction.project_ref &&
        !induction.project_ref.disabled_on &&
        induction.project_ref.is_active)
    );

    sails.log.info(`target: "${target}", induction record ids: ${active_inductions.map(i => i.id)}`);
    if(!active_inductions.length){
        return {count: active_inductions.length};
    }
    let all_valid_doc_ids = (user_docs || []).map(d => d && +d.id);

    if(target.includes('doc') && verifiable_documents && verifiable_documents.length) {
        let parent_companies = _uniq(user_inductions.map(i => (i.project_ref && i.project_ref.parent_company) || null).filter(id => +id));
        user_docs = await bulkValidateUserDocuments(user_docs, user, verifiable_documents, parent_companies, true);
    }

    let updated_records = [];

    for (let i = 0; i < active_inductions.length; i++) {
        let induction_row = active_inductions[i];
        let updating_induction = false;
        // update all user inductions with new documents.
        let additional_data = {
            ...(induction_row.additional_data || {}),
        };
        let recent_changes = (induction_row.recent_changes || []);

        let update_request = {};
        if(target.includes('doc')){
            sails.log.info(`processing induction: ${induction_row.id} for documents difference`);
            let change_log = {
                type: `profile.doc:updated`,
                creator_ref: user_ref,
                creator_name: user_name,
                timestamp: (new Date()).getTime(),
                changes: [],
            }

            let old_documents = ((induction_row.additional_data || {}).user_docs || []);
            user_docs.map(doc => {
                if(doc && !(induction_row.user_doc_ids || []).includes(doc.id)){
                    // All Newly added documents
                    change_log.changes.push({
                        entity: doc.name,
                        from: '',
                        to: '',
                        ref: doc.id,
                        sub_entity: null,
                        newly_added: true,
                    });
                }
                else if(doc && ((induction_row.user_doc_ids || []).includes(doc.id))){
                    let doc_changes = isDocModifiedByUser(old_documents, doc);
                    if(doc_changes.length){
                        // Updated existing document
                        let list = doc_changes.map(c => ({
                            entity: doc.name,
                            from: c.from,
                            to: c.to,
                            ref: doc.id,
                            sub_entity: c.key,
                            newly_added: false,
                        }));
                        change_log.changes.push(...list);
                    }
                }
            });

            let all_removed = (old_documents || []).filter((d) => {
                return !all_valid_doc_ids.includes(d.id);
            }).map(d => ({
                entity: d.name,
                from: null,
                to: null,
                ref: d.id,
                sub_entity: null,
                removed: true,
            }));
            change_log.changes.push(...all_removed);


            if(change_log.changes.length){
                sails.log.info(`processing induction: ${induction_row.id} change_log:`, change_log);
                recent_changes.push(change_log);

                additional_data = {
                    ...additional_data,
                    user_docs,
                };

                update_request = {
                    ...update_request,
                    user_doc_ids: all_valid_doc_ids,
                };

                updating_induction = true;
            }

        }

        // Below logic can be enabled to track profile changes related to this category
/*
        if (target.includes('personal')){
            sails.log.info(`processing induction: ${induction_row.id} for personal difference`);
            let old_user = ((induction_row.additional_data || {}).user_info || {});

            let change_log = {
                type: `profile.personal:updated`,
                creator_ref: user_ref,
                creator_name: user_name,
                timestamp: (new Date()).getTime(),
                changes: [],
            };
            let changes = modifiedPersonalDetails(old_user, new_user);
            change_log.changes = changes.map(c => {

                return {
                    entity: c.key,
                    from: c.from,
                    to: c.to,
                    ref: null,
                    sub_entity: null,
                    newly_added: false,
                }
            });

            if(change_log.changes.length){
                sails.log.info(`processing induction: ${induction_row.id} change_log:`, change_log);
                recent_changes.push(change_log);

                additional_data = {
                    ...additional_data,
                    user_info: new_user,
                };

                update_request = {
                    ...update_request,
                    creator_name: user_name,
                };

                updating_induction = true;
            }

        }

        if (target.includes('employment')){
            sails.log.info(`processing induction: ${induction_row.id} for employment difference`);
            let old_employment_detail = ((induction_row.additional_data || {}).employment_detail || {});

            let change_log = {
                type: `profile.employment:updated`,
                creator_ref: user_ref,
                creator_name: user_name,
                timestamp: (new Date()).getTime(),
                changes: [],
            };
            let changes = modifiedEmploymentDetails(old_employment_detail, employment_detail);
            change_log.changes = changes.map(c => {

                return {
                    entity: c.key,
                    from: c.from,
                    to: c.to,
                    ref: null,
                    sub_entity: null,
                    newly_added: false,
                }
            });

            if(change_log.changes.length){
                sails.log.info(`processing induction: ${induction_row.id} change_log:`, change_log);
                recent_changes.push(change_log);

                additional_data = {
                    ...additional_data,
                    employment_detail: employment_detail,
                };
                update_request = {
                    ...update_request,
                    employer_name: employment_detail.employer,
                    job_role: employment_detail.job_role,
                };
                updating_induction = true;
            }

        }
*/

        if (target.includes('contact')){
            sails.log.info(`processing induction: ${induction_row.id} for contact difference`);
            let old_contact_detail = ((induction_row.additional_data || {}).contact_detail || {});

            let change_log = {
                type: `profile.contact:updated`,
                creator_ref: user_ref,
                creator_name: user_name,
                timestamp: (new Date()).getTime(),
                changes: [],
            };
            let changes = modifiedContactDetails(old_contact_detail, contact_detail);
            change_log.changes = changes.map(c => {
                return {
                    entity: c.key,
                    from: c.from,
                    to: c.to,
                    ref: null,
                    sub_entity: null,
                    newly_added: false,
                }
            });

            if(change_log.changes.length){
                sails.log.info(`processing induction: ${induction_row.id} change_log:`, change_log);
                recent_changes.push(change_log);

                additional_data = {
                    ...additional_data,
                    contact_detail: contact_detail,
                };

                updating_induction = true;
            }

        }

        let contractor_id = induction_row.project_ref.parent_company;
        let contractor = (projectContractors || []).find(c => c.id === +contractor_id) || {};
        let project_country = (induction_row.project_ref.custom_field && induction_row.project_ref.custom_field.country_code) || DEFAULT_COUNTY_CODE_GB;
        let { is_health_allowed, is_medical_allowed } = checkIfHeathOrMedicalEnabled(contractor, exclusion_setting, project_country);
        sails.log.info(`[updateUserInductionsWithProfileChanges] Allowed stats induction ${induction_row.id} is_health_allowed: ${is_health_allowed} is_medical_allowed: ${is_medical_allowed}`);

        if (target.includes('health') && is_health_allowed){
            sails.log.info(`processing induction: ${induction_row.id} for health assessment difference`);
            let old_health_assessments = ((induction_row.additional_data || {}).health_assessment_answers || []);
            // sails.log.info(`health assessment`, health_assessments);


            let change_log = {
                type: `profile.health:updated`,
                creator_ref: user_ref,
                creator_name: user_name,
                timestamp: (new Date()).getTime(),
                changes: [],
            };
            let changes = compareListOfAssessmentAnswers(old_health_assessments, health_assessments, 'question_ref', 'answer');

            // sails.log.info(`health assessment`, JSON.stringify(changes, null, 2));
            change_log.changes = changes.map(c => {
                let question = (health_questions || []).find(r => r.id === +c.key) || {};
                return {
                    entity: question.question,
                    from: c.from,
                    to: c.to,
                    ref: question.id,
                    sub_entity: null,
                    newly_added: c.newly_added,
                    removed: c.removed,
                    field_type: question.type,
                }
            });

            if(change_log.changes.length){
                sails.log.info(`processing induction: ${induction_row.id} change_log:`, change_log);
                recent_changes.push(change_log);
                health_assessments = (health_assessments || []).map(row => {
                    row.question_ref = health_questions.find(hq => hq.id === row.question_ref) || row.question_ref;
                    return row;
                });

                additional_data = {
                    ...additional_data,
                    health_assessment_answers: health_assessments,
                };

                updating_induction = true;
            }


        }
        if (target.includes('medical') && is_medical_allowed){
            sails.log.info(`processing induction: ${induction_row.id} for medical assessment difference`);
            let old_medical_assessments = ((induction_row.additional_data || {}).medical_assessments_answers || []);
            // sails.log.info(`medical assessment`, medical_assessments);

            let change_log = {
                type: `profile.medical:updated`,
                creator_ref: user_ref,
                creator_name: user_name,
                timestamp: (new Date()).getTime(),
                changes: [],
            };
            let changes = compareListOfAssessmentAnswers(old_medical_assessments, medical_assessments, 'question_id', 'answer');

            // sails.log.info(`medical assessment`, JSON.stringify(changes, null, 2));
            change_log.changes = changes.map(c => {
                let question = (medical_questions || []).find(r => r.id === +c.key) || {};
                return {
                    entity: question.question,
                    from: c.from,
                    to: c.to,
                    ref: question.id,
                    sub_entity: null,
                    newly_added: c.newly_added,
                    removed: c.removed,
                    field_type: question.type,
                }
            });

            if(change_log.changes.length){
                sails.log.info(`processing induction: ${induction_row.id} change_log:`, change_log);
                recent_changes.push(change_log);

                additional_data = {
                    ...additional_data,
                    medical_assessments_answers: medical_assessments,
                };

                updating_induction = true;
            }

        }

        if(updating_induction){

            sails.log.info(`updating induction: ${induction_row.id}, current status was: ${induction_row.status_code}`);
            let updated_one = await sails.models.inductionrequest.updateOne({id: induction_row.id}).set({
                status_code: ([1,3,4,5].includes(induction_row.status_code)) ? induction_row.status_code : 6, // Move to `In Review` state
                additional_data,
                recent_changes,
                ...update_request,
            });

            updated_records.push({
                id: updated_one.id,
            });
            let project_info = {
                id: updated_one.project_ref,
                name: (additional_data.project && additional_data.project.name),
            };
            let last_change = recent_changes[recent_changes.length - 1];
            notifyManagersAboutCompetencyChanges(user_name, project_info, updated_one, last_change).catch(sails.log.error);
        }

    }

    return {count: updated_records.length};
};

const getProject = async (projectId, completeRow = false) => {
    try{
        let project = await sails.models.project.findOne({id: projectId});
        if(project && project.id){
            return completeRow ? project : project.created_by;
        }
    }catch(e){
        sails.log.error('Failed to get project record', e);
    }
    return null;
};

const populateInductionEmptySlots = async (project_ref, fromDayjs, project_timezone = fall_back_timezone, forDays = 30) => {
    // create min max dates
    let from = fromDayjs.startOf('day');
    let to = from.add(forDays, 'day').endOf('day');
    let induction_slots_info = await sails.models.projectsetting_reader.findOne({
        where: {project_ref: project_ref, name: INDUCTION_SLOTS_INFO},
    });
    if (!induction_slots_info || !induction_slots_info.id) {
        return false;
    }
    let slot_meta_per_day = (induction_slots_info.value || []).reduce((group, meta) => {
        if (!group[meta.weekday_num]) {
            group[meta.weekday_num] = [];
        }
        group[meta.weekday_num].push(meta);
        return group;
    }, {});

    sails.log.info(`populate induction slots project(${project_ref}) from/to`, from.format(dbDateFormat_YYYY_MM_DD), '', to.format(dbDateFormat_YYYY_MM_DD), 'tz:', project_timezone);
    // get existing slot list
    let all_slots = await sails.models.projectinductionslot_reader.find({
        select: ['location', 'slot_date_time', 'cutoff_seconds', 'total_slots', 'booked_slots'],
        where: {
            project_ref: project_ref,
            slot_date_time: {
                '>=': from.unix(),
                '<': to.unix()
            },
        }
    });
    sails.log.info('existing slot records count', all_slots.length);

    // get missing dates
    for (let i = from; i.isBefore(to); i = i.add(1, 'day')) {
        let settings_for_day = (slot_meta_per_day[i.day()] || []);
        if (!settings_for_day.length) {
            sails.log.info('skip for weekday', i.day(), i.format(dbDateFormat_YYYY_MM_DD));
            continue;
        }
        let slots_of_day = all_slots.filter(s => dayjs.unix(+s.slot_date_time).isSame(i, 'day'))

        // all settings for the day are in settings_for_day
        // all slots for same are in slots_of_day
        // we should have slots for all settings_for_day, if not create one
        for (let j = 0; j < settings_for_day.length; j++) {
            let set = settings_for_day[j];
            let d = momentTz.tz(i.format(dbDateFormat_YYYY_MM_DD), dbDateFormat_YYYY_MM_DD, project_timezone).startOf('day').add(set.start_time, 'h').utc();
            // sails.log.info('\nLooking for slot of', d.format(dbDateFormat_YYYY_MM_DD_HH_mm_ss), 'week day: ', d.day());

            let existing_slot = slots_of_day.find(s => (+s.slot_date_time === d.unix()) && s.location === set.location);
            if (!existing_slot || !existing_slot.id) {
                sails.log.info(`creating slot record for project: ${project_ref} slot`, d.format(dbDateFormat_YYYY_MM_DD_HH_mm_ss));
                // create slot record into DB
                let new_record = await sails.models.projectinductionslot.create({
                    project_ref: project_ref,
                    slot_date_time: d.unix(),
                    cutoff_seconds: (set.has_cutoff_time && set.cutoff_hours) ? (+(set.cutoff_hours) * 60 * 60) : 0,
                    location: set.location,
                    total_slots: set.total_slots,
                    booked_slots: 0,
                });
                all_slots.push(new_record);
            }

        }
    }
    return all_slots;
};
// populateInductionEmptySlots(3, dayjs(), fall_back_timezone, 10).then(console.log).catch(console.log)

const populateInductionEmptySlotsForAll = async () => {
    let projects = await sails.models.project_reader.find({
        select: ['name', 'custom_field'],
        where: {
            disabled_on: null,
            is_active: 1,
            project_category: 'default', // Only standard project will be searchable
        }
    });
    for (let i = 0; i < projects.length; i++) {
        let has_induction_booking_enabled = (projects[i].custom_field && projects[i].custom_field.induction_slot_booking);
        let tz = (projects[i].custom_field && projects[i].custom_field.timezone) || fall_back_timezone;
        if (has_induction_booking_enabled) {
            await populateInductionEmptySlots(projects[i].id, dayjs(), tz);
        }
    }
};

const sendInductionStatusChangeAlert = async (induction_request, currentUser, origin) => {
    let sendToIds = [];
    sails.log.info('Sending Status change alert for induction-request', induction_request.id, `origin: ${origin}`);
    let project = await getProject(induction_request.project_ref, true);
    let projUsrResult = [];
    if(!project){
        sails.log.info('Invalid project reference?');
        return false;
    }
    if(origin == 'admin' && induction_request.user_ref){
        // Notify Induction User
        sendToIds = [induction_request.user_ref];
    }
    else if(origin == 'user'){
        // Notify Admin & inductor
        if(induction_request.inductor_ref){
            // in case if inductor is null
            sendToIds = [induction_request.inductor_ref];
        }
        if(project && project.id){
            let adminsResult = await allProjectAdminsByOneOfDesignations(project.id, [ 'nominated', 'custom']);
            adminsResult = filterProjectUsersEmailEligibility(adminsResult, 'induction');
            projUsrResult = adminsResult.map(u=> u.user_ref);
            sails.log.info('nom managers, count:', projUsrResult.length);
        }
    }

    let sendToUsers = await sails.models.user_reader.find({id: sendToIds, is_active: 1, email_verified_on: { '!=': null}});
    if(projUsrResult.length) {
        sendToUsers = sendToUsers.concat(projUsrResult);
    }
    let len = (sendToUsers || []).length;
    sails.log.info('Total alerts to send', len);
    let second_last_comment = {};
    if (project.is_passport_require) {
        second_last_comment = nth(_.sortBy((induction_request.comments || []), ['timestamp']), -2);
        second_last_comment = (second_last_comment && second_last_comment.origin === origin) ? second_last_comment : {};
    }
    let has_medications = (induction_request.on_long_medication && induction_request.on_long_medication.toString().toLowerCase().trim() === 'yes');
    let currentUserAge = (currentUser.dob) ? dayjs().diff(currentUser.dob, 'year') : 0;
    let last_comment = _.last(_.sortBy((induction_request.comments || []), ['timestamp']));
    last_comment = last_comment.origin === origin ? last_comment : {};
    sails.log.info('Second Last Comment data is',  second_last_comment);
    sails.log.info('Last Comment data is',  last_comment);
    let inducPhrase = induction_request.additional_data.project.custom_field.induction_phrase_singlr || 'Induction';
    for (let i = 0; i < len; i++) {
        let toUser = sendToUsers[i];
        //  #${induction_request.id}
        let subject = `Status Updated: ${inducPhrase} Request for project "${project.name}"`;
        if(origin == 'user' && has_medications) { subject += ` ✚`; }
        if(origin == 'user' && currentUserAge < 18){ subject += ` ⓲`; }
        let html = await sails.renderView('pages/mail/induction-request-updated', {
            title: subject,
            user: currentUser,
            toUser: toUser,
            is_under_18: currentUserAge < 18,
            currentUserAge,
            dob: dayjs(currentUser.dob).format('DD-MM-YYYY'),
            origin,
            project,
            has_medications: has_medications,
            induction_request: induction_request,
            second_last_comment,
            last_comment,
            type: 'html',
            layout: false
        });

        sails.log.info('Sending mail to', toUser.email);
        await EmailService.sendMail(subject, [toUser.email], html);
        sails.log.info('Email sent successfully to', toUser.email);
    }
};


const deriveDistanceMatrixRegion = (country_code) => {
    if (country_code === 'GB') {
        // https://developers.google.com/maps/documentation/distance-matrix/distance-matrix#region
        // As per doc, GB is not valid, It should be UK
        country_code = 'UK';
    }
    return country_code;
}

const getDistanceMatrix = async (origins, destinations, region = 'UK') => {
    region = deriveDistanceMatrixRegion(region);
    let distance_matrix_key = sails.config.custom.DISTANCE_METRIX_KEY || '';
    try {
        let api_response = await HttpService.makeGET(`https://maps.google.com/maps/api/distancematrix/json`, {
            origins: origins,
            destinations: destinations,
            sensor: false,
            units: 'metric',
            mode: 'driving',
            region,
            key: distance_matrix_key
        }, {}, true, 15000);
        sails.log.info('Google distance response', (api_response.data && api_response.data.status));
        return (api_response.data && api_response.data.rows) || [];
    } catch (e) {
        sails.log.error('Failed to query google for distance matrix', e);
    }
    return [];
};

class TravelTimeExtended {
    constructor(induction) {
        this.from_postcode = induction.from_postcode; // ?
        this.to_postcode = induction.to_postcode; // ?

        this.travel_time = {
            to_work: induction.travel_time.to_work,
            to_work_dm: induction.travel_time.distance_matrix || {}, // Google Distance Matrix

            to_home: induction.travel_time.to_home,
            to_home_dm: induction.travel_time.distance_matrix || {} // Google Distance Matrix
        };

        this.travel_method = induction.travel_method;
        this.vehicle_reg_number = induction.vehicle_reg_number;
        this.vehicle_info = induction.travel_time.vehicle_info;

        this.valid_from = null; // should start of day for a date.  (seconds)
        this.valid_till = null; // should be end of day for a date.  (seconds/null)

        this.timestamp = null; //   (ms)
        this.creator_ref = null;

    }
}

const getActiveTravelTime = (induction, day) => {
    let overrides = get(induction, `travel_time.overrides`, []);
    if(!overrides || !overrides.length){
        // return default travel time
        return new TravelTimeExtended(induction);
    }
    let now = day.unix();

    let activeTravelTime = overrides.filter(o => {
        return ((o.valid_from <= now && o.valid_till >= now) || (o.valid_from <= now && o.valid_till === null));
    }).sort((a, b) => a.valid_from - b.valid_from).pop();

    return activeTravelTime || new TravelTimeExtended(induction);
};

const constructDistanceMatrix = async (inductionRequest, project_postcode, country_code = 'UK') => {

    let travel_time = inductionRequest.travel_time || {};
    if(project_postcode){
        sails.log.info('got induction request data to update user travel distance Matrix.', project_postcode.id);
        let address = get(inductionRequest, 'additional_data.contact_detail', {});
        const region = deriveDistanceMatrixRegion(country_code);
        let origins = `${address.city ? address.city : ''} ${address.post_code ? address.post_code : ''} ${region||''}`.replace(/\s+/g, ' ');
        let destinations = `${project_postcode} ${region}`;
        let distanceMatrix = await getDistanceMatrix(origins, destinations, region);

        travel_time.distance_matrix = get(distanceMatrix, `[0]elements[0]`, (travel_time.distance_matrix || {}));
    }
    return travel_time;
};

/**
 *  Check if given project is actually a "Company project" under "Project portal" - RC617
 * @param project
 * @param company
 * @returns {boolean}
 */
const isInheritedProjectOfCompany = (project, company) => {
    return (
        company.has_company_portal &&
        (project.project_category === 'default') &&
        (project.contractor || '').toString().trim().toLowerCase() === (company.name || '').toString().trim().toLowerCase()
    );
};

const getPendingAssessmentStatus = (user_onboard_status = {}, features_status = {}) => {
    let health_answers_required = !(features_status) || (features_status.health_assessment);
    let medical_answers_required = !(features_status) || (features_status.medical_assessment);
    let ask_health_answers = !user_onboard_status.health_assessment && health_answers_required;
    let ask_medical_answers = !user_onboard_status.medical_assessments && medical_answers_required;

    return {
        ask: {health: ask_health_answers, medical: ask_medical_answers},
        enabled: {health_answers_required, medical_answers_required},
    }
}

const buildRatio = (x, y) => {
    let gcd = calc_gcd(x, y);
    let r1 = x / gcd;
    let r2 = y / gcd;
    return  r1 + ":" + r2;
};

let calc_gcd = (n1, n2) => {
    if(n2 === 0){
        return n1;
    }
    let num1, num2;
    if (n1 < n2) {
        num1 = n1;
        num2 = n2;
    } else {
        num1 = n2;
        num2 = n1;
    }
    let remain = num2 % num1;
    while (remain > 0) {
        num2 = num1;
        num1 = remain;
        remain = num2 % num1;
    }
    return num1;
};

const expandProjectClosecalls = async (project_close_calls, includeStatusMessage) => {
    // reduce array to get image Ids
    // get those images

    let cc_images = project_close_calls.reduce((ids, record) => {
        if (record.cc_images && record.cc_images.length) {
            ids.push(...record.cc_images);
        }
        return ids;
    }, []);

    let corrective_images = project_close_calls.reduce((ids, record) => {
        if (record.corrective_images && record.corrective_images.length) {
            ids.push(...record.corrective_images);
        }
        return ids;
    }, []);

    cc_images = _.uniq(cc_images);
    corrective_images = _.uniq(corrective_images);
    sails.log.info('Fetching close call images', cc_images);
    sails.log.info('Fetching close call corrective images', corrective_images);
    let totalIds = _.uniq(cc_images.concat(corrective_images));
    totalIds = totalIds.filter(function (el) {
        return el != null;
    });

    let totalImages = [];
    if(totalIds.length){
        totalImages = await sails.models.userfile_reader.find({
            where: {id: totalIds},
            select: ['createdAt','file_mime', 'id', 'name', 'file_url', 'sm_url', 'md_url', 'user_id', 'img_translation', 'file_type']
        });
    }
    if  ((totalImages && totalImages.length) || includeStatusMessage) {
        project_close_calls = project_close_calls.map(record => {
            if (record.cc_images) {
                record.cc_images = record.cc_images.map(cc_image => {
                    return totalImages.find(image => image.id === cc_image);
                });
            }

            if (record.corrective_images) {
                record.corrective_images = record.corrective_images.map(corrective_image => {
                    return totalImages.find(image => image.id === corrective_image);
                });
            }

            record.status_message = buildCloseCallStatus(record.status);
            return record;
        });
    }

    project_close_calls = await populateInductionEmployerRef(project_close_calls);

    return project_close_calls
};

// @deprecated use getDailyTimeEventV2
let getDailyBadgeEvent = async (project_optima_setting, from_date, to_date, badgeIds = [], singleUserOnly = false) => {
    try {
        let cut_off_hour = (isNaN(sails.config.custom.CUT_OFF_HOUR) ?  0 : sails.config.custom.CUT_OFF_HOUR);
        sails.log.info(`fetch badge logs from, to: ${from_date}  <= date < ${to_date}, badge_ids: ${badgeIds}, singleUserOnly: ${singleUserOnly} cut_off_hour: ${cut_off_hour}`);
        let startingNoOfEscaped = 3;
        let rawResult = await sails.sendNativeQuery(
            `SELECT
                badge_number,
                TO_CHAR((to_timestamp(event_date_time) - '${cut_off_hour} hours'::interval), '${dbDateFormat}') as date_of_yr,
                MIN(CASE WHEN event_type = 'IN' THEN event_date_time ELSE NULL END )as clock_in,
                MAX(CASE WHEN event_type = 'IN' THEN event_date_time ELSE NULL END )as recent_in,
                MAX(CASE WHEN event_type = 'OUT' THEN event_date_time ELSE NULL END ) as clock_out,
                (MAX(CASE WHEN event_type = 'OUT' THEN event_date_time ELSE NULL END ) - MIN(CASE WHEN event_type = 'IN' THEN event_date_time ELSE NULL END )) as duration_in_sec,
                count(*) as number_of_events
            FROM
              badge_event
            GROUP BY
              optima_host, badge_number, date_of_yr
            HAVING
              optima_host = $1
              AND TO_CHAR((to_timestamp(event_date_time) - '${cut_off_hour} hours'::interval), '${dbDateFormat}') >= $2
              AND TO_CHAR((to_timestamp(event_date_time) - '${cut_off_hour} hours'::interval), '${dbDateFormat}') < $3
              AND badge_number IN (${badgeIds.map(() => {
                startingNoOfEscaped++;
                return `$${startingNoOfEscaped}`;
            }).join(',')})
            ORDER BY date_of_yr ${singleUserOnly ? `DESC` : ''}, clock_in ASC;`,
            [`${project_optima_setting.host}:${project_optima_setting.port}`, from_date, to_date, ...badgeIds]
        );
        let badge_logs = [];
        if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            badge_logs = rawResult.rows;
        }
        return badge_logs;
    } catch (reportFetchError) {
        sails.log.error('Failed to fetch badge_logs', reportFetchError);
        return [];
    }
};

// @deprecated use getDailyTimeEventV2
const getDailyGeoFenceTimeEvent = async (project_id, from_date, to_date, user_ids = []) => {
    try {
        let cut_off_hour = (isNaN(sails.config.custom.CUT_OFF_HOUR) ?  0 : sails.config.custom.CUT_OFF_HOUR);
        sails.log.info(`fetch geo fence time logs from, to: ${from_date}  <= date < ${to_date}, user_ids: ${user_ids}, cut_off_hour: ${cut_off_hour}`);
        let startingNoOfEscaped = 3;
        let user_id_filter = '';
        let query_variables = [project_id, from_date, to_date];
        if(user_ids.length){
            user_id_filter = `AND user_ref IN (${user_ids.map(() => {
                startingNoOfEscaped++;
                return `$${startingNoOfEscaped}`;
            }).join(',')})`;
            query_variables.push(...user_ids);
        }

        let rawResult = await sails.sendNativeQuery(
            `SELECT
                project_ref as project_id,
                user_ref as user_id,
                TO_CHAR((to_timestamp(event_date_time) - '${cut_off_hour} hours'::interval), '${dbDateFormat}') as date_of_yr,
                MIN(CASE WHEN event_type = 'IN' THEN event_date_time ELSE NULL END )as clock_in,
                MAX(CASE WHEN event_type = 'IN' THEN event_date_time ELSE NULL END )as recent_in,
                MAX(CASE WHEN event_type = 'OUT' THEN event_date_time ELSE NULL END ) as clock_out,
                (MAX(CASE WHEN event_type = 'OUT' THEN event_date_time ELSE NULL END ) - MIN(CASE WHEN event_type = 'IN' THEN event_date_time ELSE NULL END )) as duration_in_sec,
                count(*) as number_of_events
            FROM user_time_log
            GROUP BY
              project_ref, user_ref, date_of_yr
            HAVING
              project_ref = $1
              AND TO_CHAR((to_timestamp(event_date_time) - '${cut_off_hour} hours'::interval), '${dbDateFormat}') >= $2
              AND TO_CHAR((to_timestamp(event_date_time) - '${cut_off_hour} hours'::interval), '${dbDateFormat}') < $3
              ${(user_ids.length ? user_id_filter : '')}
            ORDER BY date_of_yr, clock_in ASC`,
            query_variables
        );
        let time_logs = [];
        if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            time_logs = rawResult.rows;
        }
        return time_logs;
    } catch (reportFetchError) {
        sails.log.error('Failed to fetch geo-fence time_logs', reportFetchError);
        return [];
    }
};

// ideally should be called for today's date only...
// otherwise eventOfToday function will not act perfectly
const getDailyTimeEventForDay = async (project_id, for_date = moment(), unique_users = [], descendingOrder = false, company_id = null, includeComments = false, includeEvents = false) => {
    let target_day = for_date.clone().format(dbDateFormat);
    sails.log.info('Time log Requested day of year => ', target_day);
    let from_date = for_date.clone().subtract(1, 'day').format(dbDateFormat);
    let to_date = for_date.clone().add(1, 'day').format(dbDateFormat);

    let two_days_badge_logs = await getDailyTimeEventV2(project_id, from_date, to_date, unique_users, descendingOrder, company_id, includeComments, includeEvents);

    let user_groups = _groupBy(two_days_badge_logs, l => l.user_id);

    return Object.keys(user_groups).map(user_id => {
        return eventOfToday(target_day, user_groups[user_id]);
    }).filter(l => l && l.user_id);
};

const getUserCompanyTimeEvents = async (companyId, userId) => {
    let query_variables = [companyId, userId];
    let rawResult = await sails.sendNativeQuery(
        `SELECT
            project_ref as project_id,
            user_ref as user_id,
            TO_CHAR(day_of_yr, 'YYYY-MM-DD') as day_of_yr,
            first_in as clock_in,
            recent_in as recent_in,
            last_out as clock_out,
            total_in_sec as duration_in_sec,
            adjustment_minutes as adjustment,
            (CASE WHEN (total_in_sec IS NOT NULL) THEN ((COALESCE(total_in_sec, 0) + COALESCE(adjustment_minutes, 0) * 60)::integer) ELSE NULL END) as effective_time,
            -- in_time_type,
            comments,
            json_array_length(events) as number_of_events
        FROM user_daily_log
        WHERE
        company_ref = $1
        AND user_Ref = $2

        ORDER BY day_of_yr DESC`,
        query_variables
    );

    let time_logs = [];
    if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
        time_logs = rawResult.rows;
    }
    return time_logs;
};

const getDailyTimeEventV2 = async (projects, from_date, to_date, user_ids = [], descendingOrder = false, company_id = null, includeComments = false, includeEvents = false, includeRevision = false, includeDurations = false) => {
    try {
        sails.log.info(`fetch daily time logs from, to: ${from_date}  <= date < ${to_date}, user_ids count: ${(user_ids || []).length}, company_id: ${company_id}, include: (${includeComments}, ${includeEvents}, ${includeRevision}, ${includeDurations})`);
        let startingNoOfEscaped = 2;
        let query_variables = [from_date, to_date];
        let user_id_filter = ' AND user_ref IS NOT NULL ';
        let company_id_filter = '';
        projects = (HttpService.typeOf(projects, 'number') || HttpService.typeOf(projects, 'string')) ? (String(projects).split(',')) : projects;
        let project_ids_filter = ` project_ref IN (${projects.map(() => {
            startingNoOfEscaped++;
            return `$${startingNoOfEscaped}`;
        }).join(',')}) `;
        query_variables.push(...projects);
        if(company_id){
            startingNoOfEscaped++;
            query_variables.push(company_id);
            company_id_filter = ` AND company_ref = $${startingNoOfEscaped} `;
        }
        if(user_ids.length) {
            user_id_filter = `AND user_ref IN (${user_ids.map(() => {
                startingNoOfEscaped++;
                return `$${startingNoOfEscaped}`;
            }).join(',')})`;
            query_variables.push(...user_ids);
        }
        let rawResult = await sails.sendNativeQuery(
            `SELECT
                project_ref as project_id,
                user_ref as user_id,
                TO_CHAR(day_of_yr, 'YYYY-MM-DD') as day_of_yr,
                first_in as clock_in,
                recent_in as recent_in,
                last_out as clock_out,
                total_in_sec as duration_in_sec,
                adjustment_minutes as adjustment,
                (CASE WHEN (total_in_sec IS NOT NULL) THEN ((COALESCE(total_in_sec, 0) + COALESCE(adjustment_minutes, 0) * 60)::integer) ELSE NULL END) as effective_time,
                ${(includeComments ? 'comments,' : '')}
                ${(includeEvents ? 'events,' : '')}
                ${(includeDurations ? 'durations,' : '')}
                ${(includeRevision ? 'user_revision_ref as user_revision_ref,' : '')}
            json_array_length(events) as number_of_events
            FROM user_daily_log
            WHERE
            ${project_ids_filter}
            ${(company_id_filter.length ? company_id_filter : '')}
            AND day_of_yr >= $1
            AND day_of_yr < $2
            ${user_id_filter}
            ORDER BY day_of_yr ${descendingOrder ? 'DESC' : ''}, first_in ASC`,
            query_variables
        );

        let time_logs = [];
        if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            time_logs = rawResult.rows;
        }
        return time_logs;
    } catch (reportFetchError) {
        sails.log.error('Failed to fetch daily time_logs', reportFetchError);
        return [];
    }
};

const getAllDailyTimeEvent = async (project_id, user_ids = [], company_id) => {
    try {
        sails.log.info(`fetch ALL daily time logs, project_id: ${project_id} company_id: ${company_id}, user_ids: ${user_ids}`);
        let startingNoOfEscaped = 1;
        let user_id_filter = ' AND user_ref IS NOT NULL ';
        let company_id_filter = '';
        let query_variables = [project_id];
        if(company_id){
            startingNoOfEscaped++;
            query_variables.push(company_id);
            company_id_filter = ` AND company_ref = $${startingNoOfEscaped} `;
        }
        if(user_ids.length) {
            user_id_filter = `AND user_ref IN (${user_ids.map(() => {
                startingNoOfEscaped++;
                return `$${startingNoOfEscaped}`;
            }).join(',')})`;
            query_variables.push(...user_ids);
        }
        let rawResult = await sails.sendNativeQuery(
            `SELECT
                project_ref as project_id,
                user_ref as user_id,
                TO_CHAR(day_of_yr, 'YYYY-MM-DD') as day_of_yr,
                first_in as clock_in,
                recent_in as recent_in,
                last_out as clock_out,
                total_in_sec as duration_in_sec,
                adjustment_minutes as adjustment,
                (CASE WHEN (total_in_sec IS NOT NULL) THEN ((COALESCE(total_in_sec, 0) + COALESCE(adjustment_minutes, 0) * 60)::integer) ELSE NULL END) as effective_time,
                -- in_time_type,
                comments,
                json_array_length(events) as number_of_events
            FROM user_daily_log
            WHERE
            project_ref = $1
            ${(company_id_filter.length ? company_id_filter : '')}
            ${user_id_filter}
            ORDER BY day_of_yr, first_in ASC`,
            query_variables
        );

        let time_logs = [];
        if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            time_logs = rawResult.rows;
        }
        return time_logs;
    } catch (reportFetchError) {
        sails.log.error('Failed to fetch ALL daily time_logs', reportFetchError);
        return [];
    }
};

const getAllVisitorsTimeEvents = async (project_id, visitor_ids = [], company_id) => {
    try {
        sails.log.info(`fetch Visitor daily time logs, project_id: ${project_id} company_id: ${company_id}, visitor_ids: ${visitor_ids}`);
        let startingNoOfEscaped = 1;
        let visitor_ref_filter = ' AND visitor_ref IS NOT NULL ';
        let company_id_filter = '';
        let query_variables = [project_id];
        if(company_id){
            startingNoOfEscaped++;
            query_variables.push(company_id);
            company_id_filter = ` AND company_ref = $${startingNoOfEscaped} `;
        }
        if(visitor_ids.length) {
            visitor_ref_filter = `AND visitor_ref IN (${visitor_ids.map(() => {
                startingNoOfEscaped++;
                return `$${startingNoOfEscaped}`;
            }).join(',')})`;
            query_variables.push(...visitor_ids);
        }
        let rawResult = await sails.sendNativeQuery(
            `SELECT
                project_ref as project_id,
                visitor_ref as visitor_id,
                TO_CHAR(day_of_yr, 'YYYY-MM-DD') as day_of_yr,
                first_in as clock_in,
                recent_in as recent_in,
                last_out as clock_out,
                total_in_sec as duration_in_sec,
                adjustment_minutes as adjustment,
                (CASE WHEN (total_in_sec IS NOT NULL) THEN ((COALESCE(total_in_sec, 0) + COALESCE(adjustment_minutes, 0) * 60)::integer) ELSE NULL END) as effective_time,
                -- in_time_type,
                comments
                -- json_array_length(events) as number_of_events
            FROM user_daily_log
            WHERE
            project_ref = $1
            ${(company_id_filter.length ? company_id_filter : '')}
            ${visitor_ref_filter}
            ORDER BY day_of_yr, first_in ASC`,
            query_variables
        );

        let time_logs = [];
        if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            time_logs = rawResult.rows;
        }
        return time_logs;
    } catch (reportFetchError) {
        sails.log.error('Failed to fetch Visitors daily time_logs', reportFetchError);
        return [];
    }
};

const getVisitorsTimeLogForDates = async (project_id, from_date, to_date, visitor_ids = [], descendingOrder = false, company_id = null, include_events = false) => {
    try {
        sails.log.info(`fetch visitor time logs from, to: ${from_date}  <= date < ${to_date}, visitor_ids count: ${(visitor_ids || []).length}, company_id: ${company_id}, events: ${include_events}`);
        let startingNoOfEscaped = 3;
        let visitor_id_filter = ' AND visitor_ref IS NOT NULL ';
        let company_id_filter = '';
        let query_variables = [project_id, from_date, to_date];
        if(company_id){
            startingNoOfEscaped++;
            query_variables.push(company_id);
            company_id_filter = ` AND company_ref = $${startingNoOfEscaped} `;
        }
        if(visitor_ids.length) {
            visitor_id_filter = `AND visitor_ref IN (${visitor_ids.map(() => {
                startingNoOfEscaped++;
                return `$${startingNoOfEscaped}`;
            }).join(',')})`;
            query_variables.push(...visitor_ids);
        }
        let rawResult = await sails.sendNativeQuery(
            `SELECT
                project_ref as project_id,
                visitor_ref as visitor_id,
                TO_CHAR(day_of_yr, 'YYYY-MM-DD') as day_of_yr,
                first_in as clock_in,
                recent_in as recent_in,
                last_out as clock_out,
                total_in_sec as duration_in_sec,
                adjustment_minutes as adjustment,
                ${(include_events ? 'events,' : '')}
                (CASE WHEN (total_in_sec IS NOT NULL) THEN ((COALESCE(total_in_sec, 0) + COALESCE(adjustment_minutes, 0) * 60)::integer) ELSE NULL END) as effective_time
            -- json_array_length(events) as number_of_events
            FROM user_daily_log
            WHERE
            project_ref = $1
            ${(company_id_filter.length ? company_id_filter : '')}
            AND day_of_yr >= $2
            AND day_of_yr < $3
            ${visitor_id_filter}
            ORDER BY day_of_yr ${descendingOrder ? 'DESC' : ''}, first_in ASC`,
            query_variables
        );

        let time_logs = [];
        if (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            time_logs = rawResult.rows;
        }
        return time_logs;
    } catch (reportFetchError) {
        sails.log.error('Failed to fetch visitor time logs', reportFetchError);
        return [];
    }
};

const removeRejectedInductions = async (finalCallbackFn) => {
    sails.log.info('Running removeRejectedInductions ---- ');
    try {
        let afterEpoch = moment().subtract(120, 'hours').startOf('day').valueOf();
        sails.log.info('afterEpoch: ', afterEpoch);

        let updatedInductionRequests = await sails.models.inductionrequest.destroy({
            status_code: 0, updatedAt: {'<=': afterEpoch}
        });

        let deletedIds = (updatedInductionRequests || []).map(p => p.id);
        sails.log.info('removeRejectedInductions -> deletedIds ->', deletedIds);
        finalCallbackFn(null, [updatedInductionRequests]);
    }catch (e) {
        sails.log.error(`Something went wrong in cron job`, e);
        finalCallbackFn(e);
    }
};

let isStillOnSite = (event_of_today = {}) => {

    //has_out_punch_today = event_of_today && event_of_today.clock_out || null;
    let is_still_on_site = (event_of_today && event_of_today.clock_in && !event_of_today.clock_out) || false;

    // Have in & out both, but IN is after OUT, means user is still IN
    if(event_of_today && event_of_today.clock_out && event_of_today.recent_in){
        //console.log('difference in (in-out) ms is', d);
        if(+event_of_today.recent_in > +event_of_today.clock_out){
            is_still_on_site = true;
        }
    }
    else if(event_of_today && event_of_today.clock_out && event_of_today.clock_in){
        let d = moment.unix(+event_of_today.clock_in).diff(moment.unix(+event_of_today.clock_out));
        //console.log('difference in (in-out) ms is', d);
        if(d > 0){
            is_still_on_site = true;
        }
    }

    return is_still_on_site;
};

// @deprecated: @aakash: deprecated since 15th March 2025
const expandProjectTake5s = async (project_take5s = [], cb) => {
    // reduce array to get image Ids
    let users_id = project_take5s.reduce((ids, record) => {
        if (record.user_ref) {
            ids.push(record.user_ref.id);
        }
        return ids;
    }, []);

    try {
        let usersEmpDetail = await sails.models.userempdetail.find({user_ref: users_id});
        if(usersEmpDetail  && usersEmpDetail.length) {
            project_take5s = project_take5s.map(record => {
                if (record.user_ref && record.user_ref.id) {
                    record.user_employer = usersEmpDetail.find(ue => ue.user_ref === record.user_ref.id);
                }

                return record;
            });
        }
        let images = project_take5s.reduce((ids, record) => {
            if (record.images && record.images.length) {
                ids.push(...record.images);
            }
            return ids;
        }, []);
        let imageIds = _.uniq(images);
        if (imageIds.length) {
            let imageFiles = await sails.models.userfile_reader.find({id: imageIds});
            if  (imageFiles && imageFiles.length) {
                project_take5s = project_take5s.map(record => {
                    if (record.images) {
                        record.images = (imageFiles || []).filter(image => (record.images || []).includes(image.id))
                    }
                    return record;
                });
            }
        }

        const promises = project_take5s.map(async record => {
            if ((record.register && record.register.length) || (record.guest_register && record.guest_register.length)) {
                let registers = await sails.models.userempdetail.find({
                    where: {user_ref: record.register}
                });
                registers = await populateUserRefs(registers, 'user_ref', []);
                record.attendees = registers.reduce(function (prev, item, i) {
                    let name = item.user_ref.first_name+' '+item.user_ref.last_name;
                    prev.push({name: name, emp: item.employer});
                    return prev;
                }, []);

                if (record.guest_register && record.guest_register.length) {
                    record.attendees.push(...record.guest_register);
                }
            }

            return record;
        });
        project_take5s = await Promise.all(promises);
    } catch (e) {
        sails.log.info('Failed to fetch image records', e);
        return cb(e, null);
    }
    return cb(null, project_take5s);
};

const expandUserDocFiles = async (user_documents = [], cb) => {
    try {
        let user_doc_ids = user_documents.reduce((list, record) => {
            if(record.user_files){
                list.push(...(record.user_files.map(file => (file && file.id) ? file.id : file).filter(id => +id)));
            }
            return list;
        }, []);

        sails.log.info(`expand details of user_doc file ids: ${user_doc_ids}`);

        if (!user_doc_ids.length) {
            return cb(null, user_documents);
        }
        let documents = await sails.models.userfile.find({
            where: {id: user_doc_ids},
            select: ['id', 'name', 'file_url', 'createdAt','file_mime', 'sm_url', 'md_url', 'user_id', 'img_translation']
        });

        user_documents = user_documents.map(record => {
            if(record.user_files){
                let ids = (record.user_files || []).map(file => (file && file.id) ? file.id : +file);
                record.user_files = documents.filter(doc => ids.includes(doc.id));
            }
            return record;
        });
    } catch (e) {
        sails.log.info('Failed to fetch document files', e);
        return cb(e, null);
    }
    return cb(null, user_documents);
};

const translateUserDocIntoPages = async (documents, expand = true) => {
    if(expand){
        documents = await (expandUserDocFiles(documents, (expandErr, expandedList) =>  expandedList || documents));
    }
    return (documents || []).reduce((rows, doc) => {
        let compact_doc = {
            doc_id: doc.id,
            name: doc.name,
            is_verified: doc.is_verified,
            _is_verifiable: doc._is_verifiable,
            verification_info: doc.verification_info,
            description: doc.description,
            doc_number: doc.doc_number,
            expiry_date: doc.expiry_date,
            children: doc.children,
        };
        let files_per_doc = (doc.user_files || []).reduce((file_row, fileInstance, index) => {

            if(fileInstance.img_translation && fileInstance.img_translation.length){
                // expand all images & user image mime
                file_row.push(...(fileInstance.img_translation.map((img_url, idx) => ({
                    ...compact_doc,
                    show_doc_info: (index === 0 && idx === 0),  // so that document info gets rendered only once per doc..
                    file_mime: 'image/jpeg',
                    file_url: img_url,
                }))));
            }else{
                // use file url
                file_row.push({
                    ...compact_doc,
                    show_doc_info: (index === 0),  // so that document info gets rendered only once per doc..
                    file_mime: fileInstance.file_mime,
                    file_url: fileInstance.file_url,
                    md_url: fileInstance.md_url,
                });
            }

            return file_row;
        }, []);
        rows.push(...files_per_doc);
        return rows;
    }, []);
};

const expandProgressImages = async (progress_photos = []) => {
    // reduce array to get image Ids
    // get those images

    let pp_images = progress_photos.reduce((ids, record) => {
        if (record.pp_images && record.pp_images.length) {
            ids.push(...record.pp_images);
        }
        return ids;
    }, []);

    let users_id = progress_photos.reduce((ids, record) => {
        if (record.user_ref) {
            ids.push(record.user_ref.id);
        }
        return ids;
    }, []);

    pp_images = _.uniq(pp_images);
    sails.log.info('Fetching progress photo images', pp_images.length);
    let totalIds = pp_images.filter(function (el) {
        return el != null;
    });
    try {
        if (totalIds.length) {
            let totalImages = await sails.models.userfile_reader.find({
                where: {id: totalIds},
                select: ['id', 'user_id', 'name', 'file_url', 'createdAt','file_mime', 'sm_url', 'md_url', 'img_translation']
            });
            let usersEmpDetail = await sails.models.userempdetail.find({user_ref: users_id});

            if  (totalImages && totalImages.length) {
                progress_photos = progress_photos.map(record => {
                    if (record.pp_images) {
                        record.pp_images = record.pp_images.map(pp_images => {
                            return totalImages.find(image => image.id === pp_images);
                        });
                    }
                    return record;
                });
            }

            if(usersEmpDetail  && usersEmpDetail.length) {
                progress_photos = progress_photos.map(record => {
                    if (record.user_ref && record.user_ref.id) {
                        record.user_employer = usersEmpDetail.find(ue => ue.user_ref === record.user_ref.id);
                    }

                    return record;
                });
            }
        }
    } catch (e) {
        sails.log.info('Failed to fetch warning image records', e);
        return [];
    }
    return progress_photos;
};

const totalProgressImagesCount = async (progress_photos = []) => {
    // reduce array to get image Ids
    // get those images

    let pp_images = progress_photos.reduce((ids, record) => {
        if (record.pp_images && record.pp_images.length) {
            ids.push(...record.pp_images);
        }
        return ids;
    }, []);

    // pp_images = _.uniq(pp_images);
    let totalIds = pp_images.filter(function (el) {
        return el != null;
    });
    return totalIds.length
};

const groupByAlbumProgressImages = async (progress_photos = []) => {
    let albumData = [];
    let all_photo_count = 0;
    let all_last_photo  = null;

    if (progress_photos.length > 0) {
        let unique_pp_images_ids = [];
        progress_photos.map(l => {
            if(l.recent_pp_images && l.recent_pp_images.length) {
                unique_pp_images_ids.push(Math.max(...l.recent_pp_images))
            }
        });
        unique_pp_images_ids = _uniq(unique_pp_images_ids);
        let pp_user_files = await sails.models.userfile_reader.find({where: {id: unique_pp_images_ids}}).select(['id','file_url']);

        progress_photos.map(record => {
            let photo_count = parseInt(record.total_pp_count);
            if (record.id) {
                let recent_pp_images_id = Math.max(...record.recent_pp_images)
                let item = {
                    id : record.id,
                    title : record.title,
                    photo_count : photo_count,
                    last_photo: null
                }
                if (recent_pp_images_id > 0) {
                    let last_file = pp_user_files.find(v => v.id === recent_pp_images_id) || null;
                    if (last_file) {
                        item.last_photo = last_file.file_url;
                    }
                }
                albumData.push(item);
            }
            all_photo_count += photo_count;
        });

        // All default album recent progress photo
        let pp_images_id = Math.max(...unique_pp_images_ids);
        let last_file = pp_user_files.find(v => v.id === pp_images_id) || null;
        if (last_file) {
            all_last_photo = last_file.file_url;
        }
    }
    let allAlbumObj = {id : 0, title : "All", photo_count : all_photo_count, last_photo: all_last_photo}
    albumData.push(allAlbumObj);
    return albumData
};

const expandDeliveryNoteImgs = async (delivery_notes = []) => {
    // reduce array to get image Ids
    // get those images

    let dn_images = delivery_notes.reduce((ids, record) => {
        if (record.dn_images && record.dn_images.length) {
            ids.push(...record.dn_images);
        }
        return ids;
    }, []);

    let users_id = delivery_notes.reduce((ids, record) => {
        if (record.user_ref) {
            ids.push(record.user_ref.id);
        }
        return ids;
    }, []);

    dn_images = _.uniq(dn_images);
    sails.log.info('Fetching progress photo images', dn_images);
    let totalIds = dn_images.filter(function (el) {
        return el != null;
    });
    try {
        if (totalIds.length) {
            let totalImages = await sails.models.userfile_reader.find({
                where: {id: totalIds},
                select: ['id', 'user_id', 'name', 'file_url', 'createdAt','file_mime', 'sm_url', 'md_url', 'img_translation']
            });
            let usersEmpDetail = await sails.models.userempdetail.find({user_ref: users_id});

            if  (totalImages && totalImages.length) {
                delivery_notes = delivery_notes.map(record => {
                    if (record.dn_images) {
                        record.dn_images = record.dn_images.map(dn_images => {
                            return totalImages.find(image => image.id === dn_images);
                        });
                    }
                    return record;
                });
            }

            if(usersEmpDetail  && usersEmpDetail.length) {
                delivery_notes = delivery_notes.map(record => {
                    if (record.user_ref && record.user_ref.id) {
                        record.user_employer = usersEmpDetail.find(ue => ue.user_ref === record.user_ref.id);
                    }

                    return record;
                });
            }
        }
    } catch (e) {
        sails.log.info('Failed to fetch warning image records', e);
        return [];
    }
    return delivery_notes;
};

const sendWeeklyTimesheetProcess = async (userInfo, projectIds, from_date, to_date) => {
    sails.log.info(`processing user time-sheet request, user: ${userInfo.id}, from, to: ${from_date}  <= date < ${to_date} projects:`, projectIds);

    // userInfo.id = 97;

    let project_details = (await sails.models.project_reader.find({
        where: {id: projectIds},
        select: ['name', 'contractor']
    })) || [];

    let badge_logs = [];
    for (const projectId of (projectIds || [])) {

        sails.log.info('Processing project', projectId);
        let project = project_details.find(p => p.id === projectId) || {};
        // get daily time log of each project
        let daily_logs = await getDailyTimeEventV2(projectId, from_date, to_date, [userInfo.id], false, null, false, false, true);

        let logs_with_details = daily_logs.map(l => {
            l.additional_data = {
                project
            };
            return l;
        });
        sails.log.info(`Total logs for ${projectId} are:`, logs_with_details.length);

        badge_logs.push(...logs_with_details);
    }

    sails.log.info('Total time Log', badge_logs.length);
    if(!badge_logs.length){
        sails.log.info(`Skipping due to no time log for user: ${userInfo.id}, duration ${from_date} : ${to_date}`);
        return { success: true, message: "There are no time logs for selected duration.", sentStatus: false, user: userInfo.id }
    }
    // extract list of all user revisions
    // and back fill those revisions in time logs
    let user_revisions = _uniq((badge_logs || []).filter(l => l.user_revision_ref).map(l => l.user_revision_ref));


    if(user_revisions.length){
        sails.log.info('get user revisions', user_revisions);
        // get revisions
        let revisions_list = await sails.models.userrevision.find({
            where: {id: user_revisions},
            select: ['employment','personal']
        });
        badge_logs.forEach(l => {
            l.additional_data.employment_detail = Object.assign({}, (revisions_list.find(r => r.id === l.user_revision_ref) || {}).employment);
        });
    }

    let total_time = badge_logs.reduce((sum, l) => {
        if(l.effective_time){
            sum = sum + parseInt(l.effective_time);
        }
        return sum;
    }, 0);

    sails.log.info('Total time seconds', total_time);
    let d = moment.duration(+total_time, 'seconds');
    let str = [];
    if(moment.isDuration(d)){
        let m = d.minutes();
        d = d.subtract(m, 'minutes');
        let h = Math.round(d.asHours());
        if(h){
            str.push(`${h} hr`);
        }
        if(m){
            str.push(`${m} minutes`);
        }
    }
    let total_time_str = str.join(' ');

    // Sort records by date
    badge_logs = sortBadgeLogsByDay(badge_logs, 'day_of_yr');

    // Group Records in Chunk of 40 size, so that 40 records per page.
    let badge_log_groups = [];
    while (badge_logs.length) {
        badge_log_groups.push(badge_logs.splice(0, 40));
    }

    let full_name = getUserFullName(userInfo);
    let from_date_str = moment(from_date, dbDateFormat).format(displayDateFormat);
    let to_date_str = moment(to_date, dbDateFormat).format(displayDateFormat);
    let subject = `${full_name} - Timesheet - [${from_date_str} - ${to_date_str}]`;
    sails.log.info('Sending Timesheet alert to user', subject, 'tz:', userInfo.timezone);
    // Render View
    let html = await sails.renderView(`pages/user-time-sheet-report-page`, {
        title: `User Timesheet`,
        user: userInfo || {},
        full_name,
        from_date: from_date_str,
        to_date: to_date_str,
        badge_log_groups,
        total_time_str,

        get,
        duration: moment.duration,
        moment,
        formatDate: (str) => {
            return moment(str, dbDateFormat).format(displayDateFormat);
        },
        unix: (epoch_sec) => {
            return userInfo.timezone ? momentTz(+epoch_sec, 'X').tz(userInfo.timezone) : moment.unix(+epoch_sec);
        },
        displayEmploymentCompany: (ed) => {
            return (ed && ed.employment_company) ? `(${ed.employment_company})` : '';
        },

        layout: false
    });

    let pdfBuffer = await downloadPdfViaGenerator({
        req: {headers: {}},
        res: {set: () => {}, ok: () => {}},
        html,
        tool: 'weekly-timesheet',
        file_name: `weekly-timesheet-${dayjs().valueOf()}-${uuid4()}`,
        heading_line: 'Timesheet',
        project_line: full_name,
        date_line: `${from_date_str} to ${to_date_str}`,
        use_inndex_logo: true,
        has_cover: false,
        has_one_page: false,
        responseType:'pdfBuffer'
    });
    // @note: below comment block is to help debugging via test user
    // return pdfInstance.stream.pipe(res);
    // const fs = require('fs');            const path = require('path');let filePath = path.resolve(process.cwd(), `logs/mail-${subject.replace(/[^\w]/gi, '_').substring(0, 10)}.html`);sails.log.info('File Dump: ', filePath); fs.writeFileSync(filePath, html);
    // return res.send(html);


    // OR
    // const fs = require('fs');            const path = require('path');
    // sails.log.info('Skipping mail...');
    // sails.log.info('Subject', subject);
    // let filePath = path.resolve(process.cwd(), `logs/mail-${subject.replace(/[^\w]/gi, '_').substring(0, 10)}-${(new Date()).getTime()}.html`);
    // let filePath2 = path.resolve(process.cwd(), `logs/mail-${subject.replace(/[^\w]/gi, '_').substring(0, 10)}-${(new Date()).getTime()}.pdf`);
    // sails.log.info('HTML Dump: ', filePath);
    // sails.log.info('Email Dump: ', filePath2);
    // let writeStream = fs.createWriteStream(filePath2);
    // fs.writeFileSync(filePath, html);
    // // pdfInstance.stream.pipe(writeStream);
    // return {success: true, filePath};

    let emailHtml = `<p>Hi ${full_name}</p>

    <p>Please see timesheet attached for date {${from_date_str} - ${to_date_str}}</p>

    <p>Work Safe.</p>

    <p>Thanks<br/>
    The innDex Team</p>`;
    let attachmentName = `${subject}.pdf`;
    try{
        let sentStatus = await sendRawEmail(subject, [userInfo.email], emailHtml, pdfBuffer, attachmentName, true);
        return { success: true, message: "Timesheet export has been sent to your email address.", sentStatus, user: userInfo.id }
    }catch(failure){
        sails.log.info('Failed to send mail', failure);
        return { success: false, message: "Something went wrong while sending weekly timesheet.", user: userInfo.id }
    }
};

const processWeeklyTimesheetRequest = async (userId, from_date, to_date) => {
    let userInfo = await sails.models.user_reader.findOne({id: userId});

    if (!userInfo || !userInfo.id) {
        return { success: false, message: "Not found", user: userId };
    }
    let projectsResult = await sails.sendNativeQuery(`SELECT DISTINCT project_ref
                                                      FROM user_daily_log
                                                      WHERE user_ref = $1
                                                        AND day_of_yr >= $2
                                                        AND day_of_yr < $3`,
        [userInfo.id, from_date, to_date]);
    let projectIds = (projectsResult.rows || []).map(row => row.project_ref);
    sails.log.info(`user ${userInfo.id} has worked on ${projectIds} projects`);

    if (projectIds.length) {
        projectIds = _uniq(projectIds);
        return await sendWeeklyTimesheetProcess(userInfo, projectIds, from_date, to_date);
    }
    return { success: false, message: "No time logs for this week", user: userId };
};

const sendWeeklyCCemail = async () => {

    try{
        sails.log.info('execute `sendWeeklyCCemail` cron process initializing.');
        let sevenDaysEpoch = moment().tz("Europe/London").subtract(7, 'days').startOf('day').valueOf();
        let sevenDaysPostEpoch = moment().tz("Europe/London").subtract(7, 'days').endOf('day').valueOf();
        let rawSelectDbQuery = "SELECT * FROM public.close_call WHERE \"createdAt\" BETWEEN '" + sevenDaysEpoch + "' AND '" + sevenDaysPostEpoch + "' AND status = 1";
        sails.log.info(`Find close calls to be notified:`, rawSelectDbQuery);
        let rawSelectDbResult = await sails.sendNativeQuery(rawSelectDbQuery);
        if (HttpService.typeOf(rawSelectDbResult.rows, 'array') && rawSelectDbResult.rows.length) {
            for (let i = 0, len = rawSelectDbResult.rows.length; i < len; i++) {
                let closeCall = rawSelectDbResult.rows[i];
                let projectData = await sails.models.project_reader.findOne({where: {id: closeCall.project_ref, is_active: 1}, select: ['name', 'custom_field']});
                if(!projectData || !projectData.id){
                    sails.log.info('Project not found OR is not active, id:', closeCall.project_ref);
                    continue;
                }
                let projUsrResult = await allProjectAdminsByOneOfDesignations(projectData.id, ['nominated', 'custom']);
                projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, 'close_calls');
                let cc_phrase = projectData.custom_field.cc_phrase_singlr;
                let projectName = projectData.name;
                let tz = getProjectTimezone(projectData);
                sails.log.info('Send email to nom managers, count:', projUsrResult.length);
                for (let j = 0, len = projUsrResult.length; j < len; j++) {
                    let nomManager = projUsrResult[j].user_ref || {};
                    try {
                        let subject = `${cc_phrase} #${closeCall.cc_number} Closeout Required - Project: ${projectName}`;
                        let html = await sails.renderView('pages/mail/close-out-awiting-notif', {
                            title: subject,
                            closeCall,
                            nomManager,
                            projectName,
                            cc_phrase,
                            tz,
                            moment: moment,
                            layout: false
                        });
                        await EmailService.sendMail(subject, [nomManager.email], html);
                    } catch (e) {
                        sails.log.info('Unable to send email to nom manager.', e);
                        return false;
                    }
                }
            }
        }
    }catch(failure){
        sails.log.info('Failed to send mail', failure);
        return { success: false, message: "Something went wrong while processing `sendWeeklyCCemail`." }
    }
};

const expandProjectClerkOfWorks = async (project_clerk_of_works = []) => {
    // reduce array to get image Ids
    // get those images

    let cow_images = project_clerk_of_works.reduce((ids, record) => {
        if (record.cow_images && record.cow_images.length) {
            ids.push(...record.cow_images);
        }
        if(record.pins && record.pins.length) {
            ids.push(...record.pins.map(p => p.file_id));
        }
        return ids;
    }, []);

    promises = project_clerk_of_works.map(async record => {
        record.comments = await sails.models.clerkofworkcomments.find({cow_id: record.id});
        // console.log(record.comments);
        (record.comments || []).map(c => {
            if (c.images && c.images.length) {
                cow_images.push(...c.images);
            }
        })
        return record;
    });
    project_clerk_of_works = await Promise.all(promises);

    let users_id = project_clerk_of_works.reduce((ids, record) => {
        if (record.user_ref) {
            ids.push(record.user_ref.id);
        }
        return ids;
    }, []);

    cow_images = _.uniq(cow_images);
    sails.log.info('Fetching clerk of work images', cow_images);

    totalIds = cow_images.filter(function (el) {
        return el != null;
    });

    try {
        if (totalIds.length) {
            let totalImages = await sails.models.userfile.find({
                where: {id: totalIds},
                select: ['id', 'createdAt', 'user_id', 'name', 'file_url','file_mime', 'sm_url', 'md_url', 'img_translation']
            });
            let usersEmpDetail = await sails.models.userempdetail.find({user_ref: users_id});

            if  (totalImages && totalImages.length) {
                project_clerk_of_works = project_clerk_of_works.map(record => {
                    if (record.cow_images) {
                        record.cow_images = record.cow_images.map(cow_image => {
                            return totalImages.find(image => image.id === cow_image);
                        });
                    }
                    if(record.pins) {
                        record.pins = record.pins.map(pin => {
                            pin.file_id = totalImages.find(img => img.id === pin.file_id);
                            return pin;
                        });
                    }
                    record.comments = (record.comments || []).map(comment => {
                        comment.images = (comment.images || []).map(image_id => {
                            return totalImages.find(image => image.id === image_id);
                        });
                        return comment;
                    });
                    return record;
                });
            }

            if(usersEmpDetail  && usersEmpDetail.length) {
                project_clerk_of_works = project_clerk_of_works.map(record => {
                    if (record.user_ref && record.user_ref.id) {
                        record.user_employer = usersEmpDetail.find(ue => ue.user_ref === record.user_ref.id);
                    }

                    return record;
                });
            }
        }
    } catch (e) {
        sails.log.info('Failed to fetch image records', e);
        return null;
    }
    return project_clerk_of_works;
};

const expandIncidentReports = async (incident_reports = []) => {
    let users_id = [], other_users_id = [], job_role_ids = [], attachment_ids = [], project_division_ids = [];
    let tagged_user_ids = [];

    (incident_reports || []).forEach(function(record) {
        if (record.user_ref) {
            users_id.push(record.user_ref.id);
        }
        if (record.site_treatment && record.site_treatment.job_role_ref) {
            job_role_ids.push(record.site_treatment.job_role_ref);
        }
        if (record.attachment_file_ids && record.attachment_file_ids.length) {
            attachment_ids.push(...(record.attachment_file_ids || []).map(e => e['file']));
        }
        if (record.review_photo_ids && record.review_photo_ids.length) {
            attachment_ids.push(...(record.review_photo_ids || []).map(e => e['file']));
        }
        if (record.project_ref && record.project_ref.division_ref) {
            project_division_ids.push(record.project_ref.division_ref);
        }

        (record.incident_actions || []).map(action => {
            if(action.tag_user_ref) {
                tagged_user_ids.push(action.tag_user_ref);
            }

            if(action.close_out && action.close_out.images) {
                attachment_ids.push(...(action.close_out.images));
            }
            return action;
        });
    });

    let taggedUsersInfo = await sails.models.user_reader.find({
        where: {id: _uniq(tagged_user_ids)},
        select: ['id', 'first_name', 'middle_name', 'last_name', 'email']
    });

    let filesObj = await sails.models.userfile.find({
        where: {id: _uniq(attachment_ids)},
        select: ['id', 'sm_url', 'md_url', 'file_url', 'file_mime', 'name', 'img_translation']
    });

    let divisions = await sails.models.companydivision.find({
        where: {id: _uniq(project_division_ids)},
        select: ['name']
    });

    if(filesObj && filesObj.length || divisions && divisions.length || taggedUsersInfo.length) {
        incident_reports = (incident_reports || []).map(record => {
            if (record.attachment_file_ids && record.attachment_file_ids.length) {
                record.attachment_file_ids = (record.attachment_file_ids || []).map(data => {
                    data.file = filesObj.find(file => file.id === data.file);
                    return data;
                });
            }
            if (record.review_photo_ids && record.review_photo_ids.length) {
                record.review_photo_ids = (record.review_photo_ids || []).map(data => {
                    data.file = filesObj.find(file => file.id === data.file);
                    return data;
                });
            }
            if (record.project_ref && record.project_ref.division_ref) {
                record.project_ref.division_ref = divisions.find(d => d.id === record.project_ref.division_ref);
            }

            record.incident_actions = (record.incident_actions || []).map(action => {
                if(action.tag_user_ref) {
                    action.tag_user_ref = taggedUsersInfo.find(user => user.id == action.tag_user_ref);
                }

                if(action.close_out && action.close_out.images) {
                    action.close_out.images = filesObj.filter(file => (action.close_out.images || []).includes(file.id));
                }
                return action;
            });

            return record;
        });
    }

    try {
        let usersEmpDetail = await sails.models.userempdetail.find({
            where: {
                or: [
                    {user_ref: [...users_id, ...other_users_id]},
                    {id: job_role_ids}
                ]
            },
        });

        let contactDetails = await sails.models.contactdetail.find({user_ref: users_id})

        if(usersEmpDetail  && usersEmpDetail.length) {
            incident_reports = incident_reports.map(record => {
                if (record.user_ref && record.user_ref.id) {
                    record.user_employer = usersEmpDetail.find(ue => ue.user_ref === record.user_ref.id);
                    let userContact = contactDetails.find(ue => ue.user_ref === record.user_ref.id);
                    record.user_address = `${userContact.street ? userContact.street : ''} ${userContact.city ? userContact.city : ''} ${userContact.post_code ? userContact.post_code : ''}`.replace(/\s+/g, ' ');
                }
                if (record.site_treatment && record.site_treatment.job_role_ref) {
                    record.site_treatment.job_role = usersEmpDetail.find(ue => ue.id === record.site_treatment.job_role_ref);
                }
                return record;
            });
        }
    } catch (e) {
        sails.log.info('Failed to expand job roles for incident reports', e);
        return null;
    }
    return incident_reports;
};

const expandProjectGoodCalls = async (project_good_calls = []) => {
    // reduce array to get image Ids, close out images id and user Ids
    let images = project_good_calls.reduce((ids, record) => {
        if (record.images && record.images.length) {
            ids.push(...record.images);
        }

        if (record.closeout_detail && record.closeout_detail.images && record.closeout_detail.images.length) {
            ids.push(...record.closeout_detail.images);
        }

        return ids;
    }, []);

    let users_id = project_good_calls.reduce((ids, record) => {
        if (record.user_ref) {
            ids.push(record.user_ref.id);
        }
        return ids;
    }, []);

    let imageIds = _.uniq(images);
    sails.log.info('Fetching good call images', imageIds);
    imageIds = imageIds.filter(imageId => imageId != null);
    if (imageIds.length) {
        let imageFiles = await sails.models.userfile_reader.find({id: imageIds});

        if  (imageFiles && imageFiles.length) {
            project_good_calls = project_good_calls.map(record => {
                if (record.images) {
                    record.images = (imageFiles || []).filter(image => (record.images || []).includes(image.id))
                }

                if (record.closeout_detail && record.closeout_detail.images && record.closeout_detail.images.length) {
                    record.closeout_detail.images = (imageFiles || []).filter(image => (record.closeout_detail.images || []).includes(image.id))
                }
                return record;
            });
        }
    }
    project_good_calls = await populateInductionEmployerRef(project_good_calls);
    return project_good_calls;
};

const buildStatusMessage = (code) => {
    if(code === 1) {
        return 'Pending';
    } else if(code === 2){
        return 'Accepted';
    } else if(code === 3){
        return 'In Review';
    } else if(code === 0) {
        return 'Rejected';
    }
};

const buildGCStatusMessage = (code) => {
    if(code === 1){
        return 'Open';
    }
    else if(code === 2){
        return 'Closed Out';
    }
    else if(code === 3){
        return 'No Action';
    }
};

const buildAssetStatusMessage = (code) => {
    if(code === 1){
        return 'Pending';
    } else if(code === 2){
        return 'Approved';
    } else if (code === 3) {
        return 'Declined'
    }
};

const toolKeyMapping = {
    toolbox_talks: 'alternate_userlist_toolboxtalks',
    task_briefings: 'alternate_userlist_taskbriefings',
    work_package_plan: 'alternate_userlist_wpps',
    rams: 'alternate_userlist_rams',
};

const getShowAlternativeUserList = (project, toolKey) => {
    if (!project || !project.custom_field) {
      return false; // Fallback for projects without custom_field
    }

    return project.custom_field[toolKeyMapping[toolKey]] || false;
};


/*
 * NOTE: Change in this method will affect following features:
 * - Toolbox Talks,
 * - Task Briefings,
 * - Take 5s,
 * - Work Package Plan(WPP)
 * - Risk Assessment & Method Statements(RAMs)
 *
 * toolKeys: "toolbox_talks", "rams", "task_briefings", "work_package_plan", "take_5s"
 *
 * Recommend: It will require to test all above features on change anything in the method.
 * */
const expandToolboxTalks = async (projectId, additionalFilter, toolboxTalks, toolKey, briefed_by_user_ref = 0, total_record_count) => {
    if(!Object.keys(briefingToolTables || {}).includes(toolKey)) {
        sails.log.error(`expandToolboxTalks: Invalid toolKey ${toolKey} supplied.`);
        return {};
    }
    sails.log.info(`briefed_by_user_ref:   ${briefed_by_user_ref}`);
    let briefings = [];
    let received_briefings = [];
    let registerByBriefedBy = [];
    let registerByAttendee = [];
    let isReceivedBriefing = false;
    let filter = { tool_key: toolKey, project_ref: projectId, ...additionalFilter };
    sails.log.info(`Tool briefings filters: `, filter);
    for (let i = 0; i < toolboxTalks.length ; i++) {
        isReceivedBriefing = false;
        let record = toolboxTalks[i];
        filter.tool_record_ref = record.id
        let recBriefings = await sails.models.toolbriefings_reader.find({
            where: filter,
            select: ['id', 'briefed_at', 'briefed_by', 'briefed_by_name', 'project_ref', 'tool_record_ref', 'register', 'guest_register']
        });
        recBriefings = await populateUserRefs(recBriefings, 'briefed_by', ['id', 'first_name', 'last_name']);

        record.briefed_count = (recBriefings || []).length; //Populating correct briefing count.
        let registerProjectRefs = (recBriefings || []).reduce((ids, r) => {
            if(r && r.project_ref) {
                ids.push(r.project_ref);
            }
            return ids;
        }, []);

        let showAlternativeUserList;
        if(toolKey !== BRIEFING_TOOL_KEY.TOOL_BOX_TALKS || (!record.company_ref && record.project_ref)) {
            showAlternativeUserList = getShowAlternativeUserList(record.project_ref, toolKey);
        } else {
            //can be optimize further to fetch project single time and associate with all briefings
            recBriefings = await populateProjectRefs(recBriefings, 'project_ref', ['custom_field']);
        }

        let projectsData = [];
        if(registerProjectRefs && registerProjectRefs.length) {
            projectsData = await sails.models.project_reader.find({
                where: { id: registerProjectRefs },
                select: ['id', 'name', 'custom_field']
            });
        }
        registerByBriefedBy = recBriefings;
        if (briefed_by_user_ref) {
            registerByAttendee = (recBriefings || []).filter(briefing => ((briefing.register || []).some(r => r.user_ref === briefed_by_user_ref)));
            if (registerByAttendee.length) {
                isReceivedBriefing = true;
            }

            registerByBriefedBy = (recBriefings || []).filter(briefing => ((
                briefing.briefed_by && briefing.briefed_by.id == briefed_by_user_ref)
            ));
            if (!registerByBriefedBy.length && !registerByAttendee.length) {
                continue;
            }
        }

        registerByAttendee = await expandBriefingRegisters(registerByAttendee, toolKey, showAlternativeUserList);
        registerByBriefedBy = await expandBriefingRegisters(registerByBriefedBy, toolKey, showAlternativeUserList);
        record.user_employer = await sails.models.userempdetail.findOne({user_ref: record.user_ref.id});

        if (isReceivedBriefing && registerByAttendee.length) {
            record.register = registerByAttendee;
            received_briefings.push(Object.assign({},record));
        }
        record.register = registerByBriefedBy;
        if (record.status != undefined) {
            record.status_message = buildStatusMessage(record.status);
        }

        if (projectsData && projectsData.length && record.register) {
            record.register = record.register.map(r => {
                if (r.project_ref) {
                    r.project_ref = projectsData.find(proj => proj.id === r.project_ref);
                }
                return r;
            });
        }
        //if true when caller use user filter
        if(briefed_by_user_ref) {
            if (record.register.length) {
                briefings.push(Object.assign({},record));
            }
        } else {
            briefings.push(Object.assign({},record));
        }
    }
    return {expanded_briefings: briefings, received_briefings, total_record_count};
};

const expandBriefingRegisters = async (registers, toolKey, showAlternativeUserList) => {
    if (registers && registers.length) {
        const recPromises = registers.map(async r => {
            let registers = [];
            if(toolKey === BRIEFING_TOOL_KEY.TOOL_BOX_TALKS && !showAlternativeUserList){
                showAlternativeUserList = getShowAlternativeUserList(r.project_ref, toolKey);
            }
            if(showAlternativeUserList) {
                registers = await sails.models.userempdetail.find({
                    where: {user_ref: (r.register || []).map(r => r.user_ref).filter(Number)}
                });
            } else {
                if(r.register && r.register.length) {
                    // check for records without project_ref
                    if(r.project_ref) {
                        registers = await getInductionEmployerByUserIds(_uniq((r.register || []).map(r => r.user_ref).filter(Number)), (HttpService.typeOf(r.project_ref, 'number') ? r.project_ref : r.project_ref.id), [2, 6]) || [];
                    } else {
                        registers = await sails.models.userempdetail.find({
                            where: {user_ref: (r.register || []).map(r => r.user_ref).filter(Number)}
                        });
                    }
                }
            }
            let signatures = await sails.models.usersignature_reader.find({
                where: {briefing_ref: r.id}
            });
            registers = await populateUserRefs(registers, 'user_ref', []);
            r.allattendees = registers.reduce(function (prev, item, i) {
                let name = item.user_ref.first_name+' '+item.user_ref.last_name;
                let signature = (signatures || []).find((object) => {
                    return object.user_ref == item.user_ref.id
                });
                prev.push({id: item.user_ref.id, name: name, emp: showAlternativeUserList ? item.employer : item.user_employer, job_role: item.job_role, sign: signature ? signature.sign : null});
                return prev;
            }, []);

            //push guest attendees as well
            if (r.guest_register && r.guest_register.length) {
                r.guest_register.map(gr => {
                    let signature = (signatures || []).find((object) => {
                        return object.guest_reg_id == gr.id
                    });
                    r.allattendees.push({...gr, sign: signature ? signature.sign : null, emp: gr.employer});
                });
            }
            if (r.briefed_by && r.briefed_by.id) {
                r.briefed_by_name = getUserFullName(r.briefed_by);
                r.briefed_by_user_ref = r.briefed_by.id;
                r.briefed_by = getUserFullName(r.briefed_by);
            } else {
                r.briefed_by = r.briefed_by_name;
            }
            if(!r.briefed_by){
                r.briefed_by = r.briefed_by_name;
            }
            r.briefed_at = +r.briefed_at;//typecasting to int.
            return r;
        });
        registers = await Promise.all(recPromises);
    }
    return registers;
}

//Send Mail to Nominated Managers: Uses by Toolbox Talks and Task Briefing feature
const sendMailToNM = async (toolboxTalk, typeTitle, mailSubject, mailBodyText, mail_body='new-toolbox-talk') => {
    let feature = (typeTitle === 'Toolbox Talk') ? "toolbox_talks" : '';
    feature = (typeTitle === '') ? "task_briefings" : feature;
    feature = (typeTitle === 'rams') ? "rams" : feature;
    feature = (typeTitle === 'wpp') ? "work_package_plan" : feature;
    let projUsrResult = await allProjectAdminsByOneOfDesignations(toolboxTalk.project_ref, [ 'nominated', 'custom']);
    projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, feature);
    sails.log.info('Send email to nom managers, count:', projUsrResult.length);
    if(mail_body === 'new-toolbox-talk'){

        let recipients = projUsrResult.map(pu => ({
            id: (pu.user_ref || {}).id,
            name: getUserFullName(pu.user_ref || {}),
            email: (pu.user_ref || {}).email,
        })).filter(r => !!r.email);
        return await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.PLAIN_MESSAGE_ALERT, recipients, {
            messageInfo: {
                mail_title: mailSubject,
                html_message: mailBodyText,
                origin_action: `${feature}` // for tracing purpose.
            },
        });
    }

    for (let j = 0, len = projUsrResult.length; j < len; j++) {
        let nomManager = projUsrResult[j].user_ref || {};
        let user_name = getUserFullName(nomManager);
        let emailHtml = await sails.renderView('pages/mail/mail-content', {
            title: mailSubject,
            mail_body: mail_body,
            user_name,
            mailBodyText,
            layout: false
        });
        sails.log.info('Sending mail to', nomManager.email);
        await EmailService.sendMail(mailSubject, [nomManager.email], emailHtml);
        sails.log.info(`${typeTitle} email has been sent`);
    }
};

//Send Mail to Invitees: Uses by Toolbox Talks and Task Briefing feature
const sendToolboxTalksInvitation = async (inviteRequest, project, subject, user_name, featureTitle) => {
    for(const item of inviteRequest.items) {
        let itemTitle = '';
        if (featureTitle === 'Toolbox Talk') {
            itemTitle = item.talk_title;
        } else {
            itemTitle = item.briefing_title;
        }

        for(const userToInvite of inviteRequest.usersToInvite) {
            let emailHtml = await sails.renderView('pages/mail/mail-content', {
                title: subject,
                feature_title: featureTitle,
                mail_body: 'invite-to-toolboxtalk',
                recipient_name: userToInvite.name,
                project_name: project.name,
                talk_title: itemTitle,
                invited_by: user_name,
                layout: false
            });
            sails.log.info('Sending mail to', userToInvite.email);
            await EmailService.sendMail(subject, [userToInvite.email], emailHtml);
        }
    }
};

const sendMailToNominatedManagerCPA = async (company_ref, project_ref, user_ref, feature_name, feature_phrase, is_anonymous=false, body_name='mail-to-company-nm', data_obj={}) => {
    let projUsrResult = await allResourceAdminsWithOneOfDesignations(company_ref, ['nominated'], true, 'COMPANY_PROJECT_ADMIN', project_ref.id);
    sails.log.info('Send email to nom managers, count:', projUsrResult.length);
    let subject = `${feature_name}: Project - ${project_ref.name}`;
    for (let j = 0, len = projUsrResult.length; j < len; j++) {
        let nomManager = projUsrResult[j].user_ref || {};
        let user_name = getUserFullName(nomManager);
        let submitted_by = getUserFullName(user_ref);
        let mail_content = `${submitted_by} has ${feature_phrase} for project ${project_ref.name}`;
        if (is_anonymous) {
            mail_content = `A ${feature_name} has been raised anonymously for project ${project_ref.name}`;
        }

        let emailHtml = await sails.renderView('pages/mail/mail-content', {
            title: subject,
            mail_body: body_name,
            user_name,
            mail_content,
            data_obj,
            submitted_by,
            project_name: project_ref.name,
            layout: false
        });

        sails.log.info('Sending mail to', nomManager.email);
        await EmailService.sendMail(subject, [nomManager.email], emailHtml);
        sails.log.info(`${feature_name} notification email has been sent`);
    }
};

const sendMailToNominatedManagerPA = async (project_ref, user_ref, feature_name, feature_phrase, is_anonymous=false, body_name='mail-to-company-nm', data_obj={}) => {
    let projUsrResult = await allResourceAdminsWithOneOfDesignations(project_ref.id, ['nominated'], true, 'SITE_ADMIN');
    sails.log.info('Send email to nom managers, count:', projUsrResult.length);
    let subject = `${feature_name}: Project - ${project_ref.name}`;
    for (let j = 0, len = projUsrResult.length; j < len; j++) {
        let nomManager = projUsrResult[j].user_ref || {};
        let user_name = getUserFullName(nomManager);
        let submitted_by = getUserFullName(user_ref);
        let mail_content = `${submitted_by} has ${feature_phrase} for project ${project_ref.name}`;
        if (is_anonymous) {
            mail_content = `A ${feature_name} has been raised anonymously for project ${project_ref.name}`;
        }

        let emailHtml = await sails.renderView('pages/mail/mail-content', {
            title: subject,
            mail_body: body_name,
            user_name,
            mail_content,
            data_obj,
            submitted_by,
            project_name: project_ref.name,
            layout: false
        });

        sails.log.info('Sending mail to', nomManager.email);
        await EmailService.sendMail(subject, [nomManager.email], emailHtml);
        sails.log.info(`${feature_name} notification email has been sent`);
    }
};

const expandUsersByIds = async (userIds = [], select = ['id','first_name','last_name','email']) => {
    sails.log.info('Expanding users.', userIds);
    let users = await sails.models.user_reader.find({
        where: {id: userIds},
        select: select
    });

    users = (users || []).map(user => {
        user.name = getUserFullName(user);
        return user;
    });

    sails.log.info('Expanded users, ', users.length);
    return users;
};

/**
 *
 * @param projectId
 * @param expand
 * @returns {Promise<*[]>}
 */
const getMandatoryCompetencyExceptionList = async (projectId, expand = true) => {
    sails.log.info(`Expanding mandatory competency exception list, project: ${projectId}`);
    let existingSetting = await sails.models.projectsetting_reader.findOne({
        project_ref: projectId,
        name: MANDATORY_COMPETENCY_EXCEPTION_USERS,
    });
    if (existingSetting && existingSetting.value) {
        return expand ? (await expandUsersByIds(existingSetting.value, ['id', 'first_name', 'middle_name', 'last_name'])) : existingSetting.value;
    }
    return [];
};

const getInspectionTourMetaChecklist = async(checklistType) => {
    let checklist = await sails.models.inndexsetting.findOne({
        where: {name: checklistType},
    });

    if (checklist && checklist.id) {
        checklist = _.chain(checklist.value)
            .value();
        sails.log.info('inspection tour query successful', checklist ? checklist.length : null);
    }

    return checklist;
};

//Inspection Tour
const getChecklistItemsReferences = async (projectInfo, projectInspectionTour) => {
    let responsibleUserIds = [];
    let taggedCompanyIds = [];
    if (projectInfo.project_type === 'industrial') {
        let industrialResUserIds = (projectInspectionTour.industrial_checklist || []).reduce((userIds, item) => {
            if (item.responsible_user_ref && !isNaN(item.responsible_user_ref)) {
                userIds.push(item.responsible_user_ref);
            }

            if (item.tagged_company_ref && item.tagged_company_ref.length) {
                taggedCompanyIds.push(...item.tagged_company_ref);
            }
            return userIds;
        }, []);
        responsibleUserIds.push(...industrialResUserIds);

        let additionalResUserIds = (projectInspectionTour.additional_checklist || []).reduce((userIds, item) => {
            if (item.responsible_user_ref && !isNaN(item.responsible_user_ref)) {
                userIds.push(item.responsible_user_ref);
            }

            if (item.tagged_company_ref && item.tagged_company_ref.length) {
                taggedCompanyIds.push(...item.tagged_company_ref);
            }
            return userIds;
        }, []);
        responsibleUserIds.push(...additionalResUserIds);
    } else {
        let commonResUserIds = (projectInspectionTour.common_checklist || []).reduce((userIds, item) => {
            if (item.responsible_user_ref && !isNaN(item.responsible_user_ref)) {
                userIds.push(item.responsible_user_ref);
            }

            if (item.tagged_company_ref && item.tagged_company_ref.length) {
                taggedCompanyIds.push(...item.tagged_company_ref);
            }
            return userIds;
        }, []);
        responsibleUserIds.push(...commonResUserIds);
        if (projectInfo.project_type === 'rail') {
            let railResUserIds = (projectInspectionTour.rail_checklist || []).reduce((userIds, item) => {
                if (item.responsible_user_ref && !isNaN(item.responsible_user_ref)) {
                    userIds.push(item.responsible_user_ref);
                }

                if (item.tagged_company_ref && item.tagged_company_ref.length) {
                    taggedCompanyIds.push(...item.tagged_company_ref);
                }
                return userIds;
            }, []);
            responsibleUserIds.push(...railResUserIds);
        }
    }

    sails.log.info('Responsible User Ids of Items, ', responsibleUserIds);
    let usersInfo = await sails.models.user_reader.find({
        where: {id: _uniq(responsibleUserIds)},
        select: ['first_name', 'middle_name', 'last_name', 'email', 'timezone']
    });

    sails.log.info('Tagged Company Ids of Items, ', taggedCompanyIds);
    let companiesInfo = await sails.models.createemployer_reader.find({
        where: {id: _uniq(taggedCompanyIds)},
        select: ['name']
    });

    return {checklistItemsResponsibleUsers: usersInfo, checklistItemsTaggedCompanies: companiesInfo};
};

const getInspectionTours = async (filter, OrderBy, limitedData = false, expandItems = false,) => {
    let projectInspectionTours = [];
    let total_record_count = 0;
    if (!limitedData) {
        let { where: {project_ref = 0, finalised, user_ref, searchTerm = "",createdAt}, skip, limit } = filter
        let {records, total} = await getInspectionToursFn(project_ref, limit, skip, 'id', 'DESC', {searchTerm, finalised, user_ref, createdAt});
        projectInspectionTours = records;
        total_record_count = total;
        projectInspectionTours = await populateProjectRefs(projectInspectionTours, 'project_ref', []);
        projectInspectionTours = await populateUserRefs(projectInspectionTours, 'responsible_manager_user_ref', []);
        projectInspectionTours = await populateUserRefs(projectInspectionTours, 'supervisor_user_ref', []);
    } else {
        projectInspectionTours = await sails.models.projectinspectiontour.find({
            ...filter,
            select: ['record_id', 'user_ref', 'project_ref', 'common_checklist', 'rail_checklist', 'industrial_checklist', 'additional_checklist', 'createdAt']
        })
            .sort([
                {id: 'DESC'}
            ]);
        projectInspectionTours = await populateProjectRefs(projectInspectionTours, 'project_ref', []);
    }
    projectInspectionTours = (projectInspectionTours || []).filter(it => it.project_ref != null);

    sails.log.info('Fetched project inspection tours.', projectInspectionTours.length);

    if (expandItems) {
        let projectInfo = {};
        let itemAppendixImageIds = [];
        let responsibleUserIds = [];
        let taggedCompanyIds = [];
        for (projectInspectionTour of projectInspectionTours) {
            projectInfo = projectInspectionTour.project_ref;

            if (projectInfo.project_type === 'industrial') {
                (projectInspectionTour.industrial_checklist || []).map(item => {
                    if (item.appendix.length) {
                        itemAppendixImageIds.push(...item.appendix);
                    }
                    if (item.responsible_user_ref) {
                        responsibleUserIds.push(+item.responsible_user_ref);
                    }

                    if (item.tagged_company_ref && item.tagged_company_ref.length) {
                        taggedCompanyIds.push(...item.tagged_company_ref);
                    }
                    return item;
                });

                (projectInspectionTour.additional_checklist || []).map(item => {
                    if (item.appendix.length) {
                        itemAppendixImageIds.push(...item.appendix);
                    }
                    if (item.responsible_user_ref) {
                        responsibleUserIds.push(+item.responsible_user_ref);
                    }
                    if (item.tagged_company_ref && item.tagged_company_ref.length) {
                        taggedCompanyIds.push(...item.tagged_company_ref);
                    }
                    return item;
                });
            } else {
                (projectInspectionTour.common_checklist || []).map(item => {
                    if (item.appendix.length) {
                        itemAppendixImageIds.push(...item.appendix);
                    }
                    if (item.responsible_user_ref) {
                        responsibleUserIds.push(+item.responsible_user_ref);
                    }
                    if (item.tagged_company_ref && item.tagged_company_ref.length) {
                        taggedCompanyIds.push(...item.tagged_company_ref);
                    }
                    return item;
                });

                if (projectInfo.project_type === 'rail') {
                    (projectInspectionTour.rail_checklist || []).map(item => {
                        if (item.appendix.length) {
                            itemAppendixImageIds.push(...item.appendix);
                        }
                        if (item.responsible_user_ref) {
                            responsibleUserIds.push(+item.responsible_user_ref);
                        }
                        if (item.tagged_company_ref && item.tagged_company_ref.length) {
                            taggedCompanyIds.push(...item.tagged_company_ref);
                        }
                        return item;
                    });
                }
            }
        }

        sails.log.info('Appendix images of Items, ', _uniq(itemAppendixImageIds));
        let itemAppendixImages = await sails.models.userfile.find({
            where: {id: _uniq(itemAppendixImageIds)},
            select: ['id', 'file_url', 'sm_url']
        });

        let responsibleUsers = await sails.models.user_reader.find({
            where: {id: _uniq(responsibleUserIds)},
            select: ['first_name', 'middle_name', 'last_name', 'email']
        });

        sails.log.info('Tagged Company Ids of Items, ', taggedCompanyIds);
        let companiesInfo = await sails.models.createemployer_reader.find({
            where: {id: _uniq(taggedCompanyIds)},
            select: ['name']
        });

        for (projectInspectionTour of projectInspectionTours) {
            projectInfo = projectInspectionTour.project_ref;
            if (projectInfo.project_type === 'industrial') {
                (projectInspectionTour.industrial_checklist || []).map(item => {
                    if (item.appendix.length) {
                        item.appendix = itemAppendixImages.filter(img => item.appendix.includes(img.id));
                    }
                    if (item.responsible_user_ref) {
                        item.responsible_user_ref = responsibleUsers.find(user => user.id === item.responsible_user_ref) || null;
                    }

                    if (item.tagged_company_ref && item.tagged_company_ref.length) {
                        item.tagged_company_ref = companiesInfo.filter(company => item.tagged_company_ref.includes(company.id));
                    }
                    return item;
                });

                (projectInspectionTour.additional_checklist || []).map(item => {
                    if (item.appendix.length) {
                        item.appendix = itemAppendixImages.filter(img => item.appendix.includes(img.id));
                    }
                    if (item.responsible_user_ref) {
                        item.responsible_user_ref = responsibleUsers.find(user => user.id === item.responsible_user_ref) || null;
                    }
                    if (item.tagged_company_ref && item.tagged_company_ref.length) {
                        item.tagged_company_ref = companiesInfo.filter(company => item.tagged_company_ref.includes(company.id));
                    }
                    return item;
                });
            } else {
                (projectInspectionTour.common_checklist || []).map(item => {
                    if (item.appendix.length) {
                        item.appendix = itemAppendixImages.filter(img => item.appendix.includes(img.id));
                    }
                    if (item.responsible_user_ref) {
                        item.responsible_user_ref = responsibleUsers.find(user => user.id === item.responsible_user_ref) || null;
                    }
                    if (item.tagged_company_ref && item.tagged_company_ref.length) {
                        item.tagged_company_ref = companiesInfo.filter(company => item.tagged_company_ref.includes(company.id));
                    }
                    return item;
                });

                if (projectInfo.project_type === 'rail') {
                    (projectInspectionTour.rail_checklist || []).map(item => {
                        if (item.appendix.length) {
                            item.appendix = itemAppendixImages.filter(img => item.appendix.includes(img.id));
                        }
                        if (item.responsible_user_ref) {
                            item.responsible_user_ref = responsibleUsers.find(user => user.id === item.responsible_user_ref) || null;
                        }
                        if (item.tagged_company_ref && item.tagged_company_ref.length) {
                            item.tagged_company_ref = companiesInfo.filter(company => item.tagged_company_ref.includes(company.id));
                        }
                        return item;
                    });
                }
            }
        }
    }
    projectInspectionTours.map(record =>{
        record.record_ref = buildRecordRef(record);
        record.createdAt = +record.createdAt;
        record.updatedAt = +record.updatedAt;
        return record
    })

    let commonChecklist = await getInspectionTourMetaChecklist('inspection_tour_common_checklist');
    let railChecklist = await getInspectionTourMetaChecklist('inspection_tour_rail_checklist');
    let industrialChecklist = await getInspectionTourMetaChecklist('inspection_tour_industrial_checklist');

    return {projectInspectionTours, commonChecklist, railChecklist, industrialChecklist,total_record_count};
};

const itemCloseoutDueReminder = async () => {
    let {projectInspectionTours, commonChecklist, railChecklist, industrialChecklist} = await getInspectionTours({where: {status:1, finalised: true}}, 'DESC', true, true);

    for (let inspectionTour of projectInspectionTours) {
        let metaChecklist = [];
        let inspectionTourChecklist = [];
        if (inspectionTour.project_ref.project_type === 'industrial') {
            metaChecklist = [...industrialChecklist, ...inspectionTour.additional_checklist];
            inspectionTourChecklist = [...inspectionTour.industrial_checklist, ...inspectionTour.additional_checklist];
        } else {
            metaChecklist = commonChecklist;
            inspectionTourChecklist = inspectionTour.common_checklist;
            if (inspectionTour.project_ref.project_type === 'rail') {
                metaChecklist = [...commonChecklist, ...railChecklist];
                inspectionTourChecklist = [...inspectionTour.common_checklist, ...inspectionTour.rail_checklist];
            }
        }
        for (let item of inspectionTourChecklist) {
            if (item.responsible_user_ref && item.responsible_user_ref.email
                && item.close_out && !Object.keys(item.close_out).length && item.closeout_due_on
                && (moment().format('L') === moment(+item.closeout_due_on).format('L'))
            ) {
                let imagesSrc = [];
                if(item.appendix && item.appendix.length) {
                    imagesSrc = (item.appendix || []).reduce((arr, imageObj) => {
                        if (imageObj.id) {
                            arr.push((imageObj.sm_url || imageObj.file_url));
                        }
                        return arr;
                    }, [])
                }


                let rating_color = '#000';
                if (['good', 'yes'].includes(item.answer)) {
                    rating_color = "#92F282";
                } else if (['poor', 'no'].includes(item.answer)) {
                    rating_color = "#FE8282";
                } else if (item.answer === 'fair') {
                    rating_color = "#ffa500a6";
                }

                let itemMetaInfo = (metaChecklist || []).find(metaItem => metaItem.question_id == item.question_id);
                let subject = `Inspection: ${inspectionTour.project_ref.name}`;
                let html = await sails.renderView('pages/mail/mail-content', {
                    mail_body: 'inspection-tour-item-to-closeout-due',
                    title: subject,
                    responsible_user_fname: item.responsible_user_ref.first_name,
                    inspection_number: buildRecordRef(inspectionTour),
                    project_name: inspectionTour.project_ref.name,
                    item_title: `${itemMetaInfo.category} - ${itemMetaInfo.question}`,
                    rating: (item.answer) ? item.answer.toUpperCase() : 'N/A',
                    rating_color,
                    item_summary: item.summary,
                    action: (item.corrective_action_required) ? item.corrective_action_required : 'N/A',
                    appendix_images: imagesSrc,
                    layout: false
                });
                await EmailService.sendMail(subject, [item.responsible_user_ref.email], html);
            }
        }
    }
    return true;
};

//Send Mail to Nominated Managers: Used by Inspection Tour, Inspection Builder.
const sendInspectionMailToNomMngr = async (project_ref, user_ref, purpose, data_obj={}) => {
    let projUsrResult = await allProjectAdminsByOneOfDesignations(project_ref.id, [ 'nominated', 'custom']);
    if(purpose !== 'Excavation Inspection Submitted') {
        projUsrResult = filterProjectUsersEmailEligibility(projUsrResult, 'inspections');
    }
    sails.log.info('Send inspection email to nom managers, count:', projUsrResult.length);
    let subject = `${purpose}: Project - ${project_ref.name}`;
    for (let j = 0, len = projUsrResult.length; j < len; j++) {
        let nomManager = projUsrResult[j].user_ref || {};
        let user_name = getUserFirstName(nomManager);
        let submitted_by = getUserFullName(user_ref);
        let emailHtml = await sails.renderView('pages/mail/mail-content', {
            title: subject,
            mail_body: 'inspection-mail-to-company-nm',
            user_name,
            submitted_by,
            project_name: project_ref.name,
            data_obj,
            layout: false
        });
        sails.log.info('Sending inspection mail to', nomManager.email);
        await EmailService.sendMail(subject, [nomManager.email], emailHtml);
        sails.log.info(`${purpose} email has been sent`);
    }
};

const getTimezone = async (id, type, recordInfo={}) => {
    sails.log.info(`get timezone for ${type} with ID: ${id}`);
    let timezone = fall_back_timezone;
    if (type == 'project') {
        if (!recordInfo.id) {
            recordInfo = await sails.models.project_reader.findOne({
                where: {
                    id: id
                },
                select: ['name', 'custom_field']
            });
        }

        timezone = (recordInfo && recordInfo.custom_field && recordInfo.custom_field.timezone) || timezone;
    } else {
        let companySetting = await sails.models.companysetting_reader.findOne({
            where: {
                company_ref: id,
                name: 'timezone'
            },
            select: ['value']
        });
        timezone = (companySetting && companySetting.timezone) || timezone;
    }
    sails.log.info(`timezone for ${type} with ID: ${id} is ${timezone}.`);
    return timezone;
};

const populateUserRefs = async (records, userRefField, select = []) => {
    sails.log.info(`Populating users, ${records.length} records`);
    let userIds = (records || []).reduce((arr, record) => {
        if (record[userRefField] && typeof record[userRefField] == 'number') {
            arr.push(record[userRefField]);
        }
        return arr;
    }, []);

    if (!userIds.length) {
        sails.log.info(`No user ref available to expand in ${records.length} records.`);
        return records;
    }

    let find = {
        where: {id: _uniq(userIds)}
    };
    if (select.length) {
        find.select = select;
    }
    let users = await sails.models.user_reader.find(find);
    records = (records || []).map(record => {
        let matchedUser = users.find(user => (record[userRefField] && user.id == record[userRefField]));
        if(matchedUser) {
            matchedUser.name = getUserFullName(matchedUser);
            record[userRefField] = matchedUser;
        }
        return record;
    });
    sails.log.info(`Expanded ${users.length} users for ${records.length} records`);
    return records;
};

const populateProjectRefs = async (records, projectRefField, select = [], additional_filter = {}) => {
    sails.log.info(`Populating projects, ${records.length} records`);
    let projectIds = (records || []).reduce((arr, record) => {
        if (record[projectRefField] && typeof record[projectRefField] == 'number') {
            arr.push(record[projectRefField]);
        }
        return arr;
    }, []);

    if (!projectIds.length) {
        sails.log.info(`No project ref available to expand in ${records.length} records.`);
        return records;
    }

    let find = {
        where: {id: _uniq(projectIds), ...additional_filter}
    };
    if (select.length) {
        find.select = select;
    }
    let projects = await sails.models.project_reader.find(find);
    records = (records || []).map(record => {
        let matchedProject = projects.find(project => (record[projectRefField] && project.id == record[projectRefField]));
        if(matchedProject) {
            matchedProject.record_id = `${(matchedProject.project_initial || '').toString().trim().toUpperCase()}${matchedProject.record_id}`;
            record[projectRefField] = matchedProject;
        }
        return record;
    });
    sails.log.info(`Expanded ${projects.length} projects for ${records.length} records`);
    return records;
};

const populateJobRoles = async (records, key = 'job_role_ref') => {
    if (!records.length) {
        return records;
    }
    let jobRolIds = [...new Set(records.map(r => r[key]))].filter(id => +id);
    if (!jobRolIds.length) {
        return records;
    }
    sails.log.info(`expanding ${key}: (${jobRolIds})`);
    let job_role_list = await sails.models.jobroles_reader.find({
        select: ['id', 'name', 'country_code'],
        where: {id: jobRolIds}
    });

    records = records.map(r => {
        r[key] = job_role_list.find(j => j.id === r[key]) || null;
        return r;
    });
    sails.log.info(`expanded total ${job_role_list.length} job_role refs for ${records.length} records`);
    return records;
};

/**
 * populate children records per user document
 * @param user_docs
 * @param document_filter
 * @returns {Promise<[]>}
 */
const populateDocumentChildren = async (user_docs, document_filter = {}) => {
    if(!user_docs || !user_docs.length){
        return user_docs;
    }
    let parent_doc_ids = user_docs.map(d => d.id);
    sails.log.info(`populating user-doc children for ${user_docs.length} parent records`);
    let child_docs = await sails.models.userdoc_reader.find({
        where: {
            ...document_filter,
            parent_doc_ref: parent_doc_ids,
        }
    });
    user_docs = user_docs.map(d => {
        d.children = child_docs.filter(child => child.parent_doc_ref === d.id);
        return d;
    });
    sails.log.info(`attached ${child_docs.length} child records with ${user_docs.length} parent records`);
    return user_docs;
};

const populateUserFileRefs = async (records, userFileRefField, select = []) => {
    sails.log.info(`Populating user file refs.`);
    let userFileIds = (records || []).reduce((arr, record) => {
        if (record[userFileRefField] && typeof record[userFileRefField] == 'number') {
            arr.push(record[userFileRefField]);
        }
        return arr;
    }, []);

    if (!userFileIds.length) {
        sails.log.info(`No userfile ref Ids available to expand in ${records.length} records.`);
        return records;
    }

    let find = {
        where: {id: _uniq(userFileIds)}
    };
    if (select.length) {
        find.select = select;
    }
    let userFiles = await sails.models.userfile_reader.find(find);
    records = (records || []).map(record => {
        let matchedFile = userFiles.find(file => (record[userFileRefField] && file.id == record[userFileRefField]));
        if(matchedFile) {
            record[userFileRefField] = matchedFile;
        }
        return record;
    });
    sails.log.info(`Expanded ${userFiles.length} user files for ${records.length} records`);
    return records;
};

/*
 * Expand file ids inside array of objects
 * Example: [{"file_refs": [1234]}, {"file_refs": [3213]}]
 * here 'field' is DB column name and subField is key inside object i.e. 'file_refs' as in above example
 * */
const populateAttachmentFileRefs = async (records, field, subField, selectColumn = ['id', 'name', 'file_url']) => {
    const fileIds = records
        .filter(record => record[field] && record[field].length)
        .flatMap(record => record[field]
            .flatMap(attachment => attachment[subField] || [])
        );

    if (!fileIds.length) {
        return records;
    }

    let files = await sails.models.userfile_reader.find({
        where: {id: fileIds},
        select: selectColumn
    });

    records.forEach(record => {
        record[field] && record[field].length && record[field].forEach(attachment => {
            attachment[subField] && attachment[subField].length &&
            (attachment[subField] = files.filter(file => attachment[subField].includes(file.id)))
        });
    });

    return records;
}

const populateEmployerRefs = async(records, employerRefField, select =[]) => {
    sails.log.info(`Populating employer refs.`);
    let employerIds = (records || []).reduce((arr, record) => {
        if (record[employerRefField] && typeof record[employerRefField] == 'number') {
            arr.push(record[employerRefField]);
        }
        return arr;
    }, []);

    if (!employerIds.length) {
        sails.log.info(`No employer ref Ids available to expand in ${records.length} records.`);
        return records;
    }

    let find = {
        where: {id: _uniq(employerIds)}
    };
    if (select.length) {
        find.select = select;
    }
    let employers = await sails.models.createemployer_reader.find(find);
    records = (records || []).map(record => {
        let matchedEmployer = employers.find(file => (record[employerRefField] && file.id == record[employerRefField]));
        if(matchedEmployer) {
            record[employerRefField] = matchedEmployer;
        }
        return record;
    });
    sails.log.info(`Expanded ${employers.length} employers for ${records.length} records`);
    return records;
}

const shareReportViaEmail = async(req, res, html, tool, attachmentName, tool_phrase, byUser, emailList, projectName) => {
    sails.log.info('[shareReportViaEmail] ', tool);
    let pdfBuffer = await instantPdfGenerator(req, res, html, tool, attachmentName, req.headers['user-agent'], {format: 'A4'}, 'pdfBuffer');
    let subject = `${tool_phrase} Report Shared`;
    let emailHtml = await sails.renderView('pages/mail/mail-content', {
        title: subject,
        mail_body: 'share-tool-report-mail',
        tool_phrase: tool_phrase,
        invited_by: getUserFullName(byUser),
        project_name: projectName,
        layout: false
    });
    for(const em of emailList) {
        sails.log.info('Sending mail to', em.email);
        await EmailService.sendRawEmail(subject, [em.email], emailHtml, pdfBuffer, `${attachmentName}.pdf`, true);
    }
}

const buildIncidentReportStatus = (code) => {
    if(code === 1){
        return 'Open';
    }
    else if(code === 2){
        return 'Reviewed';
    }
    else if(code === 3){
        return 'Closed';
    }
};

const getPdfHeaderTemplate = async(heading_line, project_line, date_line, project_logo_file, additional_line, ellipses_heading, use_inndex_logo) => {
    let logoBase64 = '';
    if(project_logo_file && project_logo_file.id && project_logo_file.file_url){
        let imageBuffer = await HttpService.fetchUrlAs(project_logo_file.file_url);
        logoBase64 = Buffer.from(imageBuffer.data).toString('base64');
    }

    return await sails.renderView(`pages/form/v2/global-pdf-header`, {
        heading_line,
        project_line,
        date_line,
        additional_line,
        logoBase64,
        use_inndex_logo: use_inndex_logo || false,
        layout: false,
        ellipses_heading,
    });
}

const getHourlyWeatherLogs = async (location_key, postcode, shift_date, shift_time) => {
    if(!location_key || !location_key.length){
        sails.log.info(`Project Location Key not attached postcode: ${postcode}`);
        return [];
    }
    let totalHrs = moment.utc(+moment(shift_time.to, "HH:mm").diff(moment(shift_time.from, "HH:mm"))).format("H");
    let date = moment(shift_date + ' ' + shift_time.from, 'YYYY-MM-DD HH:mm');
    let d = [date.format('YYYY-MM-DDTH')];
    let hrs = 1;
    for(let i = 1; i <= totalHrs; i++){
        d.push(date.clone().add(i, 'hours').format('YYYY-MM-DDTH'));
        hrs++;
    }
    //Add one hour extra round off extra minutes in `shift_time.to`.
    if(+moment(shift_time.to, "HH:mm").format('m') > 0) {
        d.push(date.clone().add(hrs, 'hours').format('YYYY-MM-DDTH'));
    }
    sails.log.info('get hourly weather logs for ', d);
    let logs = [];
    let weatherLogResult = await sails.sendNativeQuery(`SELECT * FROM weather_log
        WHERE location_key = $1
            AND day IN ('${d.join("','")}')
        ORDER BY forecast ->>'EpochDateTime' ASC`,
        [location_key]
    );
    let missingHrs = [];
    if (HttpService.typeOf(weatherLogResult.rows, 'array') && weatherLogResult.rows.length) {
        logs = weatherLogResult.rows;
        for(let i = 0, len = d.length; i < len; i++) {
            if(logs.findIndex(l => l.day === d[i]) === -1) {
                missingHrs.push(d[i]);
            }
        }
    } else {
        missingHrs = d;
        logs = [];
    }

    let isFutureDate = moment().diff(moment(missingHrs[missingHrs.length - 1], 'YYYY-MM-DDTH'), 'h') < 0;
    // Fetch weather only if it's unavailable for future hours.
    if(isFutureDate && missingHrs && missingHrs.length && missingHrs.length >= 1) {
        let accuweather_key = (sails.config.custom.HOURLY_ACCU_WEATHER_API_KEY || '');
        let response = await HttpService.makeGET(`http://dataservice.accuweather.com/forecasts/v1/hourly/12hour/${location_key}`, {
            apikey: accuweather_key,
            details: true,
            metric: true // for celcius in response
        }, {}, true, 20000);

        if (response.success && response.data.length) {
            sails.log.info(`found hrourly forecast ${location_key}`);
            let promises = response.data.map(async f => {
                let forecastDateTime = moment(f.DateTime, 'YYYY-MM-DDTHH:mm:ss').format('YYYY-MM-DDTH');
                return await updateOrCreateWeatherLog(location_key, forecastDateTime, f, 'hourly-forecast');
            });

            let futureHrLogs = await Promise.all(promises);
            futureHrLogs.map(weather_log => {
                if(d.indexOf(weather_log.day) !== -1){
                    logs.concat(weather_log)
                }
            })
        } else {
            sails.log.info('Forecast not found', location_key, ' HTTP Status: ', response.status);
        }
    }
    return logs;
};

const populateInductionEmployerRef = async (records, userRefField = 'user_ref', status_code = [2,6]) => {
    sails.log.info(`Populating user employer from induction..`);
    if (!records.length) {
        return records;
    }

    let noOfEscaped = 0;
    let project_ids = _uniq(records.map(record => (record.project_ref && (typeof record.project_ref === "number")) ? record.project_ref : (record.project_ref && record.project_ref.id ? record.project_ref.id : undefined))).filter(id => id !== undefined);
    let user_ids = _uniq(records.map(record => record[userRefField] && record[userRefField].id));
    let rawResult = await sails.sendNativeQuery(
        `SELECT DISTINCT ON (user_ref)
            user_ref, project_ref, status_code,
            additional_data->'employment_detail' as employment_detail
        FROM induction_request
        WHERE
            project_ref IN (${project_ids.map(() => {
                noOfEscaped++;
                return `$${noOfEscaped}`;
            }).join(',')}) AND
            status_code IN (${status_code.map(() => {
                noOfEscaped++;
                return `$${noOfEscaped}`;
            }).join(',')}) AND
            user_ref IN (${user_ids.map(() => {
                noOfEscaped++;
                return `$${noOfEscaped}`;
            }).join(',')})
        ORDER BY user_ref, id DESC`,
        [...project_ids, ...status_code, ...user_ids]
    );
    if (!HttpService.typeOf(rawResult.rows, 'array') || !rawResult.rows.length) {
        return records;
    }
    records = (records || []).map(record => {
        let userid = (record[userRefField] && record[userRefField].id) ? record[userRefField].id : record[userRefField];
        let projectid = (record.project_ref && record.project_ref.id) ? record.project_ref.id : record.project_ref;
        let inductUser = rawResult.rows.find(r => ((userid && r.user_ref == userid) && (projectid && r.project_ref == projectid)));
        if(inductUser && inductUser.employment_detail && inductUser.employment_detail.id) {
            let employer = inductUser.employment_detail;
            record['user_employer'] = {_id: employer.id, employer: employer.employer, job_role: employer.job_role};
        }
        return record;
    });
    return records;
};

const getUpdatedOnboardStatus = (user, section) => {
    if(!["personal", "address", "health_assessment", "employment", "competencies", "medical_assessments"].includes(section)) {
        sails.log.error('[getUpdatedOnboardStatus] Unknown `section` value supplied.');
    }
    return {...user.user_onboard_status, [section]: true};
}

const replaceAll = (str='', find, replace) => {
    if (str) {
        const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
        return (str.replace(new RegExp(escapedFind, 'g'), replace)).toLowerCase();
    }
    return '';
};

const briefingToolTables = {
    "toolbox_talks": "toolbox_talks",
    "rams": "project_rams",
    "task_briefings": "project_task_briefings",
    "work_package_plan": "project_work_package_plans",
    "take_5s": "take_5s"
};

const capitalizeFirstLetter = (s) => {
    return (typeof s == 'string') ? s.charAt(0).toUpperCase() + s.slice(1) : '';
};

const getCoordinates = async (postcode,countryCode=DEFAULT_COUNTY_CODE_GB) => {
            const response = await HttpService.makeGET(
                `https://api.mapbox.com/geocoding/v5/mapbox.places/${postcode}.json`,
                {access_token:sails.config.custom.MAPBOX_SECRET_ACCESS_KEY,
                country:countryCode}
                );
                const features = response && response.data && response.data.features;
                if (!features || !features.length) {
                    sails.log.warn(`invalid postcode - ${postcode} countryCode- ${countryCode}`);
                    return null;
                }

                return features[0].center;
  };

const calculateDistance = async (originCoordinates, destinationCoordinates, getTime = false) => {
    try {
        let response = await HttpService.makeGET(
            `https://api.mapbox.com/directions/v5/mapbox/driving-traffic/${originCoordinates.long},${originCoordinates.lat};${destinationCoordinates.long},${destinationCoordinates.lat}`,
            {access_token:sails.config.custom.MAPBOX_SECRET_ACCESS_KEY}
        );
        if (response && response.data && response.data.routes.length) {
            if(getTime){
                return response.data
            }
            const distanceMeters = response.data.routes[0].distance;
            return parseFloat(distanceMeters).toFixed(2);
        }
    } catch (error) {
        sails.log.error(error);
        return null;
    }
};
const updateTravelTime = async (inductionRequest, coordinate, failedInductions = [], location, cachedData = {}, post_code = "") => {
    let distance, travel_time;
    if (coordinate) {
        distance =
            inductionRequest.travel_time &&
            inductionRequest.travel_time.distance_matrix &&
            inductionRequest.travel_time.distance_matrix.distance &&
            inductionRequest.travel_time.distance_matrix.distance.value ||
            (cachedData[post_code] && cachedData[post_code].distance) ||
            await calculateDistance(location, {
                long: coordinate[0],
                lat: coordinate[1]
            });
    }
    if (coordinate && distance) {
        travel_time = {
            ...inductionRequest.travel_time,
            coordinate: {
                long: coordinate[0],
                lat: coordinate[1]
            },
            distance_matrix: {
                ...(inductionRequest.travel_time && inductionRequest.travel_time.distance_matrix || {}),
                distance: {
                    ...((inductionRequest.travel_time && inductionRequest.travel_time.distance_matrix &&
                        inductionRequest.travel_time.distance_matrix.distance && {
                            ...inductionRequest.travel_time.distance_matrix.distance,
                            value: inductionRequest.travel_time.distance_matrix.distance.value || distance,
                        }) || {
                        value: +distance
                    }),
                },
            },
        };
        return {travel_time, distance}
    } else {
        failedInductions && failedInductions.push(inductionRequest.id);
        sails.log.info(` induction request updation failed for id - ${inductionRequest.id}`)
        return {
            travel_time: inductionRequest.travel_time,
            failedInductions
        }
    }
}

async function processVisitingRecords(projectIds, blackListedUserIds) {
    try {
        let todayDate = moment().format('YYYY-MM-DD');


        let daily_logs = await getDailyTimeEventV2(
            projectIds,
            moment().subtract(30, 'day').format('YYYY-MM-DD'),
            moment().add(1, 'day').format('YYYY-MM-DD')
        );
        sails.log.info("Found last 30 days visiting records. Length: ", daily_logs.length);

        let last30_visited_userId = _uniq(daily_logs.reduce((arr, l) => {
            if (l.user_id && !blackListedUserIds.includes(l.user_id)) {
                arr.push(l.user_id);
            }
            return arr;
        }, []));

        let today_logs = (daily_logs || []).filter(log => log.day_of_yr == todayDate);
        sails.log.info("Found today visiting records. Length: ", today_logs.length);

        let today_visited_userId = _uniq(today_logs.reduce((arr, l) => {
            if (l.user_id && !blackListedUserIds.includes(l.user_id)) {
                arr.push(l.user_id);
            }
            return arr;
        }, []));

        return { last30_visited_userId, today_visited_userId };
    } catch (error) {
        console.error(error);
        throw new Error('An error occurred while processing visiting records.');
    };

};
const getProjectDistrict = async (projectId) => {
    sails.log.info(`Fetching districts for project ${projectId}.`)
    let projectDistricts = await sails.models.projectsetting_reader.findOne({
        select: ['value'],
        where: {'name': 'uk_districts', 'project_ref': +projectId}
    });
    projectDistricts = (projectDistricts && projectDistricts.value && projectDistricts.value.length) ? projectDistricts.value : [];
    if (projectDistricts.length) {
        let result =  await sails.models.metadistrict_reader.find({
            where: {id: projectDistricts},
            select: ['district_name']
        });
        return (result || []).map(item => item.district_name);
    }
    return [];
}

const updateCompanyProjectsSupplyChain = async (company_id, supply_chain_list = []) => {
    let company_projects = await companyFn.getCompanyAccessibleProjects(company_id, ['id', 'custom_field']);

    sails.log.info('Company supply chain list: ', supply_chain_list);

    for(const project of company_projects) {
        sails.log.info('Processing Project ', project.id, ' existing project supply chain ', project.custom_field.supply_chain_companies);

        // let supply_chain = [...(project.custom_field.supply_chain_companies || []), ...supply_chain_list];

        let custom_field = {
            ...(project.custom_field || {}),
            ...{"has_supply_chain_companies": true},
            ...{"supply_chain_companies": _uniq(supply_chain_list)},
        };
        await sails.models.project.updateOne({id: project.id}).set({custom_field: custom_field});
    }
};

const saveToolBriefing = async (projectId, id, reqBody) => {
    reqBody.project_ref = projectId;
    //sails.log.info('Validating tool briefing req: ', reqBody);
    let {validationError, payload} = validateToolBriefing(reqBody);
    if(validationError) {
        return errorObject('Request failed due to invalid data, Please try again.', {validationError});
    }

    let signatures = [], registerReq = [], guestRegReq = [];
    reqBody.register.forEach(reg => {
        if(reg.user_sign) { //check for edit case, user_sign will be available only with new briefings.
            signatures.push({ "sign": reg.user_sign, "briefing_ref": null, "user_ref": reg.user_ref });
        }
        delete reg['user_sign']; registerReq.push(reg);
    });
    reqBody.guest_register.forEach(reg => {
        if(reg.user_sign) { //check for edit case, user_sign will be available only with new briefings.
            signatures.push({ "sign": reg.user_sign, "briefing_ref": null, "guest_reg_id": reg.id });
        }
        delete reg['user_sign']; guestRegReq.push(reg);
    });
    reqBody.register = registerReq;
    reqBody.guest_register = guestRegReq;

    let briefing = {};
    if(reqBody && id > 0) {
        briefing = await sails.models.toolbriefings.updateOne({id: id}).set(reqBody);
    } else {
        briefing = await sails.models.toolbriefings.create(reqBody);
    }

    //update flag in permit request
    if (briefing.tool_key === PERMIT_REGISTER) {
        sails.log.info(`Set 'has briefing' true for permit request ${briefing.tool_record_ref}`);
        await updateHasPermitRegister(briefing.tool_record_ref);
    }

    signatures = signatures.map(sign => {
        sign.briefing_ref = briefing.id;
        return sign;
    });

    await sails.models.usersignature.createEach(signatures);
    return briefing;
};

const getToolBriefings = async (projectId, toolKey, recordIds) =>{
    sails.log.info(`fetching all briefings of ${toolKey} project_ref: ${projectId} total recordIds: ${recordIds.length}`);
    let briefings = await sails.models.toolbriefings_reader.find({
        select: ['briefed_at', 'briefed_by', 'briefed_by_name', 'tool_record_ref'],
        where : {
            project_ref: projectId,
            tool_key: toolKey,
            tool_record_ref: recordIds,
        },
        sort: ['id desc']
    });
    briefings = await populateUserRefs(briefings, 'briefed_by', ['first_name', 'last_name']);
    return briefings;
}

const populateToolsMappingCompany = async (mappings = []) => {
    let pupulatedToolsMapping = await populateEmployerRefs(mappings, 'company_id', ['name']);

    mappings = pupulatedToolsMapping.map(m => {
        if (typeof m.company_id === 'object') {
            return { tool_key: m.tool_key, folder_id: m.folder_id, company_id: m.company_id.id, company_name: m.company_id.name, folder_name: m.folder_name };
        }
        return m;
    });
    return mappings;
};

const updateHasPermitRegister = async (permitRequestId) => {
    sails.log.info("Updating permit request register flag.")
    let permitRequest = await sails.models.permitrequest.findOne({
        where: { id: +permitRequestId },
        select: ['record_id', 'status', 'start_on', 'expire_on', 'requestor_ref', 'permit_ref', 'createdAt', 'project_ref']
    });
    let updateRequest = {has_register: true};
    //if status is 'Register Pending' i.e. 8
    if (permitRequest.status === 8) {
        updateRequest.status = (+permitRequest.start_on > dayjs().valueOf()) ? 2 : 3;
        sails.log.info(`Updating permit request status to ${updateRequest.status }`);
    }
    await sails.models.permitrequest.updateOne({id: +permitRequestId}).set(updateRequest);
}

const autoDeleteIbReports = async () => {
    let oldDraftInspectionReports = await sails.models.inspectionbuilderreport.destroy({
        createdAt: {
            '<': moment().subtract(30, 'd').valueOf()
        },
        finalised: false,
    });
    sails.log.info(`Deleted ${oldDraftInspectionReports.length} IB reports.`);
}

const deriveOnSiteUsersLocation = async (daily_logs, projectId) => {
    let source_grouped_events = (daily_logs || []).reduce((group_of_events, row) => {

        // No need to continue Loop, If there is no IN event.
        if (!row.recent_in) {
            return group_of_events;
        }
        for (let i = 0, len = (row.events || []).length; i < len; i++) {
            let event = (row.events || [])[i];
            if (event.event_type === EVENT_TYPE.IN && event.event_date_time === row.recent_in) {
                let e = {
                    id: event.id,
                };
                if(event.source === VALID_SOURCES.GEO_FENCE){
                    group_of_events.geo_fence.push(e);
                }else if (event.source === VALID_SOURCES.OPTIMA){
                    group_of_events.optima.push(e);
                }
                break;
            }
        }
        return group_of_events;
    }, {optima: [], geo_fence: []});

    let users_locations = {};

    sails.log.info('Total geo sourced users', (source_grouped_events.geo_fence || []).length);
    if((source_grouped_events.geo_fence || []).length){
        let geo_events = await sails.models.usertimelog_reader.find({
            where: {
                event_type: [EVENT_TYPE.IN, EVENT_TYPE.INTERACTION],
                project_ref: +projectId,
                ...(source_grouped_events.geo_fence.length > 1 ? {or: source_grouped_events.geo_fence} : source_grouped_events.geo_fence.pop())
            },
            select: ['user_ref', 'user_location']
        });
        users_locations = (geo_events || []).reduce((users_obj, event) => {
            users_obj[event.user_ref] = ((event.user_location || {}).name || '-').trim();
            return users_obj;
        }, users_locations);
    }

    sails.log.info('Total optima sourced users', (source_grouped_events.optima || []).length);
    if((source_grouped_events.optima || []).length){
        let optima_events = await sails.models.badgeevent_reader.find({
            where: {
                event_type: [EVENT_TYPE.IN, EVENT_TYPE.INTERACTION],
                project_ref: +projectId,
                ...(source_grouped_events.optima.length > 1 ? {or: source_grouped_events.optima} : source_grouped_events.optima.pop())
            },
            select: ['user_ref', 'reader_name', 'unit_name']
        });
        users_locations = (optima_events || []).reduce((users_obj, event) => {
            users_obj[event.user_ref] = (event.reader_name || '--').replace(/entrance|exit|interaction/gi, '').trim();
            return users_obj;
        }, users_locations);
    }

    sails.log.info('Total users with locations', (Object.keys(users_locations)).length);
    return users_locations;
};


const deriveOnSiteVisitorsLocation = async (daily_logs, projectId) => {
    let source_grouped_events = (daily_logs || []).reduce((group_of_events, row) => {

        // No need to continue Loop, If there is no IN event.
        if (!row.recent_in) {
            return group_of_events;
        }
        for (let i = 0, len = (row.events || []).length; i < len; i++) {
            let event = (row.events || [])[i];
            if (event.event_type === EVENT_TYPE.IN && event.event_date_time === row.recent_in) {
                let e = {
                    id: event.id,
                };
                if(event.source === VALID_SOURCES.GEO_FENCE){
                    group_of_events.geo_fence.push(e);
                }else if (event.source === VALID_SOURCES.OPTIMA){
                    group_of_events.optima.push(e);
                }
                break;
            }
        }
        return group_of_events;
    }, {optima: [], geo_fence: []});

    let visitor_locations = {};

    sails.log.info('Total geo sourced visitors', (source_grouped_events.geo_fence || []).length);
    if((source_grouped_events.geo_fence || []).length){
        let geo_events = await sails.models.usertimelog_reader.find({
            where: {
                event_type: [EVENT_TYPE.IN, EVENT_TYPE.INTERACTION],
                project_ref: +projectId,
                ...(source_grouped_events.geo_fence.length > 1 ? {or: source_grouped_events.geo_fence} : source_grouped_events.geo_fence.pop())
            },
            select: ['visitor_ref', 'user_location']
        });
        visitor_locations = (geo_events || []).reduce((visitors_obj, event) => {
            visitors_obj[event.visitor_ref] = ((event.user_location || {}).name || '-').trim();
            return visitors_obj;
        }, visitor_locations);
    }

    sails.log.info('Total visitor with locations', (Object.keys(visitor_locations)).length);
    return visitor_locations;
};
const autoDeleteITPChecklistReports = async () => {
    let oldDraftITPChecklistReports = await sails.models.checklistinspection.destroy({
        createdAt: {
            '<': moment().subtract(30, 'd').valueOf()
        },
        finalised: false,
    });
    sails.log.info(`Deleted ${oldDraftITPChecklistReports.length} ITP checklist reports.`);
}

const getInductionEmployerForUsers = async(users, projectId) => {
    let userIds = users.map(u=> u.id);
    let inductedUsersEmployerInfo = await getInductionEmployerByUserIds(userIds, projectId, [2, 6]);
    users = users.map(u=> {
        let inductionEmployer = inductedUsersEmployerInfo.find(i=> i.user_ref === u.id);
        u.employerName = (inductionEmployer && inductionEmployer.user_employer) ? inductionEmployer.user_employer : '';
        return u;
    });
    return users;
};

const prepareRollCallUsersInfo = async (roll_call, project) => {
    let present_users = [];
    if(roll_call.present_users && roll_call.present_users.length){
        present_users = await sails.models.user_reader.find({
            select: ['first_name', 'middle_name', 'last_name', 'parent_company'],
            where:{
                id: (roll_call.present_users).map(item => item.user_ref)
            }
        });
        present_users = await getInductionEmployerForUsers(present_users, project.id);

        present_users = present_users.map(item => {
            let r = roll_call.present_users.find(i => i.user_ref === item.id);
            item.location = (r.location || '');
            return item;
        });
    }
    let un_accounted_users = [];
    if(roll_call.un_accounted_users && roll_call.un_accounted_users.length){
        un_accounted_users = await sails.models.user_reader.find({
            select: ['first_name', 'middle_name', 'last_name', 'parent_company'],
            where:{
                id: (roll_call.un_accounted_users).map(item => item.user_ref)
            }
        });
        un_accounted_users = await getInductionEmployerForUsers(un_accounted_users, project.id);

        un_accounted_users = un_accounted_users.map(item => {
            let r = roll_call.un_accounted_users.find(i => i.user_ref === item.id);
            item.location = (r.location || '');
            return item;
        });
    }
    let present_visitors = [];
    if(roll_call.present_visitors && roll_call.present_visitors.length){
        present_visitors = await sails.models.visitor_reader.find({
            select: ['first_name', 'last_name', 'employer_ref'],
            where:{
                id: (roll_call.present_visitors).map(item => item.visitor_ref)
            }
        });
        present_visitors = await populateEmployerRefs(present_visitors, 'employer_ref', []);
        present_visitors = present_visitors.map(v => ({
            is_visitor: true,
            parent_company: v.employer_ref,
            employerName: (v.employer_ref && v.employer_ref.name) ? v.employer_ref.name: '',
            ...v,
        }));

        present_visitors = present_visitors.map(item => {
            let r = roll_call.present_visitors.find(i => i.visitor_ref === item.id);
            item.location = (r.location || '');
            return item;
        });
    }
    let un_accounted_visitors = [];
    if(roll_call.un_accounted_visitors && roll_call.un_accounted_visitors.length){
        un_accounted_visitors = await sails.models.visitor_reader.find({
            select: ['first_name', 'last_name', 'employer_ref'],
            where:{
                id: (roll_call.un_accounted_visitors).map(item => item.visitor_ref)
            }
        });
        un_accounted_visitors = await populateEmployerRefs(un_accounted_visitors, 'employer_ref', []);
        un_accounted_visitors = un_accounted_visitors.map(v => ({
            is_visitor: true,
            parent_company: v.employer_ref,
            employerName: (v.employer_ref && v.employer_ref.name) ? v.employer_ref.name: '',
            ...v,
        }));

        un_accounted_visitors = un_accounted_visitors.map(item => {
            let r = roll_call.un_accounted_visitors.find(i => i.visitor_ref === item.id);
            item.location = (r.location || '');
            return item;
        });
    }

    return {present_users, un_accounted_users, present_visitors, un_accounted_visitors};
}

const convertWordDocToPdf = async (sourceFileId, req, category) => {
    let sourceFile = await sails.models.userfile.findOne({
        where: {id: sourceFileId},
        select: ['name', 'file_url']
    });

    let docFileUrl = sourceFile.file_url;
    let docFileName = sourceFile.name;
    let outputFileName = docFileName.split(".");
    let urlParts = docFileUrl.split(".");

    let lambdaPayload = {
        doc_url: docFileUrl,
        fromExt: urlParts[urlParts.length-1],
        toExt: 'pdf',
        feature_name: category
    };

    let {
        success,
        statusCode,
        data
    } = await HttpService.triggerLambdaFn(sails.config.custom.LAMBDA_DOC_TO_PDF_FN, lambdaPayload);
    //sails.log.info(`RESULT:`, {success, statusCode, data});

    if(success && (data.Location || data.location)) {
        const public_url = data.public_url || data.Location || data.location;
        sails.log.info(`Received converted PDF from lambda fn, path: ${public_url}`);
        let record = {
            file_url: public_url,
            file_mime: 'application/pdf',
            name: outputFileName[0] + '.pdf',
            user_id: req.user.id,
            category
        };
        let user_file = await sails.models.userfile.create(record);
        sails.log.info('Converted pdf file id.', user_file.id);
        return user_file.id;
    }
    sails.log.error(`Failed to convert word document to pdf, source file id: ${sourceFileId}.`);
    return;
};

/**
 *  Example Usage:
 * /[^\u0000-\u007F]/.test(' Ç, Ş, Ğ, , İ, Ö') true
 * /[^\u0000-\u007F]/.test(' testing?') true
 * /[^\u0000-\u007F]/.test('ē, č, ș, ă') true
 * /[^\u0000-\u007F]/.test('any yext goes here') false
 * */
const containsNonLatinCodepoints = (s) => {
    return /[^\u0000-\u007F]/;
}

const getDistanceMatrixInfo = async (project, origin_postcode) => {
    let country_code = (project.custom_field && project.custom_field.country_code)
    sails.log.info(`vlog derive distance travelled, from: "${origin_postcode}" to: "${project.postcode}"`);

    const region = deriveDistanceMatrixRegion(country_code);
    let origins = `${origin_postcode} ${region || ''}`.replace(/\s+/g, ' ');
    let destinations = `${project.postcode} ${region}`;
    let distanceMatrix = await getDistanceMatrix(origins, destinations, region);
    let distance_matrix = _get(distanceMatrix, `[0]elements[0]`, {});
    sails.log.info(`vlog DM details origins:${origins}, project: ${project.postcode}`, distance_matrix);
    return distance_matrix;
};

module.exports = {
    attachProfilePicWithUsersInfo,
    getFilteredInductionBySearch,
    attachProfilePicWithUserRefInfo,
    buildCompanyInductionStatus,
    getUserFullName,
    buildCloseCallStatus,
    buildGCStatusMessage,
    buildAssetStatusMessage,
    getUserFirstName,
    buildRecordRef,
    getProjectTimezone,
    convertDecimalHrToDuration,
    sortBadgeLogsByDay,
    eventOfToday,
    isFirstAider,
    isMentalHealthFirstAider,
    haveSMSTSOrSSSTSDoc,
    haveFireMarshal,
    havePts,
    haveCSCS,
    getTotalTravelDuration,
    getTotalTime,
    getTotalTravelDistance,
    secondsToHuman,
    showDuration, showDurationAsHours,
    getUserVerifiableUserDocuments,
    bulkValidateUserDocuments,
    attachUserDocument,
    getInductionBlockReason,
    expandUserDocs,
    updateUserInductionsWithProfileChanges,
    populateInductionEmptySlots,
    populateInductionEmptySlotsForAll,
    sendInductionStatusChangeAlert,
    getDistanceMatrix,
    deriveDistanceMatrixRegion,
    getActiveTravelTime,
    constructDistanceMatrix,
    isInheritedProjectOfCompany,
    getPendingAssessmentStatus,
    buildRatio,
    expandProjectClosecalls,
    getDailyBadgeEvent,
    getDailyGeoFenceTimeEvent,
    getDailyTimeEventForDay,
    getDailyTimeEventV2,
    getUserCompanyTimeEvents,
    getAllDailyTimeEvent,
    removeRejectedInductions,
    isStillOnSite,
    getAllVisitorsTimeEvents,
    getVisitorsTimeLogForDates,
    expandProjectTake5s,
    expandUserDocFiles,
    translateUserDocIntoPages,
    expandProgressImages,
    totalProgressImagesCount,
    groupByAlbumProgressImages,
    expandIncidentReports,
    expandDeliveryNoteImgs,
    expandProjectClerkOfWorks,
    sendWeeklyTimesheetProcess,
    sendWeeklyCCemail,
    sendWeeklyTimesheet: async () => {
        return {disabled: true};
        sails.log.info('execute `sendWeeklyTimesheet` cron process initializing.');

        let rawSelectDbQuery = "SELECT user_ref FROM public.user_setting WHERE name = 'weekly_timesheet' AND CAST(value as text) LIKE '%true%'";
        let rawSelectDbResult = await sails.sendNativeQuery(rawSelectDbQuery);
        let userIds = (rawSelectDbResult.rows || []).map(u => u.user_ref);
        sails.log.info('got users with setting enabled', userIds);

        let to_date = moment().format(dbDateFormat); //MOM
        let from_date = moment().subtract(7, 'days').format(dbDateFormat); //MON
        // from, to: ${from_date}  <= event_date < ${to_date}
        let parallelJobs = [];
        // create parallel Job list
        for (let i = 0, len = userIds.length; i < len; i++) {
            parallelJobs.push((function (userId, from, to, batchNo) {
                return (callback) => {
                    //@note: This lib doesn't support async function here.
                    sails.log.info(`weekly timesheet job#${batchNo} --> user:${userId}`);
                    processWeeklyTimesheetRequest(userId, from, to).then(response => {
                        callback(null, {response});
                    }).catch(callback);
                };
            })(userIds[i], from_date, to_date, i));
        }

        let outcome = await HttpService.executeInParallelLimit(parallelJobs, 3);
        sails.log.info('All parallel jobs completed', outcome);
        return 'Success';
    },
    expandProjectGoodCalls,
    expandToolboxTalks,
    sendMailToNM,
    sendToolboxTalksInvitation,
    sendMailToNominatedManagerCPA,
    sendMailToNominatedManagerPA,
    expandUsersByIds,
    getMandatoryCompetencyExceptionList,
    getInspectionTourMetaChecklist,
    getChecklistItemsReferences,
    getInspectionTours,
    itemCloseoutDueReminder,
    sendInspectionMailToNomMngr,
    getTimezone,
    populateUserRefs,
    populateProjectRefs,
    populateJobRoles,
    populateDocumentChildren,
    populateUserFileRefs,
    populateAttachmentFileRefs,
    populateEmployerRefs,
    shareReportViaEmail,
    buildIncidentReportStatus,
    getPdfHeaderTemplate,
    getHourlyWeatherLogs,
    populateInductionEmployerRef,
    getUpdatedOnboardStatus,
    replaceAll,
    buildBriefingToolStatusMessage: buildStatusMessage,
    briefingToolTables,
    capitalizeFirstLetter,
    getCoordinates,
    calculateDistance,
    updateTravelTime,
    processVisitingRecords,
    populateToolsMappingCompany,
    updateCompanyProjectsSupplyChain,
    getProjectDistrict,
    saveToolBriefing,
    getToolBriefings,
    getShowAlternativeUserList,
    autoDeleteIbReports,
    deriveOnSiteUsersLocation,
    deriveOnSiteVisitorsLocation,
    autoDeleteITPChecklistReports,
    getInductionEmployerForUsers,
    convertWordDocToPdf,
    prepareRollCallUsersInfo,
    containsNonLatinCodepoints,
    getDistanceMatrixInfo,
};
