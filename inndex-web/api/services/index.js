/**
 * Created by spatel on 27/04/20.
 */

module.exports = {
    AccessLogService: require('./AccessLogService'),
    CSCSService: require('./CSCSService'),
    RightToWorkService : require('./RightToWorkService'),
    CacheService: require('./CacheService'),
    OptimaSyncService: require('./OptimaSyncService'),
    UserRevisionService: require('./UserRevisionService'),
    ExcelService: require('./ExcelService'),
    DataProcessingService: require('./DataProcessingService'),
    ChangeLogService: require('./ChangeLogService'),
    HttpService: require('./HttpService'),
    SmartSheetService: require('./SmartSheetService'),
    ProcoreService: require('./ProcoreService'),
    TimeLogService: require('./TimeLogService'),
    TranslationService: require('./TranslationService'),
    TouchByteService: require('./TouchByteService'),
    RekognitoService: require('./RekognitoService'),
    ResponseService: require('./ResponseService'),
    TokenUtil: require('./TokenUtil'),
    EmailService: require('./EmailService'),
    PdfUtil: require('./PdfUtil'),
    NotificationService: require('./NotificationService'),
    DataToSeedService: require('./DataToSeedService'),
    ChartService: require('./ChartService'),
    WeatherSyncService: require('./WeatherSyncService'),
    SharedService: require('./SharedService'),
    VehicleService: require('./VehicleService'),
    ASiteService: require('./ASiteService'),
    FeatureExclusionUtil: require('./FeatureExclusionUtil'),
    EncryptUtil: require('./EncryptUtil'),
    ServiceConstants: require('./ServiceConstants'),
    SupportFunctions: require('./SupportFunctionUtil'),
};
