const d3 = require("d3");
const jsdom = require('jsdom');
const { J<PERSON><PERSON> } = jsdom;

const colorRed = "#D60707";
const colorGreen = '#1FA61B';
const colorYellow = "#EDB531";

const measureTextWidth = (str, fontSize = 10) => {
    if (!str) return 0;
    const widths = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0.2796875,0.2765625,0.3546875,0.5546875,0.5546875,0.8890625,0.665625,0.190625,0.3328125,0.3328125,0.3890625,0.5828125,0.2765625,0.3328125,0.2765625,0.3015625,0.5546875,0.5546875,0.5546875,0.5546875,0.5546875,0.5546875,0.5546875,0.5546875,0.5546875,0.5546875,0.2765625,0.2765625,0.584375,0.5828125,0.584375,0.5546875,1.0140625,0.665625,0.665625,0.721875,0.721875,0.665625,0.609375,0.7765625,0.721875,0.2765625,0.5,0.665625,0.5546875,0.8328125,0.721875,0.7765625,0.665625,0.7765625,0.721875,0.665625,0.609375,0.721875,0.665625,0.94375,0.665625,0.665625,0.609375,0.2765625,0.3546875,0.2765625,0.4765625,0.5546875,0.3328125,0.5546875,0.5546875,0.5,0.5546875,0.5546875,0.2765625,0.5546875,0.5546875,0.221875,0.240625,0.5,0.221875,0.8328125,0.5546875,0.5546875,0.5546875,0.5546875,0.3328125,0.5,0.2765625,0.5546875,0.5,0.721875,0.5,0.5,0.5,0.3546875,0.259375,0.353125,0.5890625]
    const avg = 0.5279276315789471
    return str
        .split('')
        .map(c => c.charCodeAt(0) < widths.length ? widths[c.charCodeAt(0)] : avg)
        .reduce((cur, acc) => acc + cur) * fontSize
}

module.exports = {
    //Inspection Tour Form
    getDonutChart: async (data, width, height, centroidAdj, radius, innerRadius, viewBox, goodRatingLabel, fairRatingLabel, poorRatingLabel, legendRight, isIndustrialProject, totalCount, legendsAdj, legendh = 155) => {
        const fakeDom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
        let body = d3.select(fakeDom.window.document).select('body');
        let arc = d3.arc()
            .outerRadius(radius - 10)
            .innerRadius(innerRadius);

        let pie = d3.pie()
            .sort(null)
            .value(function(d) {
                return d.count;
            });

        let svg = body.append('div').attr('class', 'container')
            .append("svg")
            //.style("overflow", "visible")
            .attr("viewBox",viewBox)
            .attr("width", width)
            .attr("height", height)
            .style("float", "right")
            .append("g")
            .attr("transform", "translate(" + ((width / 2) + 7) + "," + height / 2 + ")");

        if (legendRight) {
            if(goodRatingLabel) {
                svg.append("circle").attr("cx", -140).attr("cy", -30).attr("r", 6).style("fill", `${colorGreen}`);
                svg.append("text").attr("x", -155).attr("y", -30).text(goodRatingLabel).style('text-anchor', 'end').style("font-weight", "bold").style("font-size", "13px").attr("alignment-baseline", "middle");
            }
            if(isIndustrialProject) {
                if(fairRatingLabel) {
                    svg.append("circle").attr("cx", -140).attr("cy", 0).attr("r", 6).style("fill", `${colorYellow}`);
                    svg.append("text").attr("x", -155).attr("y", 0).text(fairRatingLabel).style('text-anchor', 'end').style("font-weight", "bold").style("font-size", "13px").attr("alignment-baseline", "middle");
                }

                if (poorRatingLabel) {
                    svg.append("circle").attr("cx", -140).attr("cy", 30).attr("r", 6).style("fill", `${colorRed}`);
                    svg.append("text").attr("x", -155).attr("y", 30).text(poorRatingLabel).style('text-anchor', 'end').style("font-weight", "bold").style("font-size", "13px").attr("alignment-baseline", "middle");
                }
            } else if(poorRatingLabel) {
                svg.append("circle").attr("cx", -140).attr("cy", 0).attr("r", 6).style("fill", `${colorRed}`);
                svg.append("text").attr("x", -155).attr("y", 0).text(poorRatingLabel).style('text-anchor', 'end').style("font-weight", "bold").style("font-size", "13px").attr("alignment-baseline", "middle");
            }
        } else {
            //remove fair rating
            if (!isIndustrialProject) {
                data = data.reduce((arr, item) => {
                    if (item.label && (item.label).toLowerCase() != 'fair' ) {
                        arr.push(item);
                    }
                    return arr;
                }, []);
            }

            const legend = svg.append('g')
                .attr('class', 'legend')
                .attr('id', 'legend')
                .attr('transform', `translate(0,0)`);

            const lg = legend.selectAll('g')
                .data(data)
                .enter()
                .append('g')
                .attr('transform', (d, i) => `translate(${i * 100},${height + 15})`);
            lg.append('circle')
                .style('fill', (d, i) => {
                    return d.color
                })
                .attr('cy', 5)
                .attr('r', 6)
                .attr('width', 10)
                .attr('height', 10);

            lg.append('text')
                .style('font-family', "'Nunito', Arial, Helvetica, sans-serif")
                .style('font-size', '13px')
                .attr('x', 12.5)
                .attr('y', 10)
                .text(d => d.label);

            let offset = 0;
            lg.attr('transform', function (d, i) {
                let x = offset;
                offset += (measureTextWidth(d.label, 13) + 12) + 10;
                return `translate(${x},${height + 10})`;
            });

            legend.attr('transform', function (d, i) {
                return `translate(${(width - offset + legendsAdj) / 2},${50 + legendh})`
            });
        }

        let g = svg.selectAll(".arc")
            .data(pie(data))
            .enter().append("g");

        g.append("path")
            .attr("d", arc)
            .style("fill", function(d,i) {
                return d.data.color;
            });

        let r = Math.min(width, height) / 2;
        let labelr = r + (centroidAdj);

        g.append("text")
            .style("font-size", "9px")
            .attr("transform", function(d, i) {
                let c = arc.centroid(d),
                    x = c[0],
                    y = c[1],
                    // pythagorean theorem for hypotenuse
                    h = Math.sqrt(x*x + y*y);
                let translateX = (data.length > 2 && i == 1) ? (x/h * labelr) - 2 :  (x/h * labelr);
                return "translate(" + translateX +  ',' +
                    (y/h * labelr) +  ")";
            })
            .attr("dy", ".50em")
            .style("text-anchor", "middle")
            .attr("fill", "#000")
            .attr("font-weight", "bold")
            .text(function(d) {
                return (d.data.percentage) ? `${d.data.percentage}%` : '';
            });

        /*g.append("text")
            .attr("text-anchor", "middle")
            .attr('font-size', '4em')
            .attr('y', 20);*/

        let endEdgeCircles = svg
            .selectAll('allCSlices')
            .data(pie(data))
            .enter()
            .append('circle')
            .style("fill", function(d,i) {
                return d.data.color;
            })
            .attr('r', (radius * 0.148))
            .attr('visibility',function(d,i) {
                return d.data.percentage > 0 ? 'visible' : 'hidden';
            })
            .attr('transform', function(d) {
                var c = arc.centroid(d),
                    x = c[0] + (radius * 30) * Math.sin(d.endAngle),
                    y = c[1] + (radius * 30) * - Math.cos(d.endAngle),
                    h = Math.sqrt(x*x + y*y);
                return "translate(" + (x/h * 75) +  ',' + (y/h * 75) +  ")";
            });

        endEdgeCircles.sort(function(a,b) {
            return b.index - a.index;
        }).order();

        return body.select('.container').html();
    },

    //Inspection Tour Form
    getOpenClosedBarChart: async (data, hasAllClosed, stackWidth, stackHeight, fontSize, marginTop, adjustments, colors, legends) => {
        const fakeDom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
        let body = d3.select(fakeDom.window.document).select('body');

        let width = 150;
        let marginLeftRight = 5;
        let margin = ({top: 0, right: marginLeftRight, bottom: 0, left: marginLeftRight});
        let {adj1, adj2, adj3, adj4, adj5, adj6, adj7, adj8, adj9, adj10, adj11, adj12, adj13} = adjustments;
        let total = d3.sum(data, d => d.value);
        let value = 0;

        let stack =  data.map(d => {
            return {
                name: `${d.name}`,
                count: `${d.value} items`,
                value: +d.value / total,
                startValue: +value / total,
                endValue: +(value += +d.value) / total
            }
        });

        let color = d3.scaleOrdinal()
            .domain(data.map(d => d.name))
            .range(colors);

        let x = d3.scaleLinear([0, 1], [margin.left, width - margin.right]);

        let viewBoxHeight = stackHeight + adj9;

        // append the svg object to the body of the page
        let svg = body.append('div').attr('class', 'container')
            .append("svg")
            //.style("overflow", "visible")
            .attr("width", stackWidth)
            .attr("viewBox", [-1, -(viewBoxHeight/3) + adj10, width, viewBoxHeight])
            .style("display", "block")
            .style("margin-top", marginTop);
        let rectsWidth = [];
        svg.append("g")
            .attr("stroke", "white")
            .selectAll("rect")
            .data(stack)
            .join("rect")
            .attr("fill", d => color(d.name))
            .attr("x", d => x(d.startValue))
            .attr("y", margin.top)
            .attr("rx", 1)
            .attr("width", d => {
                let rectWidth = x(d.endValue) - x(d.startValue);
                if (rectWidth) {
                    rectsWidth.push(rectWidth);
                }
                return rectWidth
            })
            .attr("height", stackHeight - margin.top - margin.bottom);

        let number = 1;
        let stackesLine = svg.append("g")
            .attr("font-family", "sans-serif")
            .attr("font-size", 6)
            .attr("opacity", .5)
            .selectAll("g")
            .data(stack.filter(d => x(d.endValue) - x(d.startValue) > 0))
            .join("g")
            .attr("fill", d => d3.lab(color(d.name)).l < 50 ? "white" : "black")
            .attr("transform", (d,index) => {
                let x = rectsWidth[index]/2;
                for (let i=0; i < index; i++) {
                    x += rectsWidth[i]
                }
                let translateY = (number % 2 == 0) ? adj1 : adj2;
                number += (d.value) ? 1 : 0;
                return `translate(${x + marginLeftRight}, ${translateY})`;
            });

        number = 1;
        let stackesText = svg.append("g")
            .attr("font-family", "sans-serif")
            .attr("font-size", 6)
            .selectAll("g")
            .data(stack.filter(d => x(d.endValue) - x(d.startValue) > 0))
            .join("g")
            .attr("fill", d => d3.lab(color(d.name)).l < 50 ? "white" : "black")
            .attr("transform", (d,index) => {
                let x = rectsWidth[index]/2;
                for (let i=0; i < index; i++) {
                    x += rectsWidth[i]
                }

                let translateY = (number % 2 == 0) ? adj3 : adj4;
                number += (d.value) ? 1 : 0;
                return `translate(${x + marginLeftRight}, ${translateY})`;
            });
        if (!hasAllClosed) {
            stackesLine
                .call(text => text.append("line")
                    .attr("y2", 2)
                    .style("stroke", "black")
                    .style("stroke-width", .2));
            stackesText
                .call(g => g.append("text")
                    .attr("x", function(d) {
                        return (d.name.length <= 6) ?  -(((d.name.length+(d.name.length*0.112))+adj11)) - adj13 : -(((d.name.length+(d.name.length*0.112))+adj11));
                    })
                    .attr("y", (stackHeight + adj5) + 'px')
                    .attr("fill", '#585454')
                    .attr("font-size", fontSize)
                    //.attr("font-weight", 'bold')
                    .text(d => d.name))
                .call(g => g.append("text")
                    .attr("x", function(d) {
                        return -((d.count.length+adj12));
                    })
                    .attr("y", (stackHeight + adj6) + 'px')
                    .attr("fill", '#000')
                    .attr("font-size", fontSize)
                    //.attr("font-weight", 'bold')
                    .attr("fill-opacity", 0.7)
                    .text(d => d.count));
        } else {
            let stackes = svg.append("g")
                .attr("font-family", "sans-serif")
                .attr("font-size", 6)
                .selectAll("text")
                .data(stack.filter(d => x(d.endValue) - x(d.startValue) > 0 ))
                .join("text")
                .attr("fill", d => d3.lab(color(d.name)).l < 50 ? "white" : "black")
                .attr("transform", d => `translate(${x(d.startValue) + 6}, 7.5)`);
            stackes.call(g => g.append("tspan")
                .attr("x", function(d) { return ((x(d.endValue) - x(d.startValue))/2) - adj7; })
                .attr("y", ((stackHeight/3) - adj8) + 'px')
                .attr("fill", '#fff')
                .attr("font-size", fontSize)
                .text(d => hasAllClosed))
        }
        return body.select('.container').html();
    },

    //Inspection Tour Form
    getStackedBarChart: async (data, columns, maxRating, width, height, colors, axisFontSize, yAxisLabel, isIndustrialProject, showLegends, legendsAdj) => {
        const fakeDom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
        let body = d3.select(fakeDom.window.document).select('body');

        let dimensions = ({
            height:height,
            width:width,
            margin: {
                top: 10,
                right: 30,
                bottom: (showLegends) ? 75 : 50,
                left: 50,
            }
        });
        let groups = d3.map(data, d => d.group);
        let subgroups = columns.slice(1);
        let stackedData = d3.stack()
            .keys(subgroups)
            (data);

        let color = d3.scaleOrdinal()
            .domain(subgroups)
            .range(colors);

        //maxRating = (maxRating < 8) ? 8 : maxRating;
        let y = d3.scaleLinear()
            .domain([0, +maxRating])
            .range([ dimensions.height, 0 ]);

        let x = d3.scaleBand()
            .domain(groups)
            .range([0, dimensions.width])
            .padding([0.5]);

        // append the svg object to the body of the page
        let svg = body.append('div').attr('class', 'container')
            .append("svg")
            //.style("overflow", "visible")
            .attr("width", dimensions.width + dimensions.margin.left + dimensions.margin.right)
            .attr("height", 30 + dimensions.height + dimensions.margin.top + dimensions.margin.bottom)
            .append("g")
            .attr("transform",
                "translate(" + dimensions.margin.left + "," + dimensions.margin.top + ")")

        //show horizontal grid lines on chart
        let yAxisGrid = d3.axisLeft(y).tickSize(0).tickFormat('');
        let stackBarWidth = (x.bandwidth() < 45) ? x.bandwidth() : 45;
        svg.append('g')
            .attr('class', 'd-y-axis-grid')
            .call(yAxisGrid);

        svg.append("g")
            .selectAll("g")
            // Enter in the stack data = loop key per key = group per group
            .data(stackedData)
            .enter().append("g")
            .attr("fill", d => color(d.key))
            .selectAll("rect")
            // enter a second time = loop subgroup per subgroup to add all rectangles
            .data(d => d)
            .enter().append("rect")
            .attr("x", d => (x(d.data.group) + ((x.bandwidth()/2)-(stackBarWidth/2))))
            .attr("y", d => y(d[1]))
            .attr("height", d => y(d[0]) - y(d[1]))
            .attr("width", stackBarWidth)

        if (yAxisLabel) {
            svg.append("text")
                .attr("class", "y label")
                .attr("text-anchor", "end")
                .attr("y", -30)
                .attr("x", -80)
                .attr("transform", "rotate(-90)")
                .text(yAxisLabel);
        }

        svg.append("g")
            .attr("class", "x axis")
            .attr("transform", "translate(0," + dimensions.height + ")")
            .call(d3.axisBottom(x).ticks(10))
            .style("font-size", axisFontSize)
            .selectAll("text")
            .style("text-anchor", "end")
            .attr("dx", "-.8em")
            .attr("dy", ".15em")
            .attr("transform", "rotate(-20)");

        // tooltip
        svg.selectAll('.x.axis>.tick')
            .append('title')
            .text(d => d);

        let ticks = 10;
        if (maxRating < 10) {
            ticks = +maxRating;
        }

        svg.append("g")
            .style("font-size", axisFontSize)
            .call(d3.axisLeft(y).tickFormat(d3.format('.0f')).ticks(ticks));

        if (showLegends) {
            //remove fair rating
            if (!isIndustrialProject) {
                stackedData = stackedData.reduce((arr, data) => {
                    if (data.key && (data.key).toLowerCase() != 'fair' ) {
                        arr.push(data);
                    }
                    return arr;
                }, []);
            }
            if (stackedData.length > 1) {
                const legend = svg.append('g')
                    .attr('class', 'legend')
                    .attr('id', 'legend')
                    .attr('transform', `translate(0,0)`);

                let data = [
                    {label: 'Compliant', value: 60},
                    {label: 'Observation/Compliant', value: 30},
                    {label: 'Non-Compliant', value: 10},
                ];
                const lg = legend.selectAll('g')
                    .data(stackedData)
                    .enter()
                    .append('g')
                    .attr('transform', (d, i) => `translate(${i * 100},${height + 15})`);
                lg.append('circle')
                    .style('fill', (d, i) => {
                        return colors[i]
                    })
                    .attr('cy', 5)
                    .attr('r', 6)
                    .attr('width', 10)
                    .attr('height', 10);

                lg.append('text')
                    .style('font-family', "'Nunito', Arial, Helvetica, sans-serif")
                    .style('font-size', '13px')
                    .attr('x', 12.5)
                    .attr('y', 10)
                    .text(d => d.key);

                let offset = 0;
                lg.attr('transform', function (d, i) {
                    let x = offset;
                    offset += (measureTextWidth(d.key, 13) + 12) + 10;
                    return `translate(${x},${height + 10})`;
                });

                legend.attr('transform', function (d, i) {
                    return `translate(${(width - offset) / 2},${50})`
                });
            }
        }

        return body.select('.container').html();
    },

    //Inspection Tour Form
    getScatterPlot: async (data, itemName, xAxisValues, allGroup, maxNumber, innerWidth, innerHeight, axisFontSize, xAxisLabel, yAxisLabel, legendsAdj, isIndustrialProject, colors, showCustomNumbers, viewBox) => {
        const fakeDom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
        let body = d3.select(fakeDom.window.document).select('body');

        let dimensions = ({
            height:innerHeight,
            width:innerWidth,
            margin: {
                top: 10,
                right: 30,
                bottom: 70,
                left: 50,
            }
        });

        // append the svg object to the body of the page
        let svg = body.append('div').attr('class', 'container')
            .append("svg")
            .attr("viewBox", viewBox)
            .style("overflow", "visible")
            .attr("width", dimensions.width + dimensions.margin.left + dimensions.margin.right)
            .attr("height", dimensions.height + dimensions.margin.top + dimensions.margin.bottom)
            .append("g")
            .attr("transform",
                "translate(" + dimensions.margin.left + "," + dimensions.margin.top + ")");

        // Reformat the data: we need an array of arrays of {x, y} tuples
        let dataReady = allGroup.map( function(grpName) { // .map allows to do something for each element of the list
            return {
                name: grpName,
                values: data.reduce((arr,d) => {
                    if (+d[grpName]) {
                        arr.push({item: d[itemName], value: +d[grpName]});
                    }
                    return arr;
                }, [])
            };
        });

        // A color scale: one color for each group
        let myColor = d3.scaleOrdinal()
            .domain(allGroup)
            .range(colors);

        if(xAxisValues.length == 2) {
            let nextValue = xAxisValues[xAxisValues.length - 1] + 10;
            xAxisValues.push(nextValue);
        }

        let x = d3.scaleLinear()
            .domain([0 , xAxisValues.length-1])
            .range([ 0, dimensions.width ]);

        // Add Y axis
        let customNumber = (maxNumber < 6) ? 8 : maxNumber;
        let y = d3.scaleLinear()
            .domain( [0, customNumber])
            .range([ dimensions.height, 0 ]);

        //show horizontal grid lines on chart
        let ticks = 10;
        if (maxNumber < 10 && maxNumber > 5) {
            ticks = +maxNumber;
        }

        let customNumbers = [0, '1/4', '1/2', '3/4', 1, 2, 3, 4, 5 ,6];
        let yAxisGrid = d3.axisLeft(y).tickSize(-dimensions.width).tickFormat('').ticks(ticks);
        svg.append('g')
            .attr('class', 'd-y-axis-grid')
            .call(yAxisGrid);

        svg.append("g")
            .attr("class", "x axis")
            .attr("transform", "translate(0," + dimensions.height + ")")
            .style("font-size", axisFontSize)
            .call(d3.axisBottom(x).ticks(xAxisValues.length-1).tickFormat(function(d, i) {
                return xAxisValues[i];
            }))
            .selectAll("text")
            .style("text-anchor", "end")
            .attr("dx", "-.8em")
            .attr("dy", ".15em")
            .attr("transform", "rotate(-35)");

        // text label for the x axis
        if (xAxisLabel) {
            svg.append("text")
                .attr("transform",
                    "translate(" + (dimensions.width / 2) + " ," +
                    (dimensions.height + dimensions.margin.top + 20) + ")")
                .style("text-anchor", "middle")
                .text(xAxisLabel);
        }

        // tooltip
        svg.selectAll('.x.axis>.tick')
            .append('title')
            .text(d => d);

        if (maxNumber <= 5 && showCustomNumbers) {
            svg.append("g")
                .style("font-size", axisFontSize)
                .call(d3.axisLeft(y).tickFormat(d => {
                     return customNumbers[d];
                }).ticks(ticks));
        } else {
            svg.append("g")
                .style("font-size", axisFontSize)
                .call(d3.axisLeft(y).tickFormat(d3.format('.0f')).ticks(ticks));
        }

        if (yAxisLabel) {
            svg.append("text")
                .attr("class", "y label")
                .attr("text-anchor", "end")
                .attr("y", -30)
                .attr("x", -(innerHeight/2) + 40)
                .attr("transform", "rotate(-90)")
                .text(yAxisLabel);
        }

        let line = d3.line()
            .x(function(d) { return  x(d.item); }) // set the x values for the line generator
            .y(function(d) { return y(+d.value); }) // set the y values for the line generator
            .curve(d3.curveMonotoneX);

        svg.selectAll("myLines")
            .data(dataReady)
            .enter()
            .append("path")
            .attr("d", function(d){ return line(d.values) } )
            .attr("stroke", function(d){ return myColor(d.name) })
            .style("stroke-width", 4)
            .style("fill", "none")

        // Add the points
        svg
        // First we need to enter in a group
            .selectAll("myDots")
            .data(dataReady)
            .enter()
            .append('g')
            .style("fill", function(d){ return myColor(d.name) })
            // Second we need to enter in the 'values' part of this group
            .selectAll("myPoints")
            .data(function(d){ return d.values })
            .enter()
            .append("circle")
            .attr("cx", function(d) { return x(d.item); })
            .attr("cy", function(d) { return y(d.value) })
            .attr("r", function(d) { return 5 })
            .attr("stroke", "white")

        //remove fair rating
        if (!isIndustrialProject) {
            dataReady = dataReady.reduce((arr, data) => {
                if (data.name && (data.name).toLowerCase() != 'fair' ) {
                    arr.push(data);
                }
                return arr;
            }, []);
        }

        //sails.log.info("dataReady ", dataReady);
        if (dataReady.length > 1) {
            const legend = svg.append('g')
                .attr('class', 'legend')
                .attr('id', 'legend')
                .attr('transform', `translate(0,0)`);

            const lg = legend.selectAll('g')
                .data(dataReady)
                .enter()
                .append('g')
                .attr('transform', (d, i) => `translate(${i * 100},${dimensions.height + 15})`);
            lg.append('circle')
                .style('fill', (d, i) => {
                    return myColor(d.name)
                })
                .attr('cy', 5)
                .attr('r', 6)
                .attr('width', 10)
                .attr('height', 10);

            lg.append('text')
                .style('font-family', "'Nunito', Arial, Helvetica, sans-serif")
                .style('font-size', '13px')
                .attr('x', 12.5)
                .attr('y', 10)
                .text(d => d.name);

            let offset = 0;
            lg.attr('transform', function (d, i) {
                let x = offset;
                offset += (measureTextWidth(d.name, 13) + 12) + 10;
                return `translate(${x},${dimensions.height + 10})`;
            });

            legend.attr('transform', function (d, i) {
                return `translate(${(dimensions.width - offset) / 2},${50 - legendsAdj})`
            });
        }

        return body.select('.container').html();
    },

    //Inspection Tour Form
    getPercentageGaugeChart: async (w, h, total, progress, centerText) => {
        const fakeDom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
        let body = d3.select(fakeDom.window.document).select('body');

        let outerRadius = (w / 2) - 5;
        let innerRadius = (w / 2) - 15;
        let color = ['#bfbfbf'];

        let percent = Math.round(100/(total)*(progress));
        let ratio = percent / 100;
        let piePercent = Math.PI * ratio;
        sails.log.info("getPercentageGaugeChart: ", `total: ${total}, progress: ${progress}, percent: ${percent}, parseInt(percent): ${parseInt(percent)}, ratio: ${ratio}, piePercent: ${piePercent}`);

        if (parseInt(percent) <= 50) {
            color.push(`${colorRed}`);
        } else if (parseInt(percent) > 50 && parseInt(percent) <= 75) {
            color.push(`${colorYellow}`);
        } else {
            color.push(`${colorGreen}`);
        }

        let arc = d3.arc()
            .innerRadius(innerRadius)
            .outerRadius(outerRadius)
            .startAngle(0)
            .endAngle(Math.PI);

        let arcLine = d3.arc()
            .innerRadius(innerRadius)
            .outerRadius(outerRadius)
            .startAngle(0)
            .endAngle(piePercent);

        let svg = body.append('div').attr('class', 'container')
            .append("svg")
            //.style("overflow", "visible")
            .attr("width", w)
            .attr("height", h)
            .append('g')
            .attr("transform", 'translate(' + w / 2 + ',' + ((h / 2) + 55) + ')');

        svg.append("text")
            .attr("text-anchor", "middle")
            .attr('x', 0)
            .attr('y',-16)
            .style("font-size","12px")
            .text(centerText);

        var path = svg.append('path')
            .attr("d", arc)
            .attr("transform", 'rotate(-90)')
            .attr("fill", color[0]);

        let path2 = svg.append('path')
            .attr("d", arcLine)
            .attr("transform", 'rotate(-90)')
            .attr("fill", color[1]);


        svg.append('text')
            .text(Math.round(percent) + "%")
            .attr('x', 0)
            .attr('y', -30)
            .style("font-size", "22px")
            .attr('text-anchor', 'middle');


        return body.select('.container').html();
    },

    //Inspection Tour Company Dashboard
    getVerLollipopChart: async (dataGroups, poorRatingData, fairRatingData, maxRating, width, height, yAxisLabel, axisFontSize, legendInfo, legendsAdj) => {
        const fakeDom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
        let body = d3.select(fakeDom.window.document).select('body');

        // set the dimensions and margins of the graph
        let margin = {top: 10, right: 10, bottom: 65, left: 50};
        width = width - margin.left - margin.right,
            height = height - margin.top - margin.bottom;

        let svg = body.append('div').attr('class', 'container')
            .append("svg")
            //.style("overflow", "visible")
            .attr("width", width + margin.left + margin.right)
            .attr("height", height + margin.top + margin.bottom)
            .append("g")
            .attr("transform",
                "translate(" + margin.left + "," + margin.top + ")");

        let x = d3.scaleBand()
            .range([0, width])
            .domain(dataGroups)
            .padding(1);

        svg.append("g")
            .attr('class', 'x axis')
            .attr("transform", "translate(0," + height + ")")
            .call(d3.axisBottom(x))
            .selectAll("text")
            .attr("transform", "rotate(-15)")
            .style("font-size", axisFontSize)
            .style("text-anchor", "end");

        // tooltip
        svg.selectAll('.x.axis>.tick')
            .append('title')
            .text(d => d);

        // Add Y axis
        let y = d3.scaleLinear()
            .domain([0, maxRating])
            .range([height, 0]);

        let ticks = 10;
        if (maxRating < 10) {
            ticks = +maxRating;
        }
        svg.append("g")
            .call(d3.axisLeft(y).tickFormat(d3.format('.0f')).ticks(ticks))
            .style("font-size", axisFontSize);

        if (yAxisLabel) {
            svg.append("text")
                .attr("class", "y label")
                .attr("text-anchor", "end")
                .attr("y", -35)
                .attr("x", -80)
                .attr("transform", "rotate(-90)")
                .text(yAxisLabel);
        }

        let positionAdj = 4;
        if (fairRatingData.length) {
            positionAdj = 8;
        }

        // Lines
        /*svg.selectAll("myline")
            .data(poorRatingData)
            .enter()
            .append("line")
            .attr("x1", function (d) {
                return x(d.group) - positionAdj;
            })
            .attr("x2", function (d) {
                return x(d.group) - positionAdj;
            })
            .attr("y1", function (d) {
                return y(d.rating);
            })
            .attr("y2", y(0))
            .attr("stroke", "#FE8282");*/

        //Bars
        svg.append("g")
            .attr("fill", `${colorRed}`)
            .selectAll("rect")
            .data(poorRatingData)
            .join("rect")
            .attr("x", (d, i) => x(d.group) - positionAdj)
            .attr("y", d => y(+d.rating))
            .attr("height", d => y(0) - y(+d.rating))
            .attr("width", 8);

        if (fairRatingData.length) {
            //Lines
            /*svg.selectAll("myline")
                .data(fairRatingData)
                .enter()
                .append("line")
                .attr("x1", function (d) {
                    return x(d.group) + positionAdj;
                })
                .attr("x2", function (d) {
                    return x(d.group) + positionAdj;
                })
                .attr("y1", function (d) {
                    return y(d.rating);
                })
                .attr("y2", y(0))
                .attr("stroke", "#ffa500a6");*/

            //Bars
            svg.append("g")
                .attr("fill", `${colorYellow}`)
                .selectAll("rect")
                .data(fairRatingData)
                .join("rect")
                .attr("x", (d, i) => x(d.group))
                .attr("y", d => y(+d.rating))
                .attr("height", d => y(0) - y(+d.rating))
                .attr("width", 8);
        }

        // Circles
        /*svg.selectAll("poorcircle")
            .data(poorRatingData)
            .enter()
            .append("circle")
            .attr("cx", function (d) {
                return x(d.group) - positionAdj;
            })
            .attr("cy", function (d) {
                return y(d.rating);
            })
            .attr("r", "3")
            .style("fill", "#FE8282")
            .attr("stroke", "black");*/

        /*if (fairRatingData.length) {
            svg.selectAll("faircircle")
                .data(fairRatingData)
                .enter()
                .append("circle")
                .attr("cx", function (d) {
                    return x(d.group) + positionAdj;
                })
                .attr("cy", function (d) {
                    return y(d.rating);
                })
                .attr("r", "3")
                .style("fill", "#ffa500a6")
                .attr("stroke", "black");
        }*/

        //Legends
        if (poorRatingData.length && fairRatingData.length) {
            const legend = svg.append('g')
                .attr('class', 'legend')
                .attr('id', 'legend')
                .attr('transform', `translate(0,0)`);

            const lg = legend.selectAll('g')
                .data(legendInfo)
                .enter()
                .append('g')
                .attr('transform', (d, i) => `translate(${i * 100},${height + 15})`);
            lg.append('circle')
                .style('fill', (d, i) => {
                    return d.color
                })
                .attr('cy', 5)
                .attr('r', 6)
                .attr('width', 10)
                .attr('height', 10);

            lg.append('text')
                .style('font-family', "'Nunito', Arial, Helvetica, sans-serif")
                .style('font-size', '13px')
                .attr('x', 12.5)
                .attr('y', 10)
                .text(d => d.name);

            let offset = 0;
            lg.attr('transform', function (d, i) {
                let x = offset;
                offset += (measureTextWidth(d.name, 13) + 12) + 10;
                return `translate(${x},${height + 10})`;
            });

            legend.attr('transform', function (d, i) {
                return `translate(${(width - offset) / 2},${50 - legendsAdj})`
            });
        }

        return body.select('.container').html();
    },

    //Inspection Tour Company Dashboard
    getHoriLollipopChart: async (dataGroups, poorRatingData, fairRatingData, maxRating, width, height, xAxisLabel, axisFontSize, legendInfo, legendsAdj) => {
        const fakeDom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
        let body = d3.select(fakeDom.window.document).select('body');

        // set the dimensions and margins of the graph
        let margin = {top: 10, right: 10, bottom: 65, left: 80};
        width = width - margin.left - margin.right,
        height = height - margin.top - margin.bottom;

        let svg = body.append('div').attr('class', 'container')
            .append("svg")
            //.style("overflow", "visible")
            .attr("width", width + margin.left + margin.right)
            .attr("height", height + margin.top + margin.bottom)
            .append("g")
            .attr("transform",
                "translate(" + margin.left + "," + margin.top + ")");

        let customNumbers = [0, '1/4', '1/2', '3/4', 1, 2, 3, 4, 5 ,6];
        let customNumber = (maxRating < 6) ? 8 : maxRating;
        // Add X axis
        let x = d3.scaleLinear()
            .domain( [0, customNumber])
            .range([ 0, width ]);

        //show horizontal grid lines on chart
        let ticks = 10;
        if (maxRating < 10 && maxRating > 5) {
            ticks = +maxRating;
        }

        if (maxRating <= 5) {
            svg.append("g")
                .attr('class', 'x axis')
                .attr("transform", "translate(0," + height + ")")
                .call(d3.axisBottom(x).tickFormat(d => {
                    return customNumbers[d];
                }).ticks(ticks))
                .selectAll("text")
                .style("font-size", axisFontSize)
                .attr("transform", "rotate(-25)")
                .style("text-anchor", "end");
        } else {
            svg.append("g")
                .attr('class', 'x axis')
                .attr("transform", "translate(0," + height + ")")
                .call(d3.axisBottom(x).tickFormat(d3.format('.0f')).ticks(ticks))
                .selectAll("text")
                .style("font-size", axisFontSize)
                .attr("transform", "rotate(-25)")
                .style("text-anchor", "end");
        }

        // tooltip
        svg.selectAll('.x.axis>.tick')
            .append('title')
            .text(d => d);

        // Y axis
        let y = d3.scaleBand()
            .range([0, height])
            .domain(dataGroups)
            .padding(1);
        svg.append("g")
            .call(d3.axisLeft(y))
            .selectAll("text")
            .attr("transform", "rotate(-50)")
            .attr("dy", "-0.52em")
            .attr("x", "-6")
            .style("font-size", axisFontSize);

        if (xAxisLabel) {
            svg.append("text")
                .attr("class", "x label")
                .attr("text-anchor", "end")
                .attr("y", height + 30)
                .attr("x", (width/2 + 22))
                //.attr("transform", "rotate(-90)")
                .text(xAxisLabel);
        }

        let positionAdj = 0;
        if (fairRatingData.length) {
            positionAdj = 4;
        }

        // Lines
        svg.selectAll("myline")
            .data(poorRatingData)
            .enter()
            .append("line")
            .attr("x1", function (d) {
                return x(d.rating);
            })
            .attr("x2", x(0))
            .attr("y1", function (d) {
                return y(d.group) - positionAdj;
            })
            .attr("y2", function (d) {
                return y(d.group) - positionAdj
                    ;
            })
            .attr("stroke", `${colorRed}`);

        if (fairRatingData.length) {
            svg.selectAll("myline")
                .data(fairRatingData)
                .enter()
                .append("line")
                .attr("x1", function (d) {
                    return x(d.rating);
                })
                .attr("x2", x(0))
                .attr("y1", function (d) {
                    return y(d.group) + positionAdj;
                })
                .attr("y2", function (d) {
                    return y(d.group) + positionAdj;
                })
                .attr("stroke", `${colorYellow}`)
        }

        // Circles
        svg.selectAll("mycircle")
            .data(poorRatingData)
            .enter()
            .append("circle")
            .attr("cx", function (d) {
                return x(d.rating);
            })
            .attr("cy", function (d) {
                return y(d.group) - positionAdj
                    ;
            })
            .attr("r", "3")
            .style("fill", `${colorRed}`)
            .attr("stroke", "black")

        if (fairRatingData.length) {
            svg.selectAll("mycircle")
                .data(fairRatingData)
                .enter()
                .append("circle")
                .attr("cx", function (d) {
                    return x(d.rating);
                })
                .attr("cy", function (d) {
                    return y(d.group) + positionAdj;
                })
                .attr("r", "3")
                .style("fill", `${colorYellow}`)
                .attr("stroke", "black")
        }

        //Legends
        if (poorRatingData.length && fairRatingData.length && legendInfo.length) {
            const legend = svg.append('g')
                .attr('class', 'legend')
                .attr('id', 'legend')
                .attr('transform', `translate(0,0)`);

            const lg = legend.selectAll('g')
                .data(legendInfo)
                .enter()
                .append('g')
                .attr('transform', (d, i) => `translate(${i * 100},${height + 15})`);
            lg.append('circle')
                .style('fill', (d, i) => {
                    return d.color
                })
                .attr('cy', 5)
                .attr('r', 6)
                .attr('width', 10)
                .attr('height', 10);

            lg.append('text')
                .style('font-family', "'Nunito', Arial, Helvetica, sans-serif")
                .style('font-size', '13px')
                .attr('x', 12.5)
                .attr('y', 10)
                .text(d => d.name);

            let offset = 0;
            lg.attr('transform', function (d, i) {
                let x = offset;
                offset += (measureTextWidth(d.name, 13) + 12) + 10;
                return `translate(${x},${height + 10})`;
            });

            legend.attr('transform', function (d, i) {
                return `translate(${(width - offset) / 2},${50 - legendsAdj})`
            });
        }

        return body.select('.container').html();
    },

    //Inspection Tour Form
    getStackedBarWithLineChart: async ({
        data,
        columns,
        lineChartData,
        lineChartItemName,
        lineChartAllGroup,
        lineChartAdj,
        maxRating,
        width,
        height,
        colors,
        axisFontSize,
        yAxisLabel,
        xAxisLabel,
        xAxisLabelAdj,
        legendData,
        legendsAdj
    }) => {
        const fakeDom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
        let body = d3.select(fakeDom.window.document).select('body');

        let dimensions = ({
            height:height,
            width:width,
            margin: {
                top: 10,
                right: 30,
                bottom: 75,
                left: 50,
            }
        });
        let groups = d3.map(data, d => d.group);
        let subgroups = columns.slice(1);
        let stackedData = d3.stack()
            .keys(subgroups)
            (data);

        let color = d3.scaleOrdinal()
            .domain(subgroups)
            .range(colors);

        //maxRating = (maxRating < 8) ? 8 : maxRating;
        let y = d3.scaleLinear()
            .domain([0, +maxRating])
            .range([ dimensions.height, 0 ]);

        let x = d3.scaleBand()
            .domain(groups)
            .range([0, dimensions.width])
            .padding([0.5]);

        // append the svg object to the body of the page
        let svg = body.append('div').attr('class', 'container')
            .append("svg")
            //.style("overflow", "visible")
            .attr("width", dimensions.width + dimensions.margin.left + dimensions.margin.right)
            .attr("height", dimensions.height + dimensions.margin.top + dimensions.margin.bottom)
            .append("g")
            .attr("transform",
                "translate(" + dimensions.margin.left + "," + dimensions.margin.top + ")")

        //show horizontal grid lines on chart
        let yAxisGrid = d3.axisLeft(y).tickSize(-dimensions.width).tickFormat('').ticks(10);
        svg.append('g')
            .attr('class', 'd-y-axis-grid')
            .call(yAxisGrid);

        svg.append("g")
            .selectAll("g")
            // Enter in the stack data = loop key per key = group per group
            .data(stackedData)
            .enter().append("g")
            .attr("fill", d => color(d.key))
            .selectAll("rect")
            // enter a second time = loop subgroup per subgroup to add all rectangles
            .data(d => d)
            .enter().append("rect")
            .attr("x", d => x(d.data.group))
            .attr("y", d => y(d[1]))
            .attr("height", d => y(d[0]) - y(d[1]))
            .attr("width",x.bandwidth())

        if (yAxisLabel) {
            svg.append("text")
                .attr("class", "y label")
                .attr("text-anchor", "end")
                .attr("y", -30)
                .attr("x", -80)
                .attr("transform", "rotate(-90)")
                .text(yAxisLabel);
        }

        // text label for the x axis
        if (xAxisLabel) {
            svg.append("text")
                .attr("transform",
                    "translate(" + ((dimensions.width / 2) + xAxisLabelAdj) + " ," +
                    (dimensions.height + dimensions.margin.top + 50) + ")")
                .style("text-anchor", "middle")
                .text(xAxisLabel);
        }

        svg.append("g")
            .attr("class", "x axis")
            .attr("transform", "translate(0," + dimensions.height + ")")
            .call(d3.axisBottom(x).ticks(10))
            .style("font-size", axisFontSize)
            .selectAll("text")
            .style("text-anchor", "end")
            .attr("dx", "-.8em")
            .attr("dy", ".15em")
            .attr("transform", "rotate(-18)");

        // tooltip
        svg.selectAll('.x.axis>.tick')
            .append('title')
            .text(d => d);

        let ticks = 10;
        if (maxRating < 10) {
            ticks = +maxRating;
        }

        svg.append("g")
            .style("font-size", axisFontSize)
            .call(d3.axisLeft(y).tickFormat(d3.format('.0f')).ticks(ticks));

        if (legendData.length) {
            if (legendData.length > 1) {
                let legendh = 100;
                let legend = svg.append('g')
                    .attr('class', 'legend')
                    .attr('transform', 'translate(' + ((dimensions.width / 4) + legendsAdj) + ',' + (dimensions.height - legendh) + ')');

                legend.selectAll('circle')
                    .data(legendData)
                    .enter()
                    .append('circle')
                    .attr('cy', function (d, i) {
                        return (i == 0) ? 145 : 161;
                    })
                    .attr('r', 6)
                    .attr('cx', 55)
                    .attr('fill', d => d.color);
                legend.selectAll('text')
                    .data(legendData)
                    .enter()
                    .append('text')
                    .text(function (d) {
                        return d.key;
                    })
                    .attr('y', function (d, i) {
                        return (i == 0) ? 138 : 155;
                    })
                    .attr('font-size', '12px')
                    .attr('x', 64)
                    .attr('text-anchor', 'start')
                    .attr('alignment-baseline', 'hanging');

            }
        }

        let dataReady = lineChartAllGroup.map( function(grpName) { // .map allows to do something for each element of the list
            return {
                name: grpName,
                values: lineChartData.reduce((arr,d) => {
                    if (+d[grpName]) {
                        arr.push({item: d[lineChartItemName], value: +d[grpName]});
                    }
                    return arr;
                }, [])
            };
        });

        // A color scale: one color for each group
        let myColor = d3.scaleOrdinal()
            .domain(lineChartAllGroup)
            .range(["#ffc36a"]);

        let adjustment = 25;

        let line = d3.line()
            .x(function(d) { return  x(d.item) + lineChartAdj; }) // set the x values for the line generator
            .y(function(d) { return y(+d.value); }) // set the y values for the line generator
            .curve(d3.curveMonotoneX);

        svg.selectAll("myLines")
            .data(dataReady)
            .enter()
            .append("path")
            .attr("d", function(d){ return line(d.values) } )
            .attr("stroke", function(d){ return myColor(d.name) })
            .style("stroke-width", 4)
            .style("fill", "none")

        //sails.log.info(JSON.stringify(dataReady));

        // Add the points
        svg
        // First we need to enter in a group
            .selectAll("myDots")
            .data(dataReady)
            .enter()
            .append('g')
            .style("fill", function(d){ return myColor(d.name) })
            // Second we need to enter in the 'values' part of this group
            .selectAll("myPoints")
            .data(function(d){ return d.values })
            .enter()
            .append("circle")
            .attr("cx", function(d) { return x(d.item) + lineChartAdj; })
            .attr("cy", function(d) { return y(d.value) })
            .attr("r", function(d) { return 5 })
            .attr("stroke", "white");

        return body.select('.container').html();
    },

    getDonutChartV2: async (dataObj, width, height, centroidAdj, radius, innerRadius, viewBox, legendStartingPosition=0) => {
        /**dataObj sample data....
         *
            {
                data: [
                    {
                        name: 'ItemName (Count)',
                        label: 'ItemName',
                        count: 'Count',
                        color: 'colorHex'
                    }
                ],
                total_count: totalCount,
                columns: ['ItemName'],
                pie_colors: ['colorHex']
            }
        **/

        const fakeDom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
        let body = d3.select(fakeDom.window.document).select('body');
        let arc = d3.arc()
            .outerRadius(radius - 10)
            .innerRadius(innerRadius);
        let data = dataObj.data;
        let svg = body.append('div').attr('class', 'container')
            .append("svg")
            //.style("overflow", "visible")
            //.attr("viewBox",viewBox)
            .attr("width", width)
            .attr("height", height)
            .append("g")
            .attr("transform", "translate(" + width / 3.2 + "," + height / 2 + ")");

        if (dataObj.total_count) {
            let pie = d3.pie()
                .sort(null)
                .value(function(d) {
                    return d.count;
                });

            let g = svg.selectAll(".arc")
                .data(pie(data))
                .enter().append("g");

            g.append("path")
                .attr("d", arc)
                .style("fill", function (d, i) {
                    return d.data.color;
                });

            let r = Math.min(width, height) / 2;
            let labelr = r + (centroidAdj);

            g.append("text")
                .style("font-size", "12px")
                .attr("transform", function (d) {
                    var c = arc.centroid(d),
                        x = c[0],
                        y = c[1],
                        // pythagorean theorem for hypotenuse
                        h = Math.sqrt(x * x + y * y);
                    return "translate(" + (x / h * labelr) + ',' +
                        (y / h * labelr) + ")";
                })
                .attr("dy", ".50em")
                .style("text-anchor", "middle")
                .attr("fill", "#000")
                .attr("font-weight", "bold")
                .text(function (d) {
                    return (d.data.count) ? `${d.data.count}` : '';
                });

            g.append("text")
                .attr("text-anchor", "middle")
                .attr('font-size', '4em')
                .attr('y', 20);
            //.text(totalCount);

            // Add one dot in the legend for each name.
            svg.selectAll("legendDots")
                .data(dataObj.columns)
                .enter()
                .append("circle")
                .attr("cx", 142)
                .attr("cy", function(d,i){ return legendStartingPosition + i*24}) // 0 is where the first dot appears. 25 is the distance between dots
                .attr("r", 6)
                .style("fill", function(value, index) { return dataObj.pie_colors[index]})

            // Add one dot in the legend for each name.
            svg.selectAll("legendLabels")
                .data(dataObj.columns)
                .enter()
                .append("text")
                .attr("x", 155)
                .attr("y", function(d,i){ return legendStartingPosition + i*25}) // 0 is where the first dot appears. 25 is the distance between dots
                .style("fill", function(value, index) { return dataObj.pie_colors[index]})
                .text(function(d){ return d})
                .attr("text-anchor", "left")
                .attr("font-size", "13px")
                .style("alignment-baseline", "middle")
        }

        return body.select('.container').html();
    },
}
