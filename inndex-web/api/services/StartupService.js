/**
 * StartupService
 * Created by spatel on 19/8/20.
 * This should be used only to define action that needs to be executed on app startup.
 */

const fs = require('fs');
const path = require('path');
const {makeGET} = require('./HttpService');
const {cleanOlderAccessLogs} = require('./AccessLogService');

let sorter = function (a, b) {
    return a.toLowerCase().localeCompare(b.toLowerCase());
};

const refreshCountriesList = async () => {
    let file_name = sails.config.constants.COUNTRIES_META_FILE;
    let file_path = path.join(process.cwd(), file_name);
    sails.log.info('Updating countries list on path:', file_path);
    let {countries, nationalities} = JSON.parse(fs.readFileSync(file_path, 'utf-8'));
    let list = countries;

    // const COUNTRY_LAYER_KEY = sails.config.custom.COUNTRY_LAYER_KEY;
    // let response = await makeGET(`http://api.countrylayer.com/v2/all?access_key=${COUNTRY_LAYER_KEY}`, {}, {}, true, 10000);
    // if(!response.success){
    //     return 'error';
    // }
    /*let list = (response.data || []).map(r => {
        let name = (r.name || '').toString().replace('Å', 'A');
        if(r.alpha3Code === 'GBR'){
            name = 'United Kingdom';
        }
        return {
            name,
            demonym: (r.demonym || '').toString().replace('Å', 'A'),
            timezones: r.timezones,
            flag: r.flag,
            code: r.alpha3Code || r.alpha2Code,
        };
    });*/
    let ukName = {
        "name": "United Kingdom",
        "timezones": [
            "UTC-08:00",
            "UTC-05:00",
            "UTC-04:00",
            "UTC-03:00",
            "UTC-02:00",
            "UTC",
            "UTC+01:00",
            "UTC+02:00",
            "UTC+06:00"
        ],
        "flag": "https://restcountries.eu/data/gbr.svg",
        "code": "GBR"
    };
    list.push({
        ...ukName,
        "demonym": "Welsh"
    }, {
        ...ukName,
        "demonym": "English"

    }, {
        ...ukName,
        "demonym": "Scottish"
    }, {
        ...ukName,
        "demonym": "Irish"
    });
    sails.log.info('Response from GET JSON call', list.length);
    if (list.length) {
        let nationalities = list.sort((a, b) => sorter(a.demonym, b.demonym))
            .filter((r, index, all) => all.findIndex(c => c.demonym === r.demonym) === index);

        let countries = list.sort((a, b) => sorter(a.name, b.name))
            .filter((r, index, all) => all.findIndex(c => c.name === r.name) === index);

        sails.log.info('Updating countries list, total count:', list.length);
        sails.log.info('Updating nationalities list, total count:', nationalities.length);
        fs.writeFileSync(file_path, JSON.stringify({countries, nationalities}));
    }
    return 'success';
};

module.exports = {

    onAppStart: async () => {
        //adding dayjs UTC & Timezone plugin to the main dayjs on startup.
        const dayjs = require('dayjs');
        const utc = require('dayjs/plugin/utc');
        const timezone = require('dayjs/plugin/timezone'); // dependent on utc plugin
        dayjs.extend(utc);
        dayjs.extend(timezone);

        if(process.env.NODE_APP_INSTANCE === undefined || process.env.NODE_APP_INSTANCE !== '0'){
            return `Skipping, no need to execute Startup actions on cluster(${process.env.NODE_APP_INSTANCE})`;
        }
        // (sails.config.custom.REFRESH_COUNTRIES_LIST_META) && await refreshCountriesList();
        (sails.config.custom.CLEAN_UP_OLD_API_ACCESS_LOGS) && await cleanOlderAccessLogs();
        return 'Startup actions completed';
    },
};
