/**
 * Created by spatel on 10/9/18.
 */

const jwt = require("jsonwebtoken");
const moment = require('moment');

const TOKEN_KEY = sails.config.models.dataEncryptionKeys.default;
const UPDATE_YOUR_APP_TO_LATEST = 'Please update your app to the latest version';
const {REFRESH_TOKEN_SECRET, APP_ENV} = sails.config.custom;
const {authErrorObject, sendResponse} = require('./../services/ResponseService');

const defaultTokenExpiryIn = (APP_ENV !== 'production') ? 7 : 3; // 3 hours in production, 7 minutes in development/staging
const defaultTokenExpiryInUnit = (APP_ENV !== 'production') ? 'm' : 'h'; // minutes in development/staging, hours in production

/**
 * Declaring duplicate fn here to eliminate circular dependency with DataProcessingService.
 * @param records
 * @param userRefField
 * @param select
 * @returns {Promise<*[]|*>}
 * @private
 */
const _populateUserRefs = async (records, userRefField = 'user_ref', select = []) => {
    sails.log.info(`Populating users, ${records.length} records`);
    let userIds = (records || []).reduce((arr, {[userRefField]: userRef}) => {
        return (userRef && typeof userRef === 'number' && !arr.includes(userRef)) ? arr.concat(userRef) : arr;
    }, []);

    if (!userIds.length) {
        sails.log.info(`No user ref available to expand in ${records.length} records.`);
        return records;
    }

    let find = {
        where: {id: userIds},
        ...(select.length ? {select} : {})
    };
    let users = await sails.models.user_reader.find(find);
    records = (records || []).map(record => {
        record[userRefField] = users.find(user => (record[userRefField] && user.id === record[userRefField])) || null;
        return record;
    });
    sails.log.info(`Expanded ${users.length} users for ${records.length} records`);
    return records;
};

const processUserRoles = (user_roles) => {
    return (user_roles || []).reduce((roles, r) => {
        if (!roles.includes(r.role)) {
            roles.push(r.role);
        }
        return roles;
    }, []);
};

const renderChanges = (changes, level = 0, changedBy = '', updatedOn = '') => {
  if (!changedBy && !updatedOn) return '';

  const rows = [];
  collectRows(changes, [], rows);

  const grouped = rows.reduce((acc, row) => {
    const section = row.section || '';
    acc[section] = acc[section] || [];
    acc[section].push(row);
    return acc;
  }, {});

  return Object.entries(grouped)
    .map(([section, fields]) =>
      fields
        .map((row, idx) => `
          <tr class="change-log__row--content">
            ${idx === 0 ? `<td rowspan="${fields.length}">${section}</td>` : ''}
            <td>${row.field}</td>
            <td>${row.oldValue}</td>
            <td>${row.newValue}</td>
          </tr>
        `)
        .join('')
    )
    .join('');
};

const collectRows = (changes, path = [], rows = []) => {
  for (const key in changes) {
    const value = changes[key];
    const newPath = [...path, key];

    if (
      value &&
      typeof value === 'object' &&
      'oldValue' in value &&
      'newValue' in value
    ) {
      let section = '';
      let field = key;

      if (newPath.length > 1) {
        // Check if we have an "Item X" pattern in the path
        const itemIndex = newPath.findIndex(part => /^Item\s+\d+$/i.test(part));

        if (itemIndex !== -1 && newPath.length >= 3) {
          // For paths like: ["Incident Actions", "Item 1", "Action Detail"]
          // Section: "Incident Actions > Item 1" (stop at Item level)
          // Field: "Action Detail" (everything after Item)
          section = newPath.slice(0, itemIndex + 1).join(' > ');
          field = newPath.slice(itemIndex + 1).join(' > ');
        } else {
          // For regular nested paths without Item pattern
          section = newPath[0];
          field = newPath.slice(1).join(' > ');
        }
      }

      rows.push({
        section,
        field,
        oldValue: formatValue(value.oldValue),
        newValue: formatValue(value.newValue)
      });
    } else if (value && typeof value === 'object') {
      collectRows(value, newPath, rows);
    }
  }
};

const formatValue = val =>
  val && typeof val === 'object' ? JSON.stringify(val) : val || '';


const permissionKeywords = {
    // Resource related keywords
    COMPANY_PREFIX: 'company',
    PROJECT_PREFIX: 'project',
    buildCompanyPermissionKey: id => `company.${id}`,
    buildProjectPermissionKey: id => `project.${id}`,
    extractResourceIds: (resource, permissions = {}) => {
        return Object.keys(permissions).map(l => l.split('.')).filter(r => r[0] === resource).map(r => +r[1]);
    }
};

const resourceIdentifier = {
    COMPANY_PREFIX: 'company',
    PROJECT_PREFIX: 'project',
    PROJECT: (id) => `project.${id}`,
    COMPANY: (id) => `company.${id}`,
    getResourceId: (prefix, resourceString) => +(resourceString.split('.').pop()),
};

const hasRole = (user_role_access, role, defaultValue = false) => {
    let UAC = user_role_access.find(u => u.role === role);
    // @todo: might need to add more filtering?
    return UAC || defaultValue;
};

const deserializeUAC = (value) => {
    if(value.designation){
        value.designation = value.designation.split(',');
    }else if(!value.designation){
        value.designation = [];
    }

    if(value.resource && (value.role === ROLES.COMPANY_ADMIN || value.role === ROLES.COMPANY_PROJECT_ADMIN)){
        value.resource = resourceIdentifier.getResourceId(resourceIdentifier.COMPANY_PREFIX, value.resource);
    }
    else if(value.resource && value.role === ROLES.SITE_ADMIN){
        value.resource = resourceIdentifier.getResourceId(resourceIdentifier.PROJECT_PREFIX, value.resource);
    }

    if(value.sub_resource && value.role === ROLES.COMPANY_PROJECT_ADMIN){
        value.sub_resource = resourceIdentifier.getResourceId(resourceIdentifier.COMPANY_PREFIX, value.sub_resource);
    }
    return value;
};

const deserializeUACList = (user_roles = [], extract = false) => {
    // will be called from isAuth ?

    let create_permission = (user_roles || []).find(uac => uac.role === ROLES.SITE_ADMIN && !uac.resource);
    let data = {
        projects: {},
        companies: {},
    };
    /*if(extract){
        data = (user_roles || []).reduce((obj, row) => {
            row = deserializeUAC(Object.assign({}, row));
            let compact_uac = {id: row.id, designation: row.designation, flags: row.flags, role: row.role};
            if(row.role === ROLES.SITE_ADMIN && row.resource){
                obj.projects[row.resource] = compact_uac;
            }else if(row.role === ROLES.COMPANY_PROJECT_ADMIN){
                obj.projects[row.sub_resource] = compact_uac;
                obj.companies[row.resource] = compact_uac;
            }
            return obj;
        }, data);
    }*/
    let roles =  processUserRoles(user_roles);
    let project_admins_v1 = getProjectAdminStatus(roles, user_roles);
    return {
        uac: {
            permissions: {
                'project:add': (create_permission && create_permission.id) ? true : undefined,
            },
            projects: extract ? data.projects : undefined,
            // allowedProjects: [],
            // allowedCompanies: [],
            // roles: [],
            // projects: [],
            // companies: [],
            project_admins_v1,
        },
        roles,
    };
};

const getProjectAdminStatus = (roles, user_roles) => {
    let project_admins_v1 = [];
    if (roles.includes(ROLES.COMPANY_ADMIN)) {
        project_admins_v1 = user_roles.reduce((arr, ur) => {
            if (ur.role === ROLES.COMPANY_ADMIN && (ur.designation && ur.designation.split(',').includes(UACCompanyDesignations.PROJECT_ADMIN))) {
                let id = resourceIdentifier.getResourceId(resourceIdentifier.COMPANY_PREFIX, ur.resource);
                if (id) {
                    arr.push(id);
                }
            }
            return arr;
        }, []);
    }
    return project_admins_v1;
};

const getAllCompanyUsers = async (companyId, expandUser = true, role = ROLES.COMPANY_ADMIN) => {
    let where = {
        resource: resourceIdentifier.COMPANY(companyId),
    };
    if(role){
        where.role = role;
    }
    let ur_records = await sails.models.userrole.find({where, sort: ['id ASC'], select: ['role', 'resource', 'sub_resource', 'designation', 'flags', 'user_ref']});
    if(expandUser){
        ur_records = await _populateUserRefs(ur_records);
    }
    // actual user ONLY
    return ur_records;
};

const getAllProjectUsers = async (projectId, expandUser = true, role = ROLES.SITE_ADMIN) => {
    let where = {
        or: [
            {
                resource: resourceIdentifier.PROJECT(projectId)
            },
            {
                sub_resource: resourceIdentifier.PROJECT(projectId),
            }
        ]
    };
    if(role === ROLES.SITE_ADMIN){
        where = {
            resource: resourceIdentifier.PROJECT(projectId),
            role: role,
        };
    }
    let ur_records = await sails.models.userrole.find({where, sort: ['id ASC'], select: ['role', 'resource', 'sub_resource', 'designation', 'permission', 'flags', 'user_ref']});
    if(expandUser) {
        let userIds = [];
        ur_records.map(ur => {
            if (ur.user_ref) {
                userIds.push(ur.user_ref);
            }
        });
        userIds = _.uniq(userIds);
        sails.log.info('Total User Ids to expand:', userIds.length);
        if(userIds.length) {
            let users = await sails.models.user_reader.find({id: userIds}).populate('profile_pic_ref');
            if(users && users.length){
                ur_records = ur_records.map(ur => {
                    if(ur.user_ref){
                        ur.user_ref = users.find(u => u.id === ur.user_ref);
                    }
                    return ur;
                });
            }
        }
    }
    return ur_records;
};

const storeUACRecordsOrError = async (records = []) => {
    try{
        sails.log.info('create new UAC records, count', records.length);
        let new_inserts = await sails.models.userrole.createEach(records);
        sails.log.info('Created UAC records');
        return new_inserts;
    }catch (exception) {
        if(exception.code === 'E_UNIQUE'){
            sails.log.info('requested records:', records);
            throw new Error('DUPLICATE_ENTRY');
        }
        throw exception;
    }

};

const hasOnlySiteManagementAccess = async (user,projectId) => {
    let uacRecord = (user.raw_uac).find(uac => (uac.resource === `project.${projectId}`));
    let hasOnlySiteManagement = false;
    if (uacRecord && uacRecord.designation === 'delivery_management') {
        hasOnlySiteManagement = true;
    }
    return hasOnlySiteManagement;
}

const validateCPAPermissions = (user_roles, companyId, projectListOnly = true) => {
    // companies with their designations
    // this can be merged into `deserializeUACList` itself ??
    let role = ROLES.COMPANY_PROJECT_ADMIN;
    let uac_records = (user_roles || []).filter(r => (r.role === role)).map(deserializeUAC).filter(r => r.resource === companyId);
    if(!uac_records.length){
        return false;
    }
    return projectListOnly ? uac_records.map(r => r.sub_resource) : uac_records;
}

const tokenType = {
    AUTH_TOKEN: 'auth',
    ANONYMOUS_PROJECT_TOKEN: 'anonymous-project',
};

const ROLES = {
    USER: 'USER',
    SUPER_ADMIN: 'SUPER_ADMIN',
    SITE_ADMIN: 'SITE_ADMIN',
    COMPANY_ADMIN: 'COMPANY_ADMIN',
    COMPANY_PROJECT_ADMIN: 'COMPANY_PROJECT_ADMIN',
};

const UACCompanyDesignations = {
    NOMINATED: 'nominated',
    PROJECT_ADMIN: 'project_admin'
};

const toolList = [
    {target:['web', 'app'], key: "dashboards", m_key: "m_dashboards", has_nom_email: false, phrase_key: "", label: "Dashboard", supports_asite: false},
    {target:[], key: "induction", m_key: "m_induction", has_nom_email: true, phrase_key: "induction_phrase", container: "custom_field", label: "Inductions", supports_asite: false},
    {target:['app'], key: "e_and_a_control", m_key: "m_e_and_a_control", has_nom_email: false, phrase_key: "", label: "Enrolment & Access Control", supports_asite: false},
    {target:[], key: "competency_expiry", m_key: "m_competency_expiry", has_nom_email: true, phrase_key: "", label: "Competency/Certification Expiry", supports_asite: false},
    {target:['web', 'app'], key: "time_management", m_key: "m_time_management", has_nom_email: false, phrase_key: "", label: "Time Management", supports_asite: false},
    {target:['web', 'app'], key: "site_messaging", m_key: "m_site_messaging", has_nom_email: false, phrase_key: "", label: "Site Messaging", supports_asite: false},
    {target:['web', 'app'], key: "good_calls", m_key: "m_good_calls", has_nom_email: true, phrase_key: "gc_phrase", container: "custom_field", label: "Good Calls", supports_asite: true},
    {target:['web', 'app'], key: "observations", m_key: "m_observations", has_nom_email: true, phrase_key: "obrs_phrase", container: "custom_field", label: "Observations", supports_asite: false},
    {target:['web', 'app'], key: "close_calls", m_key: "m_close_calls", has_nom_email: true, phrase_key: "cc_phrase", container: "custom_field", label: "Close Calls", supports_asite: true},
    {target:['web', 'app'], key: "take_5s", m_key: "m_take_5s", has_nom_email: true, phrase_key: "take5_phrase", label: "Take 5s", supports_asite: true},
    {target:['web', 'app'], key: "toolbox_talks", m_key: "m_toolbox_talks", has_nom_email: true, phrase_key: "", label: "Toolbox Talks", supports_asite: true},
    {target:['web', 'app'], key: "progress_photos", m_key: "m_progress_photos", has_nom_email: true, phrase_key: "", label: "Progress Photos", supports_asite: false},
    {target:['web', 'app'], key: "delivery_notes", m_key: "m_delivery_notes", has_nom_email: true, phrase_key: "dn_phrase", container: "custom_field", label: "Delivery Notes", supports_asite: true},
    {target:['web', 'app'], key: "collection_note", m_key: "m_collection_note", has_nom_email: true, phrase_key: "cn_phrase", container: "custom_field", label: "Collection Note", supports_asite: false},
    {target:['web', 'app'], key: "incident_report", m_key: "m_incident_report", has_nom_email: true, phrase_key: "", label: "Incident Report", supports_asite: true},
    {target:['web', 'app'], key: "inspections", m_key: "m_inspections", has_nom_email: true, phrase_key: "", label: "Inspections", supports_asite: true},
    {target:['web', 'app'], key: "clerk_of_works", m_key: "m_clerk_of_works", has_nom_email: true, phrase_key: "cow_phrase", container: "cow_setting", label: "Clerk Of Works", supports_asite: false},
    {target:['web', 'app'], key: "powra", m_key: "m_powra", has_nom_email: false, phrase_key: "powra_phrase", container: "custom_field", label: "POWRA", supports_asite: false},
    {target:['web', 'app'], key: "task_briefings", m_key: "m_task_briefings", has_nom_email: true, phrase_key: "tb_phrase", container: "custom_field", label: "Task Briefings", supports_asite: false},
    {target:['web', 'app'], key: "rams", m_key: "m_rams", has_nom_email: true, phrase_key: "rams_phrase", container: "custom_field", label: "RAMS", supports_asite: true},
    {target:['web', 'app'], key: "work_package_plan", m_key: "m_work_package_plan", has_nom_email: true, phrase_key: "wpp_phrase", container: "custom_field", label: "Work Package Plan", supports_asite: false},
    {target:['web', 'app'], key: "daily_activities", m_key: "m_daily_activities", has_nom_email: true, phrase_key: "da_phrase", container: "custom_field", label: "Daily Diaries", supports_asite: true},
    {target:['web', 'app'], key: "assets", m_key: "m_assets", has_nom_email: true, phrase_key: "assets_phrase", container: "custom_field", label: "Asset Management", supports_asite: false},
    {target:['web', 'app'], key: "transport_management", m_key: "m_transport_management", has_nom_email: false, phrase_key: "", label: "Transport Management", supports_asite: false},
    {target:['app'], key: "gate_supervisor", m_key: "m_gate_supervisor", has_nom_email: false, phrase_key: "", label: "Gate Supervisor", supports_asite: false},
    {target:['app'], key: "roll_call", m_key: "m_roll_call", has_nom_email: false, phrase_key: "", label: "Roll Call", supports_asite: false},
    {target:['web', 'app'], key: "quality_checklist", m_key: "m_quality_checklist", has_nom_email: true, phrase_key: "qcl_phrase", container: "custom_field", label: "ITPs", supports_asite: false},
    {target:['web', 'app'], key: "permit_tool", m_key: "m_permit_tool", has_nom_email: false, phrase_key: "permit_phrase", label: "Permit", supports_asite: true},
    {target:['web', 'app'], key: "documents", m_key: "m_documents", has_nom_email: false, phrase_key: "", label: "Documents", supports_asite: false},
];

const getToolByKey = (key) => {
    const tool = toolList.find(tool => tool.key === key);
    return tool ? tool.key : null;
};

const storeUserLastActiveOn = async (id) => {
    const on = moment().unix();
    sails.log.info(`storing user is active, user_ref: ${id} on: ${on}`);
    return await sails.models.user.updateOne({ id: id }).set({ last_active_on: on });
};

const authenticateUser = async (token) => {
    let decodedPayload = jwt.verify(token, TOKEN_KEY);
    if(decodedPayload.type !== tokenType.AUTH_TOKEN){
        // not a web app token
        throw new Error(`Not a ${tokenType.AUTH_TOKEN} token`);
    }
    // sails.log.info(`Decoded token for user ${decodedPayload.id}`);

    let user = await sails.models.user_reader.findOne({id: decodedPayload.id, is_active: 1});

    if(!user){
        let e = new Error();
        e.name = `Active user not found, id: ${decodedPayload.id}`;
        throw e;
    }
    user.name = `${user.first_name ? user.first_name : ''}${user.last_name ? ' ' + user.last_name : ''}`.trim();
    user.user_roles = await sails.models.userrole_reader.find({
        where: {user_ref: user.id},
        select: ['role', 'resource', 'sub_resource', 'designation', 'flags', 'permission']
    });

    let {uac, roles} = deserializeUACList(user.user_roles || [], false);
    user.roles = roles;
    user._uac = uac;
    user.raw_uac = (user.user_roles || []);
    user._uac.project_admins_v1 = getProjectAdminStatus(user.roles, user.user_roles);
    return user;
};

const getAuthorizedCompaniesCount = async (user_roles) => {
    let companiesIds = user_roles.reduce((arr,item) => {
        if (item.role === 'COMPANY_ADMIN' && item.resource) {
            let companyId = resourceIdentifier.getResourceId(resourceIdentifier.COMPANY_PREFIX, item.resource)
            arr.push(companyId);
        }
        return arr
    }, []);
    sails.log.info('Total companies authorized to user :', companiesIds && companiesIds.length);
    let count = 0;
    if(companiesIds && companiesIds.length){
        count = await sails.models.createemployer.count({
            id: companiesIds,
            has_company_portal: true
        });
    }

    return count;
};

module.exports = {

    UPDATE_YOUR_APP_TO_LATEST,

    generateUserToken: function (payload, done, expiryIn = defaultTokenExpiryIn, expiryInUnit = defaultTokenExpiryInUnit) {

        let expiresOn = moment().add(expiryIn, expiryInUnit).utc().unix(),
            encodedToken = null;

        try {
            encodedToken = jwt.sign(payload, TOKEN_KEY, {
                expiresIn: `${expiryIn}${expiryInUnit}`
            });
            const refreshToken = jwt.sign(payload, REFRESH_TOKEN_SECRET, { expiresIn: '15d' });

            let record = {
                token: refreshToken,
                platform: (payload.platform || ''),
                valid_till: moment().add(15, 'd').utc().unix()
            };
            if(payload.type === tokenType.AUTH_TOKEN){
                record.user_ref = payload.id;
                storeUserLastActiveOn(payload.id).catch(sails.log.error);
            }else if(payload.type === tokenType.ANONYMOUS_PROJECT_TOKEN){
                record.project_ref = payload.id;
            }

            sails.models.userrefreshtoken.create(record).exec(function callback(err, user){})
            return done(null, {
                expiresOn,
                token: encodedToken,
            }, refreshToken);

        } catch (err) {
            return done(err);
        }
    },

    authenticateUserToken: function (token, done) {

        authenticateUser(token).then(user => {
            done(null, user);
        }).catch(done);
    },

    authenticateRefreshToken: async (token) => {
        try {
            return jwt.verify(token, REFRESH_TOKEN_SECRET);
        } catch (e) {
            if(e && e.name === 'TokenExpiredError'){
                return {};
            }
            sails.log.info('Exception while verifying token', e);
            throw e;
        }
    },

    authenticateAnonymousProjectToken: (token, done) => {
        jwt.verify(token, TOKEN_KEY, function decodedJWT(err, decodedPayload) {
            if (err) {
                return done(err);
            }
            else if(decodedPayload.type !== tokenType.ANONYMOUS_PROJECT_TOKEN){
                // not a innTime app token
                return done(new Error(`Not a ${tokenType.ANONYMOUS_PROJECT_TOKEN} token`));
            }

            sails.models.project_reader.findOne({id: decodedPayload.id}).exec(function findOneByTokenId(queryError, project) {
                if (queryError) return done(queryError);
                return done(null, project);
            });
        });
    },

    extendUserPayload: async (user, profile_pic_ref = true, parent_company = true) => {
        try{
            if(user.profile_pic_ref && profile_pic_ref){
                user.profile_pic_ref = await sails.models.userfile_reader.findOne({id: user.profile_pic_ref});
            }
            if(user.parent_company && parent_company){
                user.parent_company = await sails.models.createemployer_reader.findOne({id: user.parent_company});
            }
        }catch (someError) {
            sails.log.error('got some error while expanding user payload', someError);
        }
        return user;
    },

    getProjectLogo: async (fieldType = 'contractor', value = '', country_code = 'GB') => {
        let project_logo_file = {};
        try {
            if(value) {
                let condition = '';
                if (fieldType == 'contractor') {
                    condition = { name: value, country_code };
                }

                if (fieldType == 'parent_company') {
                    condition = { id: value };
                }
                if (condition) {
                    let employer = await sails.models.createemployer_reader.findOne({
                        where: condition,
                        select: ['logo_file_id']
                    });

                    if (employer && employer.logo_file_id) {
                        project_logo_file = await sails.models.userfile_reader.findOne({
                            where: {id: employer.logo_file_id}
                        });
                    };
                }
            }
        } catch (someError) {
            sails.log.error('got some error while expanding user payload', someError);
        }
        return project_logo_file;
    },

    isCallerDiscontinued: (platform = '', checkForIonicApp = false) => {
        const ionic_blocking_patterns = [ `mobile 3`];
        const blocking_patterns = [
            ...Array.from({length: 11}, (_, i) => `mobile-iOS 1.${i}`),
            ...Array.from({length: 11}, (_, i) => `mobile-iOS 2.${i}`),
            ...Array.from({length: 6}, (_, i) => `mobile-iOS 3.${i}`),
            ...Array.from({length: 11}, (_, i) => `mobile-android 1.${i}`),
            ...Array.from({length: 11}, (_, i) => `mobile-android 2.${i}`),
            ...Array.from({length: 6}, (_, i) => `mobile-android 3.${i}`),
        ];

        let patterns_to_check = (checkForIonicApp) ? ionic_blocking_patterns : blocking_patterns;
        let platform_matched = (patterns_to_check.find(pattern => platform.startsWith(pattern))) !== undefined;
        if(platform_matched){
            sails.log.warn(`Discontinued platform found, platform: ${platform}`);
        }
        return platform_matched;
    },
    // buildUserRole,
    // processUserRoles,
    tokenType,

    /**
     * @deprecated This shouldn't be called anymore..
     * @param user_ref
     * @param role
     * @param resource
     * @param sub_resource
     * @param designation
     * @param flags
     * @returns {Promise<*>}
     */
    /*addUserRole: async (user_ref, {role, resource = null, sub_resource = null, designation = null, flags = {}}) =>{
        let filter = {
            user_ref,
            role
        };
        sails.log.info('============================ DEPRECATED fn addUserRole Call ========================= ');
        sails.log.info("Adding user role", role, 'for', user_ref, 'if not exist');
        let role_record = await sails.models.userrole.findOne(filter);
        if (!role_record) {
            role_record = await sails.models.userrole.create(filter);
        }
        return role_record;
    },*/
    ROLES,
    // hasRole,
    // permissionKeywords,

    UACCompanyDesignations,

    resourceIdentifier,

    getAuthorizedCompaniesCount,

    getAllowedCompaniesOfUser: (user_roles) => {
        return (user_roles || []).map(deserializeUAC).reduce((list, uac) => {
            if([ROLES.COMPANY_ADMIN, ROLES.COMPANY_PROJECT_ADMIN].includes(uac.role) && !list.includes(uac.resource)){
                list.push(uac.resource);
            }
            return list;
        }, []);
    },

    getAllowedDefaultProjectsOfUser: async (userId, excludeNull = true, deserialize = true) => {
        // projects with their designations
        // project "creators" are also supposed to be part of this list with `flags.is_default` marked true
        let where = {
            user_ref: userId,
            role: ROLES.SITE_ADMIN,
            ...(excludeNull && {resource: {'!=': null}})
        };
        sails.log.info(`Querying project with SITE-ADMIN role for user: ${userId}, where:`, where);
        let user_records = await sails.models.userrole_reader.find({
            where,
            sort: ['id ASC'],
            select: ['resource', 'role', 'designation', 'flags', 'permission']
        });
        return deserialize ? (user_records || []).map(deserializeUAC) : (user_records || []);
    },

    validateCPAPermissions,

    validateUserAccessPermissionOfProject: ({user_roles}, projectId) => {

    },

    allResourceAdminsWithOneOfDesignations: async (resourceId, designations = [], expandUser = true, role = ROLES.SITE_ADMIN, subResourceId = undefined) => {
        let where = {
            role
        };
        if(designations.length){
            where.or = designations.map(text => ({designation: {contains: text}}));
        }
        if (role === ROLES.SITE_ADMIN) {
            where.resource = resourceIdentifier.PROJECT(resourceId);
        } else if (role === ROLES.COMPANY_ADMIN) {
            where.resource = resourceIdentifier.COMPANY(resourceId);
        } else if (role === ROLES.COMPANY_PROJECT_ADMIN) {
            where.resource = resourceIdentifier.COMPANY(resourceId);
            where.sub_resource = resourceIdentifier.PROJECT(subResourceId);
        }

        sails.log.info(`get all "${designations.join(' OR ')}" of resource: ${resourceId} with role: ${role}, where:`, JSON.stringify(where));
        let ur_records = await sails.models.userrole_reader.find({where, sort: ['id ASC'], select: ['resource', 'sub_resource', 'designation', 'flags', 'permission', 'user_ref']});
        if(expandUser){
            ur_records = await _populateUserRefs(ur_records);
        }
        return ur_records;
    },

    allProjectAdminsByOneOfDesignations: async (projectId, designations = [], expandUser = true, role = ROLES.SITE_ADMIN, companyId = undefined) => {
        let where = {
            role
        };
        if(designations.length){
            where.or = designations.map(text => ({designation: {contains: text}}));
        }
        if(role === ROLES.SITE_ADMIN){
            where.resource = resourceIdentifier.PROJECT(projectId);
        }else if(role === ROLES.COMPANY_PROJECT_ADMIN){
            where.resource = resourceIdentifier.COMPANY(companyId);
            where.sub_resource = resourceIdentifier.PROJECT(projectId);
        }
        sails.log.info(`get all "${designations.join(' OR ')}" of project: ${projectId}, where:`, JSON.stringify(where));
        let ur_records = await sails.models.userrole_reader.find({where, select: ['resource', 'sub_resource', 'designation', 'flags', 'permission', 'user_ref']});
        if(expandUser){
            ur_records = await _populateUserRefs(ur_records);
        }
        return ur_records;
    },

    filterProjectUsersEmailEligibility: (project_users, feature) => {
        sails.log.info('Filter project users based on the email notification eligibility, count:', project_users.length);
        if(feature == '') { return project_users; } //return true if no feature defined.
        let filtered_project_users = (project_users || []).filter((uac)=> {
            let hasCustomAccess = (uac.designation || []).includes('custom'); // Check if user has customizable access.
            let hasNominatedAccess = (uac.designation || []).includes('nominated'); // User has marked as nominated manager.
            let hasFeatNotifyAccess = (uac.permission || []).includes(`${feature}:notify`); //Has permision to receive email notification for feature.
            let hasCustomNotifyAccess = (uac.permission || []).filter(p => p.includes(':notify')).length; //Check if user has custom notification access.
            if((hasCustomAccess && hasFeatNotifyAccess) ||
                (!hasCustomAccess && !hasCustomNotifyAccess && hasNominatedAccess) ||
                (!hasCustomAccess && hasCustomNotifyAccess && hasNominatedAccess && hasFeatNotifyAccess)) {
                return true;
            }
            return false;
        });
        return filtered_project_users;
    },

    getMyPermissionForProject: (project, user_roles) => {
        // filter user_roles records based on `project_category` and respond with single UAC record.
    },

    storeUACRecordsOrError,

    saveCompanyUsers: async ({id}, company_users = []) => {
        let existing = await getAllCompanyUsers(id, false);

        sails.log.info('found COMPANY-ADMIN records', existing.length);

        company_users = company_users.map(cu => ({
            id: cu.id,
            user_ref: (cu.user_ref && cu.user_ref.id) || cu.user_ref,
            role: ROLES.COMPANY_ADMIN,
            resource: resourceIdentifier.COMPANY(id),
            sub_resource: '',
            designation: (cu.designation || []).join(','),
            flags: {}
        }));
        // add new records
        // update existing ones
        // delete absent ones
        let newOnes = company_users.filter(cu => !cu.id);

        let existingOnes = company_users.filter(cu => cu.id);

        let deletedOnes = existing.filter(row => company_users.findIndex(cu => cu.id === row.id) === -1).map(row => row.id);

        /*sails.log.info('Company Users Update request says', {
            //company_users,
            newOnes,
            existingOnes,
            deletedOnes
        });*/

        let new_company_users = [];
        let deleted_company_users = [];
        // new one
        if (newOnes.length) {
            sails.log.info('create new project COMPANY-ADMIN');
            new_company_users = await storeUACRecordsOrError(newOnes);
        }

        // Deleted one
        if (deletedOnes.length) {
            sails.log.info('delete COMPANY-ADMIN, count', deletedOnes.length);
            deleted_company_users = await sails.models.userrole.destroy({
                resource: resourceIdentifier.COMPANY(id),
                id: deletedOnes,
            });
            sails.log.info('Deleted COMPANY-ADMIN');
        }
        // update each by company_id & id
        if (existingOnes.length) {
            sails.log.info('Total update COMPANY-ADMIN, count', existingOnes.length);
            for (let i = 0, len = existingOnes.length; i < len; i++) {
                sails.log.info('Updating COMPANY-ADMIN record id', existingOnes[i].id);
                let updated_record = await sails.models.userrole.updateOne({
                    id: existingOnes[i].id,
                    resource: resourceIdentifier.COMPANY(id),
                }).set({
                    // user_ref: existingOnes[i].user_ref, // ideally user & role shouldn't be updated.
                    // role: existingOnes[i].role,
                    // resource: existingOnes[i].resource,
                    // sub_resource: null,
                    designation: existingOnes[i].designation,
                    flags: existingOnes[i].flags
                });
            }
        }

        // lower case email
        // lower case designations
        // email.toString().toLowerCase()

        return {
            new_company_users,
            deleted_company_users,
        };
    },

    removeUAC: async (id, {role, resource, sub_resource = undefined}, idAttributeKey = 'id') => {
        let where = {[idAttributeKey]: id, role, resource, sub_resource};
        sails.log.info("Removing UAC", where);
        return await sails.models.userrole.destroy(where);
    },

    saveProjectUsers: async ({id} ,project_users = []) => {
        let existing = await getAllProjectUsers(id, false);

        sails.log.info('found project SITE-ADMIN records', existing.length);

        project_users = project_users.map(pu => ({
            id: pu.id,
            user_ref: (pu.user_ref && pu.user_ref.id) || pu.user_ref,
            role: ROLES.SITE_ADMIN,
            resource: resourceIdentifier.PROJECT(id),
            sub_resource: '',
            designation: (pu.designation || []).join(','),
            permission: pu.permission || [],
            flags: {
                ...(pu.flags || {}),
                ...(pu.is_default !== undefined) && {is_default: pu.is_default},
                ...(pu.is_delivery_manager !== undefined) && {is_delivery_manager: pu.is_delivery_manager},
            }
        })).filter(pu => pu.user_ref);
        // add new records
        // update existing ones
        // delete absent ones
        let newOnes = project_users.filter(pu => !pu.id);

        let existingOnes = project_users.filter(pu => pu.id);

        let deletedOnes = existing.filter(row => project_users.findIndex(pu => pu.id === row.id) === -1).map(row => row.id);

        /*sails.log.info('project SITE-ADMIN Update request says', {
            //project_users,
            newOnes,
            existingOnes,
            deletedOnes
        });*/


        let new_project_users = [];
        let deleted_records = [];
        // new one
        if(newOnes.length){
            sails.log.info('create new project SITE-ADMIN');
            new_project_users = await storeUACRecordsOrError(newOnes);
        }

        // Deleted one
        if(deletedOnes.length){
            sails.log.info('delete project SITE-ADMIN, count', deletedOnes.length);
            deleted_records = await sails.models.userrole.destroy({
                resource: resourceIdentifier.PROJECT(id),
                id: deletedOnes,
            });
            sails.log.info('Deleted project SITE-ADMIN');
        }
        // update each by project_id & id
        if(existingOnes.length){
            sails.log.info('Total update project SITE-ADMIN, count', existingOnes.length);
            for(let i = 0, len = existingOnes.length; i < len; i++) {
                sails.log.info('Updating project SITE-ADMIN record id', existingOnes[i].id);
                let updated_record = await sails.models.userrole.updateOne({
                    id: existingOnes[i].id,
                    resource: resourceIdentifier.PROJECT(id),
                }).set({
                    // user_ref: existingOnes[i].user_ref, // ideally user & role shouldn't be updated.
                    // role: existingOnes[i].role,
                    // resource: existingOnes[i].resource,
                    // sub_resource: null,
                    designation: existingOnes[i].designation,
                    permission: existingOnes[i].permission,
                    flags: existingOnes[i].flags
                });
            }
        }
        // notify newly added user_ref only..!!
        let newly_added_users = project_users.filter(pu => existing.findIndex(r => r.user_ref === pu.user_ref) === -1);

        return {
            newly_added_users,
            new_project_users,
            deleted_records,
        };
        // lower case email
        // lower case designations
        // email.toString().toLowerCase()

        // remove null site-admin records.
    },

    getAllCompanyUsers,

    getAllProjectUsers,

    deserializeUAC,

    addUserUACDetails: (userWithRoles) => {
        if(userWithRoles.user_roles){
            userWithRoles.uac = userWithRoles._uac;
            if(!userWithRoles.uac){
                let {uac, roles} = deserializeUACList(userWithRoles.user_roles);
                userWithRoles.uac = uac;
                userWithRoles.roles = roles;
            }

            userWithRoles.user_roles = undefined;
        }
        return userWithRoles;
    },

    getCompanyInfo: async (projectInfo, condition_override = null, select = [], populate_divisions=false) => {
        let project_logo_file = {};
        let companyName = '';
        let employer;
        if (projectInfo.contractor || projectInfo.parent_company || condition_override) {
            let condition = { name: projectInfo.contractor, country_code: ((projectInfo.custom_field && projectInfo.custom_field.country_code) || 'GB') };
            if (projectInfo.parent_company) {
                condition = { id: projectInfo.parent_company };
            }
            if(populate_divisions) {
                employer = await sails.models.createemployer.findOne({
                    where: (condition_override || condition),
                    select: ['logo_file_id', 'name', ...select]
                }).populate('divisions') || {};
            } else {
                employer = await sails.models.createemployer_reader.findOne({
                    where: (condition_override || condition),
                    select: ['logo_file_id', 'name', ...select]
                }) || {};
            }


            if (employer && employer.logo_file_id) {
                project_logo_file = await sails.models.userfile_reader.findOne({
                    where: {id: employer.logo_file_id}
                });
            };
            companyName = employer.name;
        }
        return { project_logo_file, companyName, employer };
    },

    toolList,

    // Adding here in token util, because this service doesn't have any other server as dependency
    buildInductionStatusMessage : (code) => {
        if(code === 1){
            return 'Pending';
        }
        else if(code === 6){
            return 'In Review';
        }
        else if(code === 2){
            return 'Approved';
        }
        else if(code === 0){
            return 'Rejected';
        }
        else if(code === 3){
            return 'Change Requested';
        }
        else if(code === 4){
            //Blocked
            return 'Project Block';
        }
        else if(code === 5){
            // BLACKLISTED
            return 'Company Block';
        }
    },

    buildRamsStatusMessage: (code) => {
        if(code === 1) {
            return 'Pending';
        } else if(code === 2) {
            return 'Accepted';
        } else if(code === 3) {
            return 'In Review';
        } else if(code === 0) {
            return 'Rejected';
        }
    },

    hasOnlySiteManagementAccess,

    replaceBrToNl : (str= '') => ((str) ? str.split("<br>").join("\n") : str),

    getIs_SA_CA_CPA : (req, res) => {
        let projectId = +(req.param('projectId')) || 0;
        let companyId = +(req.param('employerId') || req.param('companyId')) || 0;

        sails.log.info(`Checking if user: ${req.user.id} is SA of project: ${projectId} OR CA / CPA of company: ${companyId}`);

        if (!projectId) {
            return sendResponse(res, authErrorObject(sails.__('user_does_not_have_permission')));
        }

        let is_CA_OR_CPA = false, is_SA = false;
        if(companyId){
            let resourceCa = resourceIdentifier.COMPANY(companyId);
            let hasCompanyAdminRole = (req.user.raw_uac || []).find(uac => uac.role === ROLES.COMPANY_ADMIN && uac.resource === resourceCa);
            let companyProjectAdminRole = validateCPAPermissions(req.user.raw_uac, companyId);

            if (hasCompanyAdminRole) {
                req.user.companyAdminRole = hasCompanyAdminRole;
                is_CA_OR_CPA = true;
            } else if (companyProjectAdminRole && companyProjectAdminRole.includes(projectId)) {
                sails.log.info('CPA is allowed for companyId:', companyId);
                req.user.companyProjectAdminRole = companyProjectAdminRole;
                is_CA_OR_CPA = true;
            }
        }

        let resource = resourceIdentifier.PROJECT(projectId);
        let uac_row = (req.user.raw_uac || []).find(uac => uac.role === ROLES.SITE_ADMIN && uac.resource === resource);

        if (uac_row) {
            req.project_uac = uac_row;
            req.is_verified_sa = true;
            is_SA = true;
        }
        return {is_CA_OR_CPA, is_SA};
    },
    getToolByKey,

    renderChanges, 
      

    getPermitRequestStatusLabel : (code) => {
        let result = '';
        switch (code) {
            case 1:
                result = {label: 'Pending', color: '#FFAF14'}
                break;
            case 8:
                result = {label: 'Register Pending', color: '#FFAF14'}
                break;
            case 2:
                result = {label: 'Approved', color: '#1EC1DC'}
                break;
            case 3:
                result = {label: 'Active', color: '#5BCF67'}
                break;
            case 4:
                result = {label: 'Change Requested', color: '#F8D51D'}
                break;
            case 5:
                result = {label: 'Rejected', color: '#FF6442'}
                break;
            case 6:
                result = {label: 'Expired', color: '#8C8D84'}
                break;
            case 7:
                result = {label: 'Closed Out', color: '#4E3515'}
                break;
            case 9:
                result = {label: 'Closeout Requested', color: '#F8D51D'}
                break;
            case 10:
                result = {label: 'Cancelled', color: '#FF6442'}
                break;
        }
        return result;
    },
    checkIsNormalUser : (user) =>{
        return ((user.roles || []).length === 0) || ((user.roles || []).includes('USER') && (user.roles || []).length === 1);
    }
};
