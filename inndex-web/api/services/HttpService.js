/**
 * Created by spatel on 16/3/19.
 */

const axios = require('axios');
const fs = require('fs');
const https = require('https');
const s3Parser = require('./s3UrlParser');
const _parallelLimit = require('async/parallelLimit');
const _reflectAll = require('async/reflectAll');
const { Lambda, InvokeCommand } = require('@aws-sdk/client-lambda');
const {S3Client, GetObjectCommand} = require('@aws-sdk/client-s3');
const {INNDEX_SLS_FN_NAME} = sails.config.constants;
const {
    AWS_REGION_SLS,
} = sails.config.custom;
const lambda = new Lambda({
    region: AWS_REGION_SLS,
});
const s3Client = new S3Client({region: AWS_REGION_SLS});

const makePOST = async (url, data, headers, parsedError = true, timeout = 45000, allowAnySSL = false) => {
    sails.log.info('info', 'Calling [POST]', url);
    try {
        let httpsAgent = allowAnySSL ? (new https.Agent({
            rejectUnauthorized: false
        })) : undefined;
        let result = await axios.post(url, data, {headers, timeout, httpsAgent});
        return defaultSuccessHandler(result);
    } catch (e) {
        return parsedError ? parseNetworkError(e) : e;
    }
};

const makeGET = async (url, params, headers, parsedError = true, timeout = 45000, allowAnySSL = false) => {
    sails.log.info('info', 'Calling [GET]', url, params);
    try {
        let httpsAgent = allowAnySSL ? (new https.Agent({
            rejectUnauthorized: false
        })) : undefined;

        let result = await axios.get(url, {params, headers, timeout, httpsAgent});
        return defaultSuccessHandler(result);
    } catch (e) {
        return parsedError ? parseNetworkError(e) : e;
    }
};

const makePUT = async (url, data, headers, parsedError = true, timeout = 45000, allowAnySSL = false) => {
    sails.log.info('info', 'Calling [PUT]', url);
    try {
        let httpsAgent = allowAnySSL ? (new https.Agent({
            rejectUnauthorized: false
        })) : undefined;
        let result = await axios.put(url, data,{headers, timeout, httpsAgent});
        return defaultSuccessHandler(result);
    } catch (e) {
        return parsedError ? parseNetworkError(e) : e;
    }
};

const makeDELETE = async (url, {data, params}, headers, parsedError = true, timeout = 45000, allowAnySSL = false) => {
    sails.log.info('info', 'Calling [DELETE]', url, params, data);
    try {
        let httpsAgent = allowAnySSL ? (new https.Agent({
            rejectUnauthorized: false
        })) : undefined;
        let result = await axios.delete(url, {data, params, headers, timeout, httpsAgent});
        return defaultSuccessHandler(result);
    } catch (e) {
        return parsedError ? parseNetworkError(e) : e;
    }
};


/**
 * Default Handler for Each Network request
 * @param {*} response
 * @returns {} Success Object{ success: true, status: , data: };
 */
let defaultSuccessHandler = (response) => {
    return {success: true, status: response.status, data: response.data, headers: response.headers};
};

/**
 *  Default Handler for Error from Network requests
 * @param {*} error
 * @returns {} Error Object{ error: boolean, status: , data: , message?: , isCancel?:};
 */
const parseNetworkError = (error) => {
    let errorResponse = {};
    if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        errorResponse = {
            error: true,
            result: "error",
            status: error.response.status,
            data: error.response.data,
            headers: error.response.headers
        };
    } else if (axios.isCancel(error)) {
        // This one is intentionally canceled request
        errorResponse = {
            error: false,
            result: "canceled",
            status: null,
            data: null,
            message: error.message,
            isCancel: true
        };
    } else {
        // Something happened in setting up the request that triggered an Error
        // OR
        // The request was made but no response was received
        errorResponse = {error: true, unreachable: true, result: "error", status: null, data: null, message: error.message};
    }
    sails.log.error(JSON.stringify(errorResponse));

    return errorResponse;
};

/**
 * Map HTTP response into express response object.
 * @param {Object} expressResponse
 * @param {Object} result
 */
const proxyHTTPResponse = (expressResponse, result) => {
    // this will receive result from `defaultSuccessHandler` & `parseNetworkError` both
    if (result.status) {
        expressResponse.status(result.status).send(result.data);
    } else {
        expressResponse.status(500).send(result.data ? result.data : {error: true, message: sails.__('internal server error')});
    }
    expressResponse.end();
};


/**
 * get datatype of property
 *
 * @param {any} prop
 * @param {string} type
 * @returns {boolean}
 */
const typeOf = (prop, type) => {
    const dataset = {
        'object': '[object Object]',
        'array': '[object Array]',
        'string': '[object String]',
        'boolean': '[object Boolean]',
        'number': '[object Number]',
        'date': '[object Date]',
        'undefined': '[object Undefined]',
        'null': '[object Null]',
        'promise': '[object Promise]',
        'asyncFunction': '[object AsyncFunction]',
        'function': '[object Function]'
    };

    return (Object.prototype.toString.call(prop) === dataset[type]);
};

const fetchUrlAs = async (url, responseType, params, headers, parsedError = true, timeout = 30000) => {
    sails.log.info('info', 'Calling [GET] as', url);
    try {
        let result = await axios.get(url, {responseType: (responseType || 'arraybuffer'), params, headers, timeout});
        return defaultSuccessHandler(result);
    } catch (e) {
        return parsedError ? parseNetworkError(e) : e;
    }
};

const fetchS3UrlAsBuffer = async (url, {s3Key, s3BucketName} = {}) => {
    if (url && s3Parser.valid(url)) {
        // extract bucket + key
        let {bucket, key} = s3Parser.fromUrl(url);
        s3BucketName = bucket;
        s3Key = key;
    }
    if (!s3Key) {
        let cdnURL = s3Parser.fromCDNUrl(url);
        if (cdnURL) {
            s3BucketName = cdnURL.bucket;
            s3Key = cdnURL.key;
        }
    }
    if (!(s3Key && s3BucketName)) {
        // All info is not available, error out
        sails.log.info(`Invalid bucket / key info`);
        return {
            error: true,
            result: 'error',
            status: null,
            data: null,
            message: 'Invalid bucket / key info',
        };
    }
    try {
        sails.log.info(`S3 GET[${s3BucketName}] key: ${s3Key}`)
        let outcome = await s3Client.send(new GetObjectCommand({
            Bucket: s3BucketName,
            Key: s3Key
        }));

        // respond with buffer
        let responseBuffer = await steamToArray(outcome.Body);
        return {
            success: true,
            status: 200,
            data: responseBuffer,
            rawData: outcome,
            headers: {
                'Content-Type': outcome.ContentType,
                'Content-Length': outcome.ContentLength
            },
        };
    } catch (e) {
        sails.log.info("fetch file failed, error", e.message);
        return {
            error: true,
            result: 'error',
            status: null,
            data: e,
            message: e.message,
        };
    }
};

const steamToArray = async (s3GetObjectStream) => {
    // AS we are using node version < 17.5.0
    /*return new Promise((resolve, reject) => {
        const chunks = [];
        s3GetObjectStream.on('data', chunk => chunks.push(chunk))
        s3GetObjectStream.once('end', () => resolve(Buffer.concat(chunks)))
        s3GetObjectStream.once('error', reject)
    })*/

    // if we were using node version >= 17.5.0
    return Buffer.concat(await s3GetObjectStream.toArray())
};

const downloadUrlAt = async (writePath, url) => {

    let streamResponse = await fetchUrlAs(url, 'stream');
    if(!streamResponse.success){
        return false;
    };
    let writer = fs.createWriteStream(writePath);
    // pipe the result stream into a file on disc
    streamResponse.data.pipe(writer);

    // return a promise and resolve when download finishes
    return new Promise((resolve, reject) => {
        streamResponse.data.on('error', () => {
            resolve(false);
        });
        writer.on('finish', () => {
            resolve(true);
        });
        writer.on('error', () => {
            resolve(false);
        });
    });
};

const makeBaseUrl = ({host, port}) => {
    if(+port === 80){
        return host;
    }
    else if(+port === 443){
        return 'https://' + host.replace(/^https?:\/\//,'');
    }
    return `${host}:${port}`;
};

const parseJson = (body, defaultValue = {}) => {
    try {
        return JSON.parse(body);
    } catch (e) {
        return defaultValue;
    }
};

const decodeURIParam = (param, defaultValue = '') =>{
    try {
        return decodeURIComponent(param);
    } catch (e) {
        return defaultValue;
    }
}

const triggerLambdaFn = async (FunctionName, requestBody = {}, expectedResponseCode = 200, parseResponse = true) => {
    sails.log.info(`Calling Lambda ${FunctionName}`);
    const lambdaParams = {
        FunctionName,
        InvocationType: "RequestResponse",
        Payload: JSON.stringify({
            "body": JSON.stringify(requestBody)
        })
    };

    // Create and send the command
    let {StatusCode: statusCode, Payload, FunctionError} = await lambda.send(new InvokeCommand(lambdaParams));
    let resultObject = parseJson((Buffer.from(Payload) || '{}').toString());
    if (resultObject.statusCode) {
        statusCode = resultObject.statusCode;
    }

    if (!parseResponse) {
        sails.log.info(`Lambda ${FunctionName} response`, JSON.stringify({
            statusCode,
            resultObject,
            FunctionError
        }));
        return {
            success: (statusCode === expectedResponseCode),
            statusCode,
            rawResult: resultObject,
        };
    }
    let data = parseJson(resultObject.body || '{}');
    sails.log.info(`Lambda ${FunctionName} response`, JSON.stringify({
        statusCode,
        data,
        FunctionError
    }));
    return {
        success: (statusCode === expectedResponseCode),
        statusCode,
        data,
    }
};

const resizeOrCropImageToFace = async (user_ref, imageS3Url, {dimensions = {w: 360, h: 640}, faceBoundingBox = null, responseType = 'url'}) => {
    const payload = {
        "faceBoundingBox": faceBoundingBox,
        "imageS3Url": imageS3Url,
        "resizeWidth": dimensions.w,
        "resizeHeight": dimensions.h,
        "responseType": responseType,
        "resizeFitType": "contain",
        "outputImageQuality": 80,
        "paddingRatio": 0.6,
        "categoryPrefix": `user-${user_ref}`
    };
    sails.log.info(`[FR]: crop image to face request:`, payload);
    let {success, data} = await triggerLambdaFn(INNDEX_SLS_FN_NAME.CROP_IMAGE_TO_FACE, payload);
    sails.log.info(`[FR]: crop image to face response (${responseType}):`, data.file_url);
    return {success, file_url: data.file_url, file_base64: data.file_base64};
};

const executeInParallelLimit = (parallelJobs = [], limit = 2) => {
    return new Promise((resolve, reject) => {
        _parallelLimit(_reflectAll(parallelJobs), limit, function (err, results) {
            if (err) {
                // This shouldn't be logged
                sails.log.error(`One of parallel job failed, This shouldn't be logged...`, err);
                return reject(err);
            }
            // results from all parallel job,
            // will be an object with either an `error` or a `value` property.
            let outcomes = (results || []).map((r, index) => {
                if (r.error) {
                    sails.log.error(`job#${index} failed`, r.error);
                }
                return r.value || {error: true};
            });
            resolve(outcomes);
        });
    });
};

module.exports = {
    downloadUrlAt,
    fetchUrlAs,
    fetchS3UrlAsBuffer,
    makeGET,
    makePOST,
    proxyHTTPResponse,
    typeOf,
    makePUT,
    makeDELETE,
    makeBaseUrl,
    resizeOrCropImageToFace,
    triggerLambdaFn,
    executeInParallelLimit,
    decodeURIParam,
    parseJson,
};
