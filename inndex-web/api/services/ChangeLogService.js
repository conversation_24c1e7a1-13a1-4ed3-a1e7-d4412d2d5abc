const { makePOST, makeGET } = require("./HttpService");
let {
    ALS_BASE_URL
} = sails.config.custom



const createPrimaryKey = (projectId, featureName, recordId) =>  `${projectId}-${featureName}-${recordId}`;


const AUDIT_EVENT_TYPE = {
  SYSTEM: 'system',
  USER: 'user'
};

const AUDIT_ACTION_TYPE = {
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete'
};

/**
 * Creates an audit log for a specific action on a feature within a project.
 * 
 * @param {Number} userId - user's Id who initiated the change.
 * @param {Number} projectId - ID of the project where the action occurred.
 * @param {Number} recordId - ID of the record involved in the action.
 * @param {String} action - Type of action performed (e.g., "create", "update", "delete").
 * @param {String} featureName - Name of the feature being acted upon (refer TokenUtil ->  toolKeyMap, e.g. incident_report  ).
 * @param {Object} updatedRecord - The updated state of the resource after the action.
 * @param {String} originAction - Describes the original action that triggered this log (controller or service name).
 * @param {String} eventType - Categorization or type of event ( "system" or "user").
 */
const createLog = async (userId, projectId, recordId, action, featureName, updatedRecord, originAction, eventType) => {
    let updated_record = JSON.parse(JSON.stringify(updatedRecord));
    let primaryKey = createPrimaryKey(projectId, featureName, recordId);

    let logCreationPayload = {
        key: primaryKey,
        origin_action: originAction,
        resource_ref: recordId,
        event_type: eventType,
        action: action,
        user_ref: userId,
        resource_key: featureName,
        updated_record,
    };

    try {
        await makePOST(`${ALS_BASE_URL}api/logs/create`, logCreationPayload);
        sails.log.info('audit log record created successfully')
    } catch (e) {
        sails.error(e.message);
    }
};


const getAllLogsOfARecord = async(projectId, featureName, recordId, limit = 20, page = 1) =>{
    let primaryKey = await createPrimaryKey(projectId, featureName, recordId);
    let encodedKey = await encodeURIComponent(primaryKey);

    const params = {
        limit,
        page
    };

    let res = await makeGET(`${ALS_BASE_URL}api/logs/${encodedKey}/all`, params)
    if( res.data && res.data.success ) {
      return res.data;
    }
    return {data:{ logs: []}};
    
}
module.exports = {
    createLog,
    getAllLogsOfARecord,
    AUDIT_EVENT_TYPE,
    AUDIT_ACTION_TYPE
}