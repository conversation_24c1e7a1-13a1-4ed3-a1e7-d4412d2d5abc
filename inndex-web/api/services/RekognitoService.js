const {
    APP_ENV,
    AWS_REGION_SLS,
} = sails.config.custom;

const { Rekognition } = require('@aws-sdk/client-rekognition');
const s3urls = require('./s3UrlParser');
const _get = _.get;
const {errorObject,} = require("./ResponseService");

const rekognition = new Rekognition({
    region: AWS_REGION_SLS,
});

const getCollectionName = (projectId, type = 'project') => {
    let chunks = [
        APP_ENV,
        type,
        projectId
    ];
    return chunks.join('.');
};

const getExternalImageId = (userId, type = 'user') => {
    let chunks = [
        APP_ENV,
        type,
        userId
    ];
    return chunks.join('.');
};

const extractUserIdFromExternalImage = (externalImageId = '', typeCast = true) => {
    let str = externalImageId.split('.user.').pop();
    return (typeCast && str) ? +str : str;
};

const getImageAsBuffer = (base64Image) => {
    let dataStr = base64Image.replace(/^data:image\/\w+;base64,/, "");
    return new Buffer.from(dataStr, 'base64');
};

const getImageInstanceOf = (payload, type = 'buffer') => {
    let Image = {};
    if (type === 's3url') {
        if (!s3urls.valid(payload)) {
            let cdnURL = s3urls.fromCDNUrl(payload);
            if (cdnURL) {
                sails.log.info(`[FR]: generated CDN Image instance from type: ${type}`);
                return {
                    S3Object: {
                        Bucket: cdnURL.Bucket,
                            Name: cdnURL.Key,
                    }
                };
            }
            sails.log.info('Invalid s3 URL supplied', payload);
            return {
                error: true,
                count: 0,
                message: 'Invalid s3 URL supplied',
                code: 'InvalidS3ObjectException',
            };
        }
        let params = s3urls.fromUrl(payload);
        Image = {
            S3Object: {
                Bucket: params.Bucket,
                Name: params.Key,
            }
        };
    } else if (type === 'buffer') {
        Image = {
            Bytes: payload
        };
    } else if (type === 'base64') {
        let buffer = getImageAsBuffer(payload);
        Image = {
            // Blob of image bytes up to 5 MBs.
            // Note that the maximum image size you can pass to DetectCustomLabels is 4MB.
            Bytes: buffer,
        };
    } else {
        return {
            error: true,
            count: 0,
            message: 'Invalid Image supplied',
            code: 'InvalidImageFormatException',
        };
    }
    sails.log.info(`[FR]: generated Image instance from type: ${type}`);
    return Image;
};

const createFaceCollection = async (CollectionId) => {
    sails.log.info(`[FR]: checking if given face collection already exists? ${CollectionId}`);
    try {
        let details = await rekognition.describeCollection({
            CollectionId,
        }).catch(e => {
            // if not found, we don't want to trigger exception here.
            if (e.name === 'ResourceNotFoundException') {
                return {
                    message: e.message,
                    code: e.name,
                };
            }
            throw e;
        });

        if (details.CollectionARN) {
            return details;
        }
        sails.log.info('[FR]: face collection not found, creating one');
        let result = await rekognition.createCollection({
            CollectionId,
            Tags: {
                env: APP_ENV,
            }
        });

        if (result.CollectionArn) {
            // Keeping response consistent with describe call.
            result.CollectionARN = result.CollectionArn;
        }
        sails.log.info('[FR]: new face collection created', result.CollectionARN);
        return result;
    } catch (ex) {
        return {
            success: false,
            data: {e: ex},
        };
    }

};

const deleteFacesFromCollection = async (CollectionId, FaceIds) => {
    sails.log.info(`[FR]: removing faces from collection ${CollectionId}, FaceIds: ${FaceIds}`);
    try {
        let {DeletedFaces} = await rekognition.deleteFaces({
            CollectionId,
            FaceIds,
        });

        sails.log.info('[FR]: faces deleted', DeletedFaces);
        return {
            success: true,
            data: {_deleted: DeletedFaces},
        };
    } catch (e) {
        sails.log.info('[FR]: Exception while deleting faces', e);
        return {
            data: {e},
            success: false,
        };
    }
};

/**
 * search face into collection, IF collection not found, then create collection and execute searchFace thereafter.
 *
 * @param project_ref
 * @param searchRequest
 * @returns {Promise<Rekognition.SearchFacesByImageResponse> || Error}
 * @private
 */
const _searchOrCreateCollectionAndSearch = async (project_ref, searchRequest) => {
    try {
        return await rekognition.searchFacesByImage(searchRequest);
    } catch (e) {
        sails.log.info(`[FR]: search got error code: ${e.name}`);
        if (e.name === 'ResourceNotFoundException') {
            let {success} = await triggerCreateCollection(project_ref);
            if (!success) {
                throw e;
            }
        }
    }
    return await rekognition.searchFacesByImage(searchRequest);
};

const searchFacesByImage = async (projectId, payload, type = 'base64', singleFaceOnly = false) => {
    // let ExternalImageId = getExternalImageId(userId);
    let CollectionId = getCollectionName(projectId);
    let imageInstance = getImageInstanceOf(payload, type);
    sails.log.info(`[FR]: matching face in collection: ${CollectionId}`);
    try {
        let {SearchedFaceBoundingBox, FaceMatches, SearchedFaceConfidence} = await _searchOrCreateCollectionAndSearch(projectId, {
            CollectionId,
            FaceMatchThreshold: 92,
            QualityFilter: 'HIGH',
            MaxFaces: 2, // just in case same face existing for another externalId within same collection
            Image: imageInstance,
        });

        // let matchingFace = (FaceMatches || []).find(row => row.Face.ExternalImageId === ExternalImageId);
        let validPhoto = !!(SearchedFaceBoundingBox && SearchedFaceBoundingBox.Height);
        if(singleFaceOnly && (FaceMatches || []).length > 1){
            sails.log.info(`[FR]: more than one face matched in collection: ${CollectionId}`);
            return {
                _data: FaceMatches,
                multiple: true,
                userId: null,
                faceId: null,
                validPhoto,
                success: false,
            };
        }
        let [matchingFace] = (FaceMatches || []);

        sails.log.info(`searchFacesByImage: validPhoto: ${validPhoto} FaceMatches:`, JSON.stringify({FaceMatches}));
        // sails.log.info('searchFacesByImage: outcome:', JSON.stringify({validPhoto, SearchedFaceBoundingBox}, null, 2));
        sails.log.info(`[FR]: searchFacesByImage: collection: ${CollectionId} Similarity: ${(matchingFace && matchingFace.Similarity) || '-'} matchingFace:`, JSON.stringify(matchingFace));
        let success = !!(matchingFace && matchingFace.Face && matchingFace.Face.FaceId);
        let userId = null;
        let faceId = null;
        let faceIds = [];
        if (success) {
            userId = extractUserIdFromExternalImage(matchingFace.Face.ExternalImageId);
            faceId = matchingFace.Face.FaceId;
            faceIds = (FaceMatches || []).map((faceInstance) => faceInstance.Face.FaceId);
        }
        return {
            _data: matchingFace,
            faceBoundingBox: SearchedFaceBoundingBox,
            multiple: (FaceMatches || []).length > 1,
            userId: (userId || null),   // `extractUserIdFromExternalImage` might return invalid number
            faceId,
            faceIds,
            validPhoto,
            success,
        };
    } catch (e) {
        sails.log.info('[FR]: Exception while matching faces', e);
        return {
            _data: e,
            userId: null,
            faceId: null,
            validPhoto: false,
            success: false,
        };
    }
};

const indexFacesIntoCollection = async (CollectionId, ExternalImageId, Image) => {
    // Detect faces to verify there is only one face in the image
    let indexResult = await rekognition.indexFaces({
        CollectionId,
        ExternalImageId,
        Image,
        MaxFaces: 1,
    });

    sails.log.info('indexResult ', JSON.stringify(indexResult));
    return indexResult;
};

const detectFaces = async (s3Url, type = 's3url') => {
    let Image = getImageInstanceOf(s3Url, type);
    if (Image.error) {
        return Image;
    }

    let facesResults = await rekognition.detectFaces({
        Image,
        Attributes: ['SUNGLASSES', 'FACE_OCCLUDED']
    }).catch(e => {
        if (['InvalidImageFormatException', 'InvalidS3ObjectException'].includes(e.name)) {
            return {
                error: true,
                message: e.message,
                code: e.name,
            };
        }
        throw e;
    });
    sails.log.info('face detection results ', JSON.stringify(facesResults));
    if (!facesResults || !facesResults.FaceDetails || facesResults.FaceDetails.length === 0 || (facesResults.FaceDetails.length > 1)) {

        let multiple = (facesResults.FaceDetails && facesResults.FaceDetails.length) || 0;
        sails.log.info('Request image have multiple or zero face, count:', multiple);
        return {
            error: true,
            count: multiple,
            message: multiple ? sails.__('facial_recognition_multiple_face_detected') : sails.__('facial_recognition_no_face_detected'),
            code: multiple ? 'multiple_faces' : 'no_face',
            _faceDetails: facesResults,
        };
    }
    let [firstFace] = facesResults.FaceDetails;
    let {BoundingBox, Sunglasses, FaceOccluded, Quality, Confidence, EyeDirection, EyesOpen} = firstFace;
    sails.log.info(`face vitals`, JSON.stringify({
        Sunglasses,
        EyesOpen,
        Quality,
        Confidence,
        FaceOccluded,
        EyeDirection
    }, null, 4));
    if ((FaceOccluded && FaceOccluded.Value === true) || (Sunglasses && Sunglasses.Value === true)) {
        return {
            error: true,
            count: 1,
            _face: firstFace,
            code: 'face_obstructed',
            message:  sails.__('facial_recognition_face_obstructed'),
        };
    } else if ((Quality && Quality.Brightness < 30)) {
        return {
            error: true,
            count: 1,
            _face: firstFace,
            code: 'inadequate_lighting',
            message: sails.__('facial_recognition_inadequate_lighting'),
        };
    }
    return {
        count: 1,
        face: firstFace,
    };
};

const triggerCreateCollection = async (projectId) => {
    let collectionId = getCollectionName(projectId);
    sails.log.info(`[FR]: Requesting create collection call for : ${collectionId}`);
    let result = await createFaceCollection(collectionId);
    return {success: (!!result.CollectionARN), data: result, collectionId};
};

const triggerFaceIndexIntoCollection = async (projectId, userId, base64Image, imageS3Url) => {
    let collectionId = getCollectionName(projectId);
    let externalImageId = getExternalImageId(userId);
    sails.log.info(`[FR]: Requesting index creation into collection: ${collectionId}, externalImageId: ${externalImageId}, imageS3Url: ${imageS3Url}`);

    let isS3Url = !!imageS3Url;
    let imageInstance = getImageInstanceOf((isS3Url ? imageS3Url : base64Image), (isS3Url ? 's3url' : 'base64'));

    let searchResult = await searchFacesByImage(projectId, (isS3Url ? imageS3Url : base64Image), (isS3Url ? 's3url' : 'base64'));
    if (!searchResult.validPhoto) {
        // Not a valid image / exception occurred.
        sails.log.info(`[FR]: Failed while searching face, validPhoto: ${searchResult.validPhoto}`);
        return {
            success: false,
            data: {
                message: sails.__('facial_recognition_no_face_detected'),
                code: 'no_face',
                ...searchResult,
            }
        };
    }
    if (searchResult.success) {
        sails.log.info(`[FR]: Given face already exists into collection: ${collectionId} for: "${searchResult.userId}"`);
        return {
            success: false,
            data: {
                ...searchResult,
                message: sails.__('facial_recognition_induction_face_already_exists', searchResult.userId),
                code: 'already_exists',
                success: false,
                alreadyExists: true,
            }
        };
    }

    let {FaceRecords, UnindexedFaces} = await indexFacesIntoCollection(collectionId, externalImageId, imageInstance);
    let [firstIndexedFace] = FaceRecords || [];
    sails.log.info('index face response', JSON.stringify(firstIndexedFace, null, 2));
    if(UnindexedFaces && UnindexedFaces.length){
        sails.log.warn(`[FR]: Error: request image had multiple faces, UnindexedFaces: ${UnindexedFaces.length}`);
    }
    let face = (firstIndexedFace && firstIndexedFace.Face) || {};

    return {
        success: (!!face.FaceId),
        data: {
            faceId: face.FaceId,
            _face: face,
            faceBoundingBox: face.BoundingBox,
        }
    };
};

const triggerDeleteFaceFromCollection = async (projectId, faceIds) => {
    let collectionId = getCollectionName(projectId);
    // sails.log.info(`[FR]: Requesting delete face from collection: ${collectionId}, faceIds: ${faceIds}`);
    return await deleteFacesFromCollection(collectionId, faceIds);
};

const indexInductionUserFaceOrError = async (project_id, ir) => {
    let user_profile_pic_ref = _get(ir, 'additional_data.user_info.profile_pic_ref', {});
    if (!user_profile_pic_ref || !user_profile_pic_ref.file_url || (!s3urls.valid(user_profile_pic_ref.file_url) && !s3urls.fromCDNUrl(user_profile_pic_ref.file_url))) {
        sails.log.warn(`[FR]: Profile Image not found, induction: ${ir.id}`);
        return errorObject(sails.__('facial_recognition_enrolment_failed'));
    }
    let pic = user_profile_pic_ref.md_url || user_profile_pic_ref.file_url;
    sails.log.info(`[FR]: enabled, indexing user photo, url: ${pic}`);
    let {
        success,
        data
    } = await triggerFaceIndexIntoCollection(project_id, ir.user_ref, null, pic);
    if (!success || !data.faceId) {
        sails.log.info(`[FR]: indexing outcome`, JSON.stringify(data));
        return errorObject(sails.__('facial_recognition_enrolment_failed'), data);
    }
    sails.log.info(`[FR]: FR indexing successful, induction: ${ir.id} faceId: ${data.faceId}`);

    return {
        fr_face_id: data.faceId,
        faceBoundingBox: data.faceBoundingBox,
    };
};

const searchInductionUserFace = async (project_id, ir, excludeSelf = true) => {
    let user_profile_pic_ref = _get(ir, 'additional_data.user_info.profile_pic_ref', {});
    if (!user_profile_pic_ref || !user_profile_pic_ref.file_url || (!s3urls.valid(user_profile_pic_ref.file_url) && !s3urls.fromCDNUrl(user_profile_pic_ref.file_url))) {
        sails.log.warn(`[FR]: Profile Image not found, induction: ${ir.id}`);
        return errorObject(sails.__('facial_recognition_enrolment_failed'), {code: 'no_face'});
    }
    let pic = user_profile_pic_ref.md_url || user_profile_pic_ref.file_url;
    sails.log.info(`[FR]: enabled, indexing user photo, url: ${pic}`);

    let collectionId = getCollectionName(project_id);
    let externalImageId = getExternalImageId(ir.user_ref);
    sails.log.info(`[FR]: Requesting search face into collection: ${collectionId}, externalImageId: ${externalImageId}, imageS3Url: ${pic}`);

    let searchResult = await searchFacesByImage(project_id, pic,  's3url');
    if (!searchResult.validPhoto) {
        // Not a valid image / exception occurred.
        sails.log.info(`[FR]: Failed while searching face, validPhoto: ${searchResult.validPhoto}`);
        return errorObject(sails.__('facial_recognition_no_face_detected'), {code: 'no_face'});
    }
    sails.log.info(`[FR]: face search result project: ${project_id}, validPhoto?: ${searchResult.validPhoto}, multiple?: ${searchResult.multiple} success: ${searchResult.success}`);
    if (searchResult.success) {
        sails.log.info(`[FR]: Given face already exists into collection: ${collectionId} for: user "${searchResult.userId}"`);
        let matchingFaceId = searchResult.faceId;
        if (excludeSelf && ir.fr_face_id) {
            matchingFaceId = (searchResult.faceIds || []).filter(fId => fId !== ir.fr_face_id).pop();
        }
        if(matchingFaceId){
            return {
                success: true,
                data: {
                    ...searchResult,
                    matchingFaceId,
                    code: 'already_exists',
                    // message: sails.__('facial_recognition_induction_face_already_exists', searchResult.userId),
                    // success: false,
                    alreadyExists: true,
                }
            };
        }
    }
    return errorObject(sails.__('facial_recognition_search_failed'), {code: 'face_not_found'});
}

module.exports = {
    getCollectionName,
    searchFacesByImage,
    triggerCreateCollection,
    detectFaces,
    triggerFaceIndexIntoCollection,
    indexInductionUserFaceOrError,
    searchInductionUserFace,
    triggerDeleteFaceFromCollection,
};
