const dayjs = require('dayjs');
const momentTz = require("moment-timezone");
const _pick = require('lodash/pick');
const _groupBy = require('lodash/groupBy');
const _uniq = require('lodash/uniq');
const FormData = require('form-data');


const {
    baseUrl,
    PRO_CORE_LIVE_MODE,
    PRO_CORE_APP_CLIENT_ID: APP_CLIENT_ID,
    PRO_CORE_APP_CLIENT_SECRET: APP_CLIENT_SECRET
} = sails.config.custom;

const {
    fall_back_timezone,
    COMPANY_SETTING_KEY,
    dbDateFormat_YYYY_MM_DD,
    PROJECT_SETTING_KEY: {PROCORE_INTEGRATION_PROJECT_INFO},
    AUTH_SERVER_URL, API_SERVER_URL, CALL_BACK_URL, PROCORE_APIs,
    INNDEX_SETTING_KEY: {PROCORE_MP_CRON_OVERRIDE},
} = sails.config.constants;

const AccessLogService = require('./AccessLogService');

const {
    inductionFn: {
        getInductionEmployerByUserIds,
    },
} = require('../sql.fn');

const {
    getDailyTimeEventForDay,
    getVisitorsTimeLogForDates,
    populateUserRefs,
    populateProjectRefs,
    populateEmployerRefs,
} = require('./DataProcessingService');
const {
    errorObject,
} = require('./ResponseService');

const {
    makeGET,
    makePOST,
    typeOf,
    executeInParallelLimit,
    fetchUrlAs,
} = require('./HttpService');

const disabled_projects = [1185, 1291];

const refreshProcoreOAuthToken = async (setting) => {
    let refreshTokenResponse = await makePOST(`${AUTH_SERVER_URL}/${PROCORE_APIs.OAUTH_TOKEN_URL}`, {
        "grant_type": "refresh_token",
        "client_id": APP_CLIENT_ID,
        "client_secret": APP_CLIENT_SECRET,
        "refresh_token": setting.value.refresh_token,
        "redirect_uri": baseUrl + CALL_BACK_URL
    });
    sails.log.info('Procore response from refresh-token call', refreshTokenResponse.status);
    return refreshTokenResponse.data;
};

const constructSettingObject = (tokenResponse, {me, user_ref, procore_meta}) => {
    return {
        access_token: tokenResponse.access_token,
        token_type: tokenResponse.token_type,
        refresh_token: tokenResponse.refresh_token,
        created_at: tokenResponse.created_at,
        expire_at: tokenResponse.expires_in ? (tokenResponse.created_at + tokenResponse.expires_in) : tokenResponse.expire_at,
        me,
        user_ref,
        procore_meta/*: { // procore Target company & project id.
            company: null,
            project: null,
        }*/
    };
};

const getProjectSetting = async (projectId, settingName, defaultValue = {}) => {
    let existingSetting = await sails.models.projectsetting_reader.findOne({
        project_ref: projectId,
        name: settingName
    });
    return existingSetting || defaultValue;
}

const projectHasProCoreSetting = async (project_id) => {
    let existingSetting = await getProjectSetting(project_id, PROCORE_INTEGRATION_PROJECT_INFO);
    if (!existingSetting.id) {
        sails.log.info('Invalid project, no procore setting found, id:', project_id);
        return errorObject('Invalid project id');
    }

    // Refresh Token if expiring
    let nowUnix = dayjs().add(15, 'minute').unix(); // 15 minute as buffer
    if (!existingSetting.value.expire_at || nowUnix >= existingSetting.value.expire_at) {
        sails.log.info('procore existing token expired, refreshing', project_id);
        let refreshed = await refreshProcoreOAuthToken(existingSetting);
        sails.log.info(`refreshed project ${project_id}`, refreshed);
        if (!refreshed.access_token) {
            return errorObject('Failed while refreshing procore token', {project_id});
        }
        existingSetting = await sails.models.projectsetting.updateOne({id: existingSetting.id}).set({
            value: constructSettingObject(refreshed, existingSetting.value)
        });
    }
    // sails.log.info('Procore Token: Bearer', existingSetting.value.access_token);
    return existingSetting;
};

const getOrCreateProcoreUser = async (innDexUser, vendorId, proCoreProjectId, access_token, procore_company_id, permission_template_id = null, isEmployee = true, visitor = false) => {
    let {status: get_users_status, data: users, success} = await makeGET(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_USERS(proCoreProjectId)}`, {
        'filters[search]': innDexUser.email,
        // 'filters[employee]': `innDexId:${innDexUser.id}`,
    }, {
        Authorization: `Bearer ${access_token}`,
        'Procore-Company-Id': procore_company_id
    }, true, 15000);
    sails.log.info(`Procore ${proCoreProjectId} response from get user "${innDexUser.id}" n/w call`, get_users_status, typeOf(users, 'array'));
    // sails.log.info('Response of all users', JSON.stringify(users, null, 4));
    // sails.log.info('got users', users);
    let targetUser = ((success ? users : []) || []).find(u => u.email_address && u.email_address.toString().toLowerCase() === innDexUser.email.toLowerCase()) || {};
    if (!targetUser.id) {
        sails.log.info(`creating ${proCoreProjectId} new procore user user ${innDexUser.id} for:`, innDexUser.email, `isEmployee: ${isEmployee}`);
        let userObj = {
            "employee_id": `innDexId:${innDexUser.id}`,
            "email_address": innDexUser.email,
            "first_name": innDexUser.first_name,
            "last_name": innDexUser.last_name,
            "vendor_id": vendorId,
            "is_employee": isEmployee
        };
        if(visitor){
            userObj.employee_id = `innDexVisitorId:${innDexUser.id}`;
        }
        if(permission_template_id && typeOf(permission_template_id, 'number')) {
            userObj["permission_template_id"] = permission_template_id;
        }

        let {status: create_user_status, data: newUser, headers} = await makePOST(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_USERS(proCoreProjectId)}`, {
            user: userObj
        }, {
            Authorization: `Bearer ${access_token}`,
            'Procore-Company-Id': procore_company_id
        });
        sails.log.info(`Procore ${proCoreProjectId}  response from create user ${innDexUser.id} n/w call`, create_user_status);
        // sails.log.info('procore response of create user', JSON.stringify({newUser, headers}, null, 4));
        targetUser = newUser;
    }
    sails.log.info('Target procore user id:', targetUser.id);
    return targetUser;
};

const getOrCreateTimesheet = async (proCoreProjectId, date_YYYY_MM_DD, access_token, procore_company_id) => {
    // filters[date]=2021-06-10...2021-06-10
    let {status: get_timesheets_status, data: timesheets, success} = await makeGET(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_TIMESHEETS(proCoreProjectId)}`, {
        'filters[date]': `${date_YYYY_MM_DD}...${date_YYYY_MM_DD}`
    }, {
        Authorization: `Bearer ${access_token}`,
        'Procore-Company-Id': procore_company_id
    });
    sails.log.info(`Procore ${proCoreProjectId} response from GET timesheet ${date_YYYY_MM_DD} n/w call`, get_timesheets_status);
    let targetTimesheet = ((success ? timesheets : []) || []).find(u => u.date && u.date === date_YYYY_MM_DD) || {};
    if (!targetTimesheet.id) {
        sails.log.info('creating new procore timesheet for:', date_YYYY_MM_DD);
        let {status: create_ts_status, data: newTimesheet, headers} = await makePOST(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_TIMESHEETS(proCoreProjectId)}`, {
            "timesheet": {
                date: date_YYYY_MM_DD
            }
        }, {
            Authorization: `Bearer ${access_token}`,
            'Procore-Company-Id': procore_company_id
        });
        sails.log.info(`Procore ${proCoreProjectId} response from create timesheet ${date_YYYY_MM_DD} n/w call`, create_ts_status);
        // sails.log.info('procore response of create timesheet', JSON.stringify({newTimesheet, headers}, null, 4));
        targetTimesheet = newTimesheet;
    }
    sails.log.info('Target procore timesheet id:', targetTimesheet.id);
    return targetTimesheet;

};

const getOrCreateVendor = async (proCoreProjectId, employerName, access_token, procore_company_id) => {
    let {status: get_vendors_status, data: vendors, success} = await makeGET(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_VENDORS(proCoreProjectId)}`, {
        'filters[search]': employerName
    }, {
        Authorization: `Bearer ${access_token}`,
        'Procore-Company-Id': procore_company_id
    }, true, 15000);
    sails.log.info(`Procore ${proCoreProjectId} response from GET vendor "${employerName}" n/w call`, get_vendors_status);
    // sails.log.info('Procore response from GET n/w vendors call',  JSON.stringify(vendors, null, 4));
    let targetVendor = ((success ? vendors : []) || []).find(v => v.name && v.name.toLowerCase().trim() === employerName.toLowerCase().trim()) || {};
    if (!targetVendor.id) {
        sails.log.info(`creating ${proCoreProjectId} new procore vendor for:`, employerName);
        let {status: create_vendor_status, data: newVendor, headers} = await makePOST(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_VENDORS(proCoreProjectId)}`, {
            "vendor": {
                name: employerName,
                is_active: true,
            }
        }, {
            Authorization: `Bearer ${access_token}`,
            'Procore-Company-Id': procore_company_id
        });
        sails.log.info(`Procore ${proCoreProjectId} response from create vendor "${employerName}" n/w call`, create_vendor_status);
        // sails.log.info('procore response of create vendor', JSON.stringify({newVendor, headers}, null, 4));
        targetVendor = newVendor;
        targetVendor.is_new = true;
    }
    sails.log.info('Target procore vendor id:', targetVendor.id);
    return targetVendor;
};

const getVendorIdsOf = async (user_employers, proCoreProjectId, access_token, procore_company_id, inndex_project_id) => {
    let parallelJobs = [];
    for (let i = 0, len = user_employers.length; i < len; i++) {
        parallelJobs.push((function (vendorName, index) {
            return (callback) => {
                //@note: This lib doesn't support async function here.
                getOrCreateVendor(proCoreProjectId, vendorName, access_token, procore_company_id).then(targetVendor => {
                    callback(null, {
                        id: targetVendor.id,
                        name: vendorName,
                        is_new: targetVendor.is_new || false,
                    });
                }).catch(callback);
            };
        })(user_employers[i], i));
    }
    sails.log.info('get vendorIDs of all started, total procore calls:', parallelJobs.length);
    let outcome = await executeInParallelLimit(parallelJobs, 3);
    sails.log.info(`get procore vendors parallel jobs completed`, outcome);
    let newly_created_vendors = (outcome || []).filter(v => v.is_new);
    if(newly_created_vendors.length){
        AccessLogService.interactionLog(AccessLogService.EXTERNAL_SERVICE_ID.PROCORE, inndex_project_id, true, 200, {
            method: null,
            url: null,
            payload: newly_created_vendors,
            headers: {},
            response_body: {},
            response_headers: {}
        }, null, 'getVendorIdsOf', AccessLogService.INTERACTION_TYPE.REST).catch(sails.log.error);
    }
    return outcome;
};

const createUsersTimeCardEntries = async (project_id, target_date, user_ids = [], companyConf = {}, tz = fall_back_timezone) => {
    let existingSetting = await projectHasProCoreSetting(project_id);
    if (!existingSetting.id) {
        return existingSetting;
    }

    let procore_meta = (existingSetting.value && existingSetting.value.procore_meta) || {};
    let token = (existingSetting.value && existingSetting.value.access_token) || '';
    if(!procore_meta.project || !procore_meta.company){
        return errorObject('Please relink your procore project', {procore_meta});
    }
    sails.log.info(`project: ${project_id} Target date: ${target_date.format(dbDateFormat_YYYY_MM_DD)}, procore_project_id: ${procore_meta.project}`);

    let {mark_user_is_employee, mark_visitor_is_employee} = companyConf;
    let time_logs_for_day = await getDailyTimeEventForDay(project_id, target_date, user_ids);
    let logs_with_full_shift = time_logs_for_day.filter(log => (log.day_of_yr && log.clock_in && log.clock_out));

    let target_users = _uniq(logs_with_full_shift.map(log => +log.user_id));

    let target_day = target_date.clone().format(dbDateFormat_YYYY_MM_DD);
    let to_date = target_date.clone().add(1, 'day').format(dbDateFormat_YYYY_MM_DD);
    let all_visitor_time_logs = await getVisitorsTimeLogForDates(project_id, target_day, to_date);
    let all_visitor_logs = all_visitor_time_logs.filter(log => (log.day_of_yr && log.clock_in && log.clock_out));
    let visitor_groups = _groupBy((all_visitor_logs || []), l => l.visitor_id);
    let visitors_of_day = Object.keys(visitor_groups).map(visitor_id => (visitor_groups[visitor_id] || []).shift());
    let target_visitors = _uniq(visitors_of_day.map(log => +log.visitor_id));

    sails.log.info('Total users,visitors to export on procore:', target_users.length, target_visitors.length);
    if(!target_users.length && !target_visitors.length){
        return [];
    }

    let inductions_data = await sails.models.inductionrequest_reader.find({
        select: ['additional_data', 'status_code', 'user_ref'],
        where: {status_code: [2, 6], user_ref: target_users, project_ref: project_id},
    });

    // process one induction per user only
    let inductions_group = _groupBy(inductions_data, (i) => i.user_ref);
    let inductions = Object.keys(inductions_group).map(user_ref => (inductions_group[user_ref] || []).shift());

    let visitors = [];
    if(target_visitors.length){
        visitors = await sails.models.visitor_reader.find({
            select: ['id', 'employer_ref', 'email', 'first_name', 'last_name'],
            where: {id: target_visitors},
        });
        visitors = await populateEmployerRefs(visitors, 'employer_ref', ['id', 'name', 'country_code']);
    }

    // Vendor companies
    // let visitor_employers = visitors.map(v => v.employer_ref && v.employer_ref.name);
    let user_employers = (inductions.map(i => i.additional_data && i.additional_data.employment_detail && i.additional_data.employment_detail.employer));
    let merged_list = _uniq((user_employers || []));//.concat(visitor_employers));
    let vendors = await getVendorIdsOf(merged_list, procore_meta.project, token, procore_meta.company, project_id);

    let timesheet = await getOrCreateTimesheet(procore_meta.project, target_date.format(dbDateFormat_YYYY_MM_DD), token, procore_meta.company);
    let createdEntries = [];
    for (let i = 0; i < inductions.length; i++) {
        let {additional_data: {user_info, employment_detail, project}} = inductions[i];
        let innDexUser = user_info || {};
        let employer = (employment_detail || {}).employer;
        let vendorId = (vendors.find(v => v.name === employer) || {}).id;
        let user_time_log = logs_with_full_shift.find(log => log.user_id === innDexUser.id) || {};
        let targetUser = await getOrCreateProcoreUser(innDexUser, vendorId, procore_meta.project, token, procore_meta.company, null, mark_user_is_employee);

        if(!targetUser || !targetUser.id || !targetUser.is_employee){
            sails.log.info(`user not created on procore OR is_employee: ${(targetUser && targetUser.is_employee)}, skipping timecard entry`);
            continue;
        }
        sails.log.info('creating procore timecard entry for user:', targetUser.id);
        let timecard_entry = {
            "date": user_time_log.day_of_yr,
            "description": `record for user id (${targetUser.id})`,
            "hours": (user_time_log.effective_time ? (+(user_time_log.effective_time))/3600 : 0).toFixed(1),
            "origin_data": JSON.stringify(user_time_log),
            "login_information_id": targetUser.id,
            // "billable": true,
            // "time_in": dayjs.unix(+user_time_log.clock_in).toISOString(),
            // "time_out": dayjs.unix(+user_time_log.clock_out).toISOString(),
            "timesheet_id": timesheet.id,
            "cost_code_id": undefined,
            "line_item_type_id": undefined
        };
        let {
            status: create_user_status,
            data: entry
        } = await makePOST(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_TIMECARD_ENTRIES(procore_meta.project)}`, {
            "timecard_entry": timecard_entry
        }, {
            Authorization: `Bearer ${token}`,
            'Procore-Company-Id': procore_meta.company
        });
        sails.log.info('Procore response from create n/w call', create_user_status);
        let failed_payload = null;
        if(create_user_status !== 201){
            sails.log.info('procore payload of failed call', JSON.stringify({timecard_entry}, null, 4));
            sails.log.info('procore response of failed call', JSON.stringify({entry}, null, 4));
            failed_payload = Object.assign({}, timecard_entry);
            AccessLogService.sysEventLog(
                AccessLogService.SYSTEM_ERROR_NAME.PROCORE_TIMESHEET_CRON_ERROR,
                AccessLogService.EXTERNAL_SERVICE_ID.PROCORE,
                project_id,
                'createUsersTimeCardEntries',
                {
                    message: `Failed while exporting timesheet entry ${user_time_log.day_of_yr}, procore.project:${procore_meta.project}, user_ref: ${user_time_log.user_id}`,
                    input: timecard_entry,
                    outcome: {response: entry, status: create_user_status},
                }
            ).catch(sails.log.error);
        }
        createdEntries.push({
            target: targetUser.id,
            status: create_user_status,
            inndexId: innDexUser.id,
            id: entry.id,
            date: entry.date,
            failed: failed_payload,
        });
    }
    sails.log.info('Procore creating visitors entries', visitors.length);
    for (let i = 0; i < visitors.length; i++) {
        let innDexVisitor = visitors[i];
        let employer = (innDexVisitor.employer_ref || {}).name;
        let visitor_time_log = visitors_of_day.find(log => log.visitor_id === innDexVisitor.id) || {};

        let begin = momentTz.unix(+visitor_time_log.clock_in).tz(tz);
        let end = momentTz.unix(+visitor_time_log.clock_out).tz(tz);
        sails.log.info('creating procore visitor-log for visitor:', innDexVisitor.id, 'day:', visitor_time_log.day_of_yr);
        let {
            status: create_user_status,
            data: visitor_log_entry
        } = await makePOST(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_VISITOR_LOG(procore_meta.project)}`, {
            "visitor_log": {
                "begin_hour": begin.hour(),
                "begin_minute": begin.minute(),
                "date": visitor_time_log.day_of_yr,
                "datetime": dayjs.unix(+visitor_time_log.clock_in).toISOString(),
                "details": `${innDexVisitor.first_name} ${innDexVisitor.last_name}${employer ? ` (${employer})`: ''} innDexVisitorId:${innDexVisitor.id}`,
                "end_hour": end.hour(),
                "end_minute": end.minute()
            }
        }, {
            Authorization: `Bearer ${token}`,
            'Procore-Company-Id': procore_meta.company
        });
        sails.log.info('Procore response from create visitor-log n/w call', create_user_status);
        // sails.log.info('procore response of create timecard', JSON.stringify({entry}, null, 4));
        createdEntries.push({
            project_id,
            status: create_user_status,
            innDexVisitorId: innDexVisitor.id,
            id: visitor_log_entry.id,
            date: visitor_log_entry.date,
        });
    }
    return createdEntries;
};

const exportUsersOnInductionApproval = async (project_id, induction_list, permission_template_id = null, companyConf = {}) => {
    let called_at = (new Date()).getTime();
    if(disabled_projects.includes(project_id)){
        sails.log.info('Skipping Procore user export for', project_id);
        return [];
    }
    let existingSetting = await projectHasProCoreSetting(project_id);
    if (!existingSetting.id) {
        return existingSetting;
    }

    let procore_meta = (existingSetting.value && existingSetting.value.procore_meta) || {};
    let token = (existingSetting.value && existingSetting.value.access_token) || '';
    if(!procore_meta.project || !procore_meta.company){
        return errorObject('Please relink your procore project', {procore_meta});
    }

    // process one induction per user only
    let inductions_group = _groupBy(induction_list, (i) => i.user_ref);
    let inductions = Object.keys(inductions_group).map(user_ref => (inductions_group[user_ref] || []).shift());

    // Vendor companies
    let user_employers = _uniq(inductions.map(i => i.additional_data && i.additional_data.employment_detail && i.additional_data.employment_detail.employer));
    let vendors = await getVendorIdsOf(user_employers, procore_meta.project, token, procore_meta.company, project_id);

    let {mark_user_is_employee} = companyConf;
    let exported_users = [];
    sails.log.info('Total users to export on procore:', inductions.length);
    for (let i = 0; i < inductions.length; i++) {
        let {additional_data: {user_info, employment_detail, project}} = inductions[i];
        let innDexUser = user_info || {};
        let employer = (employment_detail || {}).employer;
        let vendorId = (vendors.find(v => v.name === employer) || {}).id;
        let targetUser = await getOrCreateProcoreUser(innDexUser, vendorId, procore_meta.project, token, procore_meta.company, permission_template_id, mark_user_is_employee);
        exported_users.push(targetUser);
    }
    let finished_at = (new Date()).getTime();
    AccessLogService.interactionLog(AccessLogService.EXTERNAL_SERVICE_ID.PROCORE, project_id, true, 200, {
        method: null,
        url: null,
        payload: {
            project_id,
            induction_list: induction_list.map(ir => {
                return {
                    id: ir.id,
                    record_id: ir.record_id,
                    user_ref: ir.user_ref,
                }
            }),
            permission_template_id
        },
        headers: ({}),
        response_body: exported_users,
        response_headers: ({})
    }, (finished_at - called_at), 'exportUsersOnInductionApproval', AccessLogService.INTERACTION_TYPE.REST).catch(sails.log.error);
    return exported_users;
};

const exportDailyTimeLogs = async () => {
    let called_at = (new Date()).getTime();
    let procore_projects = await sails.models.projectsetting_reader.find({
        where: {name: PROCORE_INTEGRATION_PROJECT_INFO, value: {'!=': null}},
    });
    procore_projects = await populateProjectRefs(procore_projects, 'project_ref', ['id', 'name', 'parent_company', 'custom_field']);

    let settings = (procore_projects || []).filter(setting => (
        setting.project_ref && setting.project_ref.id &&
        (setting.value && setting.value.procore_meta && setting.value.procore_meta.project) &&
        !(setting.project_ref.custom_field && setting.project_ref.custom_field.disable && setting.project_ref.custom_field.disable.time_log_export_on_procore)
    ));

    let parent_companies = [...new Set((settings || []).map(s => s.project_ref.parent_company))];
    sails.log.info(`Total Procore time log enabled projects, count: ${settings.length}, parent companies; ${parent_companies}`);
    let company_settings = await sails.models.companysetting_reader.find({
        select: ['id', 'company_ref', 'value'],
        where: {
            name: COMPANY_SETTING_KEY.PROCORE_CONFIG,
            company_ref: parent_companies
        }
    });
    let company_procore_config = company_settings.reduce((s, c) => {
        s[c.company_ref] = c.value;
        return s;
    }, {});
    let parallelJobs = [];
    let previous_day = dayjs().subtract(1, 'day'); // last one day only
    // create parallel Job list
    for (let i = 0, len = settings.length; i < len; i++) {
        let company_conf = company_procore_config[(settings[i].project_ref && settings[i].project_ref.parent_company)] || {};
        if(!company_conf.user_export_disabled_projects){
            company_conf.user_export_disabled_projects = [];
        }
        if(settings[i].project_ref && (disabled_projects.includes(settings[i].project_ref.id) || company_conf.user_export_disabled_projects.includes(settings[i].project_ref.id))){
            sails.log.info('Skipping Procore time log export for', settings[i].project_ref.id);
            continue;
        }

        parallelJobs.push((function (projectId, batchNo, companyConf, tz) {
            return (callback) => {
                //@note: This lib doesn't support async function here.
                sails.log.info(`Procore export job#${batchNo} --> ${projectId}, "${tz}" companyConf:`, companyConf);
                createUsersTimeCardEntries(projectId, previous_day, [], companyConf, tz).then(createdEntriesOrFalse => {
                    // we have createdEntries here
                    callback(null, {
                        projectId,
                        createdEntriesOrFalse,
                    });
                }).catch(callback);
            };
        })((settings[i].project_ref.id), i, company_conf, (settings[i].project_ref.custom_field || {}).timezone));
    }

    let outcome = await executeInParallelLimit(parallelJobs);
    sails.log.info('All procore parallel jobs completed', outcome);
    let finished_at = (new Date()).getTime();
    AccessLogService.cronResultInteractionLog(AccessLogService.EXTERNAL_SERVICE_ID.PROCORE, 'exportDailyTimeLogs', {
        previous_day: previous_day.format(),
        settings: settings.map(s => ({project: s.project_ref && s.project_ref.id}))
    }, outcome, (finished_at - called_at)).catch(sails.log.error);
    return outcome;
};

const exportActiveUsersPerCompany = async (project_id, target_date) => {
    let hour_per_person = ((+project_id === 1185) ? 10 : 8);
    let existingSetting = await projectHasProCoreSetting(project_id);
    if (!existingSetting.id) {
        return existingSetting;
    }

    let procore_meta = (existingSetting.value && existingSetting.value.procore_meta) || {};
    let token = (existingSetting.value && existingSetting.value.access_token) || '';
    if (!procore_meta.project || !procore_meta.company) {
        return errorObject('Please relink your procore project', {procore_meta});
    }

    let time_logs = await sails.models.userdailylog_reader.find({
        where: {
            project_ref: project_id,
            day_of_yr: target_date.format(dbDateFormat_YYYY_MM_DD),
            user_ref: {'!=': null}
        },
        select: ['user_ref', 'company_ref']
    });// .populate('company_ref');

    if (!time_logs.length) {
        return errorObject('No records found for procore export', {project_id, target_date: target_date.format(dbDateFormat_YYYY_MM_DD)});
    }

    let userIds = _uniq(time_logs.map(l => l.user_ref));
    sails.log.info(`Total active users on project(${project_id}): ${userIds.length} for procore manpower logs export`);
    let induction_info = await getInductionEmployerByUserIds(userIds, project_id, [6, 2, 4, 5]);

    let unique_company_ref = induction_info.reduce((o, ir) => {
        if (!ir.user_employer) {
            return o;
        }
        if (!o[ir.user_employer]) {
            o[ir.user_employer] = [];
        }
        o[ir.user_employer].push(ir.user_ref);
        return o;
    }, {});

    let companies = Object.keys(unique_company_ref);
    sails.log.info(`Total unique companies: ${companies.length} for procore manpower logs export`);

    if (!(companies || []).length) {
        return errorObject('companies not found for procore export', {project_id, target_date: target_date.format(dbDateFormat_YYYY_MM_DD)});
    }

    let vendors = await getVendorIdsOf(companies, procore_meta.project, token, procore_meta.company, project_id);
    sails.log.info(`created list of vendors, count: ${vendors.length} from procore`);
    let manpower_logs = companies.map(company => {
        let workers_count = unique_company_ref[company] || [];
        let vendorId = (vendors.find(v => v.name === company) || {}).id;
        return {
            "date": target_date.format(dbDateFormat_YYYY_MM_DD),
            "notes": "",
            "num_workers": workers_count.length,
            "num_hours": (hour_per_person), // Number of hours for each worker
            "contact_id": vendorId
        }
    });
    sails.log.info(`Total manpower logs to create: ${manpower_logs.length} on procore`);
    let createdEntries = [];
    for (let i = 0, len = manpower_logs.length; i < len; i++) {
        // sails.log.info('procore request of create manpower_log', JSON.stringify(manpower_logs[i], null, 4));
        let {
            status: create_manpower_log_status,
            data: entry
        } = await makePOST(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_MANPOWER_LOGS(procore_meta.project)}`, {
            "manpower_log": manpower_logs[i]
        }, {
            Authorization: `Bearer ${token}`,
            'Procore-Company-Id': procore_meta.company
        });
        sails.log.info('Procore response from create n/w call', create_manpower_log_status);
        // sails.log.info('procore response of create manpower_log', JSON.stringify({entry}, null, 4));
        createdEntries.push({
            status: create_manpower_log_status,
            id: entry.id,
            date: entry.date,
        });
    }
    return {done: createdEntries, project_id};
};

const exportManpowerLogsPerCompany = async (day = dayjs(), project_ids = null) => {
    let called_at = (new Date()).getTime();
    let procore_cron_overwrite_setting = await sails.models.inndexsetting_reader.findOne({name: PROCORE_MP_CRON_OVERRIDE});
    let overwrite_preference_key = `cron_${day.hour()}_${day.minute()}_projects`;
    if(!procore_cron_overwrite_setting){
        procore_cron_overwrite_setting = {
            value: {}
                // add same IDs in both of arrays below
                /*{
                "overridden_projects": [1394,1396,1397,1398,1399,1400,1401,1402,1403],
                "cron_14_0_projects": [1394,1396,1397,1398,1399,1400,1401,1402,1403]
            }*/
        };
    }

    let have_override_for_this_cron = (
        procore_cron_overwrite_setting &&
        procore_cron_overwrite_setting.value &&
        (typeOf(procore_cron_overwrite_setting.value[overwrite_preference_key], 'array')) &&
        (procore_cron_overwrite_setting.value[overwrite_preference_key]).length
    );
    if(!project_ids && have_override_for_this_cron){
        project_ids = procore_cron_overwrite_setting.value[overwrite_preference_key]
        sails.log.info(`Procore OVERRIDE preference key "${overwrite_preference_key}" found, project_ids: ${project_ids}`);
    }else if(!project_ids){
        let overridden_projects = (procore_cron_overwrite_setting && procore_cron_overwrite_setting.value && procore_cron_overwrite_setting.value.overridden_projects) || [];
        sails.log.info(`Using DEFAULT Procore cron preference, with exclusion of project ids: ${overridden_projects}`);
        if(overridden_projects && overridden_projects.length){
            project_ids = {'!=': overridden_projects};
        }
    }

    let whereClause = {
        name: PROCORE_INTEGRATION_PROJECT_INFO, value: {'!=': null},
        ...(project_ids ? {project_ref: project_ids} : {}),
    };
    sails.log.info('fetching Procore integration setting with where clause', whereClause);
    let procore_projects = await sails.models.projectsetting_reader.find({where: whereClause,});

    let settings = (procore_projects || []).filter(setting => setting.value && setting.value.procore_meta && setting.value.procore_meta.project);

    sails.log.info('Total Procore enabled projects for export', settings.length);
    let parallelJobs = [];
    for (let i = 0, len = settings.length; i < len; i++) {
        parallelJobs.push((function (projectId, batchNo) {
            return (callback) => {
                //@note: This lib doesn't support async function here.
                sails.log.info(`Procore manpower logs export job#${batchNo} --> ${projectId}`);
                exportActiveUsersPerCompany(projectId, day, []).then(createdEntriesOrError => {
                    // we have createdEntries here
                    callback(null, createdEntriesOrError);
                }).catch(callback);
            };
        })(settings[i].project_ref, i));
    }

    let outcome = await executeInParallelLimit(parallelJobs);
    sails.log.info('All procore parallel log export jobs completed', outcome);
    let finished_at = (new Date()).getTime();
    AccessLogService.cronResultInteractionLog(AccessLogService.EXTERNAL_SERVICE_ID.PROCORE, 'exportManpowerLogsPerCompany', {
        day: day.format(),
        project_ids
    }, outcome, (finished_at - called_at)).catch(sails.log.error);
    return outcome;
};

/*
 * Create Procore Observation Item when a Close Call is closed out.
 */
const exportCloseCallOnCloseOut = async (project_id, cc_data) => {
    if(+cc_data.tagged_owner) {
        [cc_data] = await populateEmployerRefs([cc_data], 'tagged_owner', ['name']);
    }
    if(+cc_data.assigned_to) {
        [cc_data] = await populateUserRefs([cc_data], 'assigned_to', ['first_name', 'last_name']);
    }

    let assignee = `<strong>Assigned To:</strong> ${(cc_data.assigned_to && cc_data.assigned_to.id) ? cc_data.assigned_to.name : '-'}<br><br>`;
    let company = `<strong>Tagged Owner:</strong> ${(cc_data.tagged_owner && cc_data.tagged_owner.id) ? cc_data.tagged_owner.name : '-'}<br><br>`;
    let additionalDetail = `<strong>Details:</strong> ${cc_data.additional_detail}<br><br>`;
    let incident = `<strong>What could have happened:</strong> ${cc_data.cc_detail}<br><br>`;
    let closedBy = `<strong>Closed Out By:</strong> ${cc_data.closed_out_by}<br><br>`;
    let raisedBy = `<strong>Raised By:</strong> ${cc_data.procore_raisedby}<br><br>`;
    let closeOutDesc = `<strong>Description:</strong> ${cc_data.corrective_detail}<br><br>`;

    cc_data.procore_description = `${raisedBy}${assignee}${company}${additionalDetail}${incident}${closedBy}${closeOutDesc}`

    await createObservationItem(project_id, cc_data);
};

const getProjectObservationTypes = async (procore_project_id, token, procore_company_id) => {
    let {status: get_types_status, data: observation_types, success} = await makeGET(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_OBSERVATION_TYPES(procore_project_id)}`, {}, {
        Authorization: `Bearer ${token}`,
        'Procore-Company-Id': procore_company_id
    });
    sails.log.info('Procore response from GET n/w call', get_types_status);
    return (success ? observation_types : []);
};

const createPositiveObservationType = async (procore_project_id, token, procore_company_id) => {
    let {
        status: create_observation_type_status,
        data: observation_types, success
    } = await makePOST(`${API_SERVER_URL}/${PROCORE_APIs.PROJECT_OBSERVATION_TYPES(procore_project_id)}`, {
        observation_type: {
            name: "Positive Observation",
            category: "safety",
            active: 1
        }
      }, {
        Authorization: `Bearer ${token}`,
        'Procore-Company-Id': procore_company_id
    });
    sails.log.info('Procore response from create n/w call', create_observation_type_status);

    return (success ? observation_types : []);
};

const getCompanyHazard = async (token, procore_company_id) => {
    let {status: get_hazards_status, data: hazards, success} = await makeGET(`${API_SERVER_URL}/${PROCORE_APIs.COMPANY_HAZARDS(procore_company_id)}`, {}, {
        Authorization: `Bearer ${token}`,
        'Procore-Company-Id': procore_company_id
    });
    sails.log.info('Procore response from GET n/w call', get_hazards_status);
    return (success ? hazards : []);
};

const createCompanyHazard = async (hazard_name, token, procore_company_id) => {
    let {
        status: create_hazard_status,
        data: hazard, success
    } = await makePOST(`${API_SERVER_URL}/${PROCORE_APIs.COMPANY_HAZARDS(procore_company_id)}`, {
        hazard: {
            name: hazard_name,
            active: true
        }
      }, {
        Authorization: `Bearer ${token}`,
        'Procore-Company-Id': procore_company_id
    });
    sails.log.info('Procore response from create n/w call', create_hazard_status);

    return (success ? hazard : []);
};

const createObservationItem = async (project_id, observation_data) => {
    let existingSetting = await projectHasProCoreSetting(project_id);
    if (!existingSetting.id) {
        return existingSetting;
    }

    let procore_meta = (existingSetting.value && existingSetting.value.procore_meta) || {};
    let token = (existingSetting.value && existingSetting.value.access_token) || '';
    if(!procore_meta.project || !procore_meta.company){
        return errorObject('Please relink your procore project', {procore_meta});
    }
    sails.log.info(`[createObservationItem] procore_project_id: ${procore_meta.project}, procore_company_id: ${procore_meta.company}`);

    let obsrvnType = await getProjectObservationTypes(procore_meta.project, token, procore_meta.company);
    let observationType = (obsrvnType || []).find(t => t.name && (t.name.toLowerCase() === "near miss")) || {};
    if(!observationType.id) {
        // observationType = await createPositiveObservationType(project_id, token, company_id);
    }
    sails.log.info(`[createObservationItem] observationType:`, observationType);

    let cc_hazard_category = observation_data.hazard_category;
    let companyHazards = await getCompanyHazard(token, procore_meta.company);
    let hazard = (companyHazards || []).find(t => t.name && (t.name.toLowerCase() === cc_hazard_category.toLowerCase())) || {};
    if(!hazard.id) {
        hazard = await createCompanyHazard(cc_hazard_category, token, procore_meta.company);
    }
    sails.log.info(`[createObservationItem] hazard:`, hazard);

    const name = (observation_data.additional_detail || '').length >= 254 ? (observation_data.additional_detail || '').slice(0, 254) : observation_data.additional_detail;
    sails.log.info(`Preparing observation form-data to be posted, project:${procore_meta.project} name:${name}`);
    const formData = new FormData();
    formData.append('project_id', procore_meta.project);
    formData.append('observation[type_id]', observationType.id);
    formData.append('observation[name]', cc_hazard_category);
    formData.append('observation[status]', "closed");
    formData.append('observation[hazard_id]', hazard.id);
    formData.append('observation[mt_location][]', observation_data.location_and_description);
    formData.append('observation[description]', observation_data.procore_description);

    let cc_images = [...observation_data.cc_images, ...observation_data.corrective_images] || [];
    sails.log.info(`Processing ${cc_images.length} images, form-data is: ${JSON.stringify(formData, null, 4)}`);
    for (let count = 0; count < cc_images.length; count++) {
        const img = cc_images[count];
        let buffer = await fetchUrlAs(img.file_url, 'arraybuffer');
        formData.append('attachments[]', buffer.data, img.name);
    }

    sails.log.info(`FormData object ready.`);
    let {
        status: create_observation_status,
        data: entry
    } = await makePOST(`${API_SERVER_URL}/${PROCORE_APIs.CREATE_OBSERVATION_ITEM}`, formData, {
        "Authorization": `Bearer ${token}`,
        "Procore-Company-Id": procore_meta.company,
        ...formData.getHeaders()
    }, true);

    sails.log.info('Procore response from create observation n/w call', create_observation_status);

    return entry;
};

module.exports = {

    refreshProcoreOAuthToken,
    constructSettingObject,
    getProjectSetting,
    projectHasProCoreSetting,
    // getOrCreateProcoreUser,
    // getOrCreateTimesheet,
    // getOrCreateVendor,
    // getVendorIdsOf,

    createUsersTimeCardEntries,

    exportDailyTimeLogs,
    exportUsersOnInductionApproval,

    // exportActiveUsersPerCompany,
    exportManpowerLogsPerCompany,
    exportCloseCallOnCloseOut,
};
