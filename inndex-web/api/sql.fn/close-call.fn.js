const {
    runReadQuery,
    VALID_SORT_DIRECTIONS,
    expandFilesReferences,
} = require('./core.fn');


const getCloseCallSql = ({projectId, userId, assignedTo, category, statusCodes, searchTerm, sortKey, sortDir, limit, offset}, countOnly = false) => {
    // if(!userId || !projectId){
    //     throw new Error('Invalid query param for getCloseCallSql, (userId, projectId)');
    // }
    let additionalSort = '';
    let variables = [projectId];
    if (userId) {
        variables.push(userId);
    }
    if (assignedTo) {
        variables.push(assignedTo);
    }
    if (category) {
        variables.push(category);
    }
    if (statusCodes.length) {
        variables.push(...statusCodes);
    }
    if (searchTerm && searchTerm.length) {
        variables.push(`%${searchTerm}%`, `%${searchTerm}%`);
    }
    if (!countOnly) {
        if (sortKey !== 'id') {
            additionalSort = `, cc.id DESC`;
        }
        variables.push(limit, offset);
    }
    let variableIndex = 0;
    let sql = `
        SELECT ${countOnly ? ` (count(cc.id))::int as total ` : `
            cc.*,
            (CASE WHEN a_t.id IS NOT NULL THEN json_build_object('id', a_t.id, 'first_name', a_t.first_name, 'last_name', a_t.last_name, 'email', a_t.email, 'employer', assignee_ued.employer) ELSE NULL END) as assigned_to,
            (CASE WHEN u.id IS NOT NULL THEN json_build_object('id', u.id, 'first_name', u.first_name, 'last_name', u.last_name, 'email', u.email) ELSE NULL END) as user_ref,
            (CASE WHEN t_o.id IS NOT NULL THEN json_build_object('id', t_o.id, 'name', t_o.name) ELSE NULL END) AS tagged_owner,
            (CASE WHEN ued.id IS NOT NULL THEN json_build_object('id', ued.id, 'employer', ued.employer, 'job_role', ued.job_role, 'type_of_employment', ued.type_of_employment, 'employment_company', ued.employment_company) ELSE NULL END) AS user_employer
            -- status_message
            `}
        FROM close_call as cc
                 LEFT JOIN users as u ON cc.user_ref = u.id
                 LEFT JOIN user_employment_detail as ued ON cc.user_ref = ued.user_ref
                 LEFT JOIN user_employment_detail as assignee_ued ON cc.assigned_to = assignee_ued.user_ref
                 LEFT JOIN employer as t_o ON cc.tagged_owner = t_o.id
                 LEFT JOIN users as a_t ON cc.assigned_to = a_t.id
        WHERE ${((projectId) ? `cc.project_ref = $${++variableIndex} ` : '')}
                  ${((userId) ? `AND cc.user_ref = $${++variableIndex} ` : '')}
                  ${((assignedTo) ? `AND cc.assigned_to = $${++variableIndex} ` : '')}
                  ${((category) ? `AND cc.hazard_category = $${++variableIndex} ` : '')}
                  ${((statusCodes.length) ? ` AND status IN (${statusCodes.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                  ${(searchTerm && searchTerm.length) ? ` AND (
              cc.hazard_category ILIKE $${++variableIndex}
              OR 'anonymous' ILIKE $${variableIndex}
              OR (cc.is_anonymous = false AND u.first_name ILIKE $${variableIndex} )
              OR (cc.is_anonymous = false AND u.middle_name ILIKE $${variableIndex} )
              OR (cc.is_anonymous = false AND u.last_name ILIKE $${variableIndex} )
              OR t_o.name ILIKE $${variableIndex}
              OR (cc.id)::text LIKE $${++variableIndex}
              OR (cc.cc_number)::text LIKE $${variableIndex}
              ) ` : ''}
                  ${(countOnly ? '' : `ORDER BY cc.${sortKey} ${sortDir} ${additionalSort} LIMIT $${++variableIndex} OFFSET $${++variableIndex}`)}`;
    return {sql, variables};
};

const getProjectCloseCallSql = ({ projectId, companyId, status, category, assigned_to, raised_by, tagged_owner, search, limit, offset }, countOnly = false) => {
    let variables = [projectId];
    let is_anonymous = false;

    if (companyId) {
        variables.push(companyId);
    }
    if (category && category.length) {
        variables.push(...category);
    }
    if (assigned_to && assigned_to.length) {
        variables.push(...assigned_to);
    }
    if (raised_by && raised_by.length) {
        is_anonymous = raised_by.some(record => record == 0);
        variables.push(...raised_by);
    }
    if(tagged_owner && tagged_owner.length) {
        variables.push(...tagged_owner)
    }
    if (status && status.length) {
        variables.push(...status);
    }
    if (search && search.length) {
        variables.push(`%${search}%`, `%${search}%`);
    }
    if (!countOnly) {
        variables.push(limit, offset);
    }
    let variableIndex = 0;
    let sql = `
        SELECT ${countOnly ? ` (count(cc.id))::int as total ` : `
            cc.*,
            (CASE WHEN ued.id IS NOT NULL THEN json_build_object('id', ued.id, 'employer', ued.employer, 'job_role', ued.job_role, 'type_of_employment', ued.type_of_employment, 'employment_company', ued.employment_company) ELSE NULL END) AS user_employer
            -- status_message
            `}
        FROM close_call as cc
                ${countOnly && !search && !search.length ? '' :
                 `LEFT JOIN users as u ON cc.user_ref = u.id
                 LEFT JOIN user_employment_detail as ued ON cc.user_ref = ued.user_ref
                 LEFT JOIN user_employment_detail as assignee_ued ON cc.assigned_to = assignee_ued.user_ref
                 LEFT JOIN employer as t_o ON cc.tagged_owner = t_o.id
                 LEFT JOIN users as a_t ON cc.assigned_to = a_t.id
                 LEFT JOIN project as project ON cc.project_ref = project.id` }
        WHERE ${((projectId) ? `cc.project_ref = $${++variableIndex} ` : '')}
                  ${((category.length) ? ` AND cc.hazard_category IN (${category.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                  ${((assigned_to.length) ? ` AND cc.assigned_to IN (${assigned_to.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                  ${((raised_by.length) ? ` AND ${is_anonymous ? '(cc.is_anonymous = true OR ' : '(cc.is_anonymous = false AND '} cc.user_ref IN (${raised_by.map(() => `$${++variableIndex}`).join(',')}) )` : '')}
                  ${((tagged_owner && tagged_owner.length) ? ` AND tagged_owner in (${tagged_owner.map(() => `$${++variableIndex}`).join(',')})` : '')}
                  ${((status.length) ? ` AND status IN (${status.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                  ${(search && search.length) ? ` AND (
              cc.hazard_category ILIKE $${++variableIndex}
              OR 'anonymous' ILIKE $${variableIndex}
              OR (cc.is_anonymous = false AND u.first_name ILIKE $${variableIndex} )
              OR (cc.is_anonymous = false AND u.middle_name ILIKE $${variableIndex} )
              OR (cc.is_anonymous = false AND u.last_name ILIKE $${variableIndex} )
               OR (cc.is_anonymous = false AND CONCAT(u.first_name, ' ', u.last_name) ILIKE $${variableIndex} )
              OR t_o.name ILIKE $${variableIndex}
              OR (cc.id)::text LIKE $${++variableIndex}
              OR (cc.cc_number)::text LIKE $${variableIndex}
              ) ` : ''}
                  ${(countOnly ? '' : `ORDER BY cc.status ASC, cc.id DESC LIMIT $${++variableIndex} OFFSET $${++variableIndex}`)}`;
    return {sql, variables};
};

const getProjectCloseCallHazardTypesSql = (projectId) => {
    let variables = [projectId];
    let variableIndex = 0;
    let sql = `
        SELECT DISTINCT cc.hazard_category as hazard_category
        FROM close_call as cc
        WHERE ${((projectId) ? `cc.project_ref = $${++variableIndex} ` : '')}`;
    return {sql, variables};
};

const getProjectCloseCallTaggedOwnersSql = (projectId) => {
    let variables = [projectId];
    let variableIndex = 0;
    let sql = `
        SELECT DISTINCT t_o.id as company_id, t_o.name as company_name
        FROM close_call as cc
            LEFT JOIN employer as t_o ON cc.tagged_owner = t_o.id
        WHERE
            ${((projectId) ? `cc.project_ref = $${++variableIndex} ` : '')} AND
            t_o.id IS NOT NULL`;
    return {sql, variables};
};


const getProjectCloseCallEmployerSql = (projectId) => {
    let variables = [projectId];
    let variableIndex = 0;
    let sql = `
        SELECT DISTINCT ued.employer
        FROM close_call as cc
            LEFT JOIN user_employment_detail as ued ON cc.user_ref = ued.user_ref
        WHERE
            cc.project_ref = $${++variableIndex} AND
            cc.is_anonymous = false AND
            ued.id IS NOT NULL`;
    return {sql, variables};
};

const getProjectCloseCallAssignedToSql = (projectId) => {
    let variables = [projectId];
    let variableIndex = 0;
    let sql = `
        SELECT id, concat_ws(' ', first_name, last_name) as name
        FROM users as u
        WHERE
            id IN (
                SELECT DISTINCT assigned_to
                FROM close_call AS cc
                WHERE
                    cc.project_ref = $${++variableIndex} AND
                    cc.assigned_to IS NOT NULL
            )`;
    return {sql, variables};
};

const getProjectCloseCallRaisedBySql = (projectId) => {
    let variables = [projectId];
    let variableIndex = 0;
    let sql = `
        SELECT id, concat_ws(' ', first_name, last_name) as name
        FROM users as u
        WHERE
            id IN (
                SELECT DISTINCT user_ref
                FROM close_call AS cc
                WHERE
                    ${((projectId) ? `cc.project_ref = $${++variableIndex} ` : '')} AND
                    cc.user_ref IS NOT NULL
            )`;
    return {sql, variables};
};

module.exports = {
    getUserCloseCallsPage: async (projectId,
                                  limit = 20,
                                  offset = 0,
                                  sortKey = 'id',
                                  sortDir = 'asc', {
                                      searchTerm = '',
                                      category,
                                      statusCodes,
                                      userId,
                                      assignedTo,
                                  } = {}) => {
        sails.log.info(`[getUserCloseCallsPage] projectId: ${projectId} q: "${searchTerm}" status: ${statusCodes || '-'}, category: ${category || '-'}, limit ${limit} offset ${offset}, sort: ${sortKey} ${sortDir}`);
        let total = 0;
        let records = [];
        if (!['id', 'status'].includes(sortKey)) {
            sails.log.warn('Invalid sortKey supplied, returning empty results');
            return {total, records};
        }
        if (!VALID_SORT_DIRECTIONS.includes(sortDir)) {
            sails.log.warn('Invalid sortDir supplied, returning empty results');
            return {total, records};
        }

        let {sql: countSql, variables: countQueryVariables} = getCloseCallSql({
            projectId,
            userId,
            assignedTo,
            category,
            statusCodes,
            searchTerm,
            // sortKey,
            // sortDir,
            // limit,
            // offset
        }, true);
        let countResult = await runReadQuery(countSql, countQueryVariables, true);
        total = (countResult.total || 0);

        if (total) {
            // No need to run select query if total is 0
            let {sql: selectSql, variables: selectQueryVariables} = getCloseCallSql({
                projectId,
                userId,
                assignedTo,
                category,
                statusCodes,
                searchTerm,
                sortKey,
                sortDir,
                limit,
                offset
            });
            records = await runReadQuery(selectSql, selectQueryVariables);
        }
        sails.log.info(`[getUserCloseCallsPage] projectId: ${projectId}, q: "${searchTerm}", status: ${statusCodes || '-'}, category: ${category || '-'}, total: ${total}, page records: ${records.length}`);

        return {total, records};
    },

    getProjectCloseCalls: async (projectId,
                                 companyId = null,
                                 status = [],
                                 category,
                                 assigned_to,
                                 raised_by,
                                 tagged_owner,
                                 search = '',
                                 limit = 30,
                                 offset = 0,
                                 countOnly = false
                                 ) => {
        sails.log.info(`[getProjectCloseCalls] projectId: ${projectId} companyId: ${companyId} q: "${search}" status: ${status || '-'}, category: ${category || '-'}, assigned_to: ${assigned_to || '-'}, raised_by: ${raised_by || '-'}, tagged_owner: ${tagged_owner || '-'}, limit ${limit} offset ${offset}`);
        let total = 0;
        let records = [];

        let {sql: countSql, variables: countQueryVariables} = getProjectCloseCallSql({
            projectId,
            companyId,
            status,
            category,
            assigned_to,
            raised_by,
            tagged_owner, 
            search,
            limit,
            offset,
        }, true);
        let countResult = await runReadQuery(countSql, countQueryVariables, true);
        total = (countResult.total || 0);

        if (!countOnly && total) {
            // No need to run select query if total is 0
            let {sql: selectSql, variables: selectQueryVariables} = getProjectCloseCallSql({
                projectId,
                companyId,
                status,
                category,
                assigned_to,
                raised_by,
                tagged_owner,
                search,
                limit,
                offset,
            });
            records = await runReadQuery(selectSql, selectQueryVariables);
        }
        sails.log.info(`[getProjectCloseCalls] projectId: ${projectId} companyId: ${companyId} q: "${search}" status: ${status || '-'}, category: ${category || '-'}, assigned_to: ${assigned_to || '-'}, raised_by: ${raised_by || '-'}, tagged_owner: ${tagged_owner || '-'}, limit ${limit} offset ${offset}, total: ${total}, page records: ${records.length}`);

        return {total, records};
    },

    getProjectCloseCallsHazardTypes: async (projectId) => {
        sails.log.info(`[getProjectCloseCallsHazardTypes] projectId: ${projectId}`);
        let records = [];

        let {sql: selectSql, variables: selectQueryVariables} = getProjectCloseCallHazardTypesSql(projectId);
        records = await runReadQuery(selectSql, selectQueryVariables);

        sails.log.info(`[getProjectCloseCallsHazardTypes] projectId: ${projectId} records: ${records.length}`);

        return records;
    },

    getProjectCloseCallsTaggedOwners: async (projectId, countOnly = false) => {
        sails.log.info(`[getProjectCloseCallsTaggedOwners] projectId: ${projectId}`);
        let records = [];

        let {sql: selectSql, variables: selectQueryVariables} = getProjectCloseCallTaggedOwnersSql(projectId);
        records = await runReadQuery(selectSql, selectQueryVariables);

        sails.log.info(`[getProjectCloseCallsTaggedOwners] projectId: ${projectId} records: ${records.length}`);

        return records;
    },

    getProjectCloseCallsEmployer: async (projectId) => {
        sails.log.info(`[getProjectCloseCallsEmployer] projectId: ${projectId}`);
        let records = [];

        let {sql: selectSql, variables: selectQueryVariables} = getProjectCloseCallEmployerSql(projectId);
        records = await runReadQuery(selectSql, selectQueryVariables);

        sails.log.info(`[getProjectCloseCallsEmployer] projectId: ${projectId} records: ${records.length}`);

        return records;
    },

    getProjectCloseCallsAssignedTo: async (projectId) => {
        sails.log.info(`[getProjectCloseCallsAssignedTo] projectId: ${projectId}`);
        let records = [];

        let {sql: selectSql, variables: selectQueryVariables} = getProjectCloseCallAssignedToSql(projectId);
        records = await runReadQuery(selectSql, selectQueryVariables);

        sails.log.info(`[getProjectCloseCallsAssignedTo] projectId: ${projectId} records: ${records.length}`);

        return records;
    },

    getProjectCloseCallsRaisedBy: async (projectId) => {
        sails.log.info(`[getProjectCloseCallsRaisedBy] projectId: ${projectId}`);
        let records = [];

        let {sql: selectSql, variables: selectQueryVariables} = getProjectCloseCallRaisedBySql(projectId);
        records = await runReadQuery(selectSql, selectQueryVariables);

        sails.log.info(`[getProjectCloseCallsRaisedBy] projectId: ${projectId} records: ${records.length}`);

        return records;
    },

    expandCCFiles: async (idList, columnsToPopulate = ['cc_images', 'corrective_images']) => {
        return await expandFilesReferences(idList, 'close_call', columnsToPopulate);
    },
};
