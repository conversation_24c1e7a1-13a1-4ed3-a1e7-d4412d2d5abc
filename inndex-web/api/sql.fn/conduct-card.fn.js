const _uniq = require('lodash/uniq');
const {
    runReadQuery,
} = require('./core.fn');

const {
    getUserEmployerByInductionIds,
} = require('./induction.fn');

const getUccSql = ({
                       conductCardIds,
                       limit,
                       offset,
                       searchTerm,
                       sortKey,
                       sortDir,
                   }, countOnly = false) => {
    let variables = [...conductCardIds];
    if (searchTerm && searchTerm.length) {
        variables.push(`%${searchTerm}%`);
    }
    if (!countOnly) {
        variables.push(limit, offset);
    }
    let variableIndex = 0;
    let sql = `SELECT ${countOnly ? ` (count(ucc.id))::int as total ` : `ucc.*,
            json_build_object('id', u1.id, 'first_name', u1.first_name, 'last_name', u1.last_name, 'email', u1.email, 'name',
            concat_ws(' ',u1.first_name,(CASE WHEN u1.middle_name = '' THEN NULL ELSE u1.middle_name END), u1.last_name)) as user_ref,
            json_build_object('id', u2.id, 'first_name', u2.first_name, 'last_name', u2.last_name, 'name', CONCAT(u2.first_name, ' ', u2.last_name)) as assigned_by_ref,
            json_build_object('id', p.id, 'name', p.name)                                          as project_ref`}
        FROM user_conduct_card ucc
        LEFT JOIN users u1 ON u1.id = ucc.user_ref
        LEFT JOIN users u2 ON u2.id = ucc.assigned_by_ref
        LEFT JOIN project p ON p.id = ucc.project_ref
        WHERE conduct_card_ref IN (${conductCardIds.map(() => `$${++variableIndex}`).join(',')})
        AND parent_ref IS NULL
        ${(searchTerm && searchTerm.length) ? ` AND ((concat_ws(' ',u1.first_name,(CASE WHEN u1.middle_name = '' THEN NULL ELSE u1.middle_name END), u1.last_name) ILIKE $${++variableIndex}) OR
        (ucc.card_detail ->> 'card_type' ILIKE $${variableIndex}) OR (ucc.card_detail ->> 'card_name' ILIKE $${variableIndex})) ` : ''}
        ${countOnly ? '' : `ORDER BY ucc.${sortKey} ${sortDir} LIMIT $${++variableIndex}
                   OFFSET $${++variableIndex}`}`;

    return {sql, variables};
};


const getCompanyProjectUccSql = ({ companyId, projectId, sortKey, sortDir }, countOnly = false) => {
    let variables = [];
    if (companyId) {
        variables.push(companyId)
    } else {
        variables.push(projectId)
    }

    let variableIndex = 0;
    let sql = `SELECT ${countOnly ? ` (count(ucc.id))::int as total ` : `ucc.*,
                ir.creator_name as name,
                json_build_object('id', u1.id, 'first_name', u1.first_name, 'last_name', u1.last_name, 'email', u1.email, 'name',
                concat_ws(' ',u1.first_name,(CASE WHEN u1.middle_name = '' THEN NULL ELSE u1.middle_name END), u1.last_name)) as user_ref,
                json_build_object('id', u2.id, 'first_name', u2.first_name, 'last_name', u2.last_name, 'name', CONCAT(u2.first_name, ' ', u2.last_name)) as assigned_by_ref,
                json_build_object('id', p.id, 'name', p.name) as project_ref,
                ir.additional_data->'employment_detail'->>'employer' as user_employer,
                ir.record_id as induction_record_id,
                ir2.creator_name as assigned_by,
                ir2.assigned_by_user_employer as assigned_by_user_employer
            `}
        FROM user_conduct_card ucc
            LEFT JOIN users u1 ON u1.id = ucc.user_ref
            LEFT JOIN users u2 ON u2.id = ucc.assigned_by_ref
            LEFT JOIN project p ON p.id = ucc.project_ref
            LEFT JOIN conduct_card cc ON cc.id = ucc.conduct_card_ref
            LEFT JOIN induction_request ir ON ir.id = ucc.induction_ref
            LEFT JOIN LATERAL (
                SELECT creator_name, additional_data->'employment_detail'->>'employer' as assigned_by_user_employer
                    FROM induction_request
                    WHERE induction_request.user_ref = ucc.assigned_by_ref
                        AND induction_request.project_ref = ucc.project_ref
                    ORDER BY id DESC
                    LIMIT 1
            ) ir2 ON true
        WHERE ${companyId ? `cc.company_ref = $${++variableIndex}` : `ucc.project_ref = $${++variableIndex}`}
            AND parent_ref IS NULL
        ${countOnly ? '' : ` ORDER BY ucc.${sortKey} ${sortDir}`}`;

    return {sql, variables};
};

module.exports = {
    getPaginateUcc: async (
        conductCardIds,
        limit = 20,
        offset = 0,
        searchTerm = '',
        sortKey = 'id',
        sortDir = 'desc') => {
        sails.log.info(`[getPaginateUCC] conductCardIds: ${conductCardIds} q: "${searchTerm}", limit ${limit} offset ${offset}, sort: ${sortKey} ${sortDir}`);
        let total = 0;
        let records = [];
        let {sql: countSql, variables: countQueryVariables} = getUccSql({
            conductCardIds,
            limit,
            offset,
            searchTerm,
        }, true);
        let countResult = await runReadQuery(countSql, countQueryVariables, true);
        total = (countResult.total || 0);

        if (total) {
            // No need to run select query if total is 0
            let {sql: selectSql, variables: selectQueryVariables} = getUccSql({
                conductCardIds,
                limit,
                offset,
                searchTerm,
                sortKey,
                sortDir,
            }, false);
            records = await runReadQuery(selectSql, selectQueryVariables);

            let inductionIds = [];
            for (let i=0; records.length > i; i++) {
                inductionIds.push(records[i].induction_ref);
            }

            let userInductionEmployers = await getUserEmployerByInductionIds(_uniq(inductionIds));

            for (let i=0; records.length > i; i++) {
                records[i].issued_to_employer = (userInductionEmployers || []).find(induction => induction.id === records[i].induction_ref);
            }
        }

        sails.log.info(`[getPaginateUCC] conductCardIds: ${conductCardIds}, q: "${searchTerm}", total: ${total}, page records: ${records.length}`);
        return {total, records};
    },

    getCompanyProjectUcc: async (companyId, projectId, sortKey = 'id', sortDir = 'desc') => {
        sails.log.info(`[getCompanyProjectUcc] companyId: ${companyId}, projectId: ${projectId}, sort: ${sortKey} ${sortDir}`);
        let total = 0;
        let records = [];
        let {sql: countSql, variables: countQueryVariables} = getCompanyProjectUccSql({ companyId, projectId }, true);
        let countResult = await runReadQuery(countSql, countQueryVariables, true);
        total = (countResult.total || 0);

        if (total) {
            // No need to run select query if total is 0
            let {sql: selectSql, variables: selectQueryVariables} = getCompanyProjectUccSql({ companyId, projectId, sortKey, sortDir, }, false);
            records = await runReadQuery(selectSql, selectQueryVariables);
        }

        sails.log.info(`[getCompanyProjectUcc] companyId: ${companyId}, projectId: ${projectId}, total: ${total}, page records: ${records.length}`);
        return {total, records};
    }
}
