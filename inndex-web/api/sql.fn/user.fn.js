const {
    runReadQuery, VALID_SORT_DIRECTIONS,
    escapeSearchString,
} = require('./core.fn');

const getAllUsersSql = async({searchTerm, sortKey, sortDir, offset, limit, fromDate, toDate}, countOnly = false)=>{
    let variables = [];
    let variableIndex = 0;

    let filter = [];
    if(searchTerm){
        variables.push(`%${escapeSearchString(searchTerm)}%`);
        filter.push(` CAST(id AS VARCHAR) ILIKE $${++variableIndex} OR email ILIKE $${variableIndex} OR (concat_ws(' ',first_name,(CASE WHEN middle_name = '' THEN NULL ELSE middle_name END), last_name)) ILIKE $${variableIndex}`);

    }
    if(fromDate){
        variables.push(fromDate);
        filter.push(`"createdAt" >= $${++variableIndex}`);
    }
    if(toDate){
        variables.push(toDate);
        filter.push(`"createdAt" <= $${++variableIndex}`);
    }
    if(!countOnly){
        variables.push(limit);
        variables.push(offset);
    }

    let sql = ` SELECT  ${countOnly ? ' COUNT(*) as total ': `id, is_active, first_name, middle_name, last_name,"createdAt", email_verified_on, email, last_active_on`}
        from users
        ${filter.length ?  `WHERE ${filter.join(` AND `)}` : ''}
        ${countOnly ? '' : `ORDER BY ${sortKey || 'id'} ${sortDir || 'desc'}`}
        ${countOnly? '': `LIMIT $${++variableIndex} OFFSET $${++variableIndex}`}
        `
    return {sql,variables};
}

module.exports = {
    getAllUsersFn: async (
        limit = 20,
        offset = 0,
        sortKey = 'id',
        sortDir = 'desc', {
            searchTerm = '',
            fromDate = '',
            toDate = '',
        } = {}) => {
        sails.log.info(`[getAllUsersV2]  q: "${searchTerm}" limit ${limit} offset ${offset}, sort: ${sortKey} ${sortDir}, fromDate: ${fromDate}, toDate: ${toDate}`);
        let total = 0, records = [];
        if (!['id', 'email', "last_active_on"].includes(sortKey)) {
            sails.log.warn('Invalid sortKey supplied, returning empty results');
            return {total, records};
        }
        if (!VALID_SORT_DIRECTIONS.includes(sortDir)) {
            sails.log.warn('Invalid sortDir supplied, returning empty results');
            return {total, records};
        }
        let {sql: countSql, variables: countVariables}  = await getAllUsersSql({searchTerm, sortKey, sortDir, offset, limit, fromDate, toDate}, true);
        let countResult = await runReadQuery (countSql, countVariables, true);
        total = (+countResult.total || 0);
        if(total){
            let {sql: countSql, variables: countVariables}  = await getAllUsersSql({searchTerm, sortKey, sortDir, offset, limit, fromDate, toDate});
            records = await runReadQuery (countSql, countVariables);
        }
        return {total,records};
    },
}
