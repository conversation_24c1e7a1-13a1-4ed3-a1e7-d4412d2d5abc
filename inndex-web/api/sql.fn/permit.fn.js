const _uniq = require('lodash/uniq');
const {
    runReadQuery,
    fetchFileDataByIds,
} = require('./core.fn');

const {
    getInductionEmployerByUserIds
} = require('./induction.fn');

const getPermitTemplatesSql = ({
                                   companyId,
                                   limit,
                                   offset,
                                   searchTerm,
                                   sortKey,
                                   sortDir,
                               }, countOnly = false) => {
    let variables = [companyId];
    if (searchTerm && searchTerm.length) {
        variables.push(`%${searchTerm}%`);
    }
    if (!countOnly) {
        variables.push(limit, offset);
    }
    let variableIndex = 0;
    let sql = `SELECT ${countOnly ? ` (count(pt.id))::int as total ` : `pt.*,
            json_build_object('id', u1.id, 'first_name', u1.first_name, 'last_name', u1.last_name, 'email', u1.email, 'name',
                              CONCAT(u1.first_name, ' ', u1.last_name))                            as created_by`}
               FROM permit_template pt
                        LEFT JOIN users u1 ON u1.id = pt.created_by
               WHERE company_ref = $${++variableIndex}
                   ${(searchTerm && searchTerm.length) ? ` AND ((u1.first_name ILIKE $${++variableIndex}) OR (u1.last_name ILIKE $${variableIndex}) OR (pt.permit_type ILIKE $${variableIndex}) OR (pt.ref_number ILIKE $${variableIndex})) ` : ''}
                     ${countOnly ? '' : `ORDER BY pt.${sortKey} ${sortDir} LIMIT $${++variableIndex}
                   OFFSET $${++variableIndex}`}`;

    return {sql, variables};
};

const getPermitRequestsSql = ({
                                  projectId,
                                  userId,
                                  statusFilter,
                                  permitTypeFilter,
                                  searchTerm,
                                  limit,
                                  offset,
                                  sortKey,
                                  sortDir,
                                  userEmployer,
                              }, countOnly = false) => {
    const variables = [projectId, ...(userId ? [userId] : []), ...(statusFilter.length ? statusFilter : []), ...(permitTypeFilter.length ? permitTypeFilter : []), ...(searchTerm && searchTerm.length ? [`%${searchTerm}%`] : []), ...(searchTerm && +searchTerm ? [searchTerm] : []), ...(countOnly ? [] : [limit, offset])];
    let variableIndex = 0;
    let sql = `SELECT ${countOnly ? ` (count(pr.id))::int as total ` : `pr.*,
            json_build_object('id', u.id, 'first_name', u.first_name, 'middle_name', u.middle_name, 'last_name', u.last_name) as requestor_ref,
            json_build_object('id', ppc.id, 'master_permit_managers', ppc.master_permit_managers) as config_ref,
            json_build_object('ref_number', pt.ref_number, 'permit_type', pt.permit_type, 'ref_docs', pt.ref_docs, 'expire_in', pt.expire_in, 'include_mandatory_attachments', pt.include_mandatory_attachments, 'mandatory_attachments_title', pt.mandatory_attachments_title, 'require_closeout', pt.require_closeout, 'fillable_pdf_ref', pt.fillable_pdf_ref, 'fillable_pdf_fields', pt.fillable_pdf_fields, 'field_sections', pt.field_sections, 'signatures', pt.signatures, 'take_register_when', pt.take_register_when, 'register_config', pt.register_config) as permit_ref
            `}
               FROM permit_request pr
                   ${countOnly ? `LEFT JOIN permit_template pt ON pt.id = pr.permit_ref LEFT JOIN users as u ON pr.requestor_ref = u.id` :
                       `LEFT JOIN permit_template pt ON pt.id = pr.permit_ref
                        LEFT JOIN users as u ON pr.requestor_ref = u.id
                        LEFT JOIN project_permit_config as ppc ON pr.config_ref = ppc.id
                        `}
                   ${userEmployer ? `INNER JOIN LATERAL (
                   SELECT *
                   FROM induction_request ir_sub
                   WHERE ir_sub.user_ref = pr.requestor_ref AND ir_sub.project_ref = pr.project_ref
                   AND (additional_data->'user_info'->>'parent_company' = ${userEmployer}::text OR additional_data->'user_info'->'parent_company'->>'id' = ${userEmployer}::text)
                   ORDER BY ir_sub.id
                   LIMIT 1
                   ) AS ir ON TRUE` : ``}

               WHERE pr.project_ref = $${++variableIndex} ${(userId) ? ` AND requestor_ref = $${++variableIndex}` : ''}
                   ${(statusFilter.length) ? ` AND status IN (${statusFilter.map(() => `$${++variableIndex}`).join(',')})` : ''}
                   ${(permitTypeFilter.length) ? ` AND pt.permit_type IN (${permitTypeFilter.map(() => `$${++variableIndex}`).join(',')})` : ''}
                   ${(searchTerm && searchTerm.length) ? ` AND (pt.permit_type ILIKE $${++variableIndex} OR u.first_name ILIKE $${variableIndex} OR u.last_name ILIKE $${variableIndex} OR CONCAT(u.first_name, ' ', u.last_name) ILIKE $${variableIndex} OR pt.ref_number ILIKE $${variableIndex} OR pr.requestor_company ILIKE $${variableIndex} ${(+searchTerm)  ? `OR pr.record_id = $${++variableIndex}::int` : ''})` : ''} ${countOnly ? '' : `ORDER BY CASE pr.status WHEN 3 THEN 1 END, id DESC LIMIT $${++variableIndex}
                   OFFSET $${++variableIndex}`}`;

    return {sql, variables};
};

const toolBriefingsSql = ({
                              projectId,
                              userId,
                              statusFilter,
                              permitTypeFilter,
                              searchTerm,
                              limit,
                              offset,
                              sortKey,
                              sortDir,
                          }, countOnly = false) => {
    const variables = [userId, projectId, ...(statusFilter.length ? statusFilter : []), ...(permitTypeFilter.length ? permitTypeFilter : []), ...(searchTerm && searchTerm.length ? [`%${searchTerm}%`] : []), ...(searchTerm && +searchTerm ? [searchTerm] : []), ...(countOnly ? [] : [limit, offset])];
    let variableIndex = 0;
    let sql = `SELECT ${countOnly ? ` (count(tb.id))::int as total ` : `pr.id, pr.status_logs, pr.requestor_company, pr.start_on, pr.expire_on, pr.status, pr."createdAt",
      json_build_object('id', u.id, 'first_name', u.first_name, 'middle_name', u.middle_name, 'last_name', u.last_name) as requestor_ref,
      json_build_object('ref_number', pt.ref_number, 'permit_type', pt.permit_type) as permit_ref
        `}
               FROM tool_briefings tb
                        INNER JOIN permit_request pr on pr.id = tb.tool_record_ref
                   INNER JOIN permit_template pt on pt.id = pr.permit_ref
                   LEFT JOIN users as u ON u.id = pr.requestor_ref
               WHERE EXISTS (
        SELECT 1
        FROM jsonb_array_elements(register) AS elem
        WHERE elem->>'user_ref' = $${++variableIndex}
    ) AND tb.tool_key='permit_register' AND tb.project_ref=$${++variableIndex}  ${(statusFilter.length) ? ` AND pr.status IN (${statusFilter.map(() => `$${++variableIndex}`).join(',')})` : ''} ${(permitTypeFilter.length) ? ` AND pt.permit_type IN (${permitTypeFilter.map(() => `$${++variableIndex}`).join(',')})` : ''} ${(searchTerm && searchTerm.length) ? ` AND (pt.permit_type ILIKE $${++variableIndex} OR u.first_name ILIKE $${variableIndex} OR u.last_name ILIKE $${variableIndex} OR CONCAT(u.first_name, ' ', u.last_name) ILIKE $${variableIndex} OR pt.ref_number ILIKE $${variableIndex} OR pr.requestor_company ILIKE $${variableIndex} ${(+searchTerm)  ? `OR pr.record_id = $${++variableIndex}::int` : ''})` : ''} ${countOnly ? '' : `ORDER BY CASE pr.status WHEN 3 THEN 1 END, pr.id DESC LIMIT $${++variableIndex}
                   OFFSET $${++variableIndex}`}`;
    return {sql, variables};
}

const getPermitTypes = async ({ projectId, userId }) => {
    const variables = [projectId, ...(userId ? [userId] : [])];
    let variableIndex = 0;
    let sql = `SELECT  pt.permit_type permit_type
               FROM permit_request pr
                        LEFT JOIN permit_template pt ON pt.id = pr.permit_ref
               WHERE project_ref = $${++variableIndex} ${(userId) ? ` AND requestor_ref = $${++variableIndex}` : ''}`;

    let records = await runReadQuery(sql, variables);
    let permit_types = _uniq((records || []).map(record => record.permit_type) || []);
    return permit_types;
}

module.exports = {
    getPaginatePermitTemplates: async (
        companyId,
        limit = 20,
        offset = 0,
        searchTerm = '',
        sortKey = 'id',
        sortDir = 'desc') => {
        sails.log.info(`get paginated permit templates, companyId: ${companyId} q: "${searchTerm}", limit ${limit} offset ${offset}, sort: ${sortKey} ${sortDir}`);
        let total = 0;
        let records = [];
        let {sql: countSql, variables: countQueryVariables} = getPermitTemplatesSql({
            companyId,
            limit,
            offset,
            searchTerm,
        }, true);
        let countResult = await runReadQuery(countSql, countQueryVariables, true);
        total = (countResult.total || 0);

        if (total) {
            // No need to run select query if total is 0
            let {sql: selectSql, variables: selectQueryVariables} = getPermitTemplatesSql({
                companyId,
                limit,
                offset,
                searchTerm,
                sortKey,
                sortDir,
            }, false);
            records = await runReadQuery(selectSql, selectQueryVariables);
        }

        sails.log.info(`paginated permit templates, companyId: ${companyId}, q: "${searchTerm}", total: ${total}, page records: ${records.length}`);
        return {total, records};
    },

    getPaginatePermitRequests: async (
        projectId,
        userId,
        statusFilter = null,
        permitTypeFilter = null,
        searchTerm = null,
        limit = 20,
        offset = 0,
        userEmployer = null,
        sortKey = 'id',
        sortDir = 'desc',
    ) => {
        sails.log.info(`get paginated permit requests, projectId: ${projectId}, userId: ${userId}, userEmployer: ${userEmployer}, statusFilter: ${statusFilter.join(',')}, permitTypeFilter: ${permitTypeFilter.join(',')}, q: "${searchTerm}", limit ${limit} offset ${offset}, sort: ${sortKey} ${sortDir}`);
        let total = 0;
        let records = [];
        let {sql: countSql, variables: countQueryVariables} = getPermitRequestsSql({
            projectId,
            userId,
            statusFilter,
            permitTypeFilter,
            searchTerm,
            limit,
            offset,
            userEmployer,
        }, true);
        let countResult = await runReadQuery(countSql, countQueryVariables, true);
        total = (countResult.total || 0);

        let permit_types = [];
        if (total) {
            // No need to run select query if total is 0
            let {sql: selectSql, variables: selectQueryVariables} = getPermitRequestsSql({
                projectId,
                userId,
                statusFilter,
                permitTypeFilter,
                searchTerm,
                limit,
                offset,
                sortKey,
                sortDir,
                userEmployer,
            }, false);
            records = await runReadQuery(selectSql, selectQueryVariables);

            permit_types = await getPermitTypes({
                projectId,
                userId
            });

            let fileIds = [];
            let signatoryUserIDs = [];
            records = records.map(permitRequest => {
                signatoryUserIDs.push(permitRequest.requestor_ref.id);
                let signatoryIds = permitRequest.signatures.map(sign => sign.signatory_user_ref);
                signatoryUserIDs.push(...signatoryIds);

                let mandatoryAttachmentsFileRefs = permitRequest.mandatory_attachments.reduce((arr, attach) => {
                    arr.push(...attach.attachment_file_refs);
                    return arr;
                }, [])
                fileIds.push(...(permitRequest.permit_ref.ref_docs || []), ...mandatoryAttachmentsFileRefs);

                return permitRequest;
            });

            let files = [];
            if (fileIds.length) {
                files = await fetchFileDataByIds(_uniq(fileIds));
            }

            let usersInfo = [];
            if (signatoryUserIDs.length) {
                usersInfo = await getInductionEmployerByUserIds(_uniq(signatoryUserIDs), projectId, [2, 6]);
            }

            records = records.map(permitRequest => {
                permitRequest.requestor_ref.user_induction_info = usersInfo.find(user => user.user_ref === permitRequest.requestor_ref.id);
                permitRequest.signatures = permitRequest.signatures.map(sign => {
                    sign.signatory_user_ref = usersInfo.find(user => user.user_ref === sign.signatory_user_ref);
                    return sign;
                });

                if (permitRequest.permit_ref.ref_docs && permitRequest.permit_ref.ref_docs.length) {
                    permitRequest.permit_ref.ref_docs = files.filter(file => (permitRequest.permit_ref.ref_docs).includes(file.id));
                }

                permitRequest.mandatory_attachments = permitRequest.mandatory_attachments.map(attach => {
                    attach.attachment_file_refs = files.filter(file => attach.attachment_file_refs.includes(file.id));
                    return attach;
                })
                return permitRequest;
            })
        }

        sails.log.info(`paginated permit requests, page records: ${records.length}`);
        return {total, records, permit_types};
    },

    getPermitConfigBySignOffUser: async (
        projectId,
        userId
    ) => {
        let variables = [projectId, userId];
        let variableIndex = 0;
        let selectSql = `WITH temp_table AS (SELECT id,
                                                    permit_ref,
                                                    (arr.item_object ->>'state')::numeric state, arr.item_object ->'signatories' permit_managers, master_permit_managers
                                             FROM project_permit_config,
                                                  jsonb_array_elements(sign_off) with ordinality arr(item_object, position)
                                             where project_ref = $${++variableIndex})
                         SELECT id, permit_ref, state, permit_managers, master_permit_managers
                         FROM temp_table
                         where (permit_managers @> ($${++variableIndex})::jsonb OR master_permit_managers @> ($${variableIndex})::jsonb)`;

        return  await runReadQuery(selectSql, variables);
    },

    expandFileRefs: async (fileIds) => {
        return await fetchFileDataByIds(fileIds);
    },

    getBriefedByUserPermits: async (
        projectId,
        userId,
        statusFilter = null,
        permitTypeFilter = null,
        searchTerm = null,
        limit = 20,
        offset = 0,
        sortKey = 'id',
        sortDir = 'desc') => {
        sails.log.info(`get paginated briefed permit requests by user ${userId}, projectId: ${projectId}, statusFilter: ${statusFilter.join(',')}, permitTypeFilter: ${permitTypeFilter.join(',')}, q: "${searchTerm}", limit ${limit} offset ${offset}, sort: ${sortKey} ${sortDir}`);
        let total = 0;
        let records = [];
        let {sql: countSql, variables: countQueryVariables} = toolBriefingsSql({
            projectId,
            userId,
            statusFilter,
            permitTypeFilter,
            searchTerm,
            limit,
            offset,
        }, true);
        let countResult = await runReadQuery(countSql, countQueryVariables, true);
        total = (countResult.total || 0);

        let permit_types = [];
        if (total) {
            // No need to run select query if total is 0
            let {sql: selectSql, variables: selectQueryVariables} = toolBriefingsSql({
                projectId,
                userId,
                statusFilter,
                permitTypeFilter,
                searchTerm,
                limit,
                offset,
                sortKey,
                sortDir,
            }, false);
            records = await runReadQuery(selectSql, selectQueryVariables);

            permit_types = await getPermitTypes({
                projectId,
                userId
            });
        }

        sails.log.info(`paginated permit requests, page records: ${records.length}`);
        return {total, records, permit_types};
    }
}
