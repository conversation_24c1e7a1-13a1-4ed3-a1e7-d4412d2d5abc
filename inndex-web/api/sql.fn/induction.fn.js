const {
    runReadQuery,
    VALID_SORT_DIRECTIONS, escapeSearchString,
} = require('./core.fn');

const dayjs = require('dayjs');
const {
    dbDateFormat_YYYY_MM_DD,
} = sails.config.constants;

const {
    typeOf
} = require('../services/HttpService')

const {
    resourceIdentifier,
    ROLES,
} = require('../services/TokenUtil')

module.exports = {
    /**
     *
     * @param projectId
     * @param {statusCodes}
     * @param transform
     * @returns {Promise<[{name}]|[]>}
     */
    getInductionUserEmployers: async (projectId, {statusCodes, userIds = []}, transform = true) => {
        sails.log.info(`[getInductionUserEmployers] projectId: ${projectId}, statusCodes: ${statusCodes}, userIds: ${userIds}, transform: ${transform}`);
        let variables = [projectId];
        let variableIndex = 1;
        if (statusCodes.length) {
            variables.push(...statusCodes);
        }
        if (userIds.length) {
            variables.push(...userIds);
        }
        let sql = `SELECT DISTINCT additional_data -> 'employment_detail' ->> 'employer' as name
                ${((userIds.length) ? `, user_ref` : '')}
                    FROM induction_request
                    WHERE project_ref = $1
                    AND additional_data -> 'employment_detail' ->> 'employer' is not null
                        ${((statusCodes.length) ? ` AND
                        status_code IN (${statusCodes.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                        ${((userIds.length) ? ` AND
                        user_ref IN (${userIds.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                   ORDER BY name`;
        let records = await runReadQuery(sql, variables);
        sails.log.info(`[getInductionUserEmployers] projectId: ${projectId}, statusCodes: ${statusCodes}, userIds: ${userIds}, total records: ${records.length}`);
        if(transform){
            return records.map(r => r.name);
        }
        return records;
    },
    /**
     *
     * @param projectId
     * @param {statusCodes}
     * @param transform
     * @returns {Promise<[{name}]|[]>}
     */
    getInductionUserJobRoles: async (projectId, {statusCodes}, transform = true) => {
        sails.log.info(`[getInductionUserJobRoles] projectId: ${projectId}, statusCodes: ${statusCodes}, transform: ${transform}`);
        let variables = [projectId];
        let variableIndex = 1;
        if (statusCodes.length) {
            variables.push(...statusCodes);
        }
        let sql = `SELECT DISTINCT additional_data -> 'employment_detail' ->> 'job_role' as name
                   FROM induction_request
                   WHERE project_ref = $1
                     AND additional_data -> 'employment_detail' ->> 'job_role' is not null
                       ${((statusCodes.length) ? ` AND
                status_code IN (${statusCodes.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                   ORDER BY name`;
        let records = await runReadQuery(sql, variables);
        sails.log.info(`[getInductionUserJobRoles] projectId: ${projectId}, statusCodes: ${statusCodes}, total records: ${records.length}`);
        if (transform) {
            return records.map(r => r.name);
        }
        return records;
    },

    /**
     * get names of ALL people inducted on given project.
     *
     * @param projectId
     * @param {statusCodes, sortKey, sortDir, limit, offset, searchTerm, searchUserIds}
     * @param extraColumns
     * @returns {Promise<{records: *[]}|{records: ({}|*[])}>}
     */
    getProjectInductions: async (projectId,
        {
            statusCodes = [],
            sortKey = 'name',
            sortDir = 'asc',
            limit = 20,
            offset = 0,
            searchTerm = '',
            searchUserIds = [],
            employer = null,
        } = {}, extraColumns = []) => {
        sails.log.info(`[getProjectInductions] projectId: ${projectId}, filter.q: ${searchTerm || '-'}, filter.status: ${statusCodes || '-'}, filter.users: ${searchUserIds}, limit: ${limit}, employer: ${employer}`);
        let records = [];
        if (!['id', 'name'].includes(sortKey)) {
            sails.log.warn('Invalid sortKey supplied, returning empty results');
            return {records};
        }
        if (!VALID_SORT_DIRECTIONS.includes(sortDir)) {
            sails.log.warn('Invalid sortDir supplied, returning empty results');
            return {records};
        }

        let variables = [projectId];
        if (statusCodes.length) {
            variables.push(...statusCodes);
        }
        if (searchUserIds.length) {
            variables.push(...searchUserIds);
        }
        if (searchTerm && searchTerm.length) {
            variables.push(`%${escapeSearchString(searchTerm)}%`);
        }
        if (employer) {
            variables.push(employer);
        }
        if (limit > 0) {
            variables.push(limit, offset);
        }
        let variableIndex = 0;

        let sql = `
            SELECT id,
                   record_id::int,
                   ${extraColumns && extraColumns.length ? ` ${extraColumns.join(', ')}, ` : ''}
                                   user_ref,
                   creator_name as name
            FROM induction_request
            WHERE project_ref = ${`$${++variableIndex}`} ${((statusCodes.length) ? ` AND
                status_code IN (${statusCodes.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                ${((searchUserIds.length) ? ` AND
                user_ref IN (${searchUserIds.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                ${(searchTerm && searchTerm.length) ? ` AND (
              creator_name ILIKE $${++variableIndex}
              ) ` : ''}
               ${employer ? ` AND
                    (additional_data -> 'employment_detail' ->> 'employer') = $${++variableIndex}
                 ` : ''}
            ORDER BY "${sortKey}" ${sortDir} ${(limit > 0) ? ` LIMIT $${++variableIndex} OFFSET $${++variableIndex}` : ''}`;

        records = await runReadQuery(sql, variables);

        sails.log.info(`[getProjectInductions] projectId: ${projectId}, filter.q: ${searchTerm || '-'}, filter.status: ${statusCodes || '-'}, filter.users: ${searchUserIds}, records: ${records.length}`);

        return {records};
    },

    /**
     * get inductions data to compute project dashboard info
     * @param projectId
     * @returns {Promise<[{}]>}
     */
    getInductionsForDashboard: async (projectId) => {
        sails.log.info(`[getInductionsForDashboard] projectId: ${projectId}`);
        let sql = `SELECT id, user_ref, creator_name,
                  additional_data->'user_info'->>'gender' as gender,
                  additional_data->'user_info'->'profile_pic_ref'->>'file_url' as pic_url,
                  additional_data->'contact_detail'->>'mobile_no' as mobile_no,
                  additional_data->'contact_detail'->'mobile_number' as mobile_number,
                  additional_data->'employment_detail'->>'employer' as employer,
                  additional_data->'user_docs' as user_docs
                   FROM induction_request
                   WHERE project_ref = $1
                   AND status_code IN (2, 6, 4, 5)
                   `;
        let records = await runReadQuery(sql, [projectId]);
        sails.log.info(`[getInductionsForDashboard] projectId: ${projectId}, total records: ${records.length}`);
        return records;
    },
    /**
     * get all inductions of given Project
     *      Supports Pagination, search, sorting, filtering.
     * @param projectId
     * @param limit
     * @param offset
     * @param sortKey
     * @param sortDir
     * @param filter <{searchTerm, statusCodes, employer, jobRole, userIds}>
     * @param extraColumns
     * @returns {Promise<{total: number, records: [<{id, record_id, user_ref, name, profile_pic_ref, employer, status_code}>]}>}
     */
    getProjectInductionsPage: async (projectId,
                                     limit = 20,
                                     offset = 0,
                                     sortKey = 'id',
                                     sortDir = 'asc', {
                                         searchTerm = '',
                                         statusCodes,
                                         employer,
                                         jobRole = [],
                                         userIds = []
                                     } = {},
                                     extraColumns = []) => {

        sails.log.info(`[getProjectInductionsPage] projectId: ${projectId} extra: ${extraColumns.length} q: "${searchTerm}" filter.status: ${statusCodes || '-'}, limit ${limit} offset ${offset}, sort: ${sortKey} ${sortDir}`);
        let total = 0;
        let records = [];
        if (!['id', 'name', "createdAt", "employer"].includes(sortKey)) {
            sails.log.warn('Invalid sortKey supplied, returning empty results');
            return {total, records};
        }else if(sortKey === 'createdAt'){
            sortKey = 'id';
        }
        if (!VALID_SORT_DIRECTIONS.includes(sortDir)) {
            sails.log.warn('Invalid sortDir supplied, returning empty results');
            return {total, records};
        }

        let getSql = (countOnly = false) => {
            let variables = [projectId];
            if (statusCodes.length) {
                variables.push(...statusCodes);
            }
            if (userIds.length) {
                variables.push(...userIds);
            }
            if (employer.length) {
                employer = employer.map(escapeSearchString)
                variables.push(...employer);
            }
            if (jobRole.length) {
                jobRole = jobRole.map(escapeSearchString)
                variables.push(...jobRole);
            }
            if (searchTerm && searchTerm.length) {
                variables.push(`%${escapeSearchString(searchTerm)}%`, `%${escapeSearchString(searchTerm)}%`);
            }
            if (!countOnly) {
                variables.push(limit, offset);
            }
            let variableIndex = 0;

            let sql = `
                SELECT ${countOnly ? ` (count(id))::int as total ` : `
                    id, record_id::int,
                    user_ref,
                    -- concat_ws(
                    --     ' ', additional_data->'user_info'->>'first_name',
                    --     (CASE WHEN additional_data->'user_info'->>'middle_name' = '' THEN NULL ELSE additional_data->'user_info'->>'middle_name' END),
                    --     additional_data->'user_info'->>'last_name'
                    -- ) as name,
                    creator_name as name,
                    ${extraColumns && extraColumns.length ? ` ${extraColumns.join(', ')}, ` : ''}
                    "updatedAt", "createdAt", status_code `}
                FROM induction_request
                WHERE project_ref = ${`$${++variableIndex}`} ${((statusCodes.length) ? ` AND
                status_code IN (${statusCodes.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                    ${((userIds.length) ? ` AND
                user_ref IN (${userIds.map(() => `$${++variableIndex}`).join(',')}) ` : '')}
                ${((employer.length) ? `AND
                    additional_data->'employment_detail'->> 'employer' IN (${employer.map(() => `$${++variableIndex}`).join(',')})` : '')}
                ${((jobRole.length) ? `AND
                    additional_data->'employment_detail'->> 'job_role' IN (${jobRole.map(() => `$${++variableIndex}`).join(',')})` : '')}
                    ${(searchTerm && searchTerm.length) ? ` AND (
              creator_name ILIKE $${++variableIndex}
              OR id::text LIKE $${++variableIndex}
              OR record_id::text LIKE $${variableIndex}
              OR user_ref::text LIKE $${variableIndex}
              ) ` : ''} ${(countOnly ? '' : `ORDER BY ${sortKey === "employer" ? "additional_data -> 'employment_detail' ->> 'employer'" : sortKey} ${sortDir} LIMIT $${++variableIndex} OFFSET $${++variableIndex}`)}`;

            return {sql, variables};
        }

        let {sql: countSql, variables: countQueryVariables} = getSql(true);
        let countResult = await runReadQuery(countSql, countQueryVariables, true);
        total = (countResult.total || 0);

        if (total) {
            // No need to run select query if total is 0
            let {sql: selectSql, variables: selectQueryVariables} = getSql();
            records = await runReadQuery(selectSql, selectQueryVariables);
        }

        sails.log.info(`[getProjectInductionsPage] projectId: ${projectId}, q: "${searchTerm}", filter.status: ${statusCodes || '-'}, filter.users: ${userIds.length}, total: ${total}, page records: ${records.length}`);

        return {total, records};
    },

    getUserInductionEmployer: async (user, projectId, statusCodes= [2, 6], expectedValue='id') => {
        let variables = [user.id, projectId, ...statusCodes];
        let variableIndex = 0;
        let rawResult = await sails.sendNativeQuery(`SELECT id, additional_data->'user_info'->'parent_company' user_employer
                                                 FROM induction_request
                                                 WHERE user_ref = $${++variableIndex} AND project_ref = $${++variableIndex} ${((statusCodes.length) ? `AND status_code IN (${statusCodes.map(() => `$${++variableIndex}`).join(',')})` : '')}
                                                 ORDER BY id DESC LIMIT 1`, variables);
        sails.log.info(`Fetched user employer from induction of user: ${user.id}. expectedValue ${expectedValue}`);

        // Validate rawResult.rows before proceeding
        if (!typeOf(rawResult.rows, 'array') || !rawResult.rows.length) {
            return 0;
        }

        let ir = rawResult.rows[0];
        let employer = ir.user_employer;

        // If employer is an object and has an ID, return based on expectedValue
        if (employer && typeof employer === 'object' && employer.id) {
            return expectedValue === 'id' ? employer.id : employer.name;
        }

        // If expectedValue is 'name' and employer is an ID, fetch from DB
        if (expectedValue === 'name' && +employer) {
            let employerRecord = await sails.models.createemployer_reader.findOne({
                where: { id: employer },
                select: ['name']
            });
            return employerRecord ? employerRecord.name : '';
        }

        return expectedValue === 'id' ? +employer : '';
    },

    getUserEmployerByInductionIds: async (inductionIds) => {
        let startingNoOfEscaped = 0;
        let inClause = inductionIds.map(() => {
            startingNoOfEscaped++;
            return `$${startingNoOfEscaped}`;
        }).join(',');

        sails.log.info(inClause);
        let records = await runReadQuery(`SELECT id, additional_data->'employment_detail'->>'employer' as user_employer
                                                     FROM induction_request
                                                     WHERE id IN (${inClause})`, [...inductionIds]);

        sails.log.info(`[getUserEmployerByInductionIds] inductionIds: ${inductionIds}, total records: ${records.length}`);

        return records;
    },

    getLastOnSiteUsersByProject: async (projectId, fromUnix, toUnix) => {
        let day_of_yr_from = dayjs.unix(fromUnix).format(dbDateFormat_YYYY_MM_DD);
        let day_of_yr_to = dayjs.unix(toUnix).format(dbDateFormat_YYYY_MM_DD);
        let rawResult = await sails.sendNativeQuery(`SELECT id, user_ref, first_in, last_out, TO_CHAR(day_of_yr, 'YYYY-MM-DD') as day_of_yr
                                                             FROM user_daily_log
                                                             WHERE id IN (SELECT MAX(id)
                                                                          FROM user_daily_log
                                                                          WHERE project_ref = $1
                                                                            AND first_in > $2
                                                                            AND ((last_out IS NULL OR last_out < $3) OR ((recent_in > last_out) AND recent_in < $3))
                                                                            AND day_of_yr >= $4
                                                                            AND day_of_yr <= $5
                                                                          AND user_ref IS NOT NULL GROUP BY user_ref);
        `, [projectId, fromUnix, toUnix, day_of_yr_from, day_of_yr_to]);

        sails.log.info(`Fetched user daily logs of project: ${projectId}, from: ${fromUnix}, ${day_of_yr_from}, to: ${toUnix}, ${day_of_yr_to}.`);
        if (typeOf(rawResult.rows, 'array') && rawResult.rows.length) {
            sails.log.info(`Found ${rawResult.rows.length} users with last on site time by project ${projectId}`);
            return (rawResult.rows).map(record => {
                record.user_last_on_site = (record.last_out || record.first_in) ? (record.first_in || record.last_out) : null;
                return record;
            });
        }
        return [];
    },

    getProjectsAndCompaniesAsSeparateListsFn: async (userId, listType) => {
        let sql = `
            SELECT
                ${ listType === 'company'? `DISTINCT parent_company as id,
                contractor as name`: ` id, name `}
            FROM
                project
            WHERE
                id IN (
                    SELECT DISTINCT
                        project_ref
                    FROM
                         induction_request
                    WHERE
                        user_ref = ${userId}
                        and status_code in(2,6)
                );
            `;

            sails.log.info(`[getProjectsAndCompaniesAsSeparateListsFn], userId:${userId}, listType: ${listType}`);
            return await runReadQuery(sql);
    },

    getInductionEmployerByUserIds: async (userIds, projectId, statusCodes, useProfileOnMissingInduction=false) => {
        let variables = [...userIds, projectId, ...statusCodes];
        if(!userIds.length) return [];
        sails.log.info(`get induction employer and user name for users ${userIds} on project ${projectId}`);
        let variableIndex = 0;
        let sql = `SELECT user_ref,
                          concat_ws(' ', additional_data -> 'user_info' ->>'first_name', additional_data -> 'user_info' ->>'last_name') as user_name,
                          additional_data->'employment_detail'->>'employer' user_employer,
                          additional_data->'employment_detail'->>'job_role' job_role
                   FROM induction_request
                   WHERE user_ref IN (${userIds.map(() => `$${++variableIndex}`).join(',')})
                     AND project_ref = $${++variableIndex}
                     AND status_code IN (${statusCodes.map(() => `$${++variableIndex}`).join(',')})
                   ORDER BY id`;
        let records = await runReadQuery(sql, variables);

        if (useProfileOnMissingInduction) {
            sails.log.info("[getInductionEmployerByUserIds]: get data from profile if no induction found");
            let inductedUserIds = (records || []).map(record => +record.user_ref);
            let nonInductedUserIds = userIds.filter(userId => !inductedUserIds.includes(userId));

            sails.log.info(`[getInductionEmployerByUserIds]: got ${nonInductedUserIds.length} user ids to fetch profile data.`, nonInductedUserIds);
            if (nonInductedUserIds.length) {
                let variables = [...nonInductedUserIds];
                let variableIndex = 0;

                let sql = `SELECT u.id user_ref,
                    concat_ws(' ', u.first_name, u.last_name) as user_name,
                    ued.employer user_employer,
                    ued.job_role job_role
                FROM users u
                LEFT JOIN user_employment_detail as ued ON u.id = ued.user_ref
                WHERE user_ref IN (${nonInductedUserIds.map(() => `$${++variableIndex}`).join(',')})`;

                let profilesData = await runReadQuery(sql, variables);
                records = [...records, ...profilesData];
            }
        }

        sails.log.info(`[getInductionEmployerByUserIds] projectId: ${projectId}, records: ${records.length}, useProfileOnMissingInduction: ${useProfileOnMissingInduction}`);
        return records;

    },

    getInductedAdmins: async (
        sortKey = 'id',
        sortDir = 'asc', {
        searchTerm = '',
        projectId = null,
        designations = ['other'],
        statusCodes = [2,6]
        } = {}) => {
        let total = 0;
        let records = [];
        if (!['id', 'name'].includes(sortKey)) {
            sails.log.warn('Invalid sortKey supplied, returning empty results');
            return {total, records};
        }
        sails.log.info(`get inducted admin of project ${projectId} with role ${ROLES.SITE_ADMIN}, resource ${resourceIdentifier.PROJECT(projectId)}, induction status ${statusCodes.join(',')}.`);
        let getSql = (countOnly = false) => {
            let variables = [ROLES.SITE_ADMIN, resourceIdentifier.PROJECT(projectId), projectId, ...statusCodes];

            let designationFilter = '';
            if(designations.length) {
                designationFilter += 'AND (';
                designationFilter += designations.reduce((string, designation, i) => {
                    string +=  `designation LIKE '%${designation}%'`;
                    string += (designations.length-1 !== i) ? ' OR ' : ')';
                    return string;
                }, '');
            }

            if (searchTerm && searchTerm.length) {
                variables.push(`${escapeSearchString(searchTerm)}%`);
            }

            let variableIndex = 0;

            let sql = `SELECT ${countOnly ? ` (count(u.id))::int as total ` : `u.id id, concat_ws(' ',u.first_name, u.last_name) as name `} from user_role_permission urp
                INNER JOIN induction_request ir ON ir.user_ref = urp.user_ref
                INNER JOIN users u ON u.id = ir.user_ref
             WHERE urp.role =  $${++variableIndex} AND urp.resource = $${++variableIndex}
               ${designationFilter}
                AND ir.project_ref = $${++variableIndex}
               AND ir.status_code IN (${statusCodes.map(() => `$${++variableIndex}`).join(',')})
                 AND u.is_active = 1
                 ${(searchTerm && searchTerm.length) ? ` AND (
                            u.first_name ILIKE $${++variableIndex}
                            OR u.last_name ILIKE $${variableIndex}
              ) ` : ''}
                 ${(countOnly ? '' : `ORDER BY ${sortKey} ${sortDir}`)}`;
            return {sql, variables};
        };

        let {sql: countSql, variables: countQueryVariables} = getSql(true);
        let countResult = await runReadQuery(countSql, countQueryVariables, true);
        total = (countResult.total || 0);

        if (total) {
            let {sql: selectSql, variables: selectQueryVariables} = getSql(false);
            records = await runReadQuery(selectSql, selectQueryVariables);
            records = [...new Map(records.map(item => [item['id'], item])).values()];
        }

        return {total, records};
    },

    getUserInductionDetailsFromUserIds : async ( userIds , projectId, limitedData = false) => {
        let variables = [...userIds, projectId];
        let variableIndex = 0;
        const sql = `
            SELECT DISTINCT
                user_ref AS id,
                additional_data -> 'user_info' ->> 'first_name' AS first_name,
                additional_data -> 'user_info' ->> 'middle_name' AS middle_name,
                additional_data -> 'user_info' ->> 'last_name' AS last_name,
                jsonb_build_object(
                    'name', additional_data -> 'employment_detail' ->> 'employer'
                ) AS parent_company
            FROM induction_Request
            WHERE
                user_ref IN (${userIds.map(() => `$${++variableIndex}`).join(',')})
                AND status_code IN (2, 6)
                AND project_ref IN ($${++variableIndex});
        `;

        let usersData = userIds.length ? await runReadQuery(sql, variables) : [];
        return usersData
    }
};
