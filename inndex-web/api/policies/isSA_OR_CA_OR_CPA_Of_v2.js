/**
 * Created by <PERSON><PERSON><PERSON> on 25/04/2024.
 */
const moment = require('moment');
const {HttpService: {typeOf}, ResponseService: {authErrorObject, sendResponse}, TokenUtil: {validateCPAPermissions, ROLES, resourceIdentifier}} = require('./../services');

const DELIVERY_MANAGER = `delivery_management`;

const isSiteAdminFeatureAllowed = (req, uac_row, is_mobile_platform) => {
    let tool_keys = (req.options && req.options.tools) ? req.options.tools : [];
    if (tool_keys.includes('*')) {
        return true;
    }
    let multiple_tools_route = (req.options && req.options.multiple) ? req.options.multiple : false;
    if (!tool_keys.length) {
        // Invalid route declaration, tools array missing or empty
        let action = (req.options && req.options.action) || 'Unknown';
        sails.log.error('UV: Restricted Invalid route declaration, tools array missing or empty', `${action}: ${req.path}`);
        return true;
        //return sendResponse(res, authErrorObject(sails.__('user_does_not_have_permission')));
    }

    if (!multiple_tools_route) {
        let key = tool_keys.shift();
        // if mobile and key is also mobile, then don't append `m_`
        if (is_mobile_platform && !key.startsWith(`m_`)) {
            key = `m_${key}`;
        }

        sails.log.info(`UV: Validating if user is allowed for "${key}:*"`);
        if (uac_row.permission.includes(`${key}:*`)) {
            return true;
        }
        sails.log.info(`UV: Feature "${key}:*" is not allowed.`);
    } else {
        //@todo: this part here needs more improvement
        for (let i = 0; i < tool_keys.length; i++) {
            let key = tool_keys[i];
            // if mobile and key is also mobile, then don't append `m_`
            if (is_mobile_platform && !key.startsWith(`m_`)) {
                key = `m_${key}`;
            }

            sails.log.info(`UV: Validating if user is allowed for "${key}:*", multiple: true`);
            if (uac_row.permission.includes(`${key}:*`)) {
                return true;
            }
        }
    }
    return false;
}

const isInductedUserOf = async (req, projectId) => {
    let userId = req.user.id;

    sails.log.info(`UV: Validating user:${userId} isInductedUserOf project:${projectId}`);
    let [first_induction] = await sails.models.inductionrequest_reader.find({
        where: {
            project_ref: projectId,
            user_ref: userId,
            status_code: [2, 6],    // Not including rejected one
        },
        select: ['id', 'creator_name', 'status_code'],
        sort: ['id'],
        limit: 1
    });

    if(!first_induction){
        sails.log.info(`UV: Restricted, Valid induction record not found, User: ${(req.user && req.user.id)}, ${req.path} Time: ${moment().format()}`);
        return false;
    }
    req.first_induction = first_induction;
    return true;
}

const is_CA_or_CPA_of = (req, parent_company, projectId) => {
    let resource = resourceIdentifier.COMPANY(parent_company);
    sails.log.info(`UV: Validating is COMPANY_ADMIN or COMPANY_PROJECT_ADMIN of ${parent_company} company.`);
    let hasCompanyAdminRole = (req.user.raw_uac || []).find(uac => uac.role === ROLES.COMPANY_ADMIN && uac.resource === resource);
    let companyProjectAdminRole = validateCPAPermissions(req.user.raw_uac, parent_company);
    if (hasCompanyAdminRole) {
        sails.log.info('UV: CA is allowed for companyId:', parent_company);
        req.user.companyAdminRole = hasCompanyAdminRole;
        req.is_verified_ca = true;
        req.is_verified_ca_cpa = true; //Adapting from policy populateAccessVariables.js
        return true;
    } else if (companyProjectAdminRole) {
        if (projectId && !companyProjectAdminRole.includes(projectId)) {
            sails.log.error('UV: Restricted COMPANY_PROJECT_ADMIN API call, User:', (req.user && req.user.id), req.path, `Time: ${moment().format()} Origin HOST:`, req.headers['host'], 'UA:', req.headers['user-agent']);
            return false;
        }
        sails.log.info(`UV: CPA is allowed for companyId: ${parent_company}, projectId: ${projectId}`);
        req.user.companyProjectAdminRole = companyProjectAdminRole;
        req.is_verified_cpa = true;
        req.is_verified_ca_cpa = true; //Adapting from policy populateAccessVariables.js
        return true;
    }
};

module.exports = async function (req, res, next) {
    if(req.user.roles.includes(ROLES.SUPER_ADMIN)) {
        sails.log.info(`UV: Validating is Super Admin.`);
        req.is_super_admin = true;
        return next();
    }

    let projectId = +(req.param('projectId')) || 0;
    let parent_company = +(req.param('employerId') || req.param('companyId')) || 0;
    let is_mobile_platform = req.is_mobile_platform;
    sails.log.info(`UV: Validating is SITE_ADMIN/CA/CPA of ${projectId} project, is_mobile_platform: ${is_mobile_platform}, companyId :${parent_company}`);

    if (!parent_company && projectId) {
        let projectInfo = await sails.models.project_reader.findOne({
            where: {id: projectId},
            select: ['id', 'parent_company']
        });
        parent_company = (projectInfo && projectInfo.parent_company) || 0;
    }

    if (parent_company) {
        sails.log.info('UV: Validate if CA has project site admin designation for companyId:', parent_company);
        if (req.user._uac.project_admins_v1 && req.user._uac.project_admins_v1.includes(parent_company)) {
            req.is_verified_sa = true;
            req.project_admins_v1 = true;
            return next();
        }
    }

    if (projectId) {
        //Equivalent to policy isInductedUserOf.js
        let policy_checks = (req.options && req.options.policy_checks) ? req.options.policy_checks : [];
        if (policy_checks.includes('isInductedUserOf')) {
            let isAllowed = await isInductedUserOf(req, projectId);
            if (isAllowed) {
                return next();
            }
        }

        let resource = resourceIdentifier.PROJECT(projectId);
        let uac_row = (req.user.raw_uac || []).find(uac => uac.role === ROLES.SITE_ADMIN && uac.resource === resource);
        if (uac_row) {
            req.project_uac = uac_row;
            req.is_verified_sa = true;
            let designation = (uac_row.designation || '').split(',');
            sails.log.info(`UV: designation: ${designation}`);

            //Equivalent to policy  isSiteAdminFeatureAllowed.js
            if ((designation || []).includes('custom')) {
                let isAllowed = isSiteAdminFeatureAllowed(req, uac_row, is_mobile_platform);
                if (isAllowed) {
                    return next();
                }
            } else {
                //Equivalent to policy isMoreThanDeliveryManager.js
                if (policy_checks.includes('isMoreThanDeliveryManager')) {
                    let is_delivery_manager_only = ((uac_row.designation) || '') === DELIVERY_MANAGER;
                    sails.log.info("UV: Validating user should be more than a delivery manager.");
                    if (is_delivery_manager_only) {
                        sails.log.error('UV: Restricted User is delivery manager only and not authorize to access the API.');
                        return sendResponse(res, authErrorObject(sails.__('user_does_not_have_permission')));
                    }
                }
                return next();
            }
        }
    }

    if (parent_company) {
        let isAllowed = is_CA_or_CPA_of(req, parent_company, projectId);
        if (isAllowed) {
            return next();
        }
    }

    sails.log.info('UV: Restricted API call, User:', (req.user && req.user.id), `${req.method}: ${req.path}`, `Time: ${moment().format()}`, `Origin platform: ${req.headers['platform'] || '-'}`);
    return sendResponse(res, authErrorObject(sails.__('user_does_not_have_permission')));
};
