/**
 * Created by spatel on 5/9/18.
 */
const {AccessLogService, ResponseService: {authErrorObject, sendResponse}, TokenUtil} = require('./../services');

module.exports = function (req, res, next) {

    let bearerHeader = req.headers["authorization"];
    if (typeof bearerHeader !== 'undefined') {
        let bearer = bearerHeader.split(" ");
        if (bearer[0] !== "Bearer") {
            return res.forbidden(authErrorObject('bearer not understood', true));
        }

        let bearerToken = bearer[1];
        TokenUtil.authenticateUserToken(bearerToken, (err, user) => {
            if(err){
                if(err.name === 'TokenExpiredError'){
                    return res.status(401).send(authErrorObject('Your Logged in Session is expired, please login again', true, {try_refresh: true}));
                }else{
                    sails.log.info(`Bearer token validation failed, token: ${bearerToken}`, 'error:', err);
                    return sendResponse(res, authErrorObject('Error authenticating, please login again', true));
                }
            }

            let action = (req.options && req.options.action) || 'Unknown';
            let platform = req.headers['platform'] || '-';
            AccessLogService.logRequest({
                path: `${req.method}: ${req.path}`,
                action,
                user_agent: req.headers['user-agent'],
                platform: platform,
                auth_token: bearerToken,
                ip: (req.headers['x-forwarded-for'] || req.connection.remoteAddress),
                user_ref: user.id
            }).catch(sails.log.error);

            // @spatel: tracking creation payloads for debugging purpose.
            if(['projectdailyactivities/createdailyactivity',
                'projectdailyactivities/updatedailyactivity',
                'closecall/createclosecall',
                'projectinspectiontour/partialcreateorupdate',
                'inspectionbuilder/createibreport',
                'inspectionbuilder/closeoutibclreportitem',
                'projectinspectiontour/getpendinginspectiontour',
                'projectinspectiontour/updatechecklisttocloseoutitem',
                'projectinspectiontour/getitemstocloseout',
                'projectrams/createrams',
                'inspectionbuilder/updateibreport',
                'inspectionbuilder/createibreportv2'
            ].includes(action)){
                AccessLogService.interactionLog(AccessLogService.EXTERNAL_SERVICE_ID.INNDEX, `user.${user.id}`, true, 200, {
                    method: req.method,
                    url: req.path,
                    payload: (req.allParams() || {}),
                    headers: req.headers,
                    response_body: {},
                    response_headers: (res.headers|| {})
                }, null, action, 'rest').catch(sails.log.error);
            }
            req.user = user;
            req.is_super_admin = (req.user && req.user.roles.includes(TokenUtil.ROLES.SUPER_ADMIN));
            req.is_mobile_platform = platform.indexOf('mobile-') !== -1;
            sails.log.info(`Authenticated id: ${user.id} st:${req.start_time} action: ${action} platform: ${platform}`);
            return next();
        });

    } else {
        return res.forbidden(authErrorObject('No token provide', true));
    }
};
