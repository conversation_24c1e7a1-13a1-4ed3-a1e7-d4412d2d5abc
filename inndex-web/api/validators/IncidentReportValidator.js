const Joi = require('joi');
const { logIfError } = require('./SharedSchema');

module.exports = (incident_meta_data) => {
    const incidentTypes = incident_meta_data.incident_type;
    const personTypes = incident_meta_data.person_type;
    const severities = incident_meta_data.severity;
    const injuryTypes = incident_meta_data.injury_type;
    const affactedParts = incident_meta_data.parts_of_body_effected;
    const locationTypes = incident_meta_data.location_type;
    const vehicleTypes = incident_meta_data.vehicle_type;
    const roadTypes = incident_meta_data.road_type;

    const stringOptSchema = Joi.string().allow('', null).optional();
    const boolNullableReq = Joi.boolean().allow('', null).required();
    const boolNullableFalse = Joi.boolean().allow('', null).valid(false);
    const affactedPersonSchema = Joi.array().items(
        Joi.object().keys({
            person_type: Joi.string().required().valid(...personTypes),
            f_name: Joi.string().required(),
            l_name: Joi.string().required(),
            contact: Joi.alternatives(Joi.number(), Joi.allow('', null)),
            contact_number: Joi.when('contact', {
                is: Joi.forbidden(),
                then: Joi.object({
                    code: Joi.string().required(),
                    number: Joi.string().required()
                })
            }),
            job_title: stringOptSchema,
            gender: Joi.string().required().valid('Man', 'Woman', 'Non Binary', 'Other', 'Prefer not to say'),
            address: stringOptSchema,
        }).unknown()
    );

    const baseSchema = Joi.object({
        incident_type: Joi.string().required().valid(...incidentTypes),
        incident_date: Joi.number().positive().allow('', null).integer(),
        finalised: Joi.boolean().required(),
        location: stringOptSchema,
        incident_details: stringOptSchema,
        incident_category: Joi.alternatives().conditional('incident_type', {
            is: Joi.string().not('Road Traffic').not('Violence or Abuse'), 
            then: Joi.string().allow('', null).required(),
            otherwise: Joi.string().valid('', null).optional() 
        }),
        attachment_file_ids: Joi.array().items(
            Joi.object().keys({
                description: Joi.string().allow('', null).optional(),
                file: Joi.number().required().positive().integer()
            }).unknown()
        ),
        abuse_type: Joi.alternatives().conditional('incident_type', {
            is: "Violence or Abuse",
            then: Joi.string().required().allow('', null).valid("Physical Abuse", "Verbal Abuse"),
            otherwise: Joi.string().valid(null, '')
        }),
        act_type: Joi.alternatives().conditional('incident_type', {
            is: "Unsafe Act/Occurrence",
            then: Joi.string().required().allow('', null).valid("Unsafe Act", "Unsafe Condition"),
            otherwise: Joi.string().valid(null, '')
        }), 
        actual_outcome: Joi.alternatives().conditional('incident_type', {
            is: Joi.valid("Near Miss", "Unsafe Act/Occurrence"),
            then: Joi.string().required().allow('', null).valid("No Damage", "No Injury", "Damage"),
            otherwise: Joi.string().valid(null, '')
        }),
        weather_conditions: Joi.alternatives().conditional('incident_type', {
            is: "Violence or Abuse",
            then: Joi.string().valid(null, ''),
            otherwise: Joi.string().required().allow('', null)
        }),
        injury_caused_by: Joi.alternatives().conditional('incident_type', {
            is: "Injury",
            then: Joi.string().required().allow('', null),
            otherwise: Joi.string().valid(null, '')
        }),
        injury_caused_by_additional: Joi.alternatives().conditional('injury_caused_by', {
            is: "Fell from height",
            then: Joi.string().required().allow('', null),
            otherwise: Joi.string().valid(null, '')
        }),
        is_chartered: Joi.alternatives().conditional('incident_type', {
            is: "Service Strike",
            then: boolNullableReq,
            otherwise: boolNullableFalse
        }),
        potential_severity: Joi.string().allow('', null).required().valid(...severities),
        actual_severity: Joi.string().allow('', null).required().valid(...severities),
        any_witnesses: boolNullableReq,
        witnesses: Joi.alternatives().conditional('any_witnesses', {
            is: true, then: Joi.array().items(
                Joi.object().keys({
                    person_type: Joi.string().required().valid(...personTypes),
                    f_name: Joi.string().required(),
                    l_name: Joi.string().required(),
                    contact: Joi.alternatives(Joi.number(), Joi.allow('', null)),
                    contact_number: Joi.when('contact', {
                        is: Joi.forbidden(),
                        then: Joi.object({
                            code: Joi.string().required(),
                            number: Joi.string().required()
                        })
                    }),
                    comments: Joi.string().allow('', null).optional(),
                }).unknown()
            ), otherwise: Joi.array().max(0)
        }),
        person_affected: Joi.alternatives().conditional('incident_type', [
            { is: "Injury", then: affactedPersonSchema },
            { is: "Health", then: affactedPersonSchema },
            { is: "Violence or Abuse", then: affactedPersonSchema, otherwise: Joi.array().max(0) }
        ]),
        injury_details: Joi.alternatives().conditional('incident_type', {
            is: "Injury",
            then: Joi.array().items(
                Joi.object().keys({
                    injury_type: Joi.string().required().valid(...injuryTypes),
                    affacted_part: Joi.string().required().valid(...affactedParts),
                    injured_side: Joi.string().required().valid("Both", "Left", "Right", "N/A")
                }).unknown()
            ),
            otherwise: Joi.array().max(0)
        }),
        is_onsite_treatment: Joi.alternatives().conditional('incident_type', [
            { is: "Injury", then: boolNullableReq },
            { is: "Health", then: boolNullableReq, otherwise: boolNullableFalse }
        ]),
        site_treatment: Joi.alternatives().conditional('is_onsite_treatment', {
            is: true,
            then: Joi.object().keys({
                details: stringOptSchema,
                administered_by: stringOptSchema,
            }).unknown(),
            otherwise: Joi.object().valid({}, null).unknown(),
        }),
        loc_env_details: Joi.alternatives().conditional('incident_type', {
            is: "Road Traffic",
            then: Joi.object().keys({
                location_type: Joi.string().allow('', null).required().valid(...locationTypes),
                road_type: Joi.alternatives().conditional('location_type', {
                    is: "Road",
                    then: Joi.string().allow('', null).required().valid(...roadTypes),
                    otherwise: Joi.string().valid(null, '')
                }),
                other_road_type: Joi.string().valid(null, ''),
                speed_limit: Joi.alternatives().conditional('location_type', {
                    is: "Road",
                    then: Joi.string().allow('', null).required(),
                    otherwise: Joi.string().valid(null, '')
                }),
                road_width: Joi.alternatives().conditional('location_type', {
                    is: "Road",
                    then: Joi.string().allow('', null).required(),
                    otherwise: Joi.string().valid(null, '')
                }),
                surface_state: stringOptSchema,
                visibility: Joi.string().allow('', null).required().valid("Good", "Satisfactory", "Poor", "Other", "Unknown"),
                other_visibility: Joi.string().valid(null, ''),
            }).unknown(),
            otherwise: Joi.object().valid({}, null)
        }),
        vehicle_details: Joi.alternatives().conditional('incident_type', {
            is: "Road Traffic",
            then: Joi.object().keys({
                reg_number: stringOptSchema,
                type: Joi.string().allow('', null).required().valid(...vehicleTypes),
                other_type: Joi.string().valid(null, ''),
                status: Joi.string().allow('', null).required().valid("Owned", "Hired", "Leased"),
                was_stationary: Joi.boolean().allow('', null),
                speed:  Joi.alternatives().conditional('was_stationary', {
                    is: false,
                    then: Joi.string().required(),
                    otherwise: Joi.string().valid(null, '')
                }),
                was_unattanded: Joi.boolean().allow('', null),
                passengers_count: Joi.alternatives().conditional('was_unattanded', {
                    is: false,
                    then: Joi.number().positive().integer(),
                    otherwise: Joi.number().valid(null, 0)
                }),
                speed: stringOptSchema,
                damage_details: stringOptSchema,
            }).unknown(),
            otherwise: Joi.object().valid({}, null)
        }),
        driver_details: Joi.alternatives().conditional('incident_type', {
            is: "Road Traffic",
            then: Joi.object().keys({
                f_name: stringOptSchema,
                l_name: stringOptSchema,
                contact: Joi.alternatives(Joi.number(), Joi.allow('', null)),
                contact_number: Joi.when('contact', {
                    is: Joi.forbidden(),
                    then: Joi.object({
                        code: Joi.string().required(),
                        number: Joi.string().required()
                    })
                }),
                job_title: stringOptSchema,
                address: stringOptSchema,
            }).unknown(),
            otherwise: Joi.object().valid({}, null)
        }),
        is_thirdparty_vehicle: Joi.alternatives().conditional('incident_type', {
            is: "Road Traffic",
            then: boolNullableReq,
            otherwise: boolNullableFalse
        }),
        thirdparty_vehicle: Joi.alternatives().conditional('is_thirdparty_vehicle', {
            is: true,
            then: Joi.array().items(
                Joi.object().keys({
                    category: Joi.string().required().valid("Person", "Property", "Vehicle"),
                    name: stringOptSchema,
                    address: stringOptSchema,
                    contact: Joi.alternatives(Joi.number(), Joi.allow('', null)),
                    insurer: stringOptSchema,
                    vehicle_damage: Joi.alternatives().conditional('category', {
                        is: "Vehicle",
                        then: Joi.boolean().required(),
                        otherwise: Joi.boolean().valid(false, null)
                    }),
                    property_damage: Joi.alternatives().conditional('category', {
                        is: "Property",
                        then: Joi.boolean().required(),
                        otherwise: Joi.boolean().valid(false, null)
                    }),
                    injury: Joi.alternatives().conditional('category', {
                        is: "Person",
                        then: Joi.boolean().required(),
                        otherwise: Joi.boolean().valid(false, null)
                    }),
                    details: stringOptSchema
                }).unknown()
            ),
            otherwise: Joi.array().max(0)
        }),
        action_details: stringOptSchema,
        incident_actions: Joi.array().items(
            Joi.object().keys({
                category: Joi.string().required(),
                action_detail: Joi.string().required(),
                tag_user_ref: Joi.number().required().positive().integer(),
                priority: Joi.string().required().valid('High', 'Medium', 'Low'),
                due_date: Joi.number().required().positive().integer(),
                id: Joi.number().required().positive().integer(),
                close_out: Joi.object({
                    close_out_at: Joi.number().integer().optional(),
                    details: Joi.string().optional(),
                    images: Joi.array().items(Joi.number()).min(0).optional(),
                    reviewed_by: Joi.string().optional()
                }).optional().unknown(),
                assigned_by_user_ref: Joi.number().required().positive().integer(),
            }).unknown()
        ).unique('id'),
        lighting_condition: Joi.string().required().valid("Daylight", "Dark", "Artificial Light"),
    });

    return {
        createIncidentRecord: (req) => {
            let createSchema = baseSchema.keys({
                project_ref: Joi.number().required(),
                company_ref: Joi.number().allow(null).optional()
            })
            const { error: validationError, value: payload } = createSchema.validate(req.body, { allowUnknown: true });
            logIfError('[createIncidentRecord]', validationError);
            return { validationError, payload };
        },

        updateIncidentRecord: (requestBody) => {
            const { error: validationError, value: payload } = baseSchema.validate(requestBody, { allowUnknown: true });
            logIfError('[updateIncidentRecord]', validationError);
            return { validationError, payload };
        }
    };
}
