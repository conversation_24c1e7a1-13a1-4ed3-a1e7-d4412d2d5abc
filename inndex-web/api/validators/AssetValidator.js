const Joi = require('joi');
const { logIfError } = require('./SharedSchema');

const checklistItemSchema = Joi.array().items(
    Joi.object().keys({
        question_id: Joi.number().unsafe().required(),
        answer: [Joi.boolean().optional(), Joi.string().allow(null, ''), Joi.number().unsafe()]
    })
);

const vehicleChecklistItemSchema = Joi.array().items(
    Joi.object().keys({
        question_id: Joi.number().required(),
        answer: [Joi.boolean().optional(), Joi.string().allow(null, '')]
    })
);

const equipmentChecklistItemSchema = Joi.object().keys({
    checked: Joi.boolean().required(),
    comment: Joi.string().allow(null, ''),
    images: Joi.array().items(Joi.number().optional())
});

const faultSchema = Joi.object().keys({
    fault_id: Joi.alternatives().try(Joi.string().required(), Joi.number().required()),
    fault: Joi.string().required(),
    date_reported: Joi.number().required(),
    reported_by: Joi.string().required(),
    images: Joi.array().items(Joi.number().optional()),
    status: Joi.string().required(),
    closedout_at: Joi.number().optional(),
    closedout_by: Joi.string().optional(),
    closedout_details: Joi.string().optional(),
    closedout_images: Joi.array().items(Joi.number().optional()),
    comments: Joi.array().optional(),
})

const faultsSchema = Joi.array().items(
    faultSchema
);

module.exports = {

    addEquipmentInspection: (req, assetType) => {
        let schema = Joi.object({
            general_checklist: (assetType == 'equipment') ? equipmentChecklistItemSchema : vehicleChecklistItemSchema,
            equipment_specific_checklist: checklistItemSchema,
            specific_checklist: checklistItemSchema,
            fault_details: faultsSchema,
            sign: Joi.string().allow('', null).optional(),
            attachment_file_ref: Joi.array().items(Joi.number().optional()),
            checklist_version: Joi.number().optional(),
        });

        const { error: validationError, value: payload } = schema.validate(req, { allowUnknown: false });
        logIfError('[addEquipmentInspection]', validationError);
        return { validationError, payload };
    },

    closeOutInspectionFault: (req) => {
        let schema = Joi.object({
            fault: faultSchema,
        });

        const { error: validationError, value: payload } = schema.validate(req, { allowUnknown: false });
        logIfError('[closeOutInspectionFault]', validationError);
        return { validationError, payload };
    },

    addTemporaryWorkInspection: (req) => {
        let schema = Joi.object({
            general_checklist: equipmentChecklistItemSchema,
            temporary_work_specific_checklist: checklistItemSchema,
            specific_checklist: checklistItemSchema,
            fault_details: faultsSchema,
            sign: Joi.string().allow('', null).optional(),
            attachment_file_ref: Joi.array().items(Joi.number().optional()),
            checklist_version: Joi.number().optional(),
        });

        const {error: validationError, value: payload} = schema.validate(req, {allowUnknown: false});
        logIfError('[addTemporaryWorkInspection]', validationError);
        
        return {validationError, payload};
    },
}
