const Joi = require('joi');
const { logIfError } = require('./SharedSchema');

module.exports = {
    validateCreateRams: (request) => {
        const schema = Joi.object().keys({
            record_id: Joi.number().optional(),
            briefing_title: Joi.string().required(),
            briefing_file_ref: Joi.number().required(),
            is_available: Joi.boolean().required(),
            tagged_owner: Joi.number().required(),
            include_during_induction: Joi.boolean().required(),
            user_ref: Joi.number().required(),
            project_ref: Joi.number().required(),
            user_revision_ref: Joi.number().required(),
            approved_by: Joi.number(),
            approved_at: Joi.number(),
            status: Joi.number(),
            revision_number: Joi.string().required(),
            revision_status: Joi.boolean(),
            reference_number: Joi.string().required(),
            group_id: Joi.number(),
            is_rams_revision_request: Joi.boolean(),
            ref_id: Joi.optional(),
            auto_approved: Joi.boolean(),
        });

        const { error: validationError, value: payload } = schema.validate(request, { allowUnknown: false });
        logIfError('[validateCreateRams]', validationError);
        return { validationError, payload };
    },
};
