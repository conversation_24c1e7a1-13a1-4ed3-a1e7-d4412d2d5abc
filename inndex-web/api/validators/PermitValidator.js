const Joi = require('joi');
const {intArray} = require('./SharedSchema');
const { logIfError } = require('./SharedSchema');

const permitTemplateCommon = {
    ref_number: Joi.string().required(),
    permit_type: Joi.string().required(),
    is_active: Joi.boolean().required(),
    ref_docs: intArray,
    expire_in: Joi.string().allow(null),
    include_mandatory_attachments: Joi.boolean().required(),
    mandatory_attachments_title: Joi.array().items(Joi.string()),
    require_closeout: Joi.boolean().required(),
    fillable_pdf_ref: Joi.number().required(),
    fillable_pdf_fields: Joi.array().items(
        Joi.object().keys({
            field_id: Joi.number().required(),
            data_field: Joi.string().optional(),
            field_name: Joi.string().required(),
            field_type: Joi.string().required(),
            field_label: Joi.string().required(),
            is_mandatory: Joi.boolean().required(),
            hide_info: Joi.boolean().required(),
            collapse: Joi.boolean(),
            section_id: Joi.number().optional(),
            options: Joi.array().items(
                Joi.object().keys({
                    label: Joi.string().required(),
                })
            ),
            linked_with: Joi.string().optional(),
        })
    ).required(),
    field_sections:  Joi.array().items(
        Joi.object().keys({
            section_id: Joi.number().required(),
            title: Joi.string().required(),
            is_disabled: Joi.boolean().optional(),
            show_options: Joi.boolean().optional()
        })
    ),
    signatures: Joi.array().items(
        Joi.object().keys({
            field_name: Joi.string().required(),
            field_label: Joi.string().required(),
            declaration: Joi.string().allow(null, ''),
            sign_number: Joi.number().allow(null),
            is_requestor: Joi.boolean().required(),
            is_closeout_requestor: Joi.boolean().required(),
            is_closeout: Joi.boolean().required(),
            link_fields: Joi.array().items(Joi.string()),
            link_sections: Joi.array().items(Joi.number()),
            hide_info: Joi.boolean().required(),
            collapse: Joi.boolean(),
        })
    ).required(),
    take_register_when: Joi.string().allow(null),
    register_config: Joi.object().keys({
        signature: Joi.string().optional(),
        user_list: Joi.string().optional(),
    }),
    font_size: Joi.number().required(),
};

const permitTemplate = Joi.object().keys({
    company_ref: Joi.number().required(),
    created_by: Joi.number().required(),
    ...permitTemplateCommon,
});

const updatePermitTemplateValid = Joi.object().keys({
    ...permitTemplateCommon,
});

const status_logs = Joi.array().items(
    Joi.object().keys({
        action: Joi.string(),
        timestamp: Joi.number(),
        note: Joi.string(),
        origin: Joi.string(),
        user_id: Joi.number().optional(),
        name: Joi.string().optional(),
        log: Joi.string().optional(),
        comment: Joi.string().allow(null, ''),
        status: Joi.object().keys({
            from: Joi.number(),
            to: Joi.number()
        }).optional(),
        status_meta: Joi.object().keys({
            label: Joi.string().required(),
            color: Joi.string().required(),
        }).optional(),
        status_code: Joi.number().optional(),
    })
);

const signatures = Joi.array().items(
    Joi.object().keys({
        sign_number: Joi.number().allow(null),
        field_name: Joi.string().required(),
        field_label: Joi.string().required(),
        sign_ref: Joi.number().required(),
        date_signed: Joi.number().required(),
        signatory_user_ref: Joi.number().allow(null),
        is_requestor: Joi.boolean().required(),
        is_closeout_requestor: Joi.boolean().required(),
        is_closeout: Joi.boolean().required(),
        link_fields: Joi.array().items(Joi.string()),
        link_sections: Joi.array().items(Joi.number()),
    })
);

const permitRequestValidatorCommon = {
    mandatory_attachments: Joi.array().items(
        Joi.object().keys({
            title: Joi.string().required(),
            attachment_file_refs: Joi.array().items(Joi.number().required()),
            attachment_type: Joi.string().optional(), //mandatory | section
            section_id: Joi.number().optional(),
        })
    ),
    pdf_fields_value: Joi.array().items(
        Joi.object().keys({
            field_id: Joi.number().required(),
            field_name: Joi.string().required(),
            linked_with: Joi.string().optional(),
            value: Joi.string().allow(null, '')
        })
    ).required(),
    signatures: signatures,
    start_on: Joi.number(),
    expire_on: Joi.number().allow(null),
    state: Joi.number().required(),
    status: Joi.number().required(),
    status_logs,
    users_to_notify: intArray,
    requestor_company: Joi.string().optional(),
};

module.exports = {
    addPermitTemplate: (reqBody) => {
        const {error: validationError, value: payload} = permitTemplate.validate(reqBody, {allowUnknown: false});
        logIfError('[create permit template validation]', validationError);
        return { validationError, payload };
    },

    updatePermitTemplate: (reqBody) => {
        const {error: validationError, value: payload} = updatePermitTemplateValid.validate(reqBody, {allowUnknown: false});
        logIfError('[update permit template validation]', validationError);
        return { validationError, payload };
    },

    validateProjectPermitConfig: (reqBody) => {
        let schema = Joi.array().items(Joi.object().keys({
            id: Joi.number().optional(),
            permit_ref: Joi.number().required(),
            project_ref: Joi.number().required(),
            user_ref: Joi.number().required(),
            is_active: Joi.boolean().required(),
            sign_off: Joi.array().items(
                Joi.object().keys({
                    sign_number: Joi.number().required(),
                    is_requestor: Joi.boolean().optional(),
                    is_closeout_requestor: Joi.boolean().optional(),
                    is_closeout: Joi.boolean().optional(),
                    field_name: Joi.string().required(),
                    field_label: Joi.string().required(),
                    link_fields: Joi.array().items(Joi.string().optional()),
                    link_sections: Joi.array().items(Joi.number().optional()),
                    signatories: Joi.array().items(Joi.number().optional()),
                    state: Joi.number().required()
                })
            ),
            master_permit_managers: Joi.array().items(Joi.number().required()),
        }))

        const {error: validationError, value: payload} = schema.validate(reqBody, {allowUnknown: false});
        logIfError('[save project permit config validation]', validationError);
        return { validationError, payload };
    },

    resubmitPermitRequestValidator: (reqBody) => {
        let schema = Joi.object().keys({
            ...permitRequestValidatorCommon
        });
        const {error: validationError, value: payload} = schema.validate(reqBody, {allowUnknown: false});
        logIfError('[resubmit permit request validation]', validationError);
        return { validationError, payload };
    },

    createPermitRequestValidator: (reqBody) => {
        let schema = Joi.object().keys({
            project_ref: Joi.number().required(),
            requestor_ref: Joi.number().required(),
            permit_ref: Joi.number().required(),
            config_ref: Joi.number().required(),
            ...permitRequestValidatorCommon
        });
        const {error: validationError, value: payload} = schema.validate(reqBody, {allowUnknown: false});
        logIfError('[create permit request validation]', validationError);
        return { validationError, payload };
    },

    signOffRequestValidator: (reqBody) => {
        let schema = Joi.object().keys({
            state: Joi.number().required(),
            status: Joi.number().optional(),
            expire_on: Joi.number().allow(null),
            status_logs,
            signatures: signatures.required(),
            users_to_notify: intArray,
            pdf_fields_value: Joi.array().items(
                Joi.object().keys({
                    field_id: Joi.number().required(),
                    field_name: Joi.string().required(),
                    linked_with: Joi.string().optional(),
                    value: Joi.string().allow(null, '')
                })
            ),
            mandatory_attachments: Joi.array().items(
                Joi.object().keys({
                    title: Joi.string().required(),
                    attachment_file_refs: Joi.array().items(Joi.number().required()),
                    attachment_type: Joi.string().optional(),
                    section_id: Joi.number().optional(),
                })
            )
        });

        const {error: validationError, value: payload} = schema.validate(reqBody, {allowUnknown: false});
        logIfError('[sign-off permit request validation]', validationError);
        return { validationError, payload };
    },

    requestCloseoutNotifyValidator: (reqBody) => {
    let schema = Joi.object().keys({
        sign: Joi.string().optional(),
        users_to_notify: intArray,
        pdf_fields_value: Joi.array().items(
            Joi.object().keys({
                field_id: Joi.number().required(),
                field_name: Joi.string().required(),
                linked_with: Joi.string().optional(),
                value: Joi.string().allow(null, '')
            })
        ),
        mandatory_attachments: Joi.array().items(
            Joi.object().keys({
                title: Joi.string().required(),
                attachment_file_refs: Joi.array().items(Joi.number().required()),
                attachment_type: Joi.string().optional(),
                section_id: Joi.number().optional(),
            })
        )
    });

    const {error: validationError, value: payload} = schema.validate(reqBody, {allowUnknown: false});
    logIfError('[request closeout notify on permit request validation]', validationError);
    return { validationError, payload };
}
}
