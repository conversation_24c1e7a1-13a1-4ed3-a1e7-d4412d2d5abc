const Joi = require('joi');
const { logIfError } = require('./SharedSchema');
const userEmploymentSchema = Joi.object({
    job_role: Joi.string().required(),
    other_job_role: Joi.string().optional().allow(null, ''),
    type_of_employment: Joi.string().required(),
    employment_company: Joi.string().allow(null, ''),
    other_type_of_employment: Joi.string().allow(null, ''),
    employer: Joi.string().required(),
    parent_company: Joi.number().required(),
});

const isFieldOptional =  (validatorList, field) => {
    return (validatorList.activeExclusions?.includes(field) || validatorList.activeOptionalItems?.includes(field));
};

const isFieldMandatory =  (validatorList, field) =>{
    return validatorList.activeMandatoryItems?.includes(field)
        || (!validatorList.activeExclusions?.includes(field)
            && validatorList.globalMandatoryItems?.includes(field));
};

module.exports = {
    saveUserSetting: (req) => {

        const schema = Joi.alternatives()
            .conditional('.name', {
                switch: [
                    {
                        is: 'weekly_timesheet', then: Joi.object({
                            name: Joi.string().required().valid('weekly_timesheet'),
                            value: Joi.boolean().required().valid(true, false),
                        })
                    },
                    {
                        is: Joi.string().regex(/^project_menu_setting_[0-9]+/), then: Joi.object({
                            name: Joi.string().required().regex(/^project_menu_setting_[0-9]+/),
                            value: Joi.object().keys({
                                order: Joi.array().items(Joi.string().required()).required()
                            }).required(),
                        })
                    },
                    {
                        is: 'multifactor_auth_config', then: Joi.object({
                            name: 'multifactor_auth_config',
                            value: Joi.object().keys({
                                two_fa_status: Joi.boolean().required().valid(true, false),
                                two_fa_devices: Joi.array().items(
                                    Joi.object().keys({
                                        label: Joi.string(),
                                        created_at: Joi.number(),
                                        enabled: Joi.boolean(),
                                        setup_key: Joi.string(),
                                        key: Joi.string()
                                    })
                                ).required()
                            }),
                        })
                    },
                    {
                        is: 'user_preferences', then: Joi.object({
                            name: Joi.string().required().valid('user_preferences'),
                            value: Joi.object().keys({
                                locale: Joi.object().keys({
                                    country: Joi.string(),
                                    language: Joi.string(),
                                    locale: Joi.string()
                                })
                            }).required()
                        })
                    },
                ]
            }).label('name');

        const { error: validationError, value: payload } = schema.validate(req.body, { allowUnknown: true });
        logIfError('[saveUserSetting]', validationError);
        return { validationError, payload };
    },

    addDocument: (req) => {
        const schema = Joi.object({
            document_title: Joi.string().required(),
            doc_file_ref: Joi.number().required(),
            employee_ref: Joi.number().required()
        });

        const { error: validationError, value: payload } = schema.validate(req, { allowUnknown: true });
        logIfError('[addDocument]', validationError);
        return { validationError, payload };
    },

    resendDocument: (req) => {
        const schema = Joi.object({
            resend_logs: Joi.array().items(
                Joi.number().required()
            )
        })

        const { error: validationError, value: payload } = schema.validate(req.body, { allowUnknown: true });
        logIfError('[resendDocument]', validationError);
        return { validationError, payload };
    },

    signedDocument: (req, markAs) => {
        const schema = Joi.object({
            sign: Joi.object().keys({
                sign: Joi.string().required(),
                signed_at: Joi.number().required(),
            })
        });

        const { error: validationError, value: payload } = schema.validate(req, { allowUnknown: true });
        logIfError('[signedDocument]', validationError);
        return { validationError, payload };
    },

    userEmploymentUpdate: (req) => {
        sails.log.info("Validating user employment details.");

        const { error: validationError, value: payload } = userEmploymentSchema.validate(req, { allowUnknown: false });
        logIfError('[userEmploymentUpdate]', validationError);
        return { validationError, payload };
    },

    userEmploymentValidate: (req, validatorList = {}) => {
        sails.log.info("[userEmploymentValidate] Validating user employment details, excluding", validatorList.activeExclusions);
        let schema = userEmploymentSchema.keys({
            reporting_to: Joi.string().optional().allow(null, ''),
            operative_type: Joi.string().optional().allow(null, ''),
            start_date_with_employer: Joi.number().required(),
            earn_mlw_e783: Joi.number().required().valid(0, 1),
            employee_number: Joi.string().optional().allow(null, ''),
        });

        if (isFieldMandatory(validatorList, 'profile.employment.employee_number')) {
            schema = schema.keys({
                employee_number: Joi.string().required()
            });
        }

        if (isFieldOptional(validatorList, 'profile.employment.min_wage')) {
            schema = schema.keys({
                earn_mlw_e783: Joi.number().optional().allow(null).valid(0, 1),
            });
        }

        if (isFieldOptional(validatorList, 'profile.employment.start_date')) {
            schema = schema.keys({
                start_date_with_employer: Joi.number().optional().allow(null),
            });
        }

        if (isFieldOptional(validatorList, 'profile.employment.employment_type')) {
            schema = schema.keys({
                type_of_employment: Joi.string().optional().allow(null),
            });
        }

        const { error: validationError, value: payload } = schema.validate(req, { allowUnknown: true });
        logIfError('[userEmploymentValidate]', validationError);
        return { validationError, payload };
    },

    userPersonalDetailValidate: (req) => {
        sails.log.info("[userPersonalDetailValidate] Validating user personal details.");
        const schema = Joi.object({
            country_code: Joi.string().required(),
            parent_company: Joi.number().required(),
            nin: Joi.string().optional().allow('', null)
        });

        const { error: validationError, value: payload } = schema.validate(req, { allowUnknown: false });
        logIfError('[userPersonalDetailValidate]', validationError);
        return { validationError, payload };
    }
};
