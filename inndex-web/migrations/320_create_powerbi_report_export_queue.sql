CREATE TABLE powerbi_report_export_queue
(
    id           serial NOT NULL
        constraint powerbi_report_export_queue_pkey
            PRIMARY KEY,
    emails jsonb default '[]'::jsonb,
    file_location text,
    expiration_time bigint,
    status int,
    additional_detail jsonb default '{}'::jsonb,
    "createdAt"  bigint,
    "updatedAt"  bigint
);

CREATE INDEX preq_et_status_index
    ON powerbi_report_export_queue (expiration_time, status);
