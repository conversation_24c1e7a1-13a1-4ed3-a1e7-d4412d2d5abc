


SELECT count(*) FROM user_file WHERE
                                   (file_url LIKE 'https://inductme-uploads.s3.eu-west-2.amazonaws.com/%' OR
                                    sm_url LIKE 'https://inductme-uploads.s3.eu-west-2.amazonaws.com/%' OR
                                    md_url LIKE 'https://inductme-uploads.s3.eu-west-2.amazonaws.com/%');


SELECT count(*) FROM user_file WHERE (img_translation::text LIKE '%https://inductme-uploads.s3.eu-west-2.amazonaws.com/%');


UPDATE user_file set file_url = REPLACE(file_url, 'https://inductme-uploads.s3.eu-west-2.amazonaws.com', 'https://cdn.inndex.co.uk') WHERE file_url LIKE 'https://inductme-uploads.s3.eu-west-2.amazonaws.com/%';

UPDATE user_file set sm_url = REPLACE(sm_url, 'https://inductme-uploads.s3.eu-west-2.amazonaws.com', 'https://cdn.inndex.co.uk') WHERE sm_url LIKE 'https://inductme-uploads.s3.eu-west-2.amazonaws.com/%';

UPDATE user_file set md_url = REPLACE(md_url, 'https://inductme-uploads.s3.eu-west-2.amazonaws.com', 'https://cdn.inndex.co.uk') WHERE md_url LIKE 'https://inductme-uploads.s3.eu-west-2.amazonaws.com/%';

UPDATE user_file set img_translation = (REPLACE(img_translation::text, 'https://inductme-uploads.s3.eu-west-2.amazonaws.com', 'https://cdn.inndex.co.uk'))::json WHERE (img_translation::text LIKE '%https://inductme-uploads.s3.eu-west-2.amazonaws.com/%');

