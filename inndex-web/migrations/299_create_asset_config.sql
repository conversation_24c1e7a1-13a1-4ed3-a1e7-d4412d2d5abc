CREATE TABLE IF NOT EXISTS asset_custom_config
(
    id           serial NOT NULL
        constraint asset_custom_config_pkey
            PRIMARY KEY,
    name                text,
    key                 text,
    alternate_phrase    text,
    company_ref         bigint,
    asset_type          text,

    default_fields jsonb default '[]'::jsonb,
    custom_fields  jsonb default '[]'::jsonb,
    
    inspection_checklist_type text,
    inspection_checklist jsonb default '[]'::jsonb,

    checklist_draft jsonb default '[]'::jsonb,
    checklist_version int default 1,

    parent_key text,

    "createdAt"   bigint,
    "updatedAt"   bigint
);

alter table asset_custom_config
    add constraint asc_company_ref_fk
        foreign key (company_ref) references employer (id) ON DELETE CASCADE;

-- indexes for asset_custom_config table
CREATE INDEX acc_company_ref ON asset_custom_config (company_ref);
CREATE INDEX acc_company_ref_asset_type ON asset_custom_config (company_ref, asset_type);
CREATE INDEX acc_key_company_ref_asset_type ON asset_custom_config (key, company_ref, asset_type);


comment on table asset_custom_config is 'This table is used to store the config settings of every asset type for a company. It provides the option to configure the show/hide default fields, change the required status of fields, and setup the checklists for an asset type which then are used to perform weekly inspection';


Alter Table project_asset_equipment add custom_fields jsonb default '[]'::jsonb;
Alter Table project_asset_vehicles add custom_fields jsonb default '[]'::jsonb;
Alter Table project_asset_temporary_work add custom_fields jsonb default '[]'::jsonb;

ALTER TABLE asset_custom_config ADD COLUMN checklist_version INT DEFAULT 1;
ALTER TABLE asset_custom_config ADD COLUMN checklist_draft jsonb default '[]'::jsonb;

ALTER TABLE asset_custom_config ADD COLUMN parent_key text default NULL;


-- update asset_custom_config set checklist_version=1;
-- update asset_custom_config set checklist_draft = asset_custom_config.inspection_checklist;

CREATE TABLE asset_inspection_checklist (
     id serial NOT NULL
        constraint inspection_checklist_versions_pkey
            PRIMARY KEY,
    asset_custom_config_ref INT,
    key                 text,
    company_ref         bigint,
    asset_type          text,

    checklist_version INT NOT NULL,
    inspection_checklist jsonb default '[]'::jsonb,
    inspection_checklist_type text,

    "createdAt"   bigint,
    "updatedAt"   bigint
);

alter table asset_inspection_checklist
    add constraint aic_company_ref_fk
        foreign key (company_ref) references employer (id) ON DELETE CASCADE;

alter table asset_inspection_checklist add column inspection_checklist_type text default NULL;

-- indexes for asset_custom_config table
CREATE INDEX aic_asset_custom_config_ref ON asset_inspection_checklist (asset_custom_config_ref);
CREATE INDEX aic_asset_ref_company_ref ON asset_inspection_checklist (asset_custom_config_ref, company_ref);

comment on table asset_inspection_checklist is 'This table is used to store the historic version of checklists against the asset types for a company. Whenever a new version of checklist is published for any asset of a company, we store it and then its used to render Inspection Reports that were performed againt that version.';


ALTER TABLE asset_equipment_inspection ADD COLUMN checklist_version INT DEFAULT 1;
ALTER TABLE asset_vehicle_inspection ADD COLUMN checklist_version INT DEFAULT 1;
ALTER TABLE asset_temporary_work_inspection ADD COLUMN checklist_version INT DEFAULT 1;

UPDATE asset_equipment_inspection SET checklist_version=1;
UPDATE asset_vehicle_inspection SET checklist_version=1;
UPDATE asset_temporary_work_inspection SET checklist_version=1;

ALTER TABLE asset_vehicle_inspection ADD specific_checklist jsonb DEFAULT '[]'::jsonb;

ALTER TABLE public.project_asset_temporary_work ADD COLUMN examination_cert_number TEXT DEFAULT NULL;
ALTER TABLE public.project_asset_temporary_work ADD COLUMN examination_cert_expiry_date BIGINT DEFAULT NULL;
ALTER TABLE public.project_asset_temporary_work ADD COLUMN examination_certificates JSON DEFAULT '[]'::json;