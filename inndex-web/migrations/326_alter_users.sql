ALTER TABLE user_employment_detail ADD employee_number TEXT NULL;

-- Update inndex_setting table to add new country code AE
update inndex_setting
set value = jsonb_set(
    jsonb (value),
    '{AE}',
    jsonb '{
        "exclude": ["profile.employment.nin", "profile.employment.min_wage"],
        "optional": [],
        "mandatory": ["profile.employment.employee_number"]
    }'
            )
WHERE name = 'exclusion_by_country_code';

update inndex_setting
set value = jsonb_set(
    jsonb (value),
    '{ALL}',
    jsonb '{
        "exclude": ["profile.employment.employee_number"],
        "optional": [],
        "mandatory": []
    }'
            )
WHERE name = 'exclusion_by_country_code';


update inndex_setting
set value = jsonb_set(
    jsonb (value),
    '{AE,visibility}',
    jsonb '{
        "project.postcode": {
            "type": "address-lookup"
        },
        "profile.contact.postcode": {
            "type": "address-lookup"
        }
    }')
WHERE name = 'exclusion_by_country_code';


update inndex_setting
set value = jsonb_set(
    jsonb (value),
    '{ALL,visibility}',
    jsonb '{
            "project.postcode": {
                "type": "postcode-lookup"
            },
            "profile.contact.postcode": {
                "type": "postcode-lookup"
            }
    }')
WHERE name = 'exclusion_by_country_code';

