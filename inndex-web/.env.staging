# staging server configs

DATABASE_URL=postgres://vdmWbP4eR62N7R6w:<EMAIL>:5432/inndex_stage
# DB for read only use only
READER_DATABASE_URL=postgres://vdmWbP4eR62N7R6w:<EMAIL>:5432/inndex_stage

STORAGE_DATABASE_URL=postgres://vdmWbP4eR62N7R6w:<EMAIL>:5432/inndex_stage
INTERACTION_LOG_ENABLED=true

# Slow API loger
# `ms` beyond which an API is considered as slow
# 0 is disabled, keep it positive number to enable it
SLOW_API_LOGGING_THRESHOLD=3000

# Token store DB details
TOKEN_STORE_DATABASE_URL=postgres://postgres:root@localhost:5432/token-store

# File Upload & Email sending
# stage-s3-and-email-service
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=A/4ul5yTETrivu1rK8Xs8K5Un6NeVDuoR1c2nxJA

S3_BUCKET=inductme-uploads

#Lambda functions
AWS_REGION_SLS=eu-west-2
NOTIFICATION_FN_KEY=9WshGfegQcR5507nuO6m
LAMBDA_QUEUE_PUSH_MESSAGE_MAILS_FN=inndex-notification-sls-dev-queue-push-notification-v1
LAMBDA_QUEUE_SITE_MESSAGE_MAILS_FN=inndex-notification-sls-dev-queue-email-message-v1
LAMBDA_SINGAL_PDF_FN=inndex-sls-dev-html-to-pdf-v1
LAMBDA_MULTIPLE_PDF_FN=inndex-sls-dev-html-to-multi-pdf-v1
LAMBDA_ZIPPED_IMGS_FN=inndex-sls-dev-archive-images-and-download-v1
LAMBDA_VIDEO_THUMBNAIL_FN=inndex-sls-dev-video-thumbnail-v1
INNDEX_S3_BUCKET=inndex

# URL should have trailing slash
# base url is for API sever domain
# public url is for web ui app / assets domain
BASE_URL=https://test-api.inndex.co.uk/
PUBLIC_URL=https://test.inndex.co.uk/
URL_SUFFIX=

# Email service configs
NEW_PROJECT_MAIL_ALERT_ADDRESSES=<EMAIL>
SOURCE_MAIL_ADDRESS=<EMAIL>
CONTACT_MAIL_ADDRESS=<EMAIL>
SUPPORT_MAIL_ADDRESSES=<EMAIL>

# Email Blocked for
EXCLUDED_EMAIL_ADDRESSES=<EMAIL>

# Test User Email for
TEST_USER_EMAIL_ADDRESSES=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

# Badge Logs Day Cut off time, starting from 0 meaning 12:00AM
CUT_OFF_HOUR=0

# firebase databse URL
FIREBASE_DB_URL=https://inndex-1554668503092-default-rtdb.firebaseio.com/

# Refresh Countries List JSON on start-up
REFRESH_COUNTRIES_LIST_META=true

# Clean-up Old Access logs on start-up
CLEAN_UP_OLD_API_ACCESS_LOGS=true

# DVLA API host, without trailing slash
DVLA_API_HOST_URL=https://driver-vehicle-licensing.api.gov.uk
DVLA_API_KEY=Ws7BeICJvi8gNyXRtnTWHGnxXfUc8xL4mw7Hpg6e

# poppler bin path, for linux only!
POPPLER_BIN_PATH=/usr/bin

# Seeding API auth key
SECURE_AUTH_KEY=yashps8hc56dyy59e5m9tdm1

OPTIMA_LISTENER_KEY=tataucszounvkkctl6n2
OPTIMA_INTEGRATION_SERVER=https://integrations.almas-industries.com/optima/
OPTIMA_GATEWAY_SECRET_KEY=Xp6vADFSpdKz7m4MGVjuX
# Sentry DNS key
SENTRY_DNS=https://<EMAIL>/1432988

# Pro core integration
PRO_CORE_LIVE_MODE=true
# sandbox innDex app
#PRO_CORE_APP_CLIENT_ID=542e8f4a273772d035fcf86658940984f7630c018478f81fd1f95cf23ba488e5
#PRO_CORE_APP_CLIENT_SECRET=210c7210e7eac3c9b41e1dfe9f2e0e6bf748dbb389a465d24cac90a7e3435403
# live innDex app
PRO_CORE_APP_CLIENT_ID=d8f21320306c3ed4724e9b5fc30d56e448577c0732838046ad492e3946de2f2e
PRO_CORE_APP_CLIENT_SECRET=4e2949c2bc3def87a9658f2b1162ff88cfd77eaee059191f8273169877686e2e

# Azure SSO integration
#AZURE_INNDEX_TENANT_ID=da42ad6d-875d-4650-9c62-21db6f73f313
AZURE_INNDEX_TENANT_ID=organizations
# innDex
# AZURE_APP_CLIENT_ID=dff28d0f-8814-4d64-9f38-994b79abd5de
# AZURE_APP_CLIENT_SECRET=****************************************

# innDex app bba91578-a875-4f95-b94b-71b0ae2fbe36
AZURE_APP_CLIENT_ID=bba91578-a875-4f95-b94b-71b0ae2fbe36
AZURE_APP_CLIENT_SECRET=****************************************
AZURE_SSO_TENANT_IDs=da42ad6d-875d-4650-9c62-21db6f73f313

POWERBI_INNDEX_TENANT_ID=da42ad6d-875d-4650-9c62-21db6f73f313
POWERBI_CLIENT_ID=dff28d0f-8814-4d64-9f38-994b79abd5de
POWERBI_CLIENT_SECRET=****************************************
POWERBI_GROUP_ID=0b770624-d259-4eb0-a918-18b25a348dce

# Slack Notifier Web HOOK URL
# channel: #random - yash
#INNDEX_SLACK_BOT_HOOK_URL=
#INNDEX_FIRST_INDUCTION_HOOK_URL=

# Google captcha key
CAPTCHA_SECRET_KEY=6Ld1HJ4UAAAAADVGE4bthtJ6PIrzmnpUElkL3Pz-

# Google Distance Metrix Key, Geocode key
DISTANCE_METRIX_KEY=AIzaSyAgAlZh8t-ALnO5mXszUPLbWXmRNWjQ19A

#CITB_API_SERVER=*****************************/achievements
#CITB_API_P_KEY=1690bc2b16204fd8a9f9773fbd9db6f1

CITB_API_SERVER=*********************************************
CITB_API_P_KEY=bde46d3764d44f7f95ef1b55d38e52be

COUNTRY_LAYER_KEY=f0be21db2291775a29bf7d7354acab3e

ACCU_WEATHER_API_KEY=MugK8oQ4A0wcpQL7cL2iM4VGantidb9G
HOURLY_ACCU_WEATHER_API_KEY=MugK8oQ4A0wcpQL7cL2iM4VGantidb9G

# Enable API execution time log
EXECUTION_TIME_LOGGING=true

# Audit-log-service base url
ALS_BASE_URL=http://127.0.0.1:5001/

# A 256-bit encryption key, encoded in `base64`.
ENC_SECRET_KEY=sXJrmB0jiDrc5PRexY/CKqfmEzqrZQKmTppwKd93waI=

MAPBOX_SECRET_ACCESS_KEY = pk.eyJ1Ijoiam9ldm91c2RlbiIsImEiOiJjbHBzZmRxY3gwM3h3MmpvZG9jbXJ4d3BqIn0.CqgggpYTmDL_G2hIoBpM6Q
