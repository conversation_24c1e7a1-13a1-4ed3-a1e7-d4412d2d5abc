<div class="wrapper">
    <section>
        <div class="section-content">
            <div class="table-outer">
                <h2>
                    <span class="tableHeading">Incident Details</span>
                </h2>
                <div class="table-border-radius">
                    <table class="table bordered-table">
                        <tbody>
                            <% if (ir.incident_type !== 'Road Traffic') { %>
                                <tr>
                                    <td>Location</td>
                                    <td><%= ir.location %></td>
                                </tr>
                            <% } %>

                            <% if (ir.lighting_condition) { %>
                                <tr>
                                    <td>Lighting Conditions</td>
                                    <td><%= ir.lighting_condition %></td>
                                </tr>
                            <% } %>

                            <% if (ir.weather_conditions) { %>
                                <tr>
                                    <td>Weather Conditions</td>
                                    <td><%= ir.weather_conditions %></td>
                                </tr>
                            <% } %>

                            <% if (ir.incident_type == 'Injury' && ir.injury_caused_by) { %>
                                <tr>
                                    <td>Incident Category</td>
                                    <td><%= ir.injury_caused_by %> <%= (ir.injury_caused_by_additional) ? '(' + ir.injury_caused_by_additional + ')' : '' %></td>
                                </tr>
                            <% } %>

                            <% if (ir.incident_type !== 'Road Traffic') { %>
                                <tr>
                                    <td>
                                        <% switch (ir.incident_type) {
                                            case 'Injury' : %>
                                                Incident Classification
                                                <% break;

                                            case 'Health':
                                            case 'Environmental':
                                            case 'Near Miss': 
                                            case 'Unsafe Act/Occurrence': %>
                                                Incident Category
                                                <% break;

                                            case 'Damage or Loss' : %>
                                                Damage/Loss Type
                                                <% break;

                                            case 'Violence or Abuse' : %>
                                                Abuse Type
                                                <% break;

                                            case 'Service Strike' : %>
                                                Type of Service Strike
                                                <% break;

                                        } %>
                                    </td>
                                    <td>
                                        <%= (ir.incident_type == 'Violence or Abuse') ? ir.abuse_type : ir.incident_category %>
                                    </td>
                                </tr>
                            <% } %>
                            <% if (ir.incident_type === 'Unsafe Act/Occurrence') { %>
                                <tr>
                                    <td>
                                        Unsafe Type
                                    </td>
                                    <td>
                                        <%= ir.act_type %>
                                    </td>
                                </tr>

                            <% } %>
                            <% if (ir.incident_type == 'Service Strike') { %>
                                <tr>
                                    <td>Chartered / Unchartered</td>
                                    <td><%= (ir.is_chartered) ? 'Chartered' : 'Unchartered' %></td>
                                </tr>
                            <% } %>
                            <tr>
                                <td>Potential Severity</td>
                                <td><%= ir.potential_severity %></td>
                            </tr>
                            <tr>
                                <td>Actual Severity</td>
                                <td><%= ir.actual_severity %></td>
                            </tr>
                            <% if (ir.incident_type === 'Near Miss' || ir.incident_type === 'Unsafe Act/Occurrence') { %>
                                <tr>
                                    <td>
                                        Actual Outcome
                                    </td>
                                    <td>
                                        <%= ir.actual_outcome %>
                                    </td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            </div>

            <% if (ir.person_affected.length && ["Injury", "Health", "Violence or Abuse"].includes(ir.incident_type)) { %>
                <div class="table-outer">
                    <h2 class="no-page-break-after">
                        <span class="tableHeading">
                            <% switch (ir.incident_type) {
                                case 'Injury' : %>
                                    Details of Injured Person
                                    <% break;

                                case 'Health' : %>
                                    Details of Affected Person
                                    <% break;

                                case 'Violence or Abuse' : %>
                                    Affected Person's
                                    <% break;

                            } %>
                        </span>
                    </h2>
                    <div class="table-border-radius">
                        <table class="table bordered-table">
                            <tbody>
                                <% for (let i = 0; i < ir.person_affected.length; i++) { %>
                                    <% var person = ir.person_affected[i]; %>
                                    <tr>
                                        <td class="gray1">Person Type</td>
                                        <td colspan="3"><%= person.person_type %></td>
                                    </tr>
                                    <tr>
                                        <td class="gray1">First Name</td>
                                        <td colspan="3"><%= person.f_name %></td>
                                    </tr>
                                    <tr>
                                        <td class="gray1">Last Name</td>
                                        <td colspan="3"><%= person.l_name %></td>
                                    </tr>
                                    <tr>
                                        <td class="gray1">Contact Number</td>
                                        <td colspan="3">
                                            <% if (person.contact_number) { %>
                                                <% if (person.contact_number.code) { %>
                                                    (+<%= person.contact_number.code %>) <%= person.contact_number.number %>
                                                <% } else if (person.contact_number.number) { %>
                                                    <%= person.contact_number.number %>
                                                <% } %>
                                            <% } else if (person.contact) { %>
                                                <%= person.contact %>
                                            <% } %>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="gray1">Job Title</td>
                                        <td colspan="3"><%= person.job_title %></td>
                                    </tr>
                                    <tr>
                                        <td class="gray1">Gender</td>
                                        <td colspan="3"><%= person.gender %></td>
                                    </tr>
                                    <tr>
                                        <td class="gray1">Address</td>
                                        <td colspan="3"><%- replaceAll(person.address, '\n', '<br />') %></td>
                                    </tr>

                                    <% if (ir.incident_type == 'Injury' || ir.incident_type == 'Health') { break; } %>

                                <% } %>
                            </tbody>
                        </table>
                    </div>
                </div>
            <% } %>
            <% if (ir.incident_type == 'Road Traffic') { %>
                <div class="table-outer">
                    <h2 class="no-page-break-after">
                        <span class="tableHeading">Location & Environment</span>
                    </h2>
                    <div class="table-border-radius">
                        <table class="table bordered-table">
                            <tbody>
                                <tr>
                                    <td class="gray1">Location Type</td>
                                    <td colspan="3"><%= ir.loc_env_details.location_type %></td>
                                </tr>
                                <tr>
                                    <td class="gray1">Location</td>
                                    <td colspan="3"><%= ir.loc_env_details.location %></td>
                                </tr>
                                <% if (ir.loc_env_details && ir.loc_env_details.location_type == 'Road') { %>
                                    <tr>
                                        <td class="gray1">Type of Road</td>
                                        <td colspan="3"><%= (ir.loc_env_details.road_type == "Other") ? ir.loc_env_details.other_road_type : ir.loc_env_details.road_type; %></td>
                                    </tr>
                                    <tr>
                                        <td class="gray1">Road Speed Limit (MPH)</td>
                                        <td colspan="3"><%= ir.loc_env_details.speed_limit %></td>
                                    </tr>
                                    <tr>
                                        <td class="gray1">Full Width of Road (approx.)</td>
                                        <td colspan="3"><%= ir.loc_env_details.road_width %></td>
                                    </tr>
                                <% } %>
                                <tr>
                                    <td class="gray1">State of Surface</td>
                                    <td colspan="3"><%= ir.loc_env_details.surface_state %></td>
                                </tr>
                                <tr>
                                    <td class="gray1">Visibility</td>
                                    <td colspan="3"><%= (ir.loc_env_details.visibility == "Other") ? ir.loc_env_details.other_visibility : ir.loc_env_details.visibility; %></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            <% } %>
<%- include('./incident-report-p2'); -%>
