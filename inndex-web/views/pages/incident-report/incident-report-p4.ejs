    <div class="table-outer">
        <% if (ir.relevant_personnel_user_refs.length) { %>
            <h2 class="no-page-break-after">
                <span class="tableHeading">Review</span>
            </h2>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="3">Project Personnel Relevant to the Incident</td>
                        </tr>
                        <tr class="no-page-break-after">
                            <td></td>
                            <td><strong>Name</strong></td>
                            <td><strong>Job Role</strong></td>
                        </tr>
                        <% ir.relevant_personnel_user_refs.forEach(function(event, i) { %>
                            <tr>
                                <td style="vertical-align: middle;">
                                    <div><%= event.user_name %></div>
                                    <div class="date"><%= momentTz(+event.added_at, 'DD-MM-YYYY HH:mm:ss') %></div>
                                </td>
                                <td><div><%- event.head %></div></td>
                                <td><div><%- event.subhead %></div></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <% if (ir.incident_events.length) { %>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="2">Events Leading up to the Incident</td>
                        </tr>
                        <% ir.incident_events.forEach(function(event, i) { %>
                            <tr>
                                <td style="vertical-align: middle;">
                                    <div><%= event.user_name %></div>
                                    <div class="date"><%= momentTz(+event.added_at, 'DD-MM-YYYY HH:mm:ss') %></div>
                                </td>
                                <td><div style="white-space: pre-wrap"><%- event.comment %></div></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <% if (ir.incident_harm.length) { %>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="2">Harm</td>
                        </tr>
                        <% ir.incident_harm.forEach(function(harm, i) { %>
                            <tr>
                                <td style="vertical-align: middle;">
                                    <div><%= harm.user_name %></div>
                                    <div class="date"><%= momentTz(+harm.added_at, 'DD-MM-YYYY HH:mm:ss') %></div>
                                </td>
                                <td><div style="white-space: pre-wrap"><%- harm.comment %></div></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <% if (ir.investigation_findings.length) { %>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="2">Investigation Findings</td>
                        </tr>
                        <% ir.investigation_findings.forEach(function(findings, i) { %>
                            <tr>
                                <td style="vertical-align: middle;">
                                    <div><%= findings.user_name %></div>
                                    <div class="date"><%= momentTz(+findings.added_at, 'DD-MM-YYYY HH:mm:ss') %></div>
                                </td>
                                <td><div style="white-space: pre-wrap"><%- findings.comment %></div></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <% if (ir.similar_incidents.length) { %>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="2">History of Similar Incidents</td>
                        </tr>
                        <% ir.similar_incidents.forEach(function(similarIncident, i) { %>
                            <tr>
                                <td style="vertical-align: middle;">
                                    <div><%= similarIncident.user_name %></div>
                                    <div class="date"><%= momentTz(+similarIncident.added_at, 'DD-MM-YYYY HH:mm:ss') %></div>
                                </td>
                                <td><div style="white-space: pre-wrap"><%- similarIncident.comment %></div></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <% if (ir.immediate_causes.length) { %>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="2">Immediate Causes</td>
                        </tr>
                        <% ir.immediate_causes.forEach(function(cause, i) { %>
                            <tr>
                                <td style="vertical-align: middle;">
                                    <div><%= cause.user_name %></div>
                                    <div class="date"><%= momentTz(+cause.added_at, 'DD-MM-YYYY HH:mm:ss') %></div>
                                </td>
                                <% var commenTxt = (cause.head) ? cause.head+': '+cause.subhead : cause.comment;  %>
                                <td><div style="white-space: pre-wrap"><%- commenTxt %></div></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <% if (ir.underlying_causes.length) { %>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="2">Underlying Causes</td>
                        </tr>
                        <% ir.underlying_causes.forEach(function(cause, i) { %>
                            <tr>
                                <td style="vertical-align: middle;">
                                    <div><%= cause.user_name %></div>
                                    <div class="date"><%= momentTz(+cause.added_at, 'DD-MM-YYYY HH:mm:ss') %></div>
                                </td>
                                <% var commenTxt = (cause.head) ? cause.head+': '+cause.subhead : cause.comment;  %>
                                <td><div style="white-space: pre-wrap"><%- commenTxt %></div></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <% if (ir.root_causes.length) { %>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="2">Root Causes</td>
                        </tr>
                        <% ir.root_causes.forEach(function(cause, i) { %>
                            <tr>
                                <td style="vertical-align: middle;">
                                    <div><%= cause.user_name %></div>
                                    <div class="date"><%= momentTz(+cause.added_at, 'DD-MM-YYYY HH:mm:ss') %></div>
                                </td>
                                <% var commenTxt = (cause.head) ? cause.head+': '+cause.subhead : cause.comment;  %>
                                <td><div style="white-space: pre-wrap"><%- commenTxt %></div></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <% if (ir.incident_conclusions.length) { %>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="2">Conclusions</td>
                        </tr>
                        <% ir.incident_conclusions.forEach(function(conclusion, i) { %>
                            <tr>
                                <td style="vertical-align: middle;">
                                    <div><%= conclusion.user_name %></div>
                                    <div class="date"><%= momentTz(+conclusion.added_at, 'DD-MM-YYYY HH:mm:ss') %></div>
                                </td>
                                <td><div style="white-space: pre-wrap"><%- conclusion.comment %></div></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <% if (ir.incident_recommendations.length) { %>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="2">Recommendations</td>
                        </tr>
                        <% ir.incident_recommendations.forEach(function(recommendation, i) { %>
                            <tr>
                                <td style="vertical-align: middle;">
                                    <div><%= recommendation.user_name %></div>
                                    <div class="date"><%= momentTz(+recommendation.added_at, 'DD-MM-YYYY HH:mm:ss') %></div>
                                </td>
                                <td><div style="white-space: pre-wrap"><%- recommendation.comment %></div></td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
        <% var reviewPhotosCount = Object.keys(ir.review_photo_ids).length; %>
        <% if(reviewPhotosCount) { let imageCount = 0; %>
            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr class="no-page-break-after">
                            <td class="head" colspan="2">Attachments</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <% let linkCount = 0; %>
            <% (ir.review_photo_ids || []).forEach(function(attachment, i) { %>
                <% //Showing non-image attachments as linked files %>
                <% if(attachment.file && attachment.file.file_mime && attachment.file.file_mime != 'image/jpeg' && attachment.file.file_mime != 'image/png') { %>
                    <% if (linkCount == 0) { %>
                    <div class="table-border-radius">
                        <table class="wd-100 bordered-table attachment-links">
                            <tbody>
                                <tr class="no-page-break-after" style="background-color: #f6f6f6;">
                                    <td><b>Description</b></td>
                                    <td style="width: 10%;"><b>Type</b></td>
                                    <td style="width: 30%;"><b>Link</b></td>
                                </tr>
                                <% } %>
                                <tr>
                                    <td><%- attachment.description %></td>
                                    <td><%= (attachment.file.file_mime == 'application/pdf') ? "PDF" : "Video" %></td>
                                    <td>
                                        <a href="<%= attachment.file.file_url %>" onclick="window.open('<%= attachment.file.file_url %>','targetWindow', 'toolbar=no, location=no, status=no, menubar=no, scrollbars=yes, resizable=yes, width=' + (+window.screen.height || 450)+ ', height=' + (+window.screen.height || 450)+ ', fullscreen=yes');return false;">
                                            <%= attachment.file.name %>
                                        </a>
                                    </td>
                                </tr>
                                <% linkCount++; %>
                                <% } %>
                                <% if ((reviewPhotosCount-1 == i) && linkCount > 0) { %>
                            </tbody>
                        </table>
                    </div>
                <% } %>
            <% }) %>

            <div class="table-border-radius">
                <table class="table bordered-table review-item">
                    <tbody>
                        <tr>
                            <td style="background-color: #fff;">
                                <% (ir.review_photo_ids || []).forEach(function(reviewPhoto, i) { %>
                                    <% if(reviewPhoto.file && reviewPhoto.file.file_mime && (reviewPhoto.file.file_mime == 'image/jpeg' || reviewPhoto.file.file_mime == 'image/png')) { %>
                                        <div class="progressPhotos">
                                            <div class="ppDescriptionImg">
                                                <img src="<%= (reviewPhoto.file && reviewPhoto.file.sm_url) ? reviewPhoto.file.sm_url : (reviewPhoto.file && reviewPhoto.file.file_url); %>" class="photo">
                                            </div>
                                            <p style="white-space: pre-wrap;text-align: center;min-height: 18px; font-size: 13px; line-height: 15px; margin:0px;overflow:hidden;display: -webkit-box;-webkit-line-clamp: 4;-webkit-box-orient: vertical; "><%- reviewPhoto.description %></p>
                                        </div>
                                        <% imageCount += 1;  %>
                                    <% } %>
                                <% }) %>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        <% } %>

        <% if((ir.incident_actions.length || ir.action_details) || ir.expected_root_cause) { %>
            <h2 class="no-page-break-after">
                <span class="tableHeading">Actions</span>
            </h2>
            <% if(ir.action_details) { %>
                <div class="table-border-radius">
                    <table class="table bordered-table">
                        <tbody>
                            <tr>
                                <td>Initial Actions Taken</td>
                                <td><%- replaceAll(ir.action_details, '\n', '<br>')%></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <% } %>

            <% if(ir.expected_root_cause) { %>
                <div class="table-border-radius">
                    <table class="table bordered-table">
                        <tbody>
                            <tr>
                                <td>Expected Root Cause</td>
                                <td><%= ir.expected_root_cause %></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <% } %>


            <% if(ir.incident_actions.length) { %>
                <% (ir.incident_actions || []).forEach(function(action, i) { %>
                    <div class="table-border-radius no-page-break-inside">
                        <table class="table bordered-table">
                            <tbody>
                                <tr>
                                    <td>Details</td>
                                    <td><%=  action.action_detail %></td>
                                </tr>
                                <tr>
                                    <td>Category</td>
                                    <td><%= action.category %></td>
                                </tr>
                                <tr>
                                    <td>Priority</td>
                                    <td><%= action.priority %></td>
                                </tr>
                                <tr>
                                    <td>Status</td>
                                    <td>
                                        <% let status = (action.close_out && action.close_out.close_out_at) ? "Closed" : "Open"; %>
                                        <strong class="status-<%= status %>"><%= status %></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Tagged User</td>
                                    <td><%= getUserFullName(action.tag_user_ref) %></td>
                                </tr>
                                <tr>
                                    <td>Due Date</td>
                                    <td><%= momentTz(+action.due_date, 'DD-MM-YYYY') %></td>
                                </tr>
                                <tr>
                                    <td>Closeout Details</td>
                                    <td><%= action.close_out && action.close_out.details %></td>
                                </tr>
                                <tr>
                                    <td>Closeout Images</td>
                                    <td>
                                        <% if(action.close_out && action.close_out.images && action.close_out.images.length) { let imgIterator = 1; %>
                                            <% for (let k = 0; k < action.close_out.images.length; k++) { %>
                                                <% let image = action.close_out.images[k] %>
                                                <div class="progressPhotos">
                                                    <div class="closeoutPhoto">
                                                        <img src="<%= (image && image.img_translation.length) ? image.img_translation[0] : (image.sm_url || image.file_url); %>" class="photo">
                                                    </div>
                                                </div>
                                            <% }; %>
                                        <% }; %>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                <% }) %>
            <% } %>
            <% if(ir.closed_out_date) { %>
            <h2 class="no-page-break-after">
                <span class="tableHeading">Incident Closeout</span>
            </h2>
            <div class="table-border-radius">
                <table class="table bordered-table">
                    <tbody>
                        <tr>
                            <td>Closeout Details</td>
                            <td><%= ir.closeout_comment %></td>
                        </tr>
                        <tr>
                            <td>Date of Closeout</td>
                            <td> <%= momentTz(ir.closed_out_date, 'DD-MM-YYYY') %></td>
                        </tr>
                        <tr>
                            <td>Closeout By</td>
                            <td>
                                <%= closedOutBy %>
                            </td>
                        </tr>
                        <tr>
                            <td>Signature</td>
                            <td>
                                <% if(ir.closeout_sign) { %>
                                <img class="sign" src="<%= ir.closeout_sign %>">
                                <% } %>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <% } %>
        <% } %>
    </div>
</div>
</section>
</div>
<% if (auditData && auditData.length) { %>
    <%- include('../change-logs/index'); -%>
<% } %>