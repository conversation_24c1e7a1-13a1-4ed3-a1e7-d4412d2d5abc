<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <link rel="stylesheet" type='text/css' href="https://fonts.googleapis.com/css?family=Open+Sans:400,500,600&display=swap"/>
        <title><%= title || 'Title Here' %></title>
        <style type="text/css">
            html, body {
                font-size: 12px;
                line-height: 18px;
            }
            body{
                background-color: #999;
            }
            .wrapper{
                background-color: #fff;
                margin: 35px auto;
            }
            h2 .fa, h2 .fas{
                padding: 0 0 0 5px;
            }
            img,legend{border:0}legend,td,th{padding:0}body,figure{margin:0}caption,th{text-align:left}h3{text-transform:uppercase}h2,h4{text-transform:capitalize}a:focus,a:hover,h5{text-decoration:underline}html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent;color:#337ab7;text-decoration:none}a:active,a:hover{outline:0}abbr[title]{border-bottom:1px dotted}b,optgroup,strong{font-weight:700}dfn{font-style:italic}mark{background:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}img{vertical-align:middle}svg:not(:root){overflow:hidden}hr{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}pre,textarea{overflow:auto}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}button{overflow:visible}button,select{text-transform:none}button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}input[type=checkbox],input[type=radio]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:0}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}input[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}*,:after,:before{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}html{font-size:10px;-webkit-tap-highlight-color:transparent}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit}a:focus,a:hover{color:#23527c}a:focus{outline:-webkit-focus-ring-color auto 5px;outline-offset:-2px}.img-responsive{display:block;max-width:100%;height:auto}.img-rounded{border-radius:6px}.img-thumbnail{padding:4px;line-height:1.42857143;background-color:#fff;border:1px solid #ddd;border-radius:4px;-webkit-transition:all .2s ease-in-out;-o-transition:all .2s ease-in-out;transition:all .2s ease-in-out;display:inline-block;max-width:100%;height:auto}.img-circle{border-radius:50%}hr{margin-top:20px;margin-bottom:20px;border:0}.sr-only{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}[role=button]{cursor:pointer}table{border-collapse:collapse;border-spacing:0;background-color:transparent}caption{padding-top:8px;padding-bottom:8px;color:#777}.table{width:100%;max-width:100%;margin-bottom:20px}.table:not(.tp-0)>tbody>tr>td,.table>tbody>tr>th,.table>tfoot>tr>td,.table>tfoot>tr>th,.table>thead>tr>td,.table>thead>tr>th{padding:4px;line-height:1.42857143;vertical-align:top}.table>thead>tr>th{vertical-align:bottom}.table .table{background-color:#fff}.table-condensed>tbody>tr>td,.table-condensed>tbody>tr>th,.table-condensed>tfoot>tr>td,.table-condensed>tfoot>tr>th,.table-condensed>thead>tr>td,.table-condensed>thead>tr>th{padding:5px}.wrapper,h2{padding-left:15px}.table-striped>tbody>tr:nth-of-type(odd){background-color:#f9f9f9}.table-hover>tbody>tr:hover,.table>tbody>tr.active>td,.table>tbody>tr.active>th,.table>tbody>tr>td.active,.table>tbody>tr>th.active,.table>tfoot>tr.active>td,.table>tfoot>tr.active>th,.table>tfoot>tr>td.active,.table>tfoot>tr>th.active,.table>thead>tr.active>td,.table>thead>tr.active>th,.table>thead>tr>td.active,.table>thead>tr>th.active{background-color:#f5f5f5}table col[class*=col-]{position:static;float:none;display:table-column}table td[class*=col-],table th[class*=col-]{position:static;float:none;display:table-cell}.table-hover>tbody>tr.active:hover>td,.table-hover>tbody>tr.active:hover>th,.table-hover>tbody>tr:hover>.active,.table-hover>tbody>tr>td.active:hover,.table-hover>tbody>tr>th.active:hover{background-color:#e8e8e8}.table>tbody>tr.success>td,.table>tbody>tr.success>th,.table>tbody>tr>td.success,.table>tbody>tr>th.success,.table>tfoot>tr.success>td,.table>tfoot>tr.success>th,.table>tfoot>tr>td.success,.table>tfoot>tr>th.success,.table>thead>tr.success>td,.table>thead>tr.success>th,.table>thead>tr>td.success,.table>thead>tr>th.success{background-color:#dff0d8}.table-hover>tbody>tr.success:hover>td,.table-hover>tbody>tr.success:hover>th,.table-hover>tbody>tr:hover>.success,.table-hover>tbody>tr>td.success:hover,.table-hover>tbody>tr>th.success:hover{background-color:#d0e9c6}.table>tbody>tr.info>td,.table>tbody>tr.info>th,.table>tbody>tr>td.info,.table>tbody>tr>th.info,.table>tfoot>tr.info>td,.table>tfoot>tr.info>th,.table>tfoot>tr>td.info,.table>tfoot>tr>th.info,.table>thead>tr.info>td,.table>thead>tr.info>th,.table>thead>tr>td.info,.table>thead>tr>th.info{background-color:#d9edf7}.table-hover>tbody>tr.info:hover>td,.table-hover>tbody>tr.info:hover>th,.table-hover>tbody>tr:hover>.info,.table-hover>tbody>tr>td.info:hover,.table-hover>tbody>tr>th.info:hover{background-color:#c4e3f3}.table>tbody>tr.warning>td,.table>tbody>tr.warning>th,.table>tbody>tr>td.warning,.table>tbody>tr>th.warning,.table>tfoot>tr.warning>td,.table>tfoot>tr.warning>th,.table>tfoot>tr>td.warning,.table>tfoot>tr>th.warning,.table>thead>tr.warning>td,.table>thead>tr.warning>th,.table>thead>tr>td.warning,.table>thead>tr>th.warning{background-color:#fcf8e3}.table-hover>tbody>tr.warning:hover>td,.table-hover>tbody>tr.warning:hover>th,.table-hover>tbody>tr:hover>.warning,.table-hover>tbody>tr>td.warning:hover,.table-hover>tbody>tr>th.warning:hover{background-color:#faf2cc}.table>tbody>tr.danger>td,.table>tbody>tr.danger>th,.table>tbody>tr>td.danger,.table>tbody>tr>th.danger,.table>tfoot>tr.danger>td,.table>tfoot>tr.danger>th,.table>tfoot>tr>td.danger,.table>tfoot>tr>th.danger,.table>thead>tr.danger>td,.table>thead>tr.danger>th,.table>thead>tr>td.danger,.table>thead>tr>th.danger{background-color:#f2dede}.table-hover>tbody>tr.danger:hover>td,.table-hover>tbody>tr.danger:hover>th,.table-hover>tbody>tr:hover>.danger,.table-hover>tbody>tr>td.danger:hover,.table-hover>tbody>tr>th.danger:hover{background-color:#ebcccc}
            .text-hide,.wrapper td:before{background-color:transparent}.table-responsive{overflow-x:auto;min-height:.01%}@media screen and (max-width:767px){.table-responsive>.table{margin-bottom:0}.table-responsive>.table>tbody>tr>td,.table-responsive>.table>tbody>tr>th,.table-responsive>.table>tfoot>tr>td,.table-responsive>.table>tfoot>tr>th,.table-responsive>.table>thead>tr>td,.table-responsive>.table>thead>tr>th{white-space:nowrap}}.clearfix:after,.clearfix:before{display:table}.center-block{display:block;margin-left:auto;margin-right:auto}.pull-right{float:right!important}.pull-left{float:left!important}.hide{display:none!important}.show{display:block!important}.invisible{visibility:hidden}.text-hide{font:0/0 a;color:transparent;text-shadow:none;border:0}h1,h2{font-weight:700}.hidden{display:none!important}.affix{position:fixed}
            body{background-color:#fff;font-family:'Open Sans',Arial,Helvetica,sans-serif;font-size:14px;color:#000; -webkit-print-color-adjust: exact;}
            .wrapper{width:100%;max-width:1170px;margin:auto;padding-right:15px}footer p,h1,h2,h3,h4,h5,h6{margin:0}.text-center{text-align:center}h1{font-size:20px;display:inline-block;text-decoration:underline;}h2,h3{font-size:18px}
            h2{margin-bottom:10px;background-color: #E9E9E9;line-height: 20px;color: #14152d; font-size: 15px;padding: 5px 8px;}
            h3,h4{font-weight:400}h3{font-style:italic}h4{font-size:12px}h5,h6{font-size:13px;font-style:italic}h5{font-weight:700;margin-bottom:5px}h6{font-weight:400;vertical-align: text-top;}.padding-sm{/*padding:50px 0*/}
            .header-inner{position:relative;}
            .table-active, .table-active>td, .table-active>th {
                background-color: rgba(0,0,0,.075);
            }
            .wd-30 td:first-child {width:30%;padding-left:15px}
            table.table-bordered > tbody > tr {
                border-top: 1px solid #bbb;
            }
            table.table-bordered > tbody > tr:last-child {
                border-bottom: 1px solid #bbb;
            }
            table.table-bordered > tbody > tr > td {
                border-top: 0 !important;
                border-bottom: 0 !important;
                background-color: #fff;
                padding: 8px 4px !important;
            }
            .table, .coverInfoTable {font-size: 14px;}

            table.bordered-table, table.bordered-table th, table.bordered-table td {
                border-collapse: collapse;
                margin-bottom: 0px !important;
            }
            table.bordered-table th, table.bordered-table td {
                border: 2px solid #B2B2B2 !important;
                padding: 5px 10px !important;
            }
            .logo{padding-top: 8px;}
            .logo img{max-height: 80px;max-width: 240px;}.wd-25 tr th{width:25%}.wd-100{width:100%}.wd-33 td{width:33.33%;padding-left:15px}.wd-50 td{width:50%;padding-left:15px}.wd-70 td:first-child{width:70%;padding-left:15px}.wd-70 td:last-child{width:30%;padding-left:15px}.wd-80 td:first-child{width:80%;padding-left:15px}.wd-20 td:first-child,.wd-80 td:last-child{width:20%;padding-left:15px}.wd-20 td:last-child{width:80%;padding-left:15px}hr{width:100%;height:1px;background-color:#4a7ebb;border-top:0;margin:0 0 15px}.table>tbody>tr>td{position:relative;padding-left:15px}
            .shiftReport td:before{content:'';position:absolute;width:5px;height:5px;border:1px solid #000;border-radius:50%;top:14px;left:-6px}.no-dot:before{display:none}.float-left{float:left}.float-right{float:right}.clearfix:after,.clearfix:before{content:" "}.clearfix:after{clear:both}.full-width{width:100%}h3.bold{font-style:normal}.vh-center{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);-moz-transform:translate(-50%,-50%)}.pd-sm.table>tbody>tr>td{padding:2px 10px}

            .page-b-always{
                page-break-before: always;
            }
            footer{
                color: #6c757d!important;
            }
            :root {
                --borderWidth: 3px;
                --height: 10px;
                --width: 6px;
                --borderColor: #78b13f;
            }
            .page-b-before { page-break-before: always; }
            .page-b-after { page-break-after: always; }
            .borderBottom-2 {
                border-bottom: 2px solid;
            }

            .progressPhotos { width: 90%; margin: 0 auto; }
            .progressPhotos {
            width: 49%;
            display: inline-block;
            vertical-align: top;
            }
            .ppDescriptionImg {height: 375px; margin: 5px auto; text-align: center; }
            .photoHr {
                width: 100%;
                margin: 10px 0px;
                border-top: 2px solid #97a1b5;
            }
            .photo {
                max-height: 100%;
                max-width: 100%;
                border-radius: 3px;
            }
            .closeoutPhoto { height: 250px; margin: 5px auto; text-align: center; }
            .coverPageTable, .coverInfoTable { table-layout: fixed; width: 100%; }
            .section-content .table-outer h2 {
                text-align: initial;
                border-radius: 3px;
            }
            .section-content .table-outer h2 .tableHeading {
                vertical-align: text-top;
            }

            .coverInfoTable {margin-bottom: 10px;}
            .coverInfoTable tr:first-child td > div {
                background-color: #E9E9E9;
                margin-right: 10px;
                display: flex;
                padding: 5px 12px;
                border-radius: 3px;
            }
            .coverInfoTable tr:first-child td > div > span {
                width: 50%;
                text-align: left;
                font-weight: 500;
            }
            .coverInfoTable tr:first-child td > div > span:last-child {text-align: right;}
            .coverInfoTable tr:first-child td:last-child > div {margin-right: 0px;}
            .table-border-radius {
                border: 2px solid #B2B2B2;
                border-radius: 3px;
                margin-bottom: 20px;
            }
            .table:not(.review-actions-data)>tbody>tr>td:first-child {
                width: 19%;
                font-weight: bold;
                background-color: #f6f6f6;
            }
            .injury-treatment-table.table>tbody>tr>td:first-child {
            width: 29%;
            }
            .witnesses-table.table>tbody>tr>td:first-child {
            width: 32%;
            }
            .table>tbody>tr>td.italic-offset{
                padding-left: 45px !important;
                font-style: italic;
            }
            .page-b-before table.table { margin-bottom: 8px !important; }
            .witnesses-table.table > tbody > tr:not(:first-child) > td, tr.detail-row-group>td {
                border-top-width: 1px !important;
                border-bottom-width: 1px !important;
            }
            .witnesses-table.table > tbody > tr:nth-child(4n - 2) > td {
                border-top-width: 2px !important;
            }

            .review-item > tbody > tr:first-child > td.head {
                text-align: center;background-color: #c6c6c6 !important;
            }
            .review-item > tbody > tr > td > div.date {font-size: 12px;font-weight: normal;}
            .review-actions-data thead > tr > th {
                padding: 14px 10px !important;/*text-align: center;*/background-color: #f6f6f6 !important;
            }
            .review-actions-data tbody > tr > td {
                padding: 10px!important;
                border-width: 1px !important;
            }
            .review-actions-data > tbody > tr:nth-child(2n) > td {
                border-bottom-width: 2px !important;
            }
            .review-actions-data > tbody > tr:nth-child(4n+3) > td, .review-actions-data > tbody > tr:nth-child(4n+4) > td {
                background-color: #F6F6F6;
            }
            .priority-High, .status-Open {
                color: #e30613;
            }
            .priority-Medium {
                color: #edb520;
            }
            .priority-Low, .status-Closed {
                color: #008d36;
            }
            .review-actions-data thead > tr > th:nth-child(5), .review-actions-data thead > tr > th:nth-child(2), .review-actions-data thead > tr > th:nth-child(3),
            .review-actions-data tbody > tr > td:nth-child(5), .review-actions-data tbody > tr > td:nth-child(2), .review-actions-data tbody > tr > td:nth-child(3){
                width: 13%;
            }
            .review-actions-data thead > tr > th:nth-child(4), .review-actions-data tbody > tr > td:nth-child(4) {
                width: 20%;
            }
            .review-actions-data thead > tr > th:nth-child(1), .review-actions-data tbody > tr > td:nth-child(1) {
                width: 40%;
            }
            table.logs>thead>tr>th {
                font-size: 15px !important;
                font-weight: 600;
            }
            .nested-header {
                background-color: white !important;
            }
            .bordered-table.attachment-links td:nth-child(2) { width: 10%;}
            .bordered-table.attachment-links td:nth-child(3) { width: 30%;}
        </style>
        <style type="text/css">
            @media print {
                html,body{
                    height: 100%;
                    margin: 0;
                }
                .wrapper{
                    display: block;
                    position: relative;
                    padding-bottom: 28px;
                }
                .table.bordered-table {
                    border-style: hidden !important;
                }
                table.bordered-table th, table.bordered-table td {
                    padding: 4px 10px !important;
                }
                h1{font-size: 14px;}
                h2{font-size: 13px;}
                h4{font-size: 11px;}
                h6{font-size: 10px;}
                footer {font-size: 10px !important;}
                .table, .coverInfoTable {font-size: 13px;}
                .table>tbody>tr>td:first-child { width: 22%;}
                .injury-treatment-table.table>tbody>tr>td:first-child {
                    width: 34%;
                }
                .witnesses-table.table>tbody>tr>td:first-child {
                    width: 38%;
                }
                
                .table-outer table.review-item tr {
                    page-break-inside: avoid;
                }
                .no-page-break-after {
                    page-break-after: avoid;
                }
                .no-page-break-inside {
                    break-inside: avoid;
                    page-break-before: always; 
                }


            }
        </style>
    </head>
    <body>
        <%- include('./incident-report/incident-report-form-cover'); -%>
        <%- include('./incident-report/incident-report-p1'); -%>
    </body>
</html>
