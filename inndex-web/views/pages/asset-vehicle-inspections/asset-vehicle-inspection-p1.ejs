<div class="wrapper">
    <div class="margin-bottom" style="min-height: 1024px;text-align: center; line-height: 25px;background-color: #ffffff;">
        <div class="wd-100" style="display: inline-block; margin: 10px auto 10px auto;">
            <div style="width: 140px; float: left;">
                <% if(project_logo_file && project_logo_file.id && project_logo_file.file_url) { %>
                    <img src="<%= project_logo_file.file_url %>" style="max-width: 100%">
                <% } else { %>
                    <img src="data:image/png;base64,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"
                            style="max-width: 100%">
                <% } %>
            </div>
        </div>

        <div class="wd-100" style="display: inline-block; margin-bottom: 6px;">
            <span style="float: left;"><h1><%= title %></h1></span>
        </div>

        <div class="wd-100" style="display: inline-block; color: #97a1b5; margin-bottom: 6px;">
            <span style="font: bold 16px Lato !important; float: left;">
                <%= project.name %>
            </span>
        </div>

        <div class="wd-100" style="display: inline-block; color: #97a1b5; margin-bottom: 6px;">
            <span style="font: bold 16px Lato !important; float: left;">Week Commencing: <%= momentTz(weekCommencingMs, 'DD MMM YYYY') %></span>
        </div>

        <div class="wd-100" style="margin-bottom: 6px;">
            <table class="coverPageTable" style="margin-bottom: 10px;">
                <tbody>
                    <tr style="background-color: #ededed;">
                        <td style="padding: 3px 8px;">
                            Vehicle Details
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="table-border-radius">
                <table class="table bordered-table inspection-detail-table" style="margin-bottom: 0 !important;border-style: hidden;">
                    <tbody>
                        <tr>
                            <td>
                                <span>Vehicle Registration/Serial Number</span>
                            </td>
                            <td colspan="3">
                                <span><%= assetRecord.serial_number %></span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span>Vehicle ID</span>
                            </td>
                            <td colspan="3">
                                <span><%= assetRecord.vehicle_id %></span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span>Vehicle Type</span>
                            </td>
                            <td colspan="3">
                                <span><%= getAssetTypeTitle(assetRecord.type_of_vehicle) %></span>
                            </td>
                        </tr>
                        <% if (assetRecord.examination_certificates && assetRecord.examination_certificates.length) { %>
                            <tr>
                                <td>
                                    <span>Thorough Examination Certificate</span>
                                </td>
                                <td style="padding: 0px!important;">
                                    <div style="display: inline-grid; width: 100%;">
                                        <span style="padding: 3px 8px; border-bottom: 0.7px solid #ededed; font-weight: bold; background-color: #ededed;">Document Number</span>
                                        <span style="padding: 3px 8px;"><%= assetRecord.examination_cert_number %></span>
                                    </div>
                                </td>
                                <td style="padding: 0px!important;">
                                    <div style="display: inline-grid; width: 100%;">
                                        <span style="padding: 3px 8px; border-bottom: 0.7px solid #ededed; font-weight: bold; background-color: #ededed;">Expiry Date</span>
                                        <span style="padding: 3px 8px;"><%= momentTz(assetRecord.examination_cert_expiry_date, "DD-MM-YYYY") %></span>
                                    </div>
                                </td>
                                <td style="padding: 0px!important;">
                                    <div style="display: inline-grid; width: 100%;">
                                        <span style="padding: 3px 8px; border-bottom: 0.7px solid #ededed; font-weight: bold; background-color: #ededed;">Attachments</span>
                                        <span style="padding: 3px 8px; display: inline-grid;">
                                            <% (assetRecord.examination_certificates || []).forEach(function(certificate, i) { %>
                                                <a href="<%= certificate.file_url %>"
                                                onclick="window.open('<%= certificate.file_url %>','targetWindow', 'toolbar=no, location=no, status=no, menubar=no, scrollbars=yes, resizable=yes, width=' + (+window.screen.height || 450)+ ', height=' + (+window.screen.height || 450)+ ', fullscreen=yes');return false;">
                                                            <%= certificate.name %>
                                                        </a>
                                            <% }) %>
                                        </span>
                                    </div>
                                </td>
                            </tr>
                        <% } %>
                        <% if (assetRecord.service_certificates && assetRecord.service_certificates.length) { %>
                            <tr>
                                <td>
                                    <span>Service</span>
                                </td>
                                <td style="padding: 0px!important;" colspan="2">
                                    <div style="display: inline-grid; width: 100%;">
                                        <span style="padding: 3px 8px; border-bottom: 0.7px solid #ededed; font-weight: bold; background-color: #ededed;">Expiry Date</span>
                                        <span style="padding: 3px 8px;"><%= momentTz(assetRecord.service_expiry_date, "DD-MM-YYYY") %></span>
                                    </div>
                                </td>
                                <td style="padding: 0px!important;">
                                    <div style="display: inline-grid; width: 100%;">
                                        <span style="padding: 3px 8px; border-bottom: 0.7px solid #ededed; font-weight: bold; background-color: #ededed;">Attachments</span>
                                        <span style="padding: 3px 8px; display: inline-grid;">
                                            <% (assetRecord.service_certificates || []).forEach(function(certificate, i) { %>
                                                <a href="<%= certificate.file_url %>"
                                                onclick="window.open('<%= certificate.file_url %>','targetWindow', 'toolbar=no, location=no, status=no, menubar=no, scrollbars=yes, resizable=yes, width=' + (+window.screen.height || 450)+ ', height=' + (+window.screen.height || 450)+ ', fullscreen=yes');return false;">
                                                                <%= certificate.name %>
                                                            </a>
                                            <% }) %>
                                        </span>
                                    </div>
                                </td>
                            </tr>
                        <% } %>
                        <% if (assetRecord.mot_certificates && assetRecord.mot_certificates.length) { %>
                            <tr>
                                <td>
                                    <span>MOT</span>
                                </td>
                                <td style="padding: 0px!important;" colspan="2">
                                    <div style="display: inline-grid; width: 100%;">
                                        <span style="padding: 3px 8px; border-bottom: 0.7px solid #ededed; font-weight: bold; background-color: #ededed;">Expiry Date</span>
                                        <span style="padding: 3px 8px;"><%= momentTz(assetRecord.mot_expiry_date, "DD-MM-YYYY") %></span>
                                    </div>
                                </td>
                                <td style="padding: 0px!important;">
                                    <div style="display: inline-grid; width: 100%;">
                                        <span style="padding: 3px 8px; border-bottom: 0.7px solid #ededed; font-weight: bold; background-color: #ededed;">Attachments</span>
                                        <span style="padding: 3px 8px; display: inline-grid;">
                                            <% (assetRecord.mot_certificates || []).forEach(function(certificate, i) { %>
                                                <a href="<%= certificate.file_url %>"
                                                onclick="window.open('<%= certificate.file_url %>','targetWindow', 'toolbar=no, location=no, status=no, menubar=no, scrollbars=yes, resizable=yes, width=' + (+window.screen.height || 450)+ ', height=' + (+window.screen.height || 450)+ ', fullscreen=yes');return false;">
                                                                <%= certificate.name %>
                                                            </a>
                                            <% }) %>
                                        </span>
                                    </div>
                                </td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="wd-100" style="margin-bottom: 8px;">
            <table class="coverPageTable" style="margin-bottom: 10px;">
                <tbody>
                    <tr style="background-color: #ededed;">
                        <td style="padding: 3px 8px;">
                            Vehicle Checklist (Version <%= checklist_version %>)
                        </td>
                    </tr>
                </tbody>
            </table>
            <table class="table bordered-table checklistTable" style="font-size: 13px;">
                <thead>
                    <tr>
                        
                        <% if(metaChecklistCategories.length >=1 || Object.keys(checkListItems).length >=1) { %>
                            <th colspan="2"
                            style="border-top: 0.7px solid #ffffff !important; border-left: 0.7px solid #ffffff !important;"></th>
                        <% } else { %>
                            <th
                            style="border-top: 0.7px solid #ffffff !important; border-left: 0.7px solid #ffffff !important;"></th>
                            
                        <% } %>
                        <% if(Object.keys(assetInspections).length) { %>
                            <% for (let index in daysWithDate) { %>
                                <th style="background: #ededed; text-align: center;"><%= daysWithDate[index] %></th>
                            <% } %>
                        <% } %>
                    </tr>
                    <tr style="background: #ededed;">
                        <th style="text-align: center;">No.</th>
                        <% if(metaChecklistCategories.length >=1 || Object.keys(checkListItems).length >=1) { %> <th>Item</th> <% } %>
                        <% for (let key in days) { %>
                            <th style="text-align: center;"><%= key %></th>
                        <% } %>
                    </tr>
                </thead>
                <tbody>
                    <% let i = 1; for (key in checkListItems) { %>
                        <tr>
                            <td style="background: #ededed; text-align: center;"><%= pad(i) %></td>
                            <td><%= checkListItems[key] %></td>
                            <% if(Object.keys(assetInspections).length) { %>
                                <% for (let day in days) { %>
                                    <% if(Object.keys(days[day]).length) { %>
                                        <% if (days[day][key] && !['mileage', 'fuel_purchased_amount', 'fuel_purchased_volume', 'plant_hours'].includes(key)) { %>
                                            <td style="text-align: center;" class="<%= key; %>"><img style="max-height: 18px;" src="<%= sails.config.custom.PUBLIC_URL %>images/check.png" alt="Right Tick"></td>
                                        <% } else if(days[day][key] && ['mileage', 'fuel_purchased_amount', 'fuel_purchased_volume', 'plant_hours'].includes(key)) { %>
                                            <td style="text-align: center;"><%= days[day][key] %></td>
                                        <% } else { %>
                                            <td style="text-align: center;" class="<%= key; %>"><img style="max-height: 18px;" src="<%= sails.config.custom.PUBLIC_URL %>images/not-applicable.png" alt="N/A"></td>
                                        <% } %>
                                    <% } else { %>
                                        <td></td>
                                    <% } %>
                                <% } %>
                            <% } %>
                        </tr>
                    <% i = i + 1; }; %>
                    <% let j = 1; if (metaChecklistCategories.length) { %>
                        <% metaChecklistCategories.forEach(function(category, i) { %>
                            <!-- <tr>
                                <td style="background: #ededed; text-align: center;"><%= pad(i) %></td>
                                <td colspan="8" style="background: #ededed; text-align: left;"><%= category %></td>
                            </tr> -->
                            <% metaChecklistCategoriesData[category].forEach(function(categoryData, key) { %>
                                <tr>
                                    <td style="background: #ededed; text-align: center;"><%= pad(j) %></td>
                                    <td><%= categoryData.question %></td>
                                    <% if(Object.keys(assetInspections).length) { %>
                                        <% for (let day in days) { %>
                                            <% if(Object.keys(days[day]).length) { %>
                                                <% let answer = getAnswer(days[day], categoryData.question_id, false, true, categoryData.checklist_version); if (answer === true) { %>
                                                    <td style="text-align: center;" class="<%= answer; %>"><img style="max-height: 18px;" src="<%= sails.config.custom.PUBLIC_URL %>images/check.png" alt="Right Tick"></td>
                                                <% } else if(answer === false) { %>
                                                    <td style="text-align: center;" class="<%= answer; %>"><img style="max-height: 18px;" src="<%= sails.config.custom.PUBLIC_URL %>images/cross.png" alt="Cross Tick"></td>
                                                <% } else if (answer === null) { %>
                                                    <td style="text-align: center;" class="<%= answer; %>"><img style="max-height: 18px;" src="<%= sails.config.custom.PUBLIC_URL %>images/not-applicable.png" alt="N/A"></td>
                                                <% } else if(answer != '') { %>
                                                    <td style="text-align: center;" class="<%= answer; %>"><%= answer %> </td>
                                                <% } else { %>
                                                    <td style="text-align: center;"></td>
                                                <% } %>
    
                                            <% } else { %>
                                                <td></td>
                                            <% } %>
                                        <% } %>
                                    <% } %>
                                </tr>
                            <% j = j + 1; }); %>
                        <% }); %>
                    <% } else if(metaChecklistCategories.length < 1 && Object.keys(checkListItems).length < 1) { %>

                        <tr>
                            <td style="background: #ededed; text-align: center;">Checks Carried Out</td>
                            <% if(Object.keys(assetInspections).length) { %>
                                <% for (let day in days) { %>
                                    <% if(Object.keys(days[day]).length) { %>
                                        <td style="text-align: center;" class="true"><img
                                                    style="max-height: 18px;"
                                                    src="<%= sails.config.custom.PUBLIC_URL %>images/check.png"
                                                    alt="Right Tick"></td>
                                    <% } else { %>
                                        <td></td>
                                    <% } %>
                                <% } %>
                            <% } %>
                        </tr>
                    <% } %>

                    <% if (hasSupportingAttachments) { %>
                        <tr>
                            <% if(metaChecklistCategories.length >=1 || Object.keys(checkListItems).length >=1) { %>
                                <td style="background: #ededed; text-align: center;"><%= pad(j) %></td>
                                <td>Supporting Attachment(s)</td>
                            <% } else { %>
                                
                                <td style="background: #ededed; text-align: center;">Supporting Attachment(s)</td>
                            <% } %>
                            
                            <% if(Object.keys(assetInspections).length) { %>
                                <% for (let day in days) { %>
                                    <% if(Object.keys(days[day]).length && days[day].attachment_file_ref && days[day].attachment_file_ref.length) { %>
                                        <td style="text-align: center;" class="true">
                                            <span style="padding: 3px 8px; display: inline-grid;">
                                                <% (days[day].attachment_file_ref || []).forEach(function(attachment, i) { %>
                                                    <a href="<%= attachment.file_url %>"
                                                    onclick="window.open('<%= attachment.file_url %>','targetWindow', 'toolbar=no, location=no, status=no, menubar=no, scrollbars=yes, resizable=yes, width=' + (+window.screen.height || 450)+ ', height=' + (+window.screen.height || 450)+ ', fullscreen=yes');return false;">
                                                                <%= attachment.id %>
                                                    </a>
                                                <% }) %>
                                            </span>
                                        </td>
                                    <% } else { %>
                                        <td></td>
                                    <% } %>
                                <% } %>
                            <% } %>
                        </tr>
                    <% } %>
                </tbody>
            </table>
            <% let isChecklistRendered = 0 %>
            <% if(Object.keys(assetInspections).length && generalChecklistCount) { %>
                <% assetInspections.forEach(function(assetInspection, i) { %>
                    <% if(Object.keys(assetInspection.general_checklist).length && !isChecklistRendered) { %>
                        <% let gc = assetInspection.general_checklist; %>
                        <table class="table bordered-table inspection-detail-table" style="margin-bottom: 0 !important;">
                            <tbody>
                                <tr>
                                    <td>
                                        <span>Date</span>
                                    </td>
                                    <td>
                                        <% if (assetInspection.createdAt) { %>
                                            <span><%= momentTz(+assetInspection.createdAt, fullDateFormat) %></span>
                                        <% } %>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>Item Checked</span>
                                    </td>
                                    <td>
                                        <% if (gc.checked) { %>
                                            <span><%= (gc.checked) ? 'Yes' : 'No' %></span>
                                        <% } %>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>Comments</span>
                                    </td>
                                    <td>
                                        <% if (gc.comment) { %>
                                            <span style="white-space: pre-wrap"><%- gc.comment %></span>
                                        <% } %>
                                    </td>
                                </tr>
                                <% if (gc.images && gc.images.length) { %>
                                    <tr>
                                        <td>
                                            <span>Images</span>
                                        </td>
                                        <td style="padding: 3px 0px; background-color: transparent !important;">
                                            <table class="table" style="margin-bottom: 0px;">
                                                <tr>
                                                    <% let iterator = 1; gc.images.forEach(function(img, i) { %>
                                                        <td style="width: 32.9%; margin: .5% .2%; display: inline-block; text-align: center; border: none !important;">
                                                            <img style="max-width:100%; max-height: 270px;"
                                                                src="<%= img.file_url; %>">
                                                        </td>
                                                        <% if (iterator == 3) { %>
                                                            <% if (gc.images.length - 1 > i) { %>
                                                </tr>
                                                <tr>
                                                    <% } } %>
                                                    <% iterator = iterator + 1; }); %>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                <% } %>
                            </tbody>
                        </table>
                        <%  isChecklistRendered += 1 } %>
                <% }) %>
            <% } %>
        </div>
    </div>
</div>

<% if(Object.keys(assetInspections).length > 1 && generalChecklistCount > 1) { %>
    <% assetInspections.forEach(function(assetInspection, i) { %>
        <% if(i > 0 && Object.keys(assetInspection.general_checklist).length) { %>
            <% let gc = assetInspection.general_checklist; %>
            <div class="wrapper">
                <div class="section-content margin-bottom" style="text-align: center; line-height: 25px;">
                    <div class="table-border-radius">
                        <table class="table bordered-table inspection-detail-table" style="margin-bottom: 0 !important;border-style: hidden;">
                            <tbody>
                                <tr>
                                    <td>
                                        <span>Date</span>
                                    </td>
                                    <td>
                                        <% if (assetInspection.createdAt) { %>
                                            <span><%= momentTz(+assetInspection.createdAt, fullDateFormat) %></span>
                                        <% } %>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>Item Checked</span>
                                    </td>
                                    <td>
                                        <% if (gc.checked) { %>
                                            <span><%= (gc.checked) ? 'Yes' : 'No' %></span>
                                        <% } %>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>Comments</span>
                                    </td>
                                    <td>
                                        <% if (gc.comment) { %>
                                            <span style="white-space: pre-wrap"><%- gc.comment %></span>
                                        <% } %>
                                    </td>
                                </tr>
                                <% if (gc.images && gc.images.length) { %>
                                    <tr>
                                        <td>
                                            <span>Images</span>
                                        </td>
                                        <td style="padding: 3px 0px; background-color: transparent !important;">
                                            <table class="table" style="margin-bottom: 0px;">
                                                <tr>
                                                    <% let iterator = 1; gc.images.forEach(function(img, i) { %>
                                                        <td style="width: 32.9%; margin: .5% .2%; display: inline-block; text-align: center; border: none !important;">
                                                            <img style="max-width:100%; max-height: 270px;"
                                                                src="<%= img.file_url; %>">
                                                        </td>
                                                        <% if (iterator == 3) { %>
                                                            <% if (gc.images.length - 1 > i) { %>
                                                </tr>
                                                <tr>
                                                    <% } } %>
                                                    <% iterator = iterator + 1; }); %>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                <% } %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <% } %>
    <% }) %>
<% } %>


<% if(Object.keys(assetInspections).length) { %>
    <div class="wrapper">
        <div class="margin-bottom" style="text-align: center; line-height: 25px;">
            <div class="wd-100" style="margin-bottom: 8px;">
                <table class="coverPageTable" style="margin-bottom: 8px;margin-top: 8px;page-break-after: avoid;">
                    <tbody>
                        <tr style="background-color: #ededed;">
                            <td style="padding: 3px 8px;">
                                Sign-off
                            </td>
                        </tr>
                    </tbody>
                </table>
                <% assetInspections.forEach(function(assetInspection, i) { %>
                    <div class="table-border-radius mb-10" style="page-break-inside: avoid;">
                        <table class="table bordered-table inspection-detail-table" style="margin-bottom: 0 !important;border-style: hidden;page-break-inside: avoid;">
                            <tbody>
                                <tr>
                                    <td>
                                        <span>Date of Inspection</span>
                                    </td>
                                    <td>
                                        <span><%= momentTz(+assetInspection.createdAt, fullDateFormat) %></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>Inspected By</span>
                                    </td>
                                    <td>
                                        <span><%= getUserFullName(assetInspection.user_ref); %></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>Signed</span>
                                    </td>
                                    <td>
                                        <% if (assetInspection.sign) { %>
                                            <span><img class="sign" src="<%= assetInspection.sign %>" style="height:40px;"/></span>
                                        <% } else { %>
                                            <span></span>
                                        <% } %>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                <% }) %>
            </div>
        </div>
    </div>
<% } %>


<% if(Object.keys(assetInspections).length) { %>
    <% let faultCounter = 1; %>
    <% assetInspections.forEach(function(assetInspection, i) { %>
        <% if(assetInspection.fault_details.length) { %>
            <div class="wrapper">
                <div class="margin-bottom" style="text-align: center; line-height: 25px;">
                    <% if(assetInspection.fault_details.length) { %>
                        <div class="wd-100" style="margin-bottom: 8px;">
                            <table class="coverPageTable" style="margin-bottom: 8px;margin-top: 8px;page-break-after: avoid;">
                                <tbody>
                                    <tr style="background-color: #ededed;">
                                        <td style="padding: 3px 8px;">
                                            Faults
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <% assetInspection.fault_details.forEach(function(fault_detail, i) { %>
                                <% if (fault_detail.fault) { %>
                                    <div class="mb-10 table-border-radius <%= (fault_detail.status) ? fault_detail.status : '' %>-border">
                                        <table class="table bordered-table inspection-detail-table" style="margin-bottom: 0 !important;border-style: hidden;">
                                            <tbody>
                                                <tr class="<%= (fault_detail.status) ? fault_detail.status : '' %>">
                                                    <td colspan="2" style="background-color: transparent;">
                                                        <span>Fault Number - <%= pad(faultCounter) %></span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <span>Reported</span>
                                                    </td>
                                                    <td>
                                                        <% if (fault_detail.date_reported) { %>
                                                            <span><%= momentTz(+fault_detail.date_reported, fullDateFormat) %></span>
                                                        <% } %>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <span>Reported By</span>
                                                    </td>
                                                    <td>
                                                        <% if (fault_detail.reported_by) { %>
                                                            <span><%= fault_detail.reported_by %></span>
                                                        <% } %>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <span>Details</span>
                                                    </td>
                                                    <td>
                                                        <% if (fault_detail.fault) { %>
                                                            <span><%= fault_detail.fault %></span>
                                                        <% } %>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <span>Status</span>
                                                    </td>
                                                    <td>
                                                        <% if (fault_detail.status) { %>
                                                            <span class="<%= (fault_detail.status) ? fault_detail.status : '' %>-font %>"><%= strToUpper(fault_detail.status) %></span>
                                                        <% } %>
                                                    </td>
                                                </tr>
                                                <% if (fault_detail.images && fault_detail.images.length) { %>
                                                    <tr>
                                                        <td colspan="2" style="padding: 3px 0px; background-color: transparent !important;">
                                                            <table class="table" style="margin-bottom: 0px;">
                                                                <tr>
                                                                    <% let iterator = 1; fault_detail.images.forEach(function(img, i) { %>
                                                                        <td style="width: 32.9%; margin: .5% .2%; display: inline-block; text-align: center; border: none !important;">
                                                                            <img style="max-width:100%; max-height: 270px;"
                                                                                src="<%= img.file_url; %>">
                                                                        </td>
                                                                        <% if (iterator == 3) { %>
                                                                            <% if (fault_detail.images.length - 1 > i) { %>
                                                                </tr>
                                                                <tr>
                                                                    <% } } %>
                                                                    <% iterator = iterator + 1; }); %>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                <% } %>
                                                <% if (fault_detail.status && fault_detail.status == 'closed') { %>
                                                    <tr>
                                                        <td>
                                                            <span>Closed Out</span>
                                                        </td>
                                                        <td>
                                                            <% if (fault_detail.closedout_at) { %>
                                                                <span><%= momentTz(+fault_detail.closedout_at, fullDateFormat) %></span>
                                                            <% } %>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <span>Closed Out By</span>
                                                        </td>
                                                        <td>
                                                            <% if (fault_detail.closedout_by) { %>
                                                                <span><%= fault_detail.closedout_by %></span>
                                                            <% } %>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <span>Closeout Details</span>
                                                        </td>
                                                        <td>
                                                            <% if (fault_detail.closedout_details) { %>
                                                                <span><%= fault_detail.closedout_details %></span>
                                                            <% } %>
                                                        </td>
                                                    </tr>
                                                    <% if (fault_detail.closedout_images && fault_detail.closedout_images.length) { %>
                                                        <tr>
                                                            <td colspan="2" style="padding: 3px 0px; background-color: transparent !important;">
                                                                <table class="table" style="margin-bottom: 0px;">
                                                                    <tr>
                                                                        <% let iterator = 1; fault_detail.closedout_images.forEach(function(img, i) { %>
                                                                            <td style="width: 32.9%; margin: .5% .2%; display: inline-block; text-align: center; border: none !important;">
                                                                                <img style="max-width:100%; max-height: 270px;"
                                                                                    src="<%= img.file_url; %>">
                                                                            </td>
                                                                            <% if (iterator == 3) { %>
                                                                                <% if (fault_detail.closedout_images.length - 1 > i) { %>
                                                                    </tr>
                                                                    <tr>
                                                                        <% } } %>
                                                                        <% iterator = iterator + 1; }); %>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    <% } %>
                                                <% } %>
                                            </tbody>
                                        </table>
                                    </div>
                                <% } %>
                                <% faultCounter += 1; %>
                            <% }) %>
                        </div>
                    <% } %>
                </div>
            </div>
        <% } %>
    <% }) %>
<% } %>
