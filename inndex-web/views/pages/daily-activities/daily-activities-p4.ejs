<div class="wrapper">
    <section class="padding-sm pd-top-25" >
        <div class="section-content">
            <div class="table-outer" style="margin-top: 10px;">
                <h2 style="page-break-after: avoid;">
                    <svg style="fill: #002060;vertical-align: middle;" xmlns="http://www.w3.org/2000/svg"
                         xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Layer_1" width="25px"
                         viewBox="0 0 512 470" enable-background="new 0 0 512 512" xml:space="preserve">
                        <g>
                            <path d="M393.704,106.004c4.82,0,8.728-3.908,8.728-8.728V76.74c0-4.82-3.908-8.728-8.728-8.728c-4.82,0-8.728,3.908-8.728,8.728   v20.536C384.976,102.096,388.884,106.004,393.704,106.004z"/>
                            <path d="M327.399,128.355c3.408,3.408,8.935,3.408,12.343,0c3.408-3.408,3.408-8.935,0-12.343l-14.521-14.521   c-3.408-3.408-8.935-3.408-12.343,0c-3.408,3.409-3.408,8.935,0,12.343L327.399,128.355z"/>
                            <path d="M460.009,236.278c-3.408-3.408-8.934-3.408-12.343,0c-3.408,3.408-3.408,8.935,0,12.343l14.521,14.521   c3.409,3.408,8.935,3.409,12.343,0c3.408-3.408,3.409-8.935,0-12.343L460.009,236.278z"/>
                            <path d="M499.281,173.589h-20.536c-4.82,0-8.728,3.908-8.728,8.728c0,4.82,3.907,8.728,8.728,8.728h20.536   c4.82,0,8.728-3.908,8.728-8.728C508.009,177.497,504.102,173.589,499.281,173.589z"/>
                            <path d="M460.009,128.355l14.521-14.521c3.409-3.408,3.409-8.935,0-12.343c-3.408-3.408-8.934-3.408-12.343,0l-14.521,14.521   c-3.408,3.409-3.408,8.935,0,12.343C451.074,131.764,456.6,131.764,460.009,128.355z"/>
                            <path d="M393.704,115.878c-28.579,0-53.054,17.018-63.293,41.181c0.171,0.117,0.348,0.225,0.519,0.342   c17.123,11.757,30.85,27.462,40.136,45.815c25.511,2.214,48.063,14.398,63.433,32.442c16.767-12.109,27.631-31.491,27.631-53.34   C462.13,145.624,431.495,115.878,393.704,115.878z"/>
                            <path d="M153.185,240.985c-1.039,0.059-2.072,0.143-3.095,0.26C151.113,241.128,152.146,241.044,153.185,240.985z"/>
                            <path d="M362.642,218.731h-1.727c-15.916-39.24-54.379-66.926-99.33-66.926c-26.868,0-51.409,9.902-70.212,26.239h0   c-0.005,0.004-0.009,0.008-0.013,0.012c-2.182,1.897-4.292,3.874-6.314,5.939c-0.007,0.007-0.014,0.014-0.021,0.021   c0.007-0.007,0.014-0.014,0.021-0.021c-0.13,0.133-0.255,0.273-0.385,0.407c0.123-0.127,0.24-0.26,0.364-0.386   c-0.125,0.127-0.242,0.261-0.366,0.389c-0.873,0.901-1.741,1.809-2.582,2.74c-0.985,1.088-1.948,2.196-2.887,3.324   c-5.89,7.081-10.885,14.934-14.812,23.383c0.485-1.043,0.985-2.078,1.502-3.102c-0.518,1.027-1.02,2.064-1.506,3.109   c-0.011,0.024-0.022,0.048-0.033,0.072c-0.582,1.255-1.133,2.527-1.667,3.808c-0.162,0.387-0.316,0.777-0.473,1.166   c-0.388,0.962-0.762,1.93-1.123,2.906c-0.146,0.395-0.294,0.788-0.435,1.184c-0.47,1.32-0.923,2.648-1.343,3.991   c-0.006,0.017-0.011,0.035-0.016,0.053c-0.418,1.342-0.802,2.698-1.169,4.061c-0.111,0.41-0.213,0.824-0.319,1.236   c-0.261,1.019-0.507,2.043-0.738,3.073c-0.094,0.418-0.19,0.834-0.279,1.254c-0.297,1.398-0.574,2.803-0.815,4.22   c-0.001,0.004-0.002,0.009-0.003,0.013c-0.931,0.004-1.856,0.038-2.777,0.089c0.888-0.05,1.782-0.08,2.681-0.086   c-31.678,0.209-57.3,28.16-57.3,62.62c0,34.59,25.813,62.63,57.656,62.63h95.186h82.532h28.703   c42.705,0,77.323-33.001,77.323-73.71S405.347,218.731,362.642,218.731z M182.075,187.145c-0.983,1.088-1.947,2.195-2.885,3.323   C180.129,189.34,181.091,188.233,182.075,187.145z M164.339,213.937c-0.581,1.253-1.131,2.522-1.664,3.801   C163.208,216.459,163.758,215.19,164.339,213.937z M162.2,218.907c-0.388,0.96-0.761,1.928-1.122,2.902   C161.439,220.835,161.813,219.868,162.2,218.907z M160.643,222.996c-0.47,1.318-0.922,2.644-1.341,3.984   C159.72,225.64,160.173,224.314,160.643,222.996z M159.282,227.043c-0.418,1.34-0.801,2.694-1.167,4.056   C158.481,229.737,158.865,228.383,159.282,227.043z M157.796,232.337c-0.261,1.018-0.506,2.041-0.737,3.071   C157.289,234.378,157.535,233.355,157.796,232.337z M155.964,240.879c0.241-1.416,0.519-2.819,0.815-4.216   C156.483,238.06,156.206,239.463,155.964,240.879z"/>
                        </g>
                    </svg>
                    <span class="tableHeading">Weather</span>
                </h2>
                <% if (weatherLog && weatherLog.length) { %>
                    <div class="textAlignCenter">
                        <% weatherLog.forEach(function(weather, i){ %>
                            <div class="flex-md-fill mb-2 d-sm-inline-block d-md-flex justify-content-center text-center flex-custom-items">
                                <div class="card card-body px-2-1 small card- shadow">
                                    <div class="hour">
                                        <strong><%= moment(weather.day, 'YYYY-MM-DDTH').format('HH') %></strong>:00
                                    </div>
                                    <div class="temperature-meter">
                                        <div class="meter-indic">
                                            <div>
                                                <img height="30px" width="30px"
                                                     src="<%= weatherIconPath(weather.forecast.WeatherIcon) %>"
                                                     alt="Icon"/>
                                                <strong><%= showTemperature(weather.forecast.Temperature.Value) %>°</strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="d-sm-inline-block mb-2" style="position: relative;">
                                            <img height="27px" width="27px" src="<%= weatherIconPath('raindrops') %>"
                                                 alt="Icon" style="transform: rotate(40deg);"/>
                                            <div class="over-image precip-text">
                                                <i><%= weather.forecast.PrecipitationProbability %>%</i>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="d-sm-inline-block" style="position: relative;">
                                            <img height="30px" width="30px" src="<%= weatherIconPath('arrow') %>"
                                                 alt="Icon"
                                                 style="transform: rotate(<%= (weather.forecast.Wind.Direction.Degrees || 0) + 'deg' %>);"/>
                                            <div class="over-image wind-text">
                                                <span><%= parseInt(weather.forecast.Wind.Speed.Value || '') %></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-sm-inline-block" style="position: relative;">
                                        <span><%= weather.forecast.Wind.Speed.Unit || '' %></span>
                                    </div>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                <% } %>
            </div>
        </div>
    </section>
</div>

