<div class="wrapper">
    <section class="padding-sm pd-top-25" >
        <div class="section-content">
            <div class="table-outer">
                <table class="additionalInfoTable">
                    <tbody>
                        <% let hasActivityDescription = dailyactivity.activities.some(function(activity) {
                            return dailyactivity.activities_description[activity.key];
                        }); %>
                        
                        <% if (hasActivityDescription) { %>
                            <tr class="borderBottom-2">
                                <td class="heading darkgrey">Descriptions:</td>
                                <td colspan="2" style="padding: 0px;">
                                    <table class="table m-0 descriptionsInfo" style="margin: 0;">
                                        <tbody>
                                        <% dailyactivity.activities.forEach(function(activity, i) { %>
                                            <% if (dailyactivity.activities_description[activity.key]) { %>
                                                <tr>
                                                    <td class="vertical-align-middle lightgrey <%= activity.color_class %>" style="border-right: 1px solid #000;">
                                                        <span><%= activity.title %></span>
                                                    </td>
                                                    <td>
                                                        <p class="text-break"><%= dailyactivity.activities_description[activity.key] %></p>
                                                    </td>
                                                </tr>
                                            <% } %>
                                        <% }) %>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        <% } %>
                        
                        <% let hasWPPStatements = dailyactivity.activities.some(function(activity) {
                            return dailyactivity.wpp[activity.key];
                        }); %>
                        
                        <% if (hasWPPStatements) { %>
                            <tr class="borderBottom-2">
                                <td class="heading darkgrey"><%= wppPhrase %> Statements:</td>
                                <td colspan="2" style="padding: 0px;">
                                    <table class="table m-0 descriptionsInfo" style="margin: 0;">
                                        <tbody>
                                        <% dailyactivity.activities.forEach(function(activity, i) { %>
                                            <% if (dailyactivity.wpp[activity.key]) { %>
                                                <tr>
                                                    <td class="lightgrey <%= activity.color_class %>" style="border-right: 1px solid #000;">
                                                        <span><%= activity.title %></span>
                                                    </td>
                                                    <td>
                                                        <span><%= dailyactivity.wpp[activity.key] %></span>
                                                    </td>
                                                </tr>
                                            <% } %>                                        
                                        <% }) %>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        <% } %>
                        
                        <% let hasTaskBriefingSheet = dailyactivity.activities.some(function(activity) {
                            return dailyactivity.task_briefing_sheets[activity.key];
                        }); %>
                        
                        <% if (hasTaskBriefingSheet) { %>
                            <tr class="borderBottom-2">
                                <td class="heading darkgrey"><%= tbPhrase %> Sheets:</td>
                                <td colspan="2" style="padding: 0px;">
                                    <table class="table m-0 descriptionsInfo" style="margin: 0;">
                                        <tbody>
                                        <% dailyactivity.activities.forEach(function(activity, i) { %>
                                            <% if (dailyactivity.task_briefing_sheets[activity.key]) { %>
                                                <tr>
                                                    <td class="lightgrey <%= activity.color_class %>" style="border-right: 1px solid #000;">
                                                        <span><%= activity.title %></span>
                                                    </td>
                                                    <td>
                                                        <span><%= dailyactivity.task_briefing_sheets[activity.key] %></span>
                                                    </td>
                                                </tr>
                                            <% } %>
                                        <% }) %>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        <% } %>
                        
                        <% let hasPermitNumber = dailyactivity.activities.some(function(activity) {
                            return dailyactivity.permit_number[activity.key];
                        }); %>
                        
                        <% if (hasPermitNumber) { %>
                            <tr class="borderBottom-2">
                                <td class="heading darkgrey"><%=    
                                    permitPhrase %> Number:</td>
                                <td colspan="2" style="padding: 0px;">
                                    <table class="table m-0 descriptionsInfo" style="margin: 0;">
                                        <tbody>
                                        <% dailyactivity.activities.forEach(function(activity, i) { %>
                                           <% if (dailyactivity.permit_number[activity.key]) { %>
                                                <tr>
                                                    <td class="lightgrey <%= activity.color_class %>" style="border-right: 1px solid #000;">
                                                        <span><%= activity.title %></span>
                                                    </td>
                                                    <td>
                                                        <span><%= dailyactivity.permit_number[activity.key] %></span>
                                                    </td>
                                                </tr>
                                            <% } %>
                                        <% }) %>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        <% } %>                     
                        <% if (dailyactivity.clientComments.length) { %>
                            <tr class="borderBottom-2">
                                <td class="heading darkgrey">Instruction received from client:</td>
                                <td colspan="2" class="activitiesCell">
                                    <% dailyactivity.clientComments.forEach(function(comment) { %>
                                        <span class="text-break"><%= comment %></span>
                                    <% }) %>
                                </td>
                            </tr>
                        <% } %>
                        <% if (dailyactivity.subcontractorComments.length) { %>
                            <tr class="borderBottom-2">
                                <td class="heading darkgrey">Instruction issued to subcontractor:</td>
                                <td colspan="2" class="activitiesCell">
                                    <% dailyactivity.subcontractorComments.forEach(function(comment) { %>
                                        <span class="text-break"><%= comment %></span>
                                    <% }) %>
                                </td>
                            </tr>
                        <% } %>
                        <% if (dailyactivity.delayComments.length) { %>
                            <tr class="borderBottom-2">
                                <td class="heading darkgrey">Delays:</td>
                                <td colspan="2" class="activitiesCell">
                                    <% dailyactivity.delayComments.forEach(function(comment) { %>
                                        <span class="text-break"><%= comment %></span>
                                    <% }) %>
                                </td>
                            </tr>
                        <% } %>
                        <% if (dailyactivity.additionalWorksComments.length) { %>
                            <tr>
                                <td class="heading darkgrey">Additional works:</td>
                                <td colspan="2" class="activitiesCell">
                                    <% dailyactivity.additionalWorksComments.forEach(function(comment) { %>
                                        <span class="text-break"><%= comment %></span>
                                    <% }) %>
                                </td>
                            </tr>
                        <% } %>
                        <% if(dailyactivity.additionalComments.length) { %>
                            <tr>
                                <td class="heading darkgrey">Additional Comments:</td>
                                <td colspan="2" class="activitiesCell">
                                    <% dailyactivity.additionalComments.forEach(function(comment) { %>
                                        <span class="pre text-break"><%= comment %></span>
                                    <% }) %>
                                </td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
                <table class="commentTable">
                    <tbody>
                    <tr>
                        <td>Work carried out in accordance with plan specification:</td>
                        <% if (dailyactivity.work_carried_out) {%>
                            <td>YES</td>
                        <% } else { %>
                            <td>NO</td>
                        <% }%>
                    </tr>
                    <tr>
                        <td>Work on schedule:</td>
                        <% if (dailyactivity.work_on_schedule) { %>
                            <td>YES</td>
                        <% } else { %>
                            <td>NO</td>
                        <% }%>
                    </tr>
                    <% if (!(dailyactivity.work_carried_out && dailyactivity.work_on_schedule)) {%>
                        <tr>
                            <td colspan="2">
                                Comments: <span class="text-break"> <%= dailyactivity.comment? dailyactivity.comment : '' %> </span>
                            </td>
                        </tr>
                    <% }%>
                    </tbody>
                </table>
            </div>

            <% if (dailyactivity.reviewer && dailyactivity.reviewer.name && dailyactivity.reviewer.sign) { %>
                <div class="table-outer" style="margin-top: 10px;">
                    <div class="table-border-radius">
                        <table class="table bordered-table" style="margin-bottom: 0 !important;border-style: hidden;">
                            <tbody>
                            <colgroup>
                                <col style="width: 33%" />
                                <col style="width: 33%" />
                                <col style="width: 33%" />
                            </colgroup>
                            <tr class="bg-light-grey">
                                <td>Reviewed</td>
                                <td>Reviewed By</td>
                                <td>Signature</td>
                            </tr>
                            <tr>
                                <td>
                                    <% if (+dailyactivity.reviewer.timestamp) { %>
                                        <%= moment(+dailyactivity.reviewer.timestamp).format(dateFormat) %>
                                        <div style="font-size: 10px;">(<%= moment(+dailyactivity.reviewer.timestamp).format('HH:mm:ss') %>)</div>
                                    <% } %>
                                </td>
                                <td>
                                    <%= dailyactivity.reviewer.name %>
                                    <% if (dailyactivity.reviewer.user_employer) { %>
                                        <div>(<%= dailyactivity.reviewer.user_employer.employer %> - <%= dailyactivity.reviewer.user_employer.job_role %>)</div>
                                    <% } %>
                                </td>
                                <% if (dailyactivity.reviewer.sign) { %>
                                    <td><img class="sign" style="width:150px; height:40px" src="<%= dailyactivity.reviewer.sign %>"/></td>
                                <% } else { %>
                                    <td></td>
                                <% } %>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            <% } %>
        </div>
    </section>
</div>
<% if (weatherLog && weatherLog.length) {%>
<%- include('./daily-activities-p4'); -%>
<% }%>
<% if (auditData && auditData.length) { %>
    <div style="margin-top: 20px;">
        <%- include('../change-logs/index'); -%>
    </div>
<% } %>
