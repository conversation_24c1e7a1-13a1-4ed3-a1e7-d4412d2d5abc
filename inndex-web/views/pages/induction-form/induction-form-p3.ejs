
<% document_pages.forEach(function(pageRow, i){ %>
<div class="wrapper">
    <%- include('./form-header'); -%>
    <section class="">
        <% if(pageRow.show_doc_info){ %>
        <h2><i class="fas fa-id-card"></i> Competencies & Certifications</h2>
        <table class="table wd-33 competency-table pd-sm">
            <tbody>
            <tr>
                <td class="no-dot"><h5 class="no-line"><%= pageRow.name %>
                        <% if(pageRow.is_verified){ %>
                        <span class="mr-1 btn btn-outline-success btn-sm" title="CITB Verified"> Verified <i class="fa fa-check"></i> </span>
                        <%} else if(pageRow._is_verifiable){ %>
                        <span class="mr-1 btn btn-outline-danger btn-sm" title="CITB Not Verified" style="white-space:nowrap;"> Not Verified <i class="fas fa-exclamation-triangle"></i> </span>
                        <%} %>
                        <br/><small><%= pageRow.description %></small></h5>
                </td>
                <td class="no-dot text-center"><h5 class="no-line"><%= pageRow.doc_number %></h5> </td>
                <td class="no-dot text-right"><h5 class="no-line"><%= pageRow.expiry_date ? momentTz(parseInt(pageRow.expiry_date)).tz(timezone).format('D/MMM/Y') : '' %></h5></td>
            </tr>
            <% if(pageRow.children){ %>
            <% (pageRow.children || []).forEach(function(child_doc, i){ %>
            <tr class="">
                <td class="no-dot"><small class="no-line"><%= child_doc.name %>
                        <% if(child_doc.is_verified){ %>
                        <span class="mr-1 btn btn-outline-success btn-sm" title="CITB Verified"> Verified <i class="fa fa-check"></i> </span>
                        <%} %>
                        <br/><small><%= child_doc.description %></small></small>
                </td>
                <td class="no-dot text-center"><small class="no-line"><%= child_doc.doc_number %></small> </td>
                <td class="no-dot text-right"><small class="no-line"><%= child_doc.expiry_date ? momentTz(parseInt(child_doc.expiry_date)).tz(timezone).format('D/MMM/Y') : '' %></small></td>
            </tr>
            <% }); %>
            <% } %>
            </tbody>
        </table>
        <% } %>
        <div class="doc">
            <% if (pageRow.file_mime && pageRow.file_mime == 'application/pdf') { %>
            <iframe src="https://docs.google.com/gview?url=<%- pageRow.file_url %>&embedded=true" frameborder="0"></iframe>
            <% }else if(pageRow.file_mime && ['image/jpeg', 'image/jpg', 'image/png'].indexOf(pageRow.file_mime) !== -1){ %>
            <img src="<%- pageRow.md_url || pageRow.file_url %>" <% if(type === 'pdf' && pageRow.show_doc_info) { %> style="max-height: 850px;" <% }else if(type === 'pdf'){ %> style="max-height: 970px;" <% } else if(type === 'html' && pageRow.show_doc_info) { %> style="max-height: 900px;" <% } else { %> style="max-height: 950px;" <% } %>/>
            <% } %>
        </div>
    </section>
    <%- include('./form-footer', {totalPages: 4, currentPageNo: `3.${i}` }); -%>
</div>
<% }); %>
