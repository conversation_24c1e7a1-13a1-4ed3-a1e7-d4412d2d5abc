<div class="wrapper">
    <%- include('./form-header'); -%>
    <section class="padding-sm pd-top-25">
        <div class="">
            <div class="table-outer">
                <h2><i class="fa fa-user" aria-hidden="true"></i> Personal Details</h2>

                <table class="table wd-33">
                    <tbody>
                    <tr>
                        <td colspan="3">Title: <b><%= user.title %></b></td>
                        <td class="no-dot sm-no-border" rowspan="4" style="border: 1px solid #ccc;min-width: 120px;max-width:120px;padding: 0;">
                            <% if(user.profile_pic_ref && user.profile_pic_ref.id){%><img style="max-width: 100%;object-fit: cover;max-height: 170px;" src="<%- (user.profile_pic_ref.sm_url || user.profile_pic_ref.file_url) %>" alt="Profile pic"/><%}else{%>
                                <div style="text-align: center;">Profile Picture</div>
                            <%}%>
                        </td>
                    </tr>
                    <tr>
                        <td>First Name: <b><%= user.first_name %></b></td>
                        <td>Middle Name(s): <b><%= user.middle_name %></b></td>
                        <td>Last Name: <b><%= user.last_name %></b></td>
                    </tr>
                    <tr>
                        <td>Date of Birth: <b><%= moment(user.dob, 'YYYY-MM-DD').format(dateFormat) %></b><% if(canShowUnder18Badge(user)){%><span class="under18Sign">18</span><%}%></td>
                        <td>Gender: <b><%= user.gender %></b></td>
                        <td>Nationality: <b><%= user.country %></b></td>
                    </tr>
                    <!-- <tr>
                        <td>Marital Status: <b><%= user.marital_status %></b></td>
                        <td>Sexual Orientation: <b><%= user.sexual_orientation %></b></td>
                        <td>Disability: <b><%= user.disability %></b></td>
                    </tr> -->
                    <!-- <tr>
                        <td>Ethnicity: <b><%= user.ethnicity %></b></td>
                        <td>Subethnicity: <b><%= user.subethnicity %></b></td>
                        <td>Religion: <b><%= user.religion %></b></td>
                    </tr> -->
                    <% if(showNIN){%>
                    <tr>
                        <!-- <td>Caring Responsibilities: <b><%= user.caring_responsibilities %></b></td> -->
                        <td>National Insurance No.: <b><%= user.nin ? user.nin : 'N/A' %></b></td>
                    </tr>
                    <%}%>
                    </tbody>
                </table>
            </div>
            <div class="table-outer">
                <h2><i class="fa fa-phone" aria-hidden="true"></i> Contact Details</h2>
                <table class="table wd-33">
                    <tbody>
                    <tr>
                        <td>City: <b><%= contact_detail.city %></b></td>
                        <td><%= (project.custom_field && project.custom_field.country_code && project.custom_field.country_code === 'US') ? 'Zip Code' : 'Post Code' %>: <b><%= contact_detail.post_code ? contact_detail.post_code : 'N/A' %></b></td>
                        <% if (induction_request.district) { %>
                            <td>District: <b><%= induction_request.district %></b></td>
                        <% }  %>
                    </tr>
                    <tr>
                        <% if (induction_request.district ) { %>
                            <td>Local Worker:
                                <%= local_worker_status %>
                            </td>
                        <% } %>
                        <td>Home No.: <b>
                            <% if (contact_detail.home_number) { %>
                                <% if (contact_detail.home_number.code) { %>
                                    (+<%= contact_detail.home_number.code %>) <%= contact_detail.home_number.number %>
                                <% } else if (contact_detail.home_number.number) { %>
                                    <%= contact_detail.home_number.number %>
                                <% } %>
                            <% } else if (contact_detail.home_no) { %>
                                <%= contact_detail.home_no %>
                            <% } %>
                        </b></td>
                        <td>Mobile No.: <b>
                            <% if (contact_detail.mobile_number) { %>
                                <% if ( contact_detail.mobile_number.code) { %>
                                    (+<%= contact_detail.mobile_number.code %>) <%= contact_detail.mobile_number.number %>
                                <% } else if(contact_detail.mobile_number.number) { %>
                                    <%= contact_detail.mobile_number.number %>
                                <% } %>
                            <% } else if(contact_detail.mobile_no) { %>
                                <%= contact_detail.mobile_no %>
                            <% } %>
                        </b></td>
                    </tr>
                    <tr>
                        <% if (!is_shadow_user) { %>
                            <td>Email: <b><%= user.email %></b></td>
                        <% } %>
                        <td>Emergency Contact: <b><%= contact_detail.emergency_contact %></b></td>
                        <td>Emergency Contact No.: <b>
                            <% if (contact_detail.emergency_contact_number) { %>
                                <% if ( contact_detail.emergency_contact_number.code) { %>
                                    (+<%= contact_detail.emergency_contact_number.code %>) <%= contact_detail.emergency_contact_number.number %>
                                <% } else if(contact_detail.emergency_contact_number.number) { %>
                                    <%= contact_detail.emergency_contact_number.number %>
                                <% } %>
                            <% } else if(contact_detail.emergency_contact_no) { %>
                                <%= contact_detail.emergency_contact_no %>
                            <% } %>
                        </b></td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <div class="table-outer">
                <h2><i class="fa fa-briefcase" aria-hidden="true"></i> Employment Details</h2>
                <table class="table wd-33" style="margin-bottom: 8px;">
                    <tbody>
                    <tr>
                        <td>Company: <b><%= employment_detail.employer || '-' %> <%= displayEmploymentCompany(employment_detail) %></b></td>
                        <!--<td>Reporting to: <b><%= employment_detail.reporting_to || '-' %></b></td>-->
                        <!--<td>Role category:  <b><%= employment_detail.operative_type || '-' %></b></td>-->
                        <td>Job Role: <b><%= employment_detail.job_role || '-' %></b></td>
                        <% if(showTypeOfEmployment){%>
                        <td>Type of employment: <b><%= employment_detail.type_of_employment || '-' %></b></td>
                        <%}%>
                    </tr>
                    <tr>
                        <% if(showEmploymentStartDate){%>
                        <td>Time with employer: <b><%= createEmploymentTime(employment_detail.start_date_with_employer) || '-' %></b></td>
                        <%}%>
                        <% if(showMinWage){%>
                        <td colspan="2">
                            <span>Do you earn above min. living wage: </span>
                            <b class="<%= numberToYesNo(employment_detail.earn_mlw_e783) === 'No' ? 'text-danger' : '' %>">
                                <%= numberToYesNo(employment_detail.earn_mlw_e783) %>
                            </b>
                        </td>
                        <%}%>
                        <!--<td>Working Arrangement: <b><%= employment_detail.working_arrangement || '-' %></b></td>-->
                    </tr>
                    <tr>
                        <% if(showEmpNbr){%>
                            <td>
                                <span>Employee Number:</span>
                                <b><%= employment_detail.employee_number ? employment_detail.employee_number : 'N/A' %></b>
                            </td>
                        <%}%>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <span style="vertical-align: top;">Comments:</span>
                            <span style="display: inline-block;"><b><%- employment_detail.comment %></b></span>
                        </td>
                    </tr>
                    </tbody>
                </table>

            </div>
            <% if (!is_shadow_user) { %>
            <div class="table-outer">
                <h2><i class="fa fa-car-alt" aria-hidden="true"></i> Travel</h2>
                <table class="table">
                    <tbody>
                    <tr>
                        <td>
                            Total Travel time:
                            <% if (travel_time_override.travel_time) { %>
                                <b>
                                    <%= totalTravelTime(travel_time_override.travel_time) %></b>
                            <% } %>
                        </td>
                        <td>
                            Travel time to work:
                            <% if (travel_time_override.travel_time && travel_time_override.travel_time.to_work) { %>
                                <b>
                                    <%= createTime(travel_time_override.travel_time.to_work) %></b>
                            <% }else{ %>
                                <b>-</b>
                            <% } %>
                        </td>
                        <td>
                            Travel time to home from work:
                            <% if (travel_time_override.travel_time && travel_time_override.travel_time.to_home) { %>
                                <b><%= createTime(travel_time_override.travel_time.to_home) %></b>
                            <% }else{ %>
                                <b>-</b>
                            <% } %>
                        </td>
                    </tr>
                    <tr>
                        <td>Method of travel: <b><%= travel_time_override.travel_method || '-' %></b></td>
                        <% if (['Car/Van (Driver)','Car (Driver)', 'Motorbike'].indexOf(travel_time_override.travel_method) !== -1) { %>
                            <td>Vehicle Reg. No.: <b><%= travel_time_override.vehicle_reg_number %></b></td>
                        <% } %>
                        <% if (travel_time_override.timestamp) { %>
                        <td>
                        Travelling From/To: <b><%= travel_time_override.from_postcode %> </b>/ <b><%= travel_time_override.to_postcode %></b>
                        </td>
                        <% } %>
                    </tr>
                    <% if (travel_time_override.vehicle_info && travel_time_override.vehicle_info.make) { %>
                    <tr>
                        <td>Vehicle Make: <b><%= travel_time_override.vehicle_info.make || '-' %></b></td>
                        <td>Fuel Type: <b><%= travel_time_override.vehicle_info.fuelType || '-' %></b></td>
                        <td>CO2 Emissions: <b><%= travel_time_override.vehicle_info.co2Emissions || '-' %> g/km</b></td>
                    </tr>
                    <% } %>
                    </tbody>
                </table>
            </div>
            <div class="table-outer">
                <h2><i class="fas fa-hard-hat"></i> Safety Assessment</h2>
                <table class="table wd-70 pd-sm mr-bt-0">
                    <tbody>
                    <tr>
                        <td>I confirm I am fit and able to undertake the work activities my role requires?:</td>
                        <td class="no-dot"><b><%= numberToYesNo(induction_request.fit_undertake_role) %></b></td>
                    </tr>
                    <tr>
                        <td>I confirm I am fit to work safely?</td>
                        <td class="no-dot"><b><%= numberToYesNo(induction_request.fit_to_work) %></b></td>
                    </tr>
                    <% if (project && project.further_policies) { %>
                        <% project.further_policies.forEach(function(further_policy, i){ %>
                            <% if (further_policy.key === 'working_hr_agreement') { %>
                            <tr>
                                <td>I will comply with the working hours agreement on site?</td>
                                <td class="no-dot"><b><%= numberToYesNo(induction_request.comply_hour_agreement) %></b></td>
                            </tr>
                            <% }
                            else if (further_policy.key === 'c_lens_policy') { %>
                            <tr>
                                <td>After reading the contact lens site directive, selected option:</td>
                                <td class="no-dot"><b><%= induction_request.site_directive_selection %></b></td>
                            </tr>
                            <% }
                            else if (further_policy.key === 'd_and_a_policy') { %>
                            <tr>
                                <td>Do you confirm that you have read and understood the drugs and alcohol policy?</td>
                                <td class="no-dot capital"><b><%= numberToYesNo(induction_request.accept_drug_alcohol_pol) %></b></td>
                            </tr>
                            <% }
                            else { %>
                            <tr>
                                <td>Do you confirm that you have read and understood the <%= further_policy.policy_name %>?</td>
                                <td class="no-dot capital"><b><%= numberToYesNo(1) %></b></td>
                            </tr>
                            <% }%>
                        <% }); %>
                    <% }%>
                    </tbody>
                </table>
            </div>
            <% } %>
        </div>
    </section>
    <%- include('./form-footer', {totalPages: 4, currentPageNo: 1}); -%>
</div>
