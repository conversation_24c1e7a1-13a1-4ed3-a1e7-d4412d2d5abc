<%
let imagesPerPage = 1; // Set images per page to 1
let totalImages = attachments.length;
let pages = Math.ceil(totalImages / imagesPerPage);
%>

<% for (let page = 0; page < pages; page++) { %>
  <div class="wrapper">
    <section class="padding-sm pd-top-25">
      <div class="">
        <div class="attachment-box">
          <div class="attachment-header">
            <h2 style="font-size:12px;font-weight:600;">Attachments</h2>
          </div>

         <div class="attachment-grid-fullView">
           <% for (let i = page * imagesPerPage; i < totalImages; i++) { %>
            <% if (i >= totalImages || i > (((page+1) * imagesPerPage) -1) ) break; %>
              <% let file = attachments[i]; %>
                <div class="attachment-item-fullView">
                  <div class="attachment-image-fullView">
                      <img src="<%= file.image.file_url %>" alt="<%= file.title %>" class="full-image" />
                  </div>
                  <div style="margin-top:5px;">
                      <p class="attachment-detail">
                          <% if (file.attachment_type === 'section') { %>
                              Section:
                          <% } %>
                          <a href="<%= file.image.file_url %>" target="_blank"><%= file.title %></a>
                      </p>
                      <p class="attachment-detail">Uploaded By: <%= file.image.user.first_name + ' ' + file.image.user.last_name %></p>
                      <p class="attachment-detail">Date Uploaded: <%= new Date(file.image.createdAt).toLocaleDateString() %></p>
                  </div>
                </div>
            <% } %>
          </div>
        </div>
      </div>
    </section>
  </div>
<% } %>
