<style type="text/css">
    @font-face {
        font-family: 'Nunito';
        font-style: normal;
        font-weight: normal;
        src: local('Nunito Regular'), local('Nunito-Regular'), url(<%= sails.config.custom.PUBLIC_URL %>fonts/Nunito-Regular.woff) format('woff');
    }
    h2 .fa, h2 .fas{
        padding: 0 0 0 5px;
    }
    img,legend{border:0}legend,td,th{padding:0}body,figure{margin:0}caption,th{text-align:left}h3{text-transform:uppercase}h2,h4{text-transform:capitalize}a:focus,a:hover,h5{text-decoration:underline}html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent;color:#337ab7;text-decoration:none}a:active,a:hover{outline:0}abbr[title]{border-bottom:1px dotted}b,optgroup,strong{font-weight:700}dfn{font-style:italic}mark{background:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}img{vertical-align:middle}svg:not(:root){overflow:hidden}hr{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}pre,textarea{overflow:auto}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}button{overflow:visible}button,select{text-transform:none}button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}input[type=checkbox],input[type=radio]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:0}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}input[type=search]{-webkit-appearance:textfield;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}*,:after,:before{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}html{font-size:10px;-webkit-tap-highlight-color:transparent}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit}a:focus,a:hover{color:#23527c}a:focus{outline:-webkit-focus-ring-color auto 5px;outline-offset:-2px}.img-responsive{display:block;max-width:100%;height:auto}.img-rounded{border-radius:6px}.img-thumbnail{padding:4px;line-height:1.42857143;background-color:#fff;border:1px solid #ddd;border-radius:4px;-webkit-transition:all .2s ease-in-out;-o-transition:all .2s ease-in-out;transition:all .2s ease-in-out;display:inline-block;max-width:100%;height:auto}.img-circle{border-radius:50%}hr{margin-top:20px;margin-bottom:20px;border:0}.sr-only{position:absolute;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}[role=button]{cursor:pointer}table{border-collapse:collapse;border-spacing:0;background-color:transparent}caption{padding-top:8px;padding-bottom:8px;color:#777}.table{width:100%;max-width:100%;margin-bottom:20px}.table:not(.tp-0)>tbody>tr>td,.table>tbody>tr>th,.table>tfoot>tr>td,.table>tfoot>tr>th,.table>thead>tr>td,.table>thead>tr>th{padding:8px;line-height:1.42857143;vertical-align:top}.table>thead>tr>th{vertical-align:bottom}.table>caption+thead>tr:first-child>td,.table>caption+thead>tr:first-child>th,.table>colgroup+thead>tr:first-child>td,.table>colgroup+thead>tr:first-child>th,.table>thead:first-child>tr:first-child>td,.table>thead:first-child>tr:first-child>th{border-top:0}.table .table{background-color:#fff}.table-condensed>tbody>tr>td,.table-condensed>tbody>tr>th,.table-condensed>tfoot>tr>td,.table-condensed>tfoot>tr>th,.table-condensed>thead>tr>td,.table-condensed>thead>tr>th{padding:5px}.wrapper,h2{padding-left:15px}.table-bordered,.table-bordered>tbody>tr>td,.table-bordered>tbody>tr>th,.table-bordered>tfoot>tr>td,.table-bordered>tfoot>tr>th,.table-bordered>thead>tr>td,.table-bordered>thead>tr>th{border:1px solid #ddd}.table-bordered>thead>tr>td,.table-bordered>thead>tr>th{border-bottom-width:2px}.table-striped>tbody>tr:nth-of-type(odd){background-color:#f9f9f9}.table-hover>tbody>tr:hover,.table>tbody>tr.active>td,.table>tbody>tr.active>th,.table>tbody>tr>td.active,.table>tbody>tr>th.active,.table>tfoot>tr.active>td,.table>tfoot>tr.active>th,.table>tfoot>tr>td.active,.table>tfoot>tr>th.active,.table>thead>tr.active>td,.table>thead>tr.active>th,.table>thead>tr>td.active,.table>thead>tr>th.active{background-color:#f5f5f5}table col[class*=col-]{position:static;float:none;display:table-column}table td[class*=col-],table th[class*=col-]{position:static;float:none;display:table-cell}.table-hover>tbody>tr.active:hover>td,.table-hover>tbody>tr.active:hover>th,.table-hover>tbody>tr:hover>.active,.table-hover>tbody>tr>td.active:hover,.table-hover>tbody>tr>th.active:hover{background-color:#e8e8e8}.table>tbody>tr.success>td,.table>tbody>tr.success>th,.table>tbody>tr>td.success,.table>tbody>tr>th.success,.table>tfoot>tr.success>td,.table>tfoot>tr.success>th,.table>tfoot>tr>td.success,.table>tfoot>tr>th.success,.table>thead>tr.success>td,.table>thead>tr.success>th,.table>thead>tr>td.success,.table>thead>tr>th.success{background-color:#dff0d8}.table-hover>tbody>tr.success:hover>td,.table-hover>tbody>tr.success:hover>th,.table-hover>tbody>tr:hover>.success,.table-hover>tbody>tr>td.success:hover,.table-hover>tbody>tr>th.success:hover{background-color:#d0e9c6}.table>tbody>tr.info>td,.table>tbody>tr.info>th,.table>tbody>tr>td.info,.table>tbody>tr>th.info,.table>tfoot>tr.info>td,.table>tfoot>tr.info>th,.table>tfoot>tr>td.info,.table>tfoot>tr>th.info,.table>thead>tr.info>td,.table>thead>tr.info>th,.table>thead>tr>td.info,.table>thead>tr>th.info{background-color:#d9edf7}.table-hover>tbody>tr.info:hover>td,.table-hover>tbody>tr.info:hover>th,.table-hover>tbody>tr:hover>.info,.table-hover>tbody>tr>td.info:hover,.table-hover>tbody>tr>th.info:hover{background-color:#c4e3f3}.table>tbody>tr.warning>td,.table>tbody>tr.warning>th,.table>tbody>tr>td.warning,.table>tbody>tr>th.warning,.table>tfoot>tr.warning>td,.table>tfoot>tr.warning>th,.table>tfoot>tr>td.warning,.table>tfoot>tr>th.warning,.table>thead>tr.warning>td,.table>thead>tr.warning>th,.table>thead>tr>td.warning,.table>thead>tr>th.warning{background-color:#fcf8e3}.table-hover>tbody>tr.warning:hover>td,.table-hover>tbody>tr.warning:hover>th,.table-hover>tbody>tr:hover>.warning,.table-hover>tbody>tr>td.warning:hover,.table-hover>tbody>tr>th.warning:hover{background-color:#faf2cc}.table>tbody>tr.danger>td,.table>tbody>tr.danger>th,.table>tbody>tr>td.danger,.table>tbody>tr>th.danger,.table>tfoot>tr.danger>td,.table>tfoot>tr.danger>th,.table>tfoot>tr>td.danger,.table>tfoot>tr>th.danger,.table>thead>tr.danger>td,.table>thead>tr.danger>th,.table>thead>tr>td.danger,.table>thead>tr>th.danger{background-color:#f2dede}.table-hover>tbody>tr.danger:hover>td,.table-hover>tbody>tr.danger:hover>th,.table-hover>tbody>tr:hover>.danger,.table-hover>tbody>tr>td.danger:hover,.table-hover>tbody>tr>th.danger:hover{background-color:#ebcccc}
    .text-hide,.wrapper td:before{background-color:transparent}.table-responsive{overflow-x:auto;min-height:.01%}@media screen and (max-width:767px){.table-responsive{width:100%;margin-bottom:15px;overflow-y:hidden;-ms-overflow-style:-ms-autohiding-scrollbar;border:1px solid #ddd}.table-responsive>.table{margin-bottom:0}.table-responsive>.table>tbody>tr>td,.table-responsive>.table>tbody>tr>th,.table-responsive>.table>tfoot>tr>td,.table-responsive>.table>tfoot>tr>th,.table-responsive>.table>thead>tr>td,.table-responsive>.table>thead>tr>th{white-space:nowrap}.table-responsive>.table-bordered{border:0}.table-responsive>.table-bordered>tbody>tr>td:first-child,.table-responsive>.table-bordered>tbody>tr>th:first-child,.table-responsive>.table-bordered>tfoot>tr>td:first-child,.table-responsive>.table-bordered>tfoot>tr>th:first-child,.table-responsive>.table-bordered>thead>tr>td:first-child,.table-responsive>.table-bordered>thead>tr>th:first-child{border-left:0}.table-responsive>.table-bordered>tbody>tr>td:last-child,.table-responsive>.table-bordered>tbody>tr>th:last-child,.table-responsive>.table-bordered>tfoot>tr>td:last-child,.table-responsive>.table-bordered>tfoot>tr>th:last-child,.table-responsive>.table-bordered>thead>tr>td:last-child,.table-responsive>.table-bordered>thead>tr>th:last-child{border-right:0}.table-responsive>.table-bordered>tbody>tr:last-child>td,.table-responsive>.table-bordered>tbody>tr:last-child>th,.table-responsive>.table-bordered>tfoot>tr:last-child>td,.table-responsive>.table-bordered>tfoot>tr:last-child>th{border-bottom:0}}.clearfix:after,.clearfix:before{display:table}.center-block{display:block;margin-left:auto;margin-right:auto}.pull-right{float:right!important}.pull-left{float:left!important}.hide{display:none!important}.show{display:block!important}.invisible{visibility:hidden}.text-hide{font:0/0 a;color:transparent;text-shadow:none;border:0}h1,h2{font-weight:700}.hidden{display:none!important}.affix{position:fixed}
    body{background-color:#fff;font-family:'Nunito',Arial,Helvetica,sans-serif;font-size:14px;color:#000; -webkit-print-color-adjust: exact;}
    .wrapper{width:100%;max-width:1170px;margin:auto;padding-right:15px}footer p,h1,h2,h3,h4,h5,h6{margin:0}.text-center{text-align:center}h1{font-size:28px;display:inline-block;text-decoration:underline;}h2,h3{font-size:18px}
    h2{/*margin-top:30px;*/margin-bottom:10px;background-color: #dae5f1;line-height: 28px;color: #14152d; font-size: 20px;}
    h3,h4{font-weight:400}h3{font-style:italic}h4{font-size:16px}h5,h6{font-size:14px;font-style:italic}h5{font-weight:700;margin-bottom:5px}h6{font-weight:400}.padding-sm{/*padding:50px 0*/}
    .header-inner{position:relative;/*padding:25px 0*/}
    /*.logo{position:absolute;left:0;top:-10px;}*/
    .logo{padding-top: 10px;}
    .logo img{max-height: 96px;max-width: 300px;}.wd-25 tr th{width:25%}.border-none{border-top:0;border-bottom:0}.pd-top-25{/*padding-top:25px*/}.wd-100{width:100%}.wd-33 td{width:33.33%;padding-left:15px}.wd-50 td{width:50%;padding-left:15px}.wd-70 td:first-child{width:70%;padding-left:15px}.wd-70 td:last-child{width:30%;padding-left:15px}.wd-80 td:first-child{width:80%;padding-left:15px}.wd-20 td:first-child,.wd-80 td:last-child{width:20%;padding-left:15px}.wd-20 td:last-child{width:80%;padding-left:15px}hr{width:100%;height:1px;background-color:#4a7ebb;border-top:0;margin:0 0 15px}.table>tbody>tr>td{position:relative;padding-left:15px}.pd-sm.table:not(.tp-0)>tbody>tr>td:first-child,.table:not(.tp-0)>tbody>tr>td:first-child{padding-left:30px}.table>tbody>tr>td:first-child:before{left:15px}
    .wrapper td:before{content:'';position:absolute;width:5px;height:5px;border:1px solid #000;border-radius:50%;top:14px;left:-6px}.no-dot:before{display:none}.float-left{float:left}.float-right{float:right}footer{/*padding-bottom:50px*/}.clearfix:after,.clearfix:before{content:" "}.clearfix:after{clear:both}.doc{border:1px solid #000;margin:10px 0 20px;min-height:200px;position:relative}.full-width{width:100%}h3.bold{font-style:normal}.vh-center{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);-moz-transform:translate(-50%,-50%)}.pd-sm.table>tbody>tr>td{padding:2px 10px}
    /*.pd-sm td:before{top:10px}*/
    .pd-sm td:before{
        top: 8px;
    }
    .decleration.table>tbody>tr>td{padding-left:15px}.mr-bt-0{margin-bottom:0}.text-right{text-align:right}.text-left{text-align:left}.no-line{text-decoration:none}.bold{font-weight:700}
    .mb-10{
        margin-bottom: 10px;
    }
    .m-0{
        margin: 0;
    }
    .pt-15{
        padding: 15px 0 0;
    }
    .page-b-always{
        page-break-before: always;
    }
    .hr {
        width: 100%;
        margin: 0 0 12px;
        border-top: 4px solid #97a1b5;
    }
    footer{
        color: #6c757d!important;
    }
    .small, small {
        font-size: 70%;
        font-weight: 400;
    }
    .text-danger{
        color: red;
    }
    .doc{
        max-width: 98%;
        min-height: 750px;
        text-align: center;
        padding-top: 1px;
    }
    <% if(type && (type === 'pdf')){%>
    .doc img, .doc iframe{
        width: 95%;
        height: 80%;
        outline: none;
    }
    <% } else { %>
    .doc img, .doc iframe{
        max-width: 95%;
        height: auto;
        outline: none;
    }
    <% } %>
    .doc iframe{
        width: 80%;
        min-height: 750px;
    }
    .table-bordered td {
        border: 1px solid #dee2e6;
    }
    .table-wd-auto td:first-child{
        width: auto;
    }
    .capital{
        text-transform: capitalize;
    }
    .btn-outline-success {
        color: green;
        border-radius: 0.25em;
        border: 1px solid #28a745;
        padding: 0px 6px;
        margin-left: 8px;
        position: absolute;
    }
    .btn-outline-danger {
        color: #dc3545;
        border-radius: 0.25em;
        border: 1px solid #a71d2a;
        font-size: 12px;
        padding: 0px 6px;
        margin-left: 8px;
        position: absolute;
    }
    :root {
        /*--borderWidth: 4px;
        --height: 16px;
        --width: 8px;*/
        --borderWidth: 3px;
        --height: 10px;
        --width: 6px;
        --borderColor: #78b13f;
    }
    .fa-check{
        margin-left: 5px;
        /*display: inline-block;
        transform: rotate(45deg);
        height: var(--height);
        width: var(--width);
        border-bottom: var(--borderWidth) solid var(--borderColor);
        border-right: var(--borderWidth) solid var(--borderColor);*/
    }
    .competency-table{
        margin-bottom: 10px;
    }
    /*.wrap-html{
        padding-bottom: 0 !important;
        white-space: pre-line;
    }*/
    .under18Sign {
        font-size: 13px;
        background-color: #F20000;
        color: white;
        border-radius: 50%;
        vertical-align: text-top;
        padding: 2px;
        margin-left: 5px;
    }
    @media print {
        html,body{
            height: 100%;
            margin: 0;
        }
        .wrapper{
            height: 100%;
            display: block;
            position: relative;
            padding-bottom: 28px;
        }
        .wrapper > footer{
            position: absolute;
            bottom: 10px;
            width: 100%;
        }
        footer .float-right{
            margin-right: 30px; /*this is because of both negative padding of wrapper */
        }
        .under18Sign {
            font-size: 10px;
            vertical-align: unset;
        }
    }
    @media (max-width: 768px) {
        body .wrapper:first-child{
            margin: 0 auto;
        }
        .wrapper td{
            font-size: .7em !important;
        }
        h2{
            font-size: 1em !important;
            padding-left: 0;
            line-height: 1.8em;
        }
        h4{
            font-size: 0.8em;
        }
        h5{
            font-size: .8em;
        }
        h1{
            font-size: 1.4em;
        }
        .table{
            margin-bottom: 10px;
        }
        .logo img{
            max-height: 70px;
            max-width: 152px;
        }
        .pd-sm.table:not(.tp-0)>tbody>tr>td:first-child, .table:not(.tp-0)>tbody>tr>td:first-child{
            padding-left: 10px;
        }
        .table>tbody>tr>td:first-child:before{
            left: 0;
        }
        .wrapper td:before{
            top: 10px;
            left: -2px;
        }
        .pd-sm td:before{
            top: 5px;
        }
        .min-padding{
            padding: 0 4px 0 0 !important;
        }
        .sm-no-border{
            border: none !important;
        }
        .under18Sign {
            font-size: 11px;
        }
    }
</style>
