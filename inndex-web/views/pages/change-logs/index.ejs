<style>
td:first-child {
  background-color: unset !important;
}
tbody td:first-child {
    font-weight: normal !important;

} 
.change-log tbody td{
  max-width: 450px !important;
  white-space: pre !important;
  text-wrap: wrap !important;
  word-wrap: break-word !important;
}
</style>

<div class="change-log wrapper" style=" margin-bottom: 35px;">
  <h2 class="no-page-break-after">
    <span class="tableHeading">Change Log</span>
  </h2>
  <div>
    <% auditData.forEach(function(log, i) { %>
      <table class="table bordered-table" style="margin-top: 20px; border-style: none !important; break-inside: avoid;">
        <thead >
          <tr style="font-weight: 600;">
            <td colspan="3">
              Updated By: <%= log.user_ref.name %> 
              <span style="float: right;">Updated on: <%= momentTz(log.createdAt, 'DD-MM-YYYY HH:mm') %></span>
            </td>
          </tr> 
          <tr >
            <th>Change</th>
            <th>Old Value</th>
            <th>Changed to</th>
          </tr>
        </thead>
        <tbody>
          <%- renderChanges(log.changes) %>
        </tbody>
      </table>
    <% }); %>
  </div>
</div>

  