<style>
td:first-child {
  background-color: unset !important;
}
tbody td:first-child {
    font-weight: normal !important;
}

.change-log table {
  table-layout: fixed !important;
  width: 100% !important;
  max-width: inherit;
}

.change-log thead tr:nth-of-type(2) th:nth-of-type(1) {
  width: 10% !important;
}
.change-log thead tr:nth-of-type(2) th:nth-of-type(2) {
  width: 10% !important;
}
.change-log thead tr:nth-of-type(2) th:nth-of-type(3) {
  width: 40% !important;
}
.change-log thead tr:nth-of-type(2) th:nth-of-type(4) {
  width: 40% !important;
}

.fw-600 {
  font-weight: 600 !important;
}


.change-log thead th {
  background-color: inherit !important;
}

.change-log tbody td {
  white-space: pre-wrap !important;
  text-wrap: wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  vertical-align: top !important;
}

/* Special styling for merged cells */
.change-log tbody td[rowspan] {
  vertical-align: middle !important;
  text-align: center !important;
}

.change-log tbody td img {
  max-width: 100% !important;
  height: auto !important;
  object-fit: contain !important;
}

.change-log tbody td * {
  max-width: 100% !important;
  box-sizing: border-box !important;
}
</style>

<div class="change-log wrapper" style=" margin-bottom: 35px;">
  <h2 class="no-page-break-after">
    <span class="tableHeading">Change Log</span>
  </h2>
  <div>
    <% auditData.forEach(function(log, i) { %>
      <table class="table bordered-table" style="margin-top: 20px; border-style: none !important; break-inside: avoid;">
        <thead >
          <tr>
            <td colspan="4">
             <span class="fw-600"> Updated By:</span> <%= log.user_ref.name %> 
              <span style="float: right;">
                <span class="fw-600">Updated on:</span>
                <span><%= momentTz(log.createdAt, 'DD-MM-YYYY HH:mm') %></span>
              </span>

            </td>
          </tr> 
          <tr style="font-weight: 600;">
            <th>Section</th>
            <th>Field</th>
            <th>Changed from</th>
            <th>Changed to</th>
          </tr>
        </thead>
        <tbody>
          <%- renderChanges(log.changes, 0, log.user_ref.name, momentTz(log.createdAt,
            __('displayFullDateFormat_slash_DD_MM_YYYY_HH_mm_ss'))) %>
        </tbody>
      </table>
    <% }); %>
  </div>
</div>

  