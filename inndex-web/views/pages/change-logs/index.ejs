<style>
td:first-child {
  background-color: unset !important;
}
tbody td:first-child {
    font-weight: normal !important;

}
.change-log tbody td{
  max-width: 450px !important;
  white-space: pre !important;
  text-wrap: wrap !important;
  word-wrap: break-word !important;
}
</style>

<div class="change-log wrapper" style=" margin-bottom: 35px;">
  <h2 class="no-page-break-after">
    <span class="tableHeading">Change Log</span>
  </h2>
  <div>
    <% auditData.forEach(function(log, i) { %>
      <table class="table bordered-table" style="margin-top: 20px; border-style: none !important; break-inside: avoid;">
        <thead>
          <tr style="font-weight: 600;">
            <th>Updated on</th>
            <th>Changed by</th>
            <th>Section</th>
            <th>Field</th>
            <th>Changed from</th>
            <th>Changed to</th>
          </tr>
        </thead>
        <tbody>
          <%- renderChanges(log.changes, 0, log.user_ref.name, momentTz(log.createdAt, 'DD/MM/YY HH:mm' )) %>
        </tbody>
      </table>
    <% }); %>
  </div>
</div>

  