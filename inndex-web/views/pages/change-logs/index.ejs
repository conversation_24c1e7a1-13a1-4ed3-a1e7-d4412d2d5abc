<style>
td:first-child {
  background-color: unset !important;
}
tbody td:first-child {
    font-weight: normal !important;
}

.change-log table {
  table-layout: fixed !important;
  width: 100% !important;
}

.change-log thead th:nth-child(1) { /* Updated on */
  width: 12% !important;
}
.change-log thead th:nth-child(2) { /* Changed by */
  width: 15% !important;
}
.change-log thead th:nth-child(3) { /* Section */
  width: 15% !important;
}
.change-log thead th:nth-child(4) { /* Field */
  width: 25% !important;
}
.change-log thead th:nth-child(5) { /* Changed from */
  width: 16% !important;
}
.change-log thead th:nth-child(6) { /* Changed to */
  width: 17% !important;
}

.change-log thead th {
  background-color: inherit !important;
}

.change-log tbody td {
  white-space: pre-wrap !important;
  text-wrap: wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  vertical-align: top !important;
}

/* Special styling for merged cells */
.change-log tbody td[rowspan] {
  vertical-align: middle !important;
  text-align: center !important;
}

/* Specific styling for data columns */
.change-log tbody td:nth-child(4) { /* Field column */
  font-weight: 500;
}
.change-log tbody td:nth-child(5),
.change-log tbody td:nth-child(6) { /* Changed from/to columns */
  font-family: monospace;
  font-size: 0.9em;
}

/* Handle images and content overflow */
.change-log tbody td img {
  max-width: 100% !important;
  height: auto !important;
  object-fit: contain !important;
}

.change-log tbody td * {
  max-width: 100% !important;
  box-sizing: border-box !important;
}
</style>

<div class="change-log wrapper" style=" margin-bottom: 35px;">
  <h2 class="no-page-break-after">
    <span class="tableHeading">Change Log</span>
  </h2>
  <div>
    <% auditData.forEach(function(log, i) { %>
      <table class="table bordered-table" style="margin-top: 20px; border-style: none !important; break-inside: avoid;">
        <thead>
          <tr style="font-weight: 600;">
            <th>Updated on</th>
            <th>Changed by</th>
            <th>Section</th>
            <th>Field</th>
            <th>Changed from</th>
            <th>Changed to</th>
          </tr>
        </thead>
        <tbody>
          <%- renderChanges(log.changes, 0, log.user_ref.name, momentTz(log.createdAt, 'DD/MM/YY HH:mm' )) %>
        </tbody>
      </table>
    <% }); %>
  </div>
</div>

  